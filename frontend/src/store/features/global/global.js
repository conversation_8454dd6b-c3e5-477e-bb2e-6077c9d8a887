/* eslint-disable no-param-reassign */
/* eslint-disable no-undef */
import { createSlice } from "@reduxjs/toolkit";
import { API, APIV3 } from "../../../utils/axios/index";
import {
    replaceSpecialCharacter,
    setErrorMessage,
} from "../../../utils/helpers/utility_helpers";

import { getQueryParameter } from "../../../utils/helpers/utility_helpers";
import { setGlobalLabelsFromGlobalConfig } from "../../../constants/Constants";

const initialState = {
    loading: 0,
    hideLoader: false,
    lengthyOpLoader: false,
    error: null,
    message: "",
    fiscalCalendar: [],
    navConfig: {},
    notifications: [],
    newSseConnection: false,
    toastMessage: [],
    sseRecievedEvents: {},
    newNotificationIndicator: false,
    global_configs: {},
    // for product and store hierarchy keys, if you want to overwite the frontend declared keys in root constants file, 
    // you can store here this will overwrite the frontend declared keys
    hierachy_keys: {},
    currency_detail:{
        currency_symbol: "$",
        currency_id: 1,
        currency_name: "USD",
    } 
};

const globalSlice = createSlice({
    name: "global",
    initialState,
    reducers: {
        requestStart(state, action) {
            state.loading += 1;
            state.error = false;
        },
        requestStop(state, action) {
            state.loading -= 1;
            state.error = false;
        },
        hideLoaderState(state, action) {
            state.hideLoader = action.payload;
        },
        requestComplete(state, action) {
            state.loading -= 1;
            state.error = false;
            state.message = replaceSpecialCharacter(
                action.payload ? action.payload : null
            );
            if (state.message) {
                const payload = {
                    key: new Date().getTime(),
                    message: state.message,
                    alertType: "info",
                };
                state.toastMessage.push(payload);
            }
        },
        requestFail(state, action) {
            const error =
                replaceSpecialCharacter(action.payload) ||
                "Something went wrong. Please try again";
            state.loading -= 1;
            state.error = replaceSpecialCharacter(error);
            state.message = "";
            if (error) {
                const payload = {
                    key: new Date().getTime(),
                    message: error,
                    alertType: "error",
                };
                state.toastMessage.push(payload);
            }
        },
        requestStartNoLoader(state, action) {
            state.error = false;
        },
        requestCompleteNoLoader(state, action) {
            state.error = false;
            state.message = replaceSpecialCharacter(
                action.payload ? action.payload : null
            );
            if (state.message) {
                const payload = {
                    key: new Date().getTime(),
                    message: state.message,
                    alertType: "info",
                };
                state.toastMessage.push(payload);
            }
        },
        requestFailNoLoader(state, action) {
            const error =
                replaceSpecialCharacter(action.payload) ||
                "Something went wrong. Please try again";
            state.error = replaceSpecialCharacter(error);
            state.message = "";
            if (error) {
                const payload = {
                    key: new Date().getTime(),
                    message: error,
                    alertType: "error",
                };
                state.toastMessage.push(payload);
            }
        },
        toastError(state, action) {
            const error =
                replaceSpecialCharacter(action.payload) ||
                "Something went wrong. Please try again";
            state.error = replaceSpecialCharacter(error);
            state.message = "";
            if (error) {
                const payload = {
                    key: new Date().getTime(),
                    message: error,
                    alertType: "error",
                };
                state.toastMessage.push(payload);
            }
        },
        removeSnack(state, action) {
            const key = action.payload;
            state.toastMessage = state.toastMessage.filter((item) => {
                return item?.key != key;
            });
        },
        resetToastMessage(state, action) {
            state.error = null;
            state.message = null;
        },
        setFiscalCalendar(state, action) {
            state.fiscalCalendar = action.payload;
        },
        setNotifications(state, action) {
            state.notifications = action.payload;
        },
        setNewSseConnection(state, action) {
            state.newSseConnection = action.payload;
        },
        toggleLengthyOpLoader(state, action) {
            state.lengthyOpLoader = action.payload;
        },
        disableLoader(state, action) {
            state.loading = 0;
        },
        setSseRecievedEvents(state, action) {
            state.sseRecievedEvents = {
                ...state.sseRecievedEvents,
                [action.payload.eventId]: action.payload,
            };
        },
        setSseEventTriggeredStatus(state, action) {
            const { eventId, status = true } = action.payload;
            if (eventId && state.sseRecievedEvents[eventId]) {
                state.sseRecievedEvents[eventId].triggeredStatus = status;
            }
        },
        setNewNotificationIndicator(state, action) {
            state.newNotificationIndicator = action.payload;
        },
        setGlobalConfigs(state, action) {
            state.global_configs = action.payload;
        },
        setCurrencyDetail(state, action) {
            state.currency_detail = action.payload;
        },
    },
});

export const {
    requestStart,
    requestComplete,
    requestFail,
    requestStartNoLoader,
    requestCompleteNoLoader,
    requestFailNoLoader,
    toastError,
    resetToastMessage,
    setFiscalCalendar,
    setNotifications,
    setNewSseConnection,
    removeSnack,
    requestStop,
    hideLoaderState,
    toggleLengthyOpLoader,
    disableLoader,
    setSseRecievedEvents,
    setSseEventTriggeredStatus,
    setNewNotificationIndicator,
    setGlobalConfigs,
    setCurrencyDetail,
} = globalSlice.actions;

export default globalSlice.reducer;

export const getFiscalCalendar = (payload) => (dispatch, getState) => {
    dispatch(requestStart());
    return API.post("/fiscal-calendar", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                const { data } = response.data;
                // const fiscalCalendarData = formatCalendarData(data);
                dispatch(setFiscalCalendar(data));
                dispatch(requestComplete());
                return data;
            } else {
                dispatch(requestFail());
                return false;
            }
        })
        .catch((error) => {
            console.log(error);
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
};

export const getNotifications = (showLoader = true) => (dispatch, getState) => {
    if (showLoader) {
        dispatch(requestStart());
    } else {
        dispatch(requestStartNoLoader());
    }
    return API.post("/notifications", { application: "promo" })
        .then((response) => {
            if (response.data && response.data.status === 200) {
                const { data } = response.data;
                // Check which notifications are new and add read = false
                dispatch(setNotifications(data));
                if (showLoader) {
                    dispatch(requestComplete());
                } else {
                    dispatch(requestCompleteNoLoader());
                }
                return data;
            }
        })
        .catch((error) => {
            console.log(error);
            const errorMessage = setErrorMessage(error);
            if (showLoader) {
                dispatch(requestFail(errorMessage));
            } else {
                dispatch(requestFailNoLoader(errorMessage));
            }
            return false;
        });
};

export const updateNotificationReadStatus = (payload) => (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    return API.post("/acknowledge-notifications", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                // Check which notifications are new and add read = false
                dispatch(requestComplete());
                return true;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
};

export const validateOperation = (payload) => async (dispatch) => {
    dispatch(requestStart());
    try {
        const response = await API.post("/validate_edit", payload)
        if (response) {
            if (response.data) {
                const { data } = response.data;
                dispatch(requestComplete());
                return data;
            } else {
                dispatch(requestFail());
                return false;
            }
        }
    } catch (error) {
        console.log(error);
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
};

export const api_markdown = (endpoint, payload, method) => {
    let apiMethod = method || "POST";
    if (apiMethod?.toLowerCase() === "get") {
        let parameterString = getQueryParameter(payload);
        endpoint = `${endpoint}?${parameterString}`; // Format payload for get API here
    }
    return {
        url: endpoint,
        method: apiMethod,
        data: payload,
    };
};

export const excelTemplateDownload = (payload, templateFileName) => async (
    dispatch
) => {
    dispatch(requestStart());
    await APIV3.get("/template-download", payload)
        .then((response) => {
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", `${templateFileName}.xlsx`);
            document.body.appendChild(link);
            link.click();
            dispatch(requestComplete("Template downloaded successfully"));
        })
        .catch((error) => {
            console.log(error);
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const removeLoader = () => (dispatch, getState) => {
    const loading = getState().pricesmartPromoReducer.global.loading;
    if (loading !== 0) {
        dispatch(disableLoader());
    }
};


export const getGlobalConfigs = () => async (dispatch) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.get("/configuration");
        if (response.data && response.data.status === 200) {
            const { data } = response.data;
            dispatch(setGlobalConfigs(data));
            dispatch(requestComplete());
            setGlobalLabelsFromGlobalConfig(data);
            return true;
        }
        return false;
    } catch (error) {
        console.log(error);
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}
