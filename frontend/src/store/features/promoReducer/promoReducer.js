import { createSlice } from "@reduxjs/toolkit";
import {
	API,
	APIV3,
	FormAPI,
} from "../../../utils/axios/index";
import _ from "lodash";

import {
	requestStart,
	requestComplete,
	requestFail,
	toggleLengthyOpLoader,
	requestStartNoLoader
} from "../global/global";

import { setErrorMessage, replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";
import { detailedSimulationMetricFormatter } from "../../../components/screens/offer/OfferHelper";

const initialState = {
	promoDetails: null,
	maxStepCount: null,
	customerOptions: [],
	channelOptions: [],
	isEdited: false,
	invalidScenarios: {},
	validOffers: {},
	isFirstCopy: false,
	activePromoId: null,
	validDiscountLevel: [],
	validOfferTypes: [],
	priorityNumberList: [],
	optValidOffers: {},
	bmsmOfferData: [],
	simulationInvalid: false,
	simulatedPromoId: null,
	isOptimise: false,
	viewByOptions: [],
	tiersData: [],
	noLoader: false,
	validTierOfferTypes: [],
	savedTierId: null,
	conflictedPromoDetails: {},
	conflictedPromoShowModal: false,
	fromStackingView: false,
	targetPromoDetails: {},
	updatedScenarioName: null,
	overideReasons: [],
	overiddenForecastData: null,
	productData: [],
	productGroupData: [],
	productDataFromUploadOrCopyPaste: {},
	storeData: [],
	storeGroupData: [],
	storeDataFromUploadOrCopyPaste: {},
	events: [],
	promoSelectedProducts: [],
	promoSelectedStores: [],
	promoSelectedProductGroups: [],
	promoSelectedStoreGroups: [],
	promoEventDetails: {},
	detailedSimulationResults: {},
	rawDetailedSimulationResults: {},
	vendorFundingDetails: {},
	scenarioUpdatedData: {},
	promoGifData: {},
	specialOfferTypeData: {}
}

const offerslice = createSlice({
	name: "offer",
	initialState,
	reducers: {
		setMaxStepCount(state, action) {
			const { payload } = _.cloneDeep(action);
			state.maxStepCount = payload;
		},
		setCustomerType(state, action) {
			const { payload } = _.cloneDeep(action);
			state.customerOptions = _.cloneDeep(payload);
		},
		setChannelType(state, action) {
			const { payload } = _.cloneDeep(action);
			state.channelOptions = _.cloneDeep(payload);
		},
		setIsEditedFlag(state, action) {
			const { payload } = _.cloneDeep(action);
			state.isEdited = payload;
		},
		setIsAllScenarionValid(state, action) {
			const { payload } = _.cloneDeep(action);
			state.invalidScenarios = payload;
		},
		setValidOffers(state, action) {
			const { payload } = _.cloneDeep(action);
			state.validOffers = payload;
		},
		setIsFirstCopySimulation(state, action) {
			const { payload } = _.cloneDeep(action);
			state.isFirstCopy = payload;
		},
		setActivePromoId(state, action) {
			const { payload } = _.cloneDeep(action);
			state.activePromoId = payload;
		},
		setPromoDetails(state, action) {
			const { payload } = _.cloneDeep(action);
			state.promoDetails = payload;
		},
		setValidDiscountLevel(state, action) {
			const { payload } = _.cloneDeep(action);
			state.validDiscountLevel = payload;
		},
		setValidOfferTypes(state, action) {
			const { payload } = _.cloneDeep(action);
			state.validOfferTypes = payload;
		},
		setPriorityNumberList(state, action) {
			const { payload } = _.cloneDeep(action);
			state.priorityNumberList = payload;
		},
		setBmsmOfferData(state, action) {
			const { payload } = _.cloneDeep(action);
			state.bmsmOfferData = payload;
		},
		setOptimizationValidOffers(state, action) {
			state.optValidOffers = action.payload;
		},
		setSimulationInvalid(state, action) {
			state.simulationInvalid = action.payload;
		},
		setSimulatedPromoId(state, action) {
			state.simulatedPromoId = action.payload;
		},
		setIsOptimise(state, action) {
			state.isOptimise = action.payload;
		},
		setViewByOptions(state, action) {
			state.viewByOptions = action.payload;
		},
		setTiersData(state, action) {
			state.tiersData = action.payload;
		},
		setNoLoader(state, action) {
			state.noLoader = action.payload;
		},
		setValidTierOfferTypes(state, action) {
			state.validTierOfferTypes = action.payload;
		},
		setSavedTierId(state, action) {
			state.savedTierId = action.payload;
		},
		setConflictedPromoDetails(state, action) {
			state.conflictedPromoDetails = action.payload;
		},
		setConflictedPromoShowModal(state, action) {
			state.conflictedPromoShowModal = action.payload;
		},
		setFromStackingView(state, action) {
			state.fromStackingView = action.payload;
		},
		setBaselineLyMetrics(state, action) {
			state.baselineLyMetrics = action.payload;
		},
		setTargetPromoDetail(state, action) {
			state.targetPromoDetails = action.payload;
		},
		setUpdatedScenarioName(state, action) {
			state.updatedScenarioName = action.payload;
		},
		setOverideReasons(state, action) {
			state.overideReasons = action.payload;
		},
		setOveriddenForecastData(state, action) {
			state.overiddenForecastData = action.payload;
		},
		setProductData(state, action) {
			state.productData = action.payload;
		},
		setProducGrouptData(state, action) {
			state.productGroupData = action.payload;
		},
		setProductDataFromUploadOrCopyPaste(state, action) {
			state.productDataFromUploadOrCopyPaste = action.payload;
		},
		setStoreData(state, action) {
			state.storeData = action.payload;
		},
		setStoreDataFromUploadOrCopyPaste(state, action) {
			state.storeDataFromUploadOrCopyPaste = action.payload;
		},
		setStoreGroupData(state, action) {
			state.storeGroupData = action.payload;
		},
		setEvents(state, action) {
			state.events = action.payload;
		},
		setSelectedPromoProducts(state, action) {
			state.promoSelectedProducts = action.payload;
		},
		setSelectedPromoStores(state, action) {
			state.promoSelectedStores = action.payload;
		},
		setSelectedPromoProductGroups(state, action) {
			state.promoSelectedProductGroups = action.payload;
		},
		setSelectedPromoStoreGroups(state, action) {
			state.promoSelectedStoreGroups = action.payload;
		},
		setPromoEventDetails(state, action) {
			state.promoEventDetails = action.payload;
		},
		setDetailedSimulationResults(state, action) {
			state.detailedSimulationResults = action.payload;
		},
		setRawDetailedSimulationResults(state, action) {
			state.rawDetailedSimulationResults = action.payload;
		},
		updatePromoDetails(state, action) {
			const { payload } = _.cloneDeep(action);
			state.promoDetails = {
				...state.promoDetails,
				...payload
			}
		},
		setVendorFundingDetails(state, action) {
			state.vendorFundingDetails = action.payload;
		},
		setScenarioUpdatedData(state, action) {
			const updatedRow = _.cloneDeep(action.payload);
			if(_.isEmpty(updatedRow)) {
				state.scenarioUpdatedData = {};
			} else {
				const tempData = _.cloneDeep(state.scenarioUpdatedData);
				tempData[updatedRow.row_id] = updatedRow;
				state.scenarioUpdatedData = tempData;
			}
		},
		setOfferModeState(state, action) {
			state.offerModeState = action.payload;
		},
		setPromoGifData(state, action) {
			state.promoGifData = action.payload;
		},
		setSpecialOfferTypeData(state, action) {
			state.specialOfferTypeData = action.payload;
		}
	}
});

export const {
	setMaxStepCount,
	setCustomerType,
	setChannelType,
	setIsEditedFlag,
	setIsAllScenarionValid,
	setValidOffers,
	setIsFirstCopySimulation,
	setActivePromoId,
	setPromoDetails,
	setValidDiscountLevel,
	setValidOfferTypes,
	setPriorityNumberList,
	setOptimizationValidOffers,
	setBmsmOfferData,
	setSimulationInvalid,
	setSimulatedPromoId,
	setIsOptimise,
	setViewByOptions,
	setTiersData,
	setNoLoader,
	setValidTierOfferTypes,
	setSavedTierId,
	setConflictedPromoDetails,
	setConflictedPromoShowModal,
	setFromStackingView,
	setBaselineLyMetrics,
	setTargetPromoDetail,
	setUpdatedScenarioName,
	setOverideReasons,
	setOveriddenForecastData,
	setProductData,
	setProducGrouptData,
	setProductDataFromUploadOrCopyPaste,
	setStoreData,
	setStoreGroupData,
	setStoreDataFromUploadOrCopyPaste,
	setEvents,
	setSelectedPromoProducts,
	setSelectedPromoStores,
	setSelectedPromoProductGroups,
	setSelectedPromoStoreGroups,
	setPromoEventDetails,
	setDetailedSimulationResults,
	setRawDetailedSimulationResults,
	updatePromoDetails,
	setVendorFundingDetails,
	setScenarioUpdatedData,
	setOfferModeState,
	setPromoGifData,
	setSpecialOfferTypeData
} = offerslice.actions;

export default offerslice.reducer;

export const getCustomerTypes = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.get(`/customer-type`)
		.then((response) => {
			if (
				response.data && response.data.status === 200
			) {
				dispatch(setCustomerType(response.data.data));
				dispatch(requestComplete());
			} else {
				dispatch(requestFail());
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const getChannelTypes = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.get(`/distribution-channel-type`)
		.then((response) => {
			if (
				response.data && response.data.status === 200
			) {
				dispatch(setChannelType(response.data.data));
				dispatch(requestComplete());
			} else {
				dispatch(requestFail());
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const isInvalidCopySimulation = (val) => (dispatch, getState) => {
	// const temp = getState().pricesmartPromoReducer.promo.invalidScenarios;

	// if(!temp && val) {
	dispatch(setIsAllScenarionValid(val));
	// }
};

export const getValidDiscountLevels = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.post("/valid-discounting-levels", payload)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setValidDiscountLevel(response.data?.data));
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};
export const getValidOfferTypes = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.post("/valid-offer-types", payload)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setValidOfferTypes(response.data?.data));
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const getPriorityNumberList = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.post("/priority-number", payload)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setPriorityNumberList(response.data?.data));
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const getBmsmOfferTypes = () => async (dispatch) => {
	dispatch(requestStart());
	return await API.get("/get-bmsm-offer-types")
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setBmsmOfferData(response.data?.data));
				return response.data?.data;
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const getStep0Basics = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	await APIV3.get(`/promo/${promo_id}/step0/basics`)
		.then((response) => {
			if (
				response.data && response.data.status === 200
			) {
				let { data } = response.data;
				let tempdata = data;

				const promoDetailCurr = _.cloneDeep(
					getState().pricesmartPromoReducer.promo.promoDetails
				);
				if (promoDetailCurr?.promo_id === tempdata?.promo_id) {
					tempdata = {
						...promoDetailCurr,
						...tempdata,
					};
				}

				dispatch(setPromoDetails(tempdata));
				dispatch(requestComplete());
				return data;
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
}

export const getStep1Basics = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	await APIV3.get(`/promo/${promo_id}/step1/basics`)
		.then((response) => {
			if (
				response.data && response.data.status === 200
			) {
				let { data } = response.data;

				const promoDetailCurr = _.cloneDeep(
					getState().pricesmartPromoReducer.promo.promoDetails
				);


				let tempdata = data;
				if (promoDetailCurr.promo_id === tempdata.promo_id) {
					tempdata = {
						...promoDetailCurr,
						...tempdata,
					};
				}

				dispatch(setPromoDetails(tempdata));
				dispatch(requestComplete());
				return tempdata;
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
}

export const getStep1Details = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	await APIV3.get(`/promo/${promo_id}/step1/details`)
		.then((response) => {
			if (
				response.data && response.data.status === 200
			) {
				let { data } = response.data;
				let tempdata = data;
				const promoDetailCurr = getState().pricesmartPromoReducer.promo.promoDetails;
				if (promoDetailCurr.promo_id === tempdata.promo_id) {
					tempdata = {
						...promoDetailCurr,
						...tempdata,
					};
				}

				dispatch(setPromoDetails(tempdata));
				dispatch(requestComplete());
				return tempdata;
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
}

export const getStep2Details = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promo/${promo_id}/step2/basics`)
		if (
			response.data && response.data.status === 200
		) {
			let { data } = response.data;
			let tempdata = data;
			const promoDetailCurr = _.cloneDeep(
				getState().pricesmartPromoReducer.promo.promoDetails
			);
			if (promoDetailCurr.promo_id === tempdata.promo_id) {
				tempdata = {
					...promoDetailCurr,
					...tempdata,
				};
			}

			dispatch(setPromoDetails(tempdata));
			dispatch(requestComplete());
			return tempdata;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
	};
}

export const getVendorFundingDetails = () => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/vendor-funding-types`)
		if (
			response.data && response.data.status === 200
		) {
			let { data } = response.data;
			dispatch(setVendorFundingDetails(data));
			dispatch(requestComplete());
			return data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
	};
}

export const getStep3Basics = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promo/${promo_id}/step3/basics`)
		if (
			response.data && response.data.status === 200
		) {
			let { data } = response.data;
			let tempdata = data;
			const promoDetailCurr = _.cloneDeep(
				getState().pricesmartPromoReducer.promo.promoDetails
			);
			if (promoDetailCurr.promo_id === tempdata.promo_id) {
				tempdata = {
					...promoDetailCurr,
					...tempdata,
				};
			}

			dispatch(setPromoDetails(tempdata));
			dispatch(requestComplete());
			return response.data.data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
	};
}

export const getStep3SimulationResults = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post(`/promo/step3/simulation-results`, payload)
		if (
			response.data && response.data.status === 200
		) {
			let { data } = response.data;
			let tempdata = data;
			const promoDetailCurr = _.cloneDeep(
				getState().pricesmartPromoReducer.promo.promoDetails
			);
			if (promoDetailCurr.promo_id === tempdata.promo_id) {
				tempdata = {
					...promoDetailCurr,
					...tempdata,
				};
			}

			dispatch(setPromoDetails(tempdata));
			dispatch(requestComplete());
			return response.data.data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
	};
}

export const getStackedOffers = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());

	await API.get(
		`/promo/${payload.promo_id}/stacked-offers`
	)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				let { data } = response.data;
				const promoDetailCurr = _.cloneDeep(
					getState().pricesmartPromoReducer.promo.promoDetails
				);
				if (promoDetailCurr.promo_id === payload.promo_id) {
					data = {
						...promoDetailCurr,
						...data,
					};
				}

				dispatch(setPromoDetails(data));
				dispatch(requestComplete());
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const getViewByOptions = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.get(
		`/view-by?type=${payload.tableType}&screen_type=${payload.screenType}`
	)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setViewByOptions(response.data?.data));
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const getTiers = (payload) => async (dispatch, getState) => {
	if (!getState().pricesmartPromoReducer.promo?.noLoader)
		dispatch(requestStart());

	return await API.post("/get-tiers", payload)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				if (!getState().pricesmartPromoReducer.promo?.noLoader)
					dispatch(requestComplete());
				dispatch(setTiersData(response.data?.data));
				return response.data?.data;
			} else {
				if (!getState().pricesmartPromoReducer.promo?.noLoader)
					dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			if (!getState().pricesmartPromoReducer.promo?.noLoader)
				dispatch(requestFail(errorMessage));
			else
				dispatch(toastError(errorMessage));
			return false;
		});
};

export const deleteTier = (payload) => async (dispatch) => {
	dispatch(requestStart());
	await API.post("/delete-tier", payload.reqObj)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				if (payload.isCallGetTier) {
					dispatch(getTiers({ promo_id: payload.reqObj.promo_id, }));
				}
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const saveTier = (payload) => async (dispatch, getState) => {
	if (payload?.has_scenario)
		dispatch(toggleLengthyOpLoader(true));

	if (!getState().pricesmartPromoReducer.promo.noLoader)
		dispatch(requestStart());
	try {
		const response = await API.post("/tier-management", payload);
		if (response) {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				if (!getState().pricesmartPromoReducer.promo.noLoader)
					dispatch(requestComplete());
				await dispatch(getTiers({ promo_id: payload.promo_id }));
				if (payload.apply) {
					dispatch(setSavedTierId({
						...response.data?.data,
						col_id: payload.col_id,
						row_id: payload.row_id,
					}));
				}
				return true;
			} else {
				if (!getState().pricesmartPromoReducer.promo.noLoader)
					dispatch(requestFail());
				return false;
			}
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);
		if (!getState().pricesmartPromoReducer.promo.noLoader) {
			dispatch(requestFail(errorMessage));
			dispatch(toggleLengthyOpLoader(false))
		}
		else
			dispatch(toastError(errorMessage));
		return false;
	}
};

export const getValidTierOfferTypes = () => async (dispatch) => {
	dispatch(requestStart());
	return await API.get("/valid-tier-offer-types")
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setValidTierOfferTypes(response.data?.data));
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const generateSimulationData = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/simulate-promo", payload)
		if (
			(response.status === 200 || response.status === true) &&
			response.data?.data?.is_conflict
		) {
			const { data } = response.data;
			dispatch(setConflictedPromoDetails(data));
			dispatch(setConflictedPromoShowModal(true));
			dispatch(toggleLengthyOpLoader(false));
			dispatch(requestFail(data.message));
			return false;
		}
		if (response.status === 200 || response.status === true) {
			dispatch(requestComplete());
			dispatch(toggleLengthyOpLoader(true));
			return true;
		}
		if (response.status !== 200) {
			dispatch(toggleLengthyOpLoader(false));
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);

		dispatch(toggleLengthyOpLoader(false));
		dispatch(requestFail(errorMessage));
		return false;

	};
}
export const getValidOffersDetails = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	const { promo_id, hierarchy_id, actionType } = payload;
	await API.get(
		`/valid-offers?promo_id=${promo_id}&simulator_action=${actionType}`
	)
		.then((response) => {
			if (response.status === 200 || response.status === true) {
				let { data } = response.data;
				let tempdata = data?.[promo_id] || data;
				if (actionType === "optimise") {
					dispatch(setOptimizationValidOffers(data));
				} else {
					dispatch(
						setValidOffers({
							...getState().pricesmartPromoReducer.promo
								.validOffers,
							[promo_id]: [
								...tempdata
							],
						})
					);
				}
				dispatch(requestComplete());
				return data;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const getTargetPromoDetail = ({ promo_id, section, step_count }) => async (
	dispatch,
	getState
) => {
	dispatch(requestStartNoLoader());
	return APIV3.get(`/promo/${promo_id}/step3/optimisation-details`)
		.then((response) => {
			if (response.status === 200 || response.status === true) {
				let { data } = response.data;

				dispatch(setTargetPromoDetail(data));
				dispatch(requestComplete());
				return data;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail());
			// Throw error to be handled by the caller, which is OfferTargetPanel.jsx
			throw error;
		});
};

export const getBaselineLyMetrics = (promo_id) => (dispatch, getState) => {
	dispatch(requestStart());
	return API.get(`/ly-targets?promo_id=${promo_id}`)
		.then((response) => {
			if (response.status === 200 || response.status === true) {
				let { data } = response.data;
				dispatch(setBaselineLyMetrics(data));
				dispatch(requestComplete());
				return data;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			// Throw error to be handled by the caller, which is OfferTargetPanel.jsx
			throw error;
		});
};

export const optimizeSimulation = (payload) => (dispatch, getState) => {
	dispatch(setIsOptimise(true));
	dispatch(toggleLengthyOpLoader(true));
	dispatch(requestStart());
	return APIV3.post("/optimise-promo", payload)
		.then((response) => {
			if (
				(response.status === 200 || response.status === true) &&
				response.data?.data?.is_conflict
			) {
				const { data } = response.data;
				dispatch(setConflictedPromoDetails(data));
				dispatch(setConflictedPromoShowModal(true));
				dispatch(toggleLengthyOpLoader(false));
				dispatch(requestFail(data.message));
				return false;
			}
			if (response.status === 200 || response.status === true) {
				dispatch(requestComplete());
				return {
					status: true,
				};
			} else {
				const errorMessage = response.message;
				dispatch(requestFail(errorMessage));
				dispatch(toastError(errorMessage));
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			if (payload.promo_target && !_.isEmpty(payload.promo_target)) {
				dispatch(toggleLengthyOpLoader(false));
				dispatch(requestFail(errorMessage));
				dispatch(toastError(errorMessage));
				// Throw error to be handled by the caller, which is OfferTargetPanel.jsx
				throw error;
			} else {
				dispatch(toggleLengthyOpLoader(false));
				dispatch(requestFail(errorMessage));
				// Throw error to be handled by the caller, which is OfferTargetPanel.jsx
				throw error;
			}
		});
};
export const editOptimizeSimulation = (payload) => (dispatch, getState) => {
	dispatch(setIsOptimise(true));
	dispatch(toggleLengthyOpLoader(true));
	dispatch(requestStart());
	return APIV3.put("/reoptimise-promo", payload)
		.then((response) => {
			if (
				(response.status === 200 || response.status === true) &&
				response.data?.data?.is_conflict
			) {
				const { data } = response.data;
				dispatch(setConflictedPromoDetails(data));
				dispatch(setConflictedPromoShowModal(true));
				dispatch(toggleLengthyOpLoader(false));
				dispatch(requestFail(data.message));
				return false;
			}
			if (response.status === 200 || response.status === true) {
				dispatch(requestComplete());
				return {
					status: true,
				};
			} else {
				const errorMessage = response.message;
				dispatch(requestFail(errorMessage));
				dispatch(toastError(errorMessage));
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			if (payload.promo_target && !_.isEmpty(payload.promo_target)) {
				dispatch(toggleLengthyOpLoader(false));
				dispatch(requestFail(errorMessage));
				dispatch(toastError(errorMessage));
				// Throw error to be handled by the caller, which is OfferTargetPanel.jsx
				throw error;
			} else {
				dispatch(toggleLengthyOpLoader(false));
				dispatch(requestFail(errorMessage));
				// Throw error to be handled by the caller, which is OfferTargetPanel.jsx
				throw error;
			}
		});
};

export const approveScenario = ({ payload }) => async (dispatch, getState) => {
	if (payload.status_id === 4 || payload.status_id === 8)
		dispatch(toggleLengthyOpLoader(true));
	else dispatch(requestStart());
	try {
		const response = await APIV3.post(`/promo/approve-scenario`, payload);

		if (
			(response.status === 200 || response.status === true) &&
			response.data?.data?.is_conflict
		) {
			const { data } = response.data;
			dispatch(setConflictedPromoDetails(data));
			dispatch(setConflictedPromoShowModal(true));
			dispatch(requestFail(data.message));
			return false;
		}
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			// Wait for SSE
			dispatch(
				requestComplete(
					response.data.message || "Successfully approved the data"
				)
			);
			return true;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};


export const updateScenarioName = (payload) => (dispatch) => {
	dispatch(requestStart());
	return API.post("/update-scenario-name", payload)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setUpdatedScenarioName(payload.scenario_name));
			} else {
				dispatch(requestFail());
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const saveAsDefault = (reqObj) => async (dispatch) => {
	dispatch(requestStart());
	const { payload, isRefreshSimulationResults, promoId, currency_detail } = reqObj
	await API.post("/promo/set-as-default", payload)
		.then(response => {
			if (response.data && response.status === 200) {
				dispatch(requestComplete(response.data.message));
				if (isRefreshSimulationResults && promoId) {
					dispatch(getStep3SimulationResults({
						promo_id: promoId,
						target_currency_id: currency_detail?.currency_id,
					}))
				}
				return response.data;
			}
			else
				dispatch(requestFail());
			return false;
		})
		.catch(error => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
}

export const getOverrideReason = () => (dispatch) => {
	dispatch(requestStart());

	return API.get("/get-override-reason")
		.then((response) => {
			if (
				response.data &&
				(response.data.status ||
					response.data.status === 200 ||
					response.data.status === true)
			) {
				dispatch(requestComplete());
				dispatch(setOverideReasons(response.data.data));
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
}

export const getOverrideForecast = (payload) => (dispatch) => {
	dispatch(requestStart());
	return API.post("/promo/get-override-forecast", payload)
		.then(response => {
			if (response.data && response.status === 200) {
				dispatch(requestComplete());
				dispatch(setOveriddenForecastData(response.data.data));
			}
			else
				dispatch(requestFail());
			return false;
		})
		.catch(error => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
}

export const submitOverrideForecast = (payload) => (dispatch) => {
	dispatch(requestStart());
	dispatch(toggleLengthyOpLoader(true));
	return API.post("/promo/override-forecast", payload)
		.then(response => {
			if (response.data && response.status === 200) {
				dispatch(requestComplete(response.data.message));
				return response.data;
			}
			else
				dispatch(requestFail());
			return false;
		})
		.catch(error => {
			console.log(error);
			const errorMessage = setErrorMessage(error);
			dispatch(toggleLengthyOpLoader(false));
			dispatch(requestFail(errorMessage));
			return false;
		});
}
export const createPromoStep0 = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/promo", payload);
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			dispatch(requestComplete());
			dispatch(setPromoDetails(response.data?.data));
			dispatch(setActivePromoId(response.data?.data?.promo_id));
			return true;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const editAtStep0 = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.put("/promo/step-0", payload);
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			dispatch(requestComplete());
			return true;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const createPlaceholderPromo = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/placeholder-promo", payload);
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			dispatch(requestComplete());
			dispatch(setPromoDetails(response.data?.data));
			dispatch(setActivePromoId(response.data?.data?.promo_id));
			return true;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const editPlaceholderPromo = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.put("/placeholder-promo", payload);
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			dispatch(requestComplete());
			return true;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getEvents = (payload) => async (dispatch) => {
	dispatch(requestStart());
	const APIEndPoint = payload?.is_locked == false ? `/get-events?is_locked=${payload?.is_locked}` : "/get-events";
	try {
		const response = await APIV3.get(APIEndPoint, payload);
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			dispatch(requestComplete());
			dispatch(setEvents(response.data?.data));
			return true;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

//  ------------------------------ get product related data ------------------------------

export const getProductGroups = (payload) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	await API.post("/get-product-groups", payload)
		.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				dispatch(requestComplete(response?.data?.message));
				dispatch(setProducGrouptData(_.cloneDeep(data)));
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);

				if (!_.isEmpty(response.data?.data)) {
					dispatch(
						setEventProductGroupData(_.cloneDeep(response.data.data))
					);
				}
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const getProducts = (payload) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	await API.post("/products", payload)
		.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				dispatch(requestComplete(response?.data?.message));
				dispatch(
					setProductData((_.cloneDeep(data)))
				);
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const getProductDataFromFile = (payload) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	try {
		const response = await FormAPI.post(`/get-products-from-file${payload.event_id ? '?eventId=' + payload.event_id : ""}`, payload.file);
		if (response.data && response.data.status === 200) {
			dispatch(
				setProductDataFromUploadOrCopyPaste(response?.data?.data)
			);
			dispatch(requestComplete(response?.data?.message));
			return true;
		} else {
			dispatch(
				requestFail(
					response?.data?.error || response?.data?.message
				)
			);
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	};
};

export const getProductFromIds = (payload) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	await API.post("/sku-details", payload)
		.then((response) => {
			if (response.data && response.data.status === 200) {
				dispatch(
					setProductDataFromUploadOrCopyPaste(response?.data?.data)
				);
				dispatch(requestComplete(response?.data?.message));
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const getProductConfigs = () => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/config-details?config=product_selections_type`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data.data;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const getProductFromExcelForProductExclusion = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await FormAPI.post(`/upload-exclusion`, payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

// ------------------------------ get store related data ------------------------------
export const getStores = (payload,) => async (dispatch, getState) => {
	dispatch(requestStart());
	await API.post("/stores", payload)
		.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				dispatch(requestComplete(response?.data?.message));
				dispatch(setStoreData(_.cloneDeep(data)));
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const getStoresFromFile = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await FormAPI.post(`/get-stores-from-file${payload.event_id ? '?eventId=' + payload.event_id : ""}`, payload.file);
		if (response) {
			if (response.data && response.data.status === 200) {
				dispatch(setStoreDataFromUploadOrCopyPaste(response?.data?.data));
				dispatch(requestComplete(response?.data?.message));
				return true;
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
				return false;
			}
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const getStoresFromIds = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.post("/store-details", payload);
		if (response) {
			if (response.data && response.data.status === 200) {
				dispatch(setStoreDataFromUploadOrCopyPaste(response?.data?.data));
				dispatch(requestComplete(response?.data?.message));
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
	};
};

export const getStoreGroups = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	await API.post("/get-store-groups", payload)
		.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				dispatch(requestComplete(response?.data?.message));
				dispatch(setStoreGroupData(_.cloneDeep(data)));
			} else {
				dispatch(
					requestFail(response?.data?.error || response?.data?.message)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const getPromoEventDetails = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	await APIV3.get(`/events/${payload.event_id}`)
		.then((response) => {
			if (response.data && response.data.status === 200) {
				dispatch(setPromoEventDetails(response.data.data));
				dispatch(requestComplete());
			} else {
				dispatch(requestFail(response?.data?.error || response?.data?.message));
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
}

export const getStoreConfigs = () => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/config-details?config=store_selections_type`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data.data;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

// ------------------------------ promo save apis ------------------------------

export const savePromoStep1 = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/promo/step-1", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return true;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const editPromoStep1 = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.put("/promo/step-1", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return true;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const getDetailedSimulationResults = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.post("/simulation-results-details", payload)
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			const formattedSimulationResults = !_.isEmpty(response.data.data)
				? detailedSimulationMetricFormatter(response.data.data)
				: [];
			const existingSimulationResults = getState().pricesmartPromoReducer.promo.detailedSimulationResults;

			dispatch(
				setDetailedSimulationResults({
					...existingSimulationResults,
					[payload.aggregation]: formattedSimulationResults,
				})
			);
			let existingRawSimulationResults = getState().pricesmartPromoReducer.promo.rawDetailedSimulationResults;
			dispatch(
				setRawDetailedSimulationResults({
					...existingRawSimulationResults,
					[payload.aggregation]: response.data.data,
				})
			);
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		console.log(error);
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	};
};

export const editPromoStep2 = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.put("/promo/step-2", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return true;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const fetchExecuteMetadata = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/exmd-promo?promo_id=${payload.promo_id}`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			const data = response.data?.data?.[0] ? _.cloneDeep(response.data?.data?.[0]) : {};
			Object.keys(data).forEach(key => {
				if (typeof data[key] !== "object" && !_.isEmpty(data[key]))
					data[key] = replaceSpecialCharacter(data[key])
				if (_.isArray(data[key]) && data[key].length > 0) {
					data[key] = _.cloneDeep(data[key][0]);
				}
			})

			return data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getTargetFolderOptions = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/exmd-target-folder?promo_id=${payload.promo_id}`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());

			return response.data?.data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getPriceFilterOptions = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/exmd-price-filter?promo_id=${payload.promo_id}`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());

			return response.data?.data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}
export const getAtsCheckOptions = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/exmd-sfcc-ats-check?promo_id=${payload.promo_id}`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());

			return response.data?.data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getDropShipOptions = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/exmd-sfcc-dropship-options?promo_id=${payload.promo_id}`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());

			return response.data?.data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getTemplateOptions = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/exmd-template-id?promo_id=${payload.promo_id}`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());

			return response.data?.data;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}
export const saveExecutionMetaData = (payload) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.post("/save-exmd", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response.data.message));
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getExclsionData = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promo/${payload.promo_id}/step1/exclusions`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			dispatch(updatePromoDetails({
				...response.data?.data
			}));
			return true;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const calculateGrossMarginAPI = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.post("/calculate-gross-margin", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const promoDownloads = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.post("/promo-downloads", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response?.data?.message));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getTableMetadata = (promo_id) => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/promo/${promo_id}/discounts/table-metadata`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());

			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getPromoDiscounts = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/get-promo-discounts", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			let { data } = response.data;
			let tempdata = data;
			const promoDetailCurr = _.cloneDeep(
				getState().pricesmartPromoReducer.promo.promoDetails
			);
			let tempSimulationData = [];
			if (promoDetailCurr.promo_id === payload.promo_id) {
				tempSimulationData = [...tempdata];
			}
			dispatch(updatePromoDetails({
				...promoDetailCurr,
				simulation: tempSimulationData
			}));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getDiscountRules = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/discount-levels?promo_id=${payload}`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getNewScenarioID = () => async (dispatch) => {
	dispatch(requestStart());
	try {
		const response = await API.get("/new-scenario-id");
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};


export const getSpecialOfferTypes = () => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get("/special-offer-types");
		if(response.data && response.data.status === 200){
			dispatch(requestComplete());
			dispatch(setSpecialOfferTypeData(response.data?.data));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getSpecialOfferTypeDetails = (specialOfferIdentifier) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/special-offer-types/${specialOfferIdentifier}`);
		if(response.data && response.data.status === 200){
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const bulkEditScenarioData = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.put("/promo/bulk-edit-discounts", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return true;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getPromoGifData = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/get-time-estimates", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			dispatch(setPromoGifData(response.data?.data));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const downloadPriceFile = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await API.get(`/promos/${payload.promo_id}/sku-price-data-download`);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const approveIaScenario = ({ payload }) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		dispatch(toggleLengthyOpLoader(true));
		const response = await APIV3.post(`/promo/approve-ia-scenario`, payload);

		
		if (
			response.data &&
			(response.data.status === 200 || response.data.status === true)
		) {
			
			dispatch(
				requestComplete(
					response.data.message || "Successfully approved the data"
				)
			);
			return true;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	} finally {
		dispatch(toggleLengthyOpLoader(false));
	}

};

export const copyScenarioData = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/promo/copy-discounts", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return true;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}
	

export const getCurrencyOptions = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/currency-filter", payload);
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}