import { createSlice } from "@reduxjs/toolkit";
import { API, APIV3 } from "../../../utils/axios/index";
import _, { set } from "lodash";

import { requestStart, requestComplete, requestFail } from "../global/global";
import { setErrorMessage } from "../../../utils/helpers/utility_helpers";

import {
    waterfallMetricDataFormatter,
    offerPerformanceMetricDataFormatter,
} from "../../../components/screens/reporting/reportingHelper";

const initialState = {
    reportingMetrics: {},
    waterfallMetrics: {},
    offerPerformanceMetrics: {},
};

const reportingSlice = createSlice({
    name: "reporting",
    initialState,
    reducers: {
        setReportingMetrics(state, action) {
            const { payload } = _.cloneDeep(action);
            state.reportingMetrics = _.cloneDeep(payload);
        },
        setWaterfallMetrics(state, action) {
            const { payload } = _.cloneDeep(action);
            state.waterfallMetrics = _.cloneDeep(payload);
        },
        setOfferPerformanceMetrics(state, action) {
            const { payload } = _.cloneDeep(action);
            state.offerPerformanceMetrics = _.cloneDeep(payload);
        },
    },
});

export const {
    setReportingMetrics,
    setWaterfallMetrics,
    setOfferPerformanceMetrics,
} = reportingSlice.actions;

export default reportingSlice.reducer;

export const fetchReportingMetrics = ({ payload, aggregation }) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    return API.post(`/get-post-offer-analysis-report`, payload)
        .then((response) => {
            if (
                response.data &&
                (response.data.status === 200 || response.data.status === 1)
            ) {
                const { data = [] } = response.data;
                // Merge with existing data in Redux
                const { reportingMetrics } = getState().pricesmartPromoReducer.reporting;

                if (data?.length > 0) {
                    // Process the API data
                    const processedData = data.map((item) => ({
                        ...item, // Spread main object
                        ...(item["Sales $"] || {}), // Flatten Sales $
                        ...(item["Sales U"] || {}), // Flatten Sales U
                        ...(item["GM $"] || {}),    // Flatten GM $
                        ...(item["GM %"] || {}),    // Flatten GM %
                        ...(item["CM $"] || {}),    // Flatten CM $
                        ...(item["CM %"] || {}),    // Flatten CM %
                        ...(item["ASP"] || {}),    // Flatten ASP
                        ...(item["AUM"] || {}),    // Flatten AUM
                    }));

                    

                    const updatedMetrics = {
                        ...reportingMetrics,
                        [aggregation]: processedData,
                    };

                    dispatch(setReportingMetrics(updatedMetrics));
                } else {
                    const updatedMetrics = {
                        ...reportingMetrics,
                        [aggregation]: [],
                    };
                    dispatch(setReportingMetrics(updatedMetrics));
                }

                dispatch(requestComplete());
            } else {
                dispatch(requestFail());
            }
        })
        .catch((error) => {
            console.error("API Error:", error);
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};


export const fetchWaterfallMetrics = ({ payload, elementLabels }) => async (
    dispatch
) => {
    dispatch(requestStart());
    return API.post(`/get-reports-graph-data`, payload)
        .then((response) => {
            if (
                response.data &&
                (response.data.status === 200 || response.data.status === 1)
            ) {
                const { data } = response.data;
                const {
                    revenueWaterfallMetricData,
                    marginWaterfallMetricData,
                } = waterfallMetricDataFormatter(data, elementLabels);
                dispatch(
                    setWaterfallMetrics({
                        margin: marginWaterfallMetricData,
                        revenue: revenueWaterfallMetricData,
                    })
                );
                dispatch(requestComplete());
            } else {
                dispatch(requestFail());
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const fetchOfferPerformanceMetrics = (payload) => async (dispatch) => {
    dispatch(requestStart());
    return APIV3.post(`/get-top-and-bottom-performing-offers`, payload)
        .then((response) => {
            if (
                response.data &&
                (response.data.status === 200 || response.data.status === 1)
            ) {
                const { data } = response.data;
                const {
                    topOfferMetricData,
                    bottomOfferMetricData,
                } = offerPerformanceMetricDataFormatter(data);
                dispatch(
                    setOfferPerformanceMetrics({
                        top: topOfferMetricData,
                        bottom: bottomOfferMetricData,
                    })
                );
                dispatch(requestComplete());
            } else {
                dispatch(requestFail());
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const downloadOfferReports = ({ payload, endpoint }) => (dispatch) => {
    dispatch(requestStart());
    API.post(`/${endpoint}`, payload)
        .then((response) => {
            dispatch(
                requestComplete(
                    "Report download will take a few minutes. You will be notified once it is completed."
                )
            );
            return response.data;
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};
