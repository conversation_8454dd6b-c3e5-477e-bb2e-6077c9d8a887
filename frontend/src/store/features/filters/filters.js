import { createSlice } from "@reduxjs/toolkit";
import _, { cloneDeep } from "lodash";
import { API, APIV3, FormAPI } from "../../../utils/axios/index";
import {
	requestStart,
	requestComplete,
	requestFail,
	requestStartNoLoader,
	requestFailNoLoader,
} from "../global/global";
import {
	findUniqueObjectsInArray,
	convertSpecialCharactersInFilters,
	setErrorMessage
} from "../../../utils/helpers/utility_helpers";

const initialState = {
	filtersData: {},
	uploadedSkuDetails: [],
};

const filterSlice = createSlice({
	name: "filters",
	initialState,
	reducers: {
		setFiltersData(state, action) {
			let tempFiltersData = cloneDeep(state.filtersData);
			const { payload, activeScreen, filterName, selectedItems } = cloneDeep(action.payload);
			let tempSelectedFiltersArray = _.map(selectedItems, item => item.value);
			// sets filters data into current filter of active screen
			tempFiltersData = {
				...tempFiltersData,
				[activeScreen]: {
					...tempFiltersData?.[activeScreen],
					[filterName]: {
						options: cloneDeep(payload),
						selectedOptions: cloneDeep(selectedItems),
						selectedOptionsArray: cloneDeep(tempSelectedFiltersArray)
					}
				}
			}
			state.filtersData = cloneDeep(tempFiltersData)

		},
		setSelectedFiltersInStore(state, action) {
			let tempFiltersData = cloneDeep(state.filtersData);
			const { payload, activeScreen, filterName } = cloneDeep(action.payload);
			let tempSelectedFiltersArray = _.map(payload, item => item.value);
			//set or change selected filter options of current filter of acive screen
			tempFiltersData = {
				...tempFiltersData,
				[activeScreen]: {
					...tempFiltersData?.[activeScreen],
					[filterName]: {
						...tempFiltersData?.[activeScreen]?.[filterName],
						selectedOptions: cloneDeep(payload),
						selectedOptionsArray: cloneDeep(tempSelectedFiltersArray)
					}
				}
			}
			state.filtersData = cloneDeep(tempFiltersData)
		},
		setSelectedGenricFiltersInStore(state, action) {
			const { payload, activeScreen, filterName } = cloneDeep(action.payload);
			//set or change selected filter options of current filter of acive screen
			state.filtersData = {
				...state.filtersData,
				[activeScreen]: {
					...state.filtersData?.[activeScreen],
					[filterName]: cloneDeep(payload)
				}
			}
		},
		setSelectedDateRangeIntoStore(state, action) {
			const { payload, activeScreen, filterName } = cloneDeep(action.payload);
			//set or change date range of active screen
			state.filtersData = {
				...state.filtersData,
				[activeScreen]: {
					...state.filtersData?.[activeScreen],
					[filterName]: cloneDeep(payload)
				}
			}
		},
		resetFiltersForNextIds(state, action) {
			const { payload, activeScreen } = cloneDeep(action.payload);
			let tempFiltersData = cloneDeep(state.filtersData?.[activeScreen]);
			//iterarte through all the id(s), if data for a id is present in store,
			//reset all the filter data for that id
			for (let filterId of payload) {
				if (filterId in tempFiltersData) {
					tempFiltersData[filterId] = {
						options: [],
						selectedOptions: [],
						selectedOptionsArray: []
					}
				}
			}
			//push it to state.
			state.filtersData = {
				...state.filtersData,
				[activeScreen]: cloneDeep(tempFiltersData)
			}

		},
		resetFilters(state, action) {
			const { activeScreen } = cloneDeep(action.payload);
			let tempFiltersData = cloneDeep(state.filtersData?.[activeScreen]);
			//iterarte through all the id(s) present int the store for current screen,
			//reset all the filter data for that id
			for (let filterId in tempFiltersData) {
				if (filterId !== "dateRange") {
					tempFiltersData[filterId] = {
						options: [],
						selectedOptions: [],
						selectedOptionsArray: []
					}
				} else {
					tempFiltersData[filterId] = {
						start_date: null,
						end_date: null
					}
				}
				//push it to state.
			}
			state.filtersData = {
				...state.filtersData,
				[activeScreen]: cloneDeep(tempFiltersData)
			}

		},
		setUploadedSkuDetails(state, action) {
			const uploadedSkuDetailsTemp = _.cloneDeep(
				state.uploadedSkuDetails
			);
			// Find unique
			const uniqueSkuDetails = findUniqueObjectsInArray(
				uploadedSkuDetailsTemp,
				action.payload,
				"product_h5_id"
			);
			state.uploadedSkuDetails = uniqueSkuDetails;
		},
		overwriteFilters(state, action) {
			const { activeScreen, filtersData } = cloneDeep(action.payload);

			state.filtersData = {
				...state.filtersData,
				[activeScreen]: cloneDeep(filtersData)
			}

		},
	}
});

export const {
	setUploadedSkuDetails,
	setFiltersData,
	setSelectedFiltersInStore,
	setSelectedGenricFiltersInStore,
	resetFiltersForNextIds,
	setSelectedDateRangeIntoStore,
	overwriteFilters,
	resetFilters
} = filterSlice.actions;

export default filterSlice.reducer;

export const getFilterOptions = ({
	requestObject,
	filterName,
	from,
	selectedItems,
	filterEndpoint,
	selectOnLoad,
}) => async (dispatch, getState) => {
		dispatch(requestStartNoLoader());
		try {
			// const v3Filters = ["event", "promo"];
			// const APITOUSE = v3Filters.includes(filterName) ? APIV3 : Markdown_API;
			const response = await APIV3.post(`/${filterEndpoint}`, requestObject);
			
			if (response.data && response.data.status === 200) {
				// Handle Special characters in label
				let data = convertSpecialCharactersInFilters(
					_.cloneDeep(response.data.data)
				);

				const filtersDataPayload = {
					payload: data || [],
					filterName: filterName || "",
					dataType: "options",
					activeScreen: from,
					selectedItems: [],
				}
				//if default filter selection is present, check the selectedItems type and set the
				//selectedItems in filtersDataPayload
				if (selectOnLoad) {
					if (selectedItems === "All") {
						filtersDataPayload.selectedItems = _.cloneDeep(data)
					}
					if (selectedItems === "First") {
						filtersDataPayload.selectedItems = _.cloneDeep(data[0])
					}
					if (typeof selectedItems === "object" && selectedItems.length) {
						filtersDataPayload.selectedItems = _.filter(data, item => selectedItems.includes(item.value))
					}
				}
				dispatch(setFiltersData(filtersDataPayload));
			}
		}
		catch (error) {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFailNoLoader(errorMessage));
		}
	};

export const setSelectedFilters = ({ data, from, filterId }) => async (dispatch, getState) => {
	let tempData = _.cloneDeep(data);
	tempData = convertSpecialCharactersInFilters(tempData);
	const filtersDataPayload = {
		payload: tempData || [],
		filterName: filterId || "",
		dataType: "options",
		activeScreen: from,
	}
	dispatch(setSelectedFiltersInStore(filtersDataPayload));

}

export const setSelectedGenricFilters = ({ data, from, filterId }) => async (dispatch, getState) => {
	const filtersDataPayload = {
		payload: data,
		filterName: filterId || "",
		activeScreen: from,
	}
	dispatch(setSelectedGenricFiltersInStore(filtersDataPayload));
}

export const setDateRangeToStore = ({ dateRange, from, key }) => async (dispatch, getState) => {
	const filtersDataPayload = {
		payload: dateRange || {},
		filterName: key || "",
		activeScreen: from,
	}
	dispatch(setSelectedDateRangeIntoStore(filtersDataPayload));

}

export const resetFiltersDataForId = ({ data, from }) => async (dispatch, getState) => {
	const filtersDataPayload = {
		payload: data || [],
		activeScreen: from,
	}
	dispatch(resetFiltersForNextIds(filtersDataPayload));
}

export const resetAllFiltersData = ({ from }) => async (dispatch, getState) => {
	const filtersDataPayload = {
		activeScreen: from,
	}
	dispatch(resetFilters(filtersDataPayload));
}


export const getProductsForSelectedFilters = (requestObject) => (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	return API.post("/products", requestObject)
		.then((response) => {
			if (response.data && response.status === 200) {
				const { data } = response.data;
				dispatch(setUploadedSkuDetails(data));
				dispatch(requestComplete());
				return data;
			} else {
				dispatch(requestFail());
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return true;
		});
};

export const getDetailsForSkuFromExcel = (file) => (dispatch, getState) => {
	dispatch(requestStart());
	const formData = new FormData();
	formData.append("file", file);

	return FormAPI.post("/get-products-from-file", formData)
		.then((response) => {
			if (response.data && response.status === 200) {
				const { data } = response.data;
				//rewrite the sortProductHierarchy function for data, and save it to sortedSkuDetails
				// const sortedSkuDetails = sortProductHierarchy(data);
				const sortedSkuDetails = _.cloneDeep(data);
				const inactiveSkus = sortedSkuDetails?.product_h5
					?.filter((sku) => sku.is_active === 0)
					?.map((s) => s.product_h5_id);

				dispatch(setUploadedSkuDetails(sortedSkuDetails?.product_h5));
				dispatch(
					setUploadError({
						invalid: data.invalid,
						out_of_event: data.out_of_event,
						inactive: inactiveSkus,
					})
				);
				dispatch(requestComplete());
			} else {
				dispatch(requestFail());
				return false;
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const getDetailsForSku = (requestObject) => (dispatch, getState) => {
	dispatch(requestStart());
	return API.post("/sku-details", requestObject)
		.then((response) => {
			if (response.data && response.status === 200) {
				const { data } = response.data;
				//rewrite the sortProductHierarchy function for data, and save it to sortedSkuDetails
				// const sortedSkuDetails = sortProductHierarchy(data);
				const sortedSkuDetails = _.cloneDeep(data);
				const inactiveSkus = sortedSkuDetails?.product_h5
					?.filter((sku) => sku.is_active === 0)
					?.map((s) => s.product_h5_id);

				dispatch(setUploadedSkuDetails(sortedSkuDetails?.product_h5));
				dispatch(
					setUploadError({
						invalid: data.invalid,
						out_of_event: data.out_of_event,
						inactive: inactiveSkus,
					})
				);
				dispatch(requestComplete());
			} else {
				dispatch(requestFail());
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return true;
		});
};

export const getCompletedOffersFilters = ({ req, filterName, from }) => async (dispatch) => {
    dispatch(requestStart());
    return API.post("/get-promos-by-filters", req)
        .then((response) => {
            if (response.status === 200) {
                const { data = [] } = response.data;
                const completedOffersData = data.map((offer) => ({
                    label: offer.label,
                    value: offer.value,
                }));
                dispatch(setFiltersData({
                    payload: completedOffersData,
                    filterName,
                    activeScreen: from,
                }));
                dispatch(requestComplete());
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};