import { createSlice } from "@reduxjs/toolkit";
import { API, FormAPI } from "../../../utils/axios/index";
import _ from "lodash";

import {
    requestStart,
    requestComplete,
    requestFail,
} from "../global/global";

import { setErrorMessage } from "../../../utils/helpers/utility_helpers";
import { api_markdown } from "../global/global";

const initialState = {
    storeConfigurationTableData: [],
    selectedStoreGroupConfigData: [],
    ungroupedStoreDetails: {},
    createStoreConfigTableData: [],
    selectedCreateStoreConfigTableData: [],
    tableDataFromUploadorCopyPaste: {},
    editStoreGroupFlag: false,
};

const storeConfigrationSlice = createSlice({
    name: "storeConfiguration",
    initialState,
    reducers: {
        setStoreConfigTableData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.storeConfigurationTableData = _.cloneDeep(payload);
        },
        setStoreUngroupedStoreData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.ungroupedStoreDetails = _.cloneDeep(payload);
        },
        setCreateStoreConfigFilterTableData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.createStoreConfigTableData = _.cloneDeep(payload);
        },
        setSelectedCreateStoreConfigTableData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.selectedCreateStoreConfigTableData = _.cloneDeep(payload);
        },
        setTableDataFromUploadOrCopyPaste(state, action) {
            const { payload } = _.cloneDeep(action);
            state.tableDataFromUploadorCopyPaste = _.cloneDeep(payload);
        },
        resetEditStoreGroupFlag(state, action) {
            state.editStoreGroupFlag = false;
        },
        resetCreateStoreConfigScreenData(state, action) {
            state.createStoreConfigTableData = [];
            state.selectedCreateStoreConfigTableData = [];
            state.tableDataFromUploadorCopyPaste = {};
            state.selectedStoreGroupConfigData = [];
        },
        setSelectedStoreGroupConfigData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.selectedStoreGroupConfigData = _.cloneDeep(payload);
        },
        storeGroupLinkClick(state, action) {
            const { payload } = _.cloneDeep(action);
            state.selectedStoreGroupConfigData = _.cloneDeep(payload);
            state.editStoreGroupFlag = true;
        }
    },
});

export const {
    setStoreConfigTableData,
    setStoreUngroupedStoreData,
    setCreateStoreConfigFilterTableData,
    setSelectedCreateStoreConfigTableData,
    setTableDataFromUploadOrCopyPaste,
    resetCreateStoreConfigScreenData,
    resetEditStoreGroupFlag,
    setSelectedStoreGroupConfigData,
    storeGroupLinkClick,
} = storeConfigrationSlice.actions;

export default storeConfigrationSlice.reducer;

export const callStoreConfigTableAPI = (
    payload,
) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/get-store-groups", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
                dispatch(setStoreConfigTableData(_.cloneDeep(data)));
            } else {
                // const errorMessage = setErrorMessage(response.error);
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setStoreConfigTableData(_.cloneDeep(response.data.data)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const callStoreConfigUngroupedDetailsAPI = (
    payload,
) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/get-ungrouped-stores-details", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
                dispatch(setStoreUngroupedStoreData(_.cloneDeep(data)));
            } else {
                // const errorMessage = setErrorMessage(response.error);
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setStoreUngroupedStoreData(_.cloneDeep(response.data.data)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const callCreateStoreConfigFilterTableAPI = (
    payload,
) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/stores", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete(response?.data?.message));
                dispatch(setCreateStoreConfigFilterTableData(_.cloneDeep(data)));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setCreateStoreConfigFilterTableData(_.cloneDeep(response.data.data)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const createStoreGroupConfig = (
    payload, method = "POST"
) => async (dispatch, getState) => {
    dispatch(requestStart());
    const data = api_markdown("store-group", payload, method);
    await API(data)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const getCreateStoreGrpDataFromExcel = (
    payload,
) => (dispatch, getState) => {
    dispatch(requestStart());
    return FormAPI.post("/get-stores-from-file", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(setTableDataFromUploadOrCopyPaste(response?.data?.data));
                dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
};

export const getCreateStoreGrpDataFromCopyPaste = (
    payload,
) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/store-details", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(setTableDataFromUploadOrCopyPaste(response?.data?.data));
                dispatch(requestComplete(response?.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const getStoreGroupProcessStatus = (
    payload
) => async (dispatch, getState) => {
    dispatch(requestStart());
    try{
        const response = await API.post("/store-group-process", payload)
        if (response.data.status === 200) {
            if (response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        } 
    }catch (error) {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        }
};


export const getStoreGroupData = (
    payload
) => async (dispatch, getState) => {
    dispatch(requestStart());
    try{
        const data = api_markdown("store-group", payload, "GET");
        const response = await API(data)
        if (response.data && response.data.status === 200) {
            dispatch(requestComplete(response?.data?.message));
                dispatch(setCreateStoreConfigFilterTableData(response.data.data?.store_details));
                return { groupName: response.data.data.store_group_name, description: response.data.data.store_group_description };
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        } catch (error) {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        }
};

export const getEffectedGroupsData = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try{
        const response = await API.get(`/store-groups/${payload.store_group_id}/effected-promos`)
        if (response.data && response.data.status === 200) {
            dispatch(requestComplete(response?.data?.message));
                return response.data?.data || [];
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        } catch (error) {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        }
}

export const storeGroupDownload = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/store-group-download", payload)
        .then((response) => {
            if (response.data.status === 200) {
                dispatch(requestComplete(response.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
}

export const deleteStoreGroup = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());  
    try{
        const response = await API.put("/store-groups-delete", payload)
            if (response.data.status === 200) {
                dispatch(requestComplete(response.data?.message));
                return true;
            } else {
                dispatch(requestFail(response.data?.message));
                return false;
            }
        } catch (error) {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        }
};

export const getStoreDetailsForStoreGroup = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try{
        const response = await API.get(`/store-groups/${payload.store_group_id}/stores`)
        if (response.data && response.data.status === 200) {
            dispatch(requestComplete(response?.data?.message));
            return response.data?.data || [];
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}