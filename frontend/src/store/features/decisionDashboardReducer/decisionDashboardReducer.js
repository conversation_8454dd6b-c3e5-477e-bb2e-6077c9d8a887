import { createSlice } from "@reduxjs/toolkit";
import { API, APIV3 } from "../../../utils/axios/index";
import _ from "lodash";

import {
    requestStart,
    requestComplete,
    requestFail,
} from "../global/global";

import { setErrorMessage } from "../../../utils/helpers/utility_helpers";

const initialState = {
    decisionDashboardChartsData: [],
    decisionDashboardTilesData: [],
    decisionDashboardTableData: [],
    decisionDashboardSelectedOffers: [],
    editedDDSelectedRowData: [],
    enableDDInlineEdit: false,
};

const decisionDashboardSlice = createSlice({
    name: "decisionDashboard",
    initialState,
    reducers: {
        setDecisonDashboardChartsData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.decisionDashboardChartsData = _.cloneDeep(payload);
        },
        setDecisonDashboardTilesData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.decisionDashboardTilesData = _.cloneDeep(payload);
        },
        setDecisonDashboardTableData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.decisionDashboardTableData = _.cloneDeep(payload);
        },
        setSelectedDecisionDashboardOffers(state, action) {
            const { payload } = _.cloneDeep(action);
            state.decisionDashboardSelectedOffers = _.cloneDeep(payload);
        },
        setEnableInlineEdit(state, action) {
            const { payload } = _.cloneDeep(action);
            if (!payload) {
                state.editedDDSelectedRowData = [];
            }
            state.enableDDInlineEdit = _.cloneDeep(payload);
        },
        editedDecisionDashboardDataSave(state, action) {
            const { payload } = action;
            let tempData = _.cloneDeep(state.editedDDSelectedRowData);
            const selectedOffers = _.cloneDeep(state.decisionDashboardSelectedOffers);
            //find the index of payload data in editedDDSelectedRowData
            const itemIndex = _.findIndex(tempData, { promo_id: payload.uniqueKeyVal });
            //if index is found then update the data else push the data
            if (itemIndex !== -1) {
                tempData[itemIndex] = {
                    ...tempData[itemIndex],
                    [payload.key]: payload.data,
                };
            } else {
                //find the index of selectedOffers data in selectedOffers
                const selectedOfferIndex = _.findIndex(selectedOffers, { promo_id: payload.uniqueKeyVal });
                //fetch promo name and offer comment from selectedOffers
                const { promo_name, offer_comment } = selectedOffers[selectedOfferIndex];
                //push the data
                tempData.push({
                    promo_id: payload.uniqueKeyVal,
                    promo_name,
                    offer_comment,
                    [payload.key]: payload.data,
                });
            }
            state.editedDDSelectedRowData = _.cloneDeep(tempData);
        }
    },
});

export const {
    setDecisonDashboardChartsData,
    setDecisonDashboardTilesData,
    setDecisonDashboardTableData,
    setSelectedDecisionDashboardOffers,
    setEnableInlineEdit,
    editedDecisionDashboardDataSave
} = decisionDashboardSlice.actions;

export default decisionDashboardSlice.reducer;

export const callDecisionDashboardChartsAPI = (
    payload,
) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/promo_results", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
                dispatch(setDecisonDashboardChartsData(_.cloneDeep(data)));
            } else {
                // const errorMessage = setErrorMessage(response.error);
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setDecisonDashboardChartsData(_.cloneDeep(response.data.data)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const callDecisionDashboardTilesAPI = (
    payload,
) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/tiles", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
                dispatch(setDecisonDashboardTilesData(_.cloneDeep(data)));
            } else {
                // const errorMessage = setErrorMessage(response.error);
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setDecisonDashboardTilesData(_.cloneDeep(response.data.data)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const callDecisionDashboardTableAPI = (
    payload,
) => async (dispatch, getState) => {
    dispatch(requestStart());
    await APIV3.post("/landing-page-data", {
        ...payload,
        screen_name: "decision_dashboard",
    })
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
                dispatch(setDecisonDashboardTableData(_.cloneDeep(data)));
            } else {
                // const errorMessage = setErrorMessage(response.error);
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setDecisonDashboardTableData(_.cloneDeep(response.data.data)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const decisionDashboardDownload = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("promo-downloads", payload)
        .then((response) => {
            if (response.data.status === 200) {
                dispatch(requestComplete(response.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
}

export const inlineEditAPICall = (payload) => (dispatch, getState) => {
    dispatch(requestStart());
    return API.post("edit-promos-info", payload)
        .then((response) => {
            if (response.data.status === 200) {
                dispatch(requestComplete(response.data?.message));
                return true;
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
}

export const withdrawOffer = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.post("/withdraw-promos", payload);
        if (response.data.status === 200) {
            dispatch(requestComplete(response.data?.message));
                return true;
            } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const deletePromo = (payload) => async (dispatch) => {
	dispatch(requestStart());
    try {
        const response = await APIV3.post("/delete-promos", payload);
        if (response.data.status === 200) {
            dispatch(requestComplete(response.data.message));
            return true;
        } else dispatch(requestFail());
        return false;
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const executePromo = (payload) => async (dispatch) => {
	dispatch(requestStart());
    try {
        const response = await APIV3.post("/execution-approve-promos", payload);
        if (response.data.status === 200) {
            dispatch(requestComplete(response.data.message));
            return true;
        } else dispatch(requestFail());
        return false;
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
};

export const getStoresDetailsOfPromo = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promos/${promo_id}/stores`);
		if(response.data && response.data.status === 200){
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getProductsDetailsOfPromo = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promos/${promo_id}/products`);
		if(response.data && response.data.status === 200){
			dispatch(requestComplete());
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getProductsDetailsOfEvent = (event_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/events/${event_id}/products`)
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response.data?.message));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};


export const getStoresDetailsOfEvent = (event_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/events/${event_id}/stores`)
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response.data?.message));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};
