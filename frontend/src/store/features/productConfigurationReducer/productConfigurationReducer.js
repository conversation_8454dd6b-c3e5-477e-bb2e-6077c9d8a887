import { createSlice } from "@reduxjs/toolkit";
import { API, FormAPI } from "../../../utils/axios/index";
import _ from "lodash";

import { requestStart, requestComplete, requestFail } from "../global/global";

import { setErrorMessage } from "../../../utils/helpers/utility_helpers";
import { api_markdown } from "../global/global";

const initialState = {
    productConfigurationTableData: [],
    selectedProductGroupConfigData: [],
    ungroupedProductDetails: {},
    createProductConfigTableData: [],
    selectedCreateProductConfigTableData: [],
    tableDataFromUploadorCopyPaste: {},
    editProductGroupFlag: false,
};

const productConfigrationSlice = createSlice({
    name: "productConfiguration",
    initialState,
    reducers: {
        setProductConfigTableData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.productConfigurationTableData = _.cloneDeep(payload);
        },
        setProductUngroupedProductData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.ungroupedProductDetails = _.cloneDeep(payload);
        },
        setCreateProductConfigFilterTableData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.createProductConfigTableData = _.cloneDeep(payload);
        },
        setSelectedCreateProductConfigTableData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.selectedCreateProductConfigTableData = _.cloneDeep(payload);
        },
        setTableDataFromUploadOrCopyPaste(state, action) {
            const { payload } = _.cloneDeep(action);
            state.tableDataFromUploadorCopyPaste = _.cloneDeep(payload);
        },
        resetEditProductGroupFlag(state, action) {
            state.editProductGroupFlag = false;
        },
        resetCreateProductConfigScreenData(state, action) {
            state.createProductConfigTableData = [];
            state.selectedCreateProductConfigTableData = [];
            state.tableDataFromUploadorCopyPaste = {};
            state.selectedProductGroupConfigData = [];
        },
        setSelectedProductGroupConfigData(state, action) {
            const { payload } = _.cloneDeep(action);
            state.selectedProductGroupConfigData = _.cloneDeep(payload);
        },
        productGroupLinkClick(state, action) {
            const { payload } = _.cloneDeep(action);
            state.selectedProductGroupConfigData = _.cloneDeep(payload);
            state.editProductGroupFlag = true;
        },
    },
});

export const {
    setProductConfigTableData,
    setProductUngroupedProductData,
    setCreateProductConfigFilterTableData,
    setSelectedCreateProductConfigTableData,
    setTableDataFromUploadOrCopyPaste,
    resetCreateProductConfigScreenData,
    resetEditProductGroupFlag,
    setSelectedProductGroupConfigData,
    productGroupLinkClick,
} = productConfigrationSlice.actions;

export default productConfigrationSlice.reducer;

export const callProductConfigTableAPI = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    await API.post("/get-product-groups", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
                dispatch(setProductConfigTableData(_.cloneDeep(data)));
            } else {
                // const errorMessage = setErrorMessage(response.error);
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(
                        setProductConfigTableData(
                            _.cloneDeep(response.data.data)
                        )
                    );
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const callProductConfigUngroupedDetailsAPI = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    await API.post("/get-ungrouped-product-details", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
                dispatch(setProductUngroupedProductData(_.cloneDeep(data)));
            } else {
                // const errorMessage = setErrorMessage(response.error);
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(
                        setProductUngroupedProductData(
                            _.cloneDeep(response.data.data)
                        )
                    );
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const callCreateProductConfigFilterTableAPI = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    await API.post("/products", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete(response?.data?.message));
                dispatch(
                    setCreateProductConfigFilterTableData(_.cloneDeep(data))
                );
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(
                        setCreateProductConfigFilterTableData(
                            _.cloneDeep(response.data.data)
                        )
                    );
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const getCreateProductGrpDataFromExcel = (payload) => (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    return FormAPI.post("/get-products-from-file", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(
                    setTableDataFromUploadOrCopyPaste(response?.data?.data)
                );
                dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
};

export const getCreateProductGrpDataFromCopyPaste = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    await API.post("/sku-details", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(
                    setTableDataFromUploadOrCopyPaste(response?.data?.data)
                );
                dispatch(requestComplete(response?.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const createProductGroupConfig = (payload, method = "POST") => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    const data = api_markdown("product-group", payload, method);
    await API(data)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const getProductGroupProcessStatus = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    try{
        const response = await API.post("/product-group-process", payload)
        if (response.data.status === 200) {
            dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        } catch (error) {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        }
};

export const getProductGroupData = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try{
        const response = await API.get(`/product-group?product_group_id=${payload.product_group_id}`)
        if (response.data && response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
                if (response.data.data?.is_hierarchy_level === 0) {
                    dispatch(
                        setCreateProductConfigFilterTableData(response.data.data?.products_details)
                    );
                }
                return {
                    groupName: response.data.data.product_group_name,
                    description: response.data.data.product_group_description,
                    isWholeCategory: response.data.data.is_hierarchy_level,
                    hierarchy: response.data.data?.hierarchy || {},
                };
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        } catch (error) {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        }
};

export const getEffectedGroupsData = (payload) => async(dispatch, getState) => {
    dispatch(requestStart());
    try{
        const response = await API.get(`/product-groups/${payload.id}/effected-promos`)
            if (response.data && response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
                return response.data?.data || [];
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
};

export const productGroupDownload = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/product-group-download", payload)
        .then((response) => {
            if (response.data.status === 200) {
                dispatch(requestComplete(response.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const deleteProductGroup = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());  
    try{
        const response = await API.put("/product-groups-delete", payload)
            if (response.data.status === 200) {
                dispatch(requestComplete(response.data?.message));
                return true;
            } else {
                dispatch(requestFail(response.data?.message));
                return false;
            }
        } catch (error) {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        }
};

export const getProductDetailsForProductGroup = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try{
        const response = await API.get(`/product-groups/${payload.product_group_id}/products`)
        if(response.data && response.data.status === 200){
            dispatch(requestComplete());
            return response.data?.data;
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}