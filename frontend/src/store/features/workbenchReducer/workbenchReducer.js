import { createSlice } from "@reduxjs/toolkit";
import { API, APIV3 } from "../../../utils/axios/index";
import _ from "lodash";

import { requestStart, requestComplete, requestFail, requestStop } from "../global/global";

import { setErrorMessage } from "../../../utils/helpers/utility_helpers";
import { tableDataFormatter } from "../../../components/screens/workbench/WorkbenchHelper";
const initialState = {
	workbenchTilesData: [],
	workbenchTableData: [],
	workbenchSelectedOffers: [],
	workbenchPromoTypes: [],
	editedWorkbenchSelectedRowData: [],
	copyOfferTableData: [],
	copyOfferEditedData: [],
	enableWorkbenchInlineEdit: false,
	isPromoEdit: false,
};

const workbenchSlice = createSlice({
	name: "workbench",
	initialState,
	reducers: {
		setWorkbenchTilesData(state, action) {
			const { payload } = _.cloneDeep(action);
			state.workbenchTilesData = _.cloneDeep(payload);
		},
		setWorkbenchTableData(state, action) {
			const { payload } = _.cloneDeep(action);
			state.workbenchTableData = _.cloneDeep(payload);
		},
		setSelectedWorkbenchOffers(state, action) {
			const { payload } = _.cloneDeep(action);
			state.workbenchSelectedOffers = _.cloneDeep(payload);
		},
		setWorkbenchPromoTypes(state, action) {
			const { payload } = _.cloneDeep(action);
			state.workbenchPromoTypes = _.cloneDeep(payload);
		},
		workbenchInlineEditDataSave(state, action) {
			const { payload } = action;
			let tempData = _.cloneDeep(state.editedWorkbenchSelectedRowData);
			const selectedOffers = _.cloneDeep(state.workbenchSelectedOffers);
			//find the index of payload data in editedWorkbenchSelectedRowData
			const itemIndex = _.findIndex(tempData, {
				promo_id: payload.uniqueKeyVal,
			});
			//if index is found then update the data else push the data
			if (itemIndex !== -1) {
				tempData[itemIndex] = {
					...tempData[itemIndex],
					[payload.key]: payload.data,
				};
			} else {
				//find the index of selectedOffers data in selectedOffers
				const selectedOfferIndex = _.findIndex(selectedOffers, {
					promo_id: payload.uniqueKeyVal,
				});
				//fetch promo name and offer comment from selectedOffers
				const { promo_name, offer_comment } = selectedOffers[
					selectedOfferIndex
				];
				//push the data
				tempData.push({
					promo_id: payload.uniqueKeyVal,
					promo_name,
					offer_comment,
					[payload.key]: payload.data,
				});
			}
			state.editedWorkbenchSelectedRowData = _.cloneDeep(tempData);
		},
		setWorkbenchEnableInlineEdit(state, action) {
			const { payload } = _.cloneDeep(action);
			if (!payload) {
				state.editedWorkbenchSelectedRowData = [];
			}
			state.enableWorkbenchInlineEdit = _.cloneDeep(payload);
		},
		setCopyOfferTableData(state, action) {
			const { payload } = _.cloneDeep(action);
			state.copyOfferTableData = _.cloneDeep(payload);
		},
		setCopyOfferEditedData(state, action) {
			const { payload } = _.cloneDeep(action);
			state.copyOfferEditedData = _.cloneDeep(payload);
		},
		workbenchCopyIAEditDataSave(state, action) {
			const { payload } = action;
			let tempData = _.cloneDeep(state.copyOfferEditedData);
			let tableData = _.cloneDeep(state.copyOfferTableData);
			
			// Find indices in both arrays
			const itemIndex = _.findIndex(tempData, {
				promo_id: payload.uniqueKeyVal,
			});
			const tableItemIndex = _.findIndex(tableData, {
				promo_id: payload.uniqueKeyVal,
			});
		
			// Update or add to editedData
			if (itemIndex !== -1) {
				tempData[itemIndex] = {
					...tempData[itemIndex],
					...payload.data,
				};
			} else {
				const selectedOfferIndex = _.findIndex(tableData, {
					promo_id: payload.uniqueKeyVal,
				});
				const { promo_name, start_date, end_date, new_promo_name } = tableData[selectedOfferIndex];
				const rowObj = {
					promo_id: payload.uniqueKeyVal,
					promo_name,
					start_date,
					end_date,
					...payload.data,
				}
				if(!payload?.data?.new_promo_name) {
					rowObj['new_promo_name'] = new_promo_name
				}
				tempData.push(rowObj);
			}
		
			// Update tableData
			if (tableItemIndex !== -1) {
				tableData[tableItemIndex] = {
					...tableData[tableItemIndex],
					...payload.data,
				};
			}
		
			state.copyOfferEditedData = _.cloneDeep(tempData);
			state.copyOfferTableData = _.cloneDeep(tableData);
		},
		setIsPromoEdit(state, action) {
			const { payload } = _.cloneDeep(action);
			state.isPromoEdit = payload;
		},
	},
});

export const {
	setWorkbenchTilesData,
	setWorkbenchTableData,
	setSelectedWorkbenchOffers,
	setWorkbenchPromoTypes,
	workbenchInlineEditDataSave,
	setWorkbenchEnableInlineEdit,
	setCopyOfferTableData,
	workbenchCopyIAEditDataSave,
	setCopyOfferEditedData,
	setIsPromoEdit,
} = workbenchSlice.actions;

export default workbenchSlice.reducer;

// Abort controller for workbench tiles API
let abortControllerWorkbenchTilesAPI = null;
export const callWorkbenchTilesAPI = (payload) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());

	if (abortControllerWorkbenchTilesAPI) {
		abortControllerWorkbenchTilesAPI.abort();
	}
	abortControllerWorkbenchTilesAPI = new AbortController();
	
	await API.post("/tiles", payload, {
		signal: abortControllerWorkbenchTilesAPI.signal
	})
	.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				dispatch(requestComplete());
				dispatch(setWorkbenchTilesData(_.cloneDeep(data)));
			} else {
				// const errorMessage = setErrorMessage(response.error);
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);

				if (!_.isEmpty(response.data?.data)) {
					dispatch(
						setWorkbenchTilesData(_.cloneDeep(response.data.data))
					);
				}
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			// AbortError is not a real error, so we don't want to show it
			if (error.message !== 'canceled') {
				dispatch(requestFail(errorMessage));
			} else {
				dispatch(requestComplete());
			}
		});
};

// Abort controller for workbench table API
// was added in a cenario where while applying filters, the user clicked on the 
// promo copy complete notification which should call the workbench table API again
// but only the filtered promo_id so this cancels the previous request
let abortControllerWorkbenchTableAPI = null;
export const callWorkbenchTableAPI = (payload) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());

	if (abortControllerWorkbenchTableAPI) {
		abortControllerWorkbenchTableAPI.abort();
	}
	abortControllerWorkbenchTableAPI = new AbortController();
	
	await APIV3.post("/landing-page-data", payload, {
		signal: abortControllerWorkbenchTableAPI.signal
	})
		.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				dispatch(requestComplete());
				dispatch(setWorkbenchTableData(tableDataFormatter(_.cloneDeep(data))));
			} else {
				// const errorMessage = setErrorMessage(response.error);
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);

				if (!_.isEmpty(response.data?.data)) {
					//* as per current flow this "tableDataFormatter" formatter is not required
					dispatch(
						setWorkbenchTableData(tableDataFormatter(_.cloneDeep(response.data.data)))
					);
				}
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			// AbortError is not a real error, so we don't want to show it
			if (error.message !== 'canceled') {
				dispatch(requestFail(errorMessage));
			} else {
				dispatch(requestComplete());
			}
		});
};

export const getPromoTypes = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.get(`/promo-status-types?screen_type=${payload.screenName}`)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(setWorkbenchPromoTypes(response.data.data));
				dispatch(requestComplete());
			} else {
				dispatch(requestFail());
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const workbenchDownload = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	await API.post("promo-downloads", payload)
		.then((response) => {
			if (response.data.status === 200) {
				dispatch(requestComplete(response.data?.message));
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const workbenchFinalizeAPICall = (payload) => (dispatch) => {
	dispatch(requestStart());
	return API.post("/is-valid-to-finalize", payload)
		.then((response) => {
			if (response.data && response.status === 200) {
				dispatch(requestComplete());
				return response.data.data;
			} else dispatch(requestFail());
			return false;
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const finalizePromo = (payload) => (dispatch) => {
	dispatch(requestStart());
	return APIV3.post("/finalise-promo", payload)
		.then((response) => {
			if (response.data && response.status === 200) {
				dispatch(requestComplete(response.data.message));
				return response.data.data;
			} else dispatch(requestFail());
			return false;
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const deletePromo = (payload) => (dispatch) => {
	dispatch(requestStart());
	return APIV3.post("/delete-promos", payload)
		.then((response) => {
			if (response.data && response.status === 200) {
				dispatch(requestComplete(response.data.message));
				return true;
			} else dispatch(requestFail());
			return false;
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
			return false;
		});
};

export const validateCopyPromo = (payload) => async(dispatch) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/validate-copy-offers", payload);
		if (response.data && response.status === 200) {
			dispatch(requestStop());
			return response.data.data;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const copyPromos = (payload) => async(dispatch) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/copy-promos", payload);
		if (response.data && response.status === 200) {
			dispatch(requestComplete(response.data.message));
			return response.data.data;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};


export const getProductsDetailsOfPromo = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promos/${promo_id}/products`)
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response.data?.message));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getStoresDetailsOfPromo = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promos/${promo_id}/stores`)
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response.data?.message));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}

}