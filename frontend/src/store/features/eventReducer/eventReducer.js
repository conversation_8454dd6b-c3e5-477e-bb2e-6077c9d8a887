import { createSlice } from '@reduxjs/toolkit';
import { API, APIV3, FormAPI } from '../../../utils/axios/index';
import { requestStart, requestComplete, requestFail } from '../global/global';
import { setErrorMessage } from '../../../utils/helpers/utility_helpers';

const initialState = {
    eventDetails: {
        date_restriction: {},
        product_restriction: {},
        store_restriction: {},
        customer_restriction: {},
        promotion_restriction: {}
    },
    activeEventId: null,
    eventTypes: [], 
    eventObjectives: [],
    // Product Restrictions
    createEventProductTableData: [],
    createEventProductGroupData: [],
    tableDataFromUploadOrCopyPaste: [],
    // Store Restrictions
    createEventStoreTableData: [],
    createEventStoreGroupData: [],
    tableDataFromUploadOrCopyPasteStore: [],
    refreshEventsData: false
}

const eventSlice = createSlice({
  name: "event",
  initialState,
  reducers: {
    setEventDetails(state, action) {
        const { payload } = _.cloneDeep(action);
        state.eventDetails = _.cloneDeep(payload);
    },
    updateEventDetails(state, action) {
        const { payload } = _.cloneDeep(action);
        state.eventDetails = { ...state.eventDetails, ...payload };
    },
    setEventProductTableData(state, action) {
        const { payload } = _.cloneDeep(action);
        state.createEventProductTableData = _.cloneDeep(payload);
    },
    setTableDataFromUploadOrCopyPaste(state, action) {
        const { payload } = _.cloneDeep(action);
        state.tableDataFromUploadOrCopyPaste = _.cloneDeep(payload);
    },
    setEventProductGroupData(state, action) {
        const { payload } = _.cloneDeep(action);
        state.createEventProductGroupData = _.cloneDeep(payload);
    },
    setEventTypes(state, action) {
        const { payload } = _.cloneDeep(action);
        state.eventTypes = _.cloneDeep(payload);
    },
    setEventObjectives(state, action) {
        const { payload } = _.cloneDeep(action);
        state.eventObjectives = _.cloneDeep(payload);
    },
    setStoreFilterTableData(state, action) {
        const { payload } = _.cloneDeep(action);
        state.createEventStoreTableData = _.cloneDeep(payload);
    },
    setTableDataFromUploadOrCopyPasteStore(state, action) {
        const { payload } = _.cloneDeep(action);
        state.tableDataFromUploadOrCopyPasteStore = _.cloneDeep(payload);
    },
    setEventStoreGroupData(state, action) {
        const { payload } = _.cloneDeep(action);
        state.createEventStoreGroupData = _.cloneDeep(payload);
    },
    setActiveEventId(state, action) {
        const { payload } = _.cloneDeep(action);
        state.activeEventId = _.cloneDeep(payload);
    },
    setRefreshEventsData(state, action) {
        const { payload } = _.cloneDeep(action);
        state.refreshEventsData = payload;
    }
  }
});

export const {
    setEventProductTableData,
    setTableDataFromUploadOrCopyPaste,
    setEventProductGroupData,

    setStoreFilterTableData,
    setTableDataFromUploadOrCopyPasteStore,
    setEventStoreGroupData,

    setEventDetails,
    updateEventDetails,
    setEventTypes,
    setEventObjectives,
    setActiveEventId,
    setRefreshEventsData
} = eventSlice.actions

export default eventSlice.reducer;

// ---------------------- Product Config ----------------------

export const callProductConfigTableAPI = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    await API.post("/get-product-groups", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete());
				dispatch(setEventProductGroupData(_.cloneDeep(data)));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
						dispatch(
							setEventProductGroupData(_.cloneDeep(response.data.data))
						);
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const callCreateProductConfigFilterTableAPI = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    await API.post("/products", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete(response?.data?.message));
                dispatch(
					setEventProductTableData((_.cloneDeep(data)))
                );
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(
                        setEventProductTableData(
                            _.cloneDeep(response.data.data.products)
                        )
                    );
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const getCreateProductGrpDataFromExcel = (payload) => (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    return FormAPI.post("/get-products-from-file", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(
                    setTableDataFromUploadOrCopyPaste(response?.data?.data)
                );
                dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
};

export const getCreateProductGrpDataFromCopyPaste = (payload) => async (
    dispatch,
    getState
) => {
    dispatch(requestStart());
    await API.post("/sku-details", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(
                    setTableDataFromUploadOrCopyPaste(response?.data?.data)
                );
                dispatch(requestComplete(response?.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};
// ----------------------------------------------------------

// ---------------------- Store Config ----------------------

export const callStoreTableDataApi = (payload,) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/stores", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete(response?.data?.message));
                dispatch(setStoreFilterTableData(_.cloneDeep(data)));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setStoreFilterTableData(_.cloneDeep(response.data.data?.stores)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const getCreateStoreGrpDataFromExcel = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    return FormAPI.post("/get-stores-from-file", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(setTableDataFromUploadOrCopyPasteStore(response?.data?.data));
                dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
                return false;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
};

export const getCreateStoreDataFromCopyPaste = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/store-details", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(setTableDataFromUploadOrCopyPasteStore(response?.data?.data));
                dispatch(requestComplete(response?.data?.message));
            } else {
                dispatch(
                    requestFail(
                        response?.data?.error || response?.data?.message
                    )
                );
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

export const getStoreGroupsFilter = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    await API.post("/get-store-groups", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                let data = _.cloneDeep(response.data.data);
                dispatch(requestComplete(response?.data?.message));
                dispatch(setEventStoreGroupData(_.cloneDeep(data)));
            } else {
                dispatch(
                    requestFail(response?.data?.error || response?.data?.message)
                );

                if (!_.isEmpty(response.data?.data)) {
                    dispatch(setEventStoreGroupData(_.cloneDeep(response.data.data)));
                }
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
};

// ----------------------------------------------------------

export const getEventTypeOptions = () => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.get("/event-types");
        if (response) {
            if (response.data && response.data.status === 200) {
                dispatch(setEventTypes(response.data.data));
                dispatch(requestComplete());
                return true;
            } else
                dispatch(requestFail());
                return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const getEventObjectiveOptions = () => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.get("/event-objectives");
        if (response.data && response.data.status === 200) {
            dispatch(setEventObjectives(response.data.data));
            dispatch(requestComplete());
            return true;
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const createEvent = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    return await APIV3.post("/event", payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(requestFail(response?.data?.error || response?.data?.message));
                return false;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
}

export const getEventDetails = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    await APIV3.get(`/events/${payload.event_id}`)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(setEventDetails(response.data.data));
                dispatch(requestComplete());
            } else {
                dispatch(requestFail(response?.data?.error || response?.data?.message));
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
        });
}

export const updateEvent = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    return await APIV3.put(`/events/${payload?.event_id}`, payload)
        .then((response) => {
            if (response.data && response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
                return true;
            } else {
                dispatch(requestFail(response?.data?.error || response?.data?.message));
                return false;
            }
        })
        .catch((error) => {
            const errorMessage = setErrorMessage(error);
            dispatch(requestFail(errorMessage));
            return false;
        });
}

export const getEffectedPromos = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.post(`/events/${payload.event_id}/effected-offers`, payload)
        if (response.data && response.data.status === 200) {
            dispatch(requestComplete());
            return response?.data?.data;
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const deleteEvent = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.post(`/delete-events`, payload);
        if (response.data && response.data.status === 200) {
            dispatch(requestComplete(response?.data?.message));
            return true;
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const getEffectedPromosFromDeleteEvent = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.post(`/events/finalized-promos`, payload);
        if (response.data && response.data.status === 200) {
            dispatch(requestComplete());
            return response?.data?.data;
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const checkIfIsUnderProcessing = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    const response = await APIV3.get(`/events/${payload?.event_id}/is-under-processing`)
    try {
        if (response) {
            if (response.data && response.data.status === 200) {
                dispatch(requestComplete());
                const data = response?.data?.data;
                return data?.is_under_processing;
            } else {
                dispatch(requestFail(response?.data?.error || response?.data?.message));
                return false;
            }
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const getEventConfigurable = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.get(`/display-configuration?module=event`);
    if (response.data && response.data.status === 200) {
        dispatch(requestComplete());
        return response?.data?.data;
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}

export const lockUnlockEvent = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.post(`/lock-unlock-events`, payload)

        if (response) {
            if (response.data && response.data.status === 200) {
                dispatch(requestComplete(response?.data?.message));
                dispatch(setRefreshEventsData(true));
                return true;
            }
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        } else {
            dispatch(requestFail("Something happened!!!"));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}   

export const resimulateEvent = (payload) => async (dispatch, getState) => {
    dispatch(requestStart());
    try {
        const response = await APIV3.post(`/resimulate-events`, payload);
        if (response.data && response.data.status === 200) {
            dispatch(requestComplete(response?.data?.message));
            return true;
        } else {
            dispatch(requestFail(response?.data?.error || response?.data?.message));
            return false;
        }
    } catch (error) {
        const errorMessage = setErrorMessage(error);
        dispatch(requestFail(errorMessage));
        return false;
    }
}       
