import { createSlice } from "@reduxjs/toolkit";
import {
	API,
	APIV3,
} from "../../../utils/axios/index";
import _, { set } from "lodash";
import { containerStore } from "../../index";

import { requestStart, requestComplete, requestFail } from "../global/global";

import { setErrorMessage } from "../../../utils/helpers/utility_helpers";
import { api_markdown } from "../global/global";

const initialState = {
	marketingCalendarData: {},
	marketingCalendarSelectedOffers: {},
	marketingCalendarPromoTypes: [],
	eventsCalendarData: {},
};

const marketingCalendarSlice = createSlice({
	name: "marketingCalendar",
	initialState,
	reducers: {
		setmarketingCalendarData(state, action) {
			const { payload } = _.cloneDeep(action);
			state.marketingCalendarData = _.cloneDeep(payload);
		},
		setSelectedMarketingCalendarOffers(state, action) {
			const { payload } = _.cloneDeep(action);
			const marketingCalendarSelectedOffersTemp = _.cloneDeep(
				state?.marketingCalendarSelectedOffers
			);
			const { data, year } = _.cloneDeep(payload);
			if (year === "ty") {
				marketingCalendarSelectedOffersTemp.ly = [];
			} else if (year === "ly") {
				marketingCalendarSelectedOffersTemp.ty = [];
			}
			marketingCalendarSelectedOffersTemp[year] = _.cloneDeep(data);
			state.marketingCalendarSelectedOffers = marketingCalendarSelectedOffersTemp;
		},
		setMarketingCalendarPromoTypes(state, action) {
			const { payload } = _.cloneDeep(action);
			state.marketingCalendarPromoTypes = _.cloneDeep(payload);
		},
		setEventsCalendarData(state, action){ 
			const { payload } = _.cloneDeep(action);
			state.eventsCalendarData = _.cloneDeep(payload);
		}
	},
});

export const {
	setmarketingCalendarData,
	setSelectedMarketingCalendarOffers,
	setMarketingCalendarPromoTypes,
	setEventsCalendarData
} = marketingCalendarSlice.actions;

export default marketingCalendarSlice.reducer;

export const getEventApi = ({ payload, timeline }) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	await APIV3.post("/landing-page-data", {
		...payload,
		screen_type: "calendar_view_events",
	})
		.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				const eventsCalendarData = _.cloneDeep(
					containerStore.getState()?.pricesmartPromoReducer
						?.marketingCalendar.eventsCalendarData
				);
				dispatch(
					setEventsCalendarData({
						...eventsCalendarData,
						[timeline]: _.cloneDeep(data),
					})
				);
				dispatch(requestComplete());
			} else {
				// const errorMessage = setErrorMessage(response.error);
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const callMarketingCalendarTableAPI = ({ payload, timeline }) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	await APIV3.post("/landing-page-data", {
		...payload,
		screen_type: "calendar_view_promos",
	})
		.then((response) => {
			if (response.data && response.data.status === 200) {
				let data = _.cloneDeep(response.data.data);
				const marketingCalendarDataTemp = _.cloneDeep(
					containerStore.getState()?.pricesmartPromoReducer
						?.marketingCalendar.marketingCalendarData
				);
				dispatch(
					setmarketingCalendarData({
						...marketingCalendarDataTemp,
						[timeline]: _.cloneDeep(data),
					})
				);
				dispatch(requestComplete());
			} else {
				// const errorMessage = setErrorMessage(response.error);
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const getPromoTypes = (payload) => async (dispatch) => {
	dispatch(requestStart());

	await API.get(`/promo-status-types?screen_type=${payload.screenName}`)
		.then((response) => {
			if (
				response.data &&
				(response.data.status === 200 || response.data.status === true)
			) {
				dispatch(setMarketingCalendarPromoTypes(response.data.data));
				dispatch(requestComplete());
			} else {
				dispatch(requestFail());
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const resimulateOffers = (payload) => async (dispatch, getState) => {
	dispatch(requestStart());
	await API.post("/resimulate-offers", payload)
		.then((response) => {
			if (response.data && response.data.status === 200) {
				dispatch(
					requestComplete(
						"Resimulation is in progress. You will be notified when the process is complete."
					)
				);
			} else {
				// const errorMessage = setErrorMessage(response.error);
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const marketingCalendarDownload = (payload) => async (
	dispatch,
	getState
) => {
	dispatch(requestStart());
	await API.post("promo-downloads", payload)
		.then((response) => {
			if (response.data.status === 200) {
				dispatch(requestComplete(response.data?.message));
			} else {
				dispatch(
					requestFail(
						response?.data?.error || response?.data?.message
					)
				);
			}
		})
		.catch((error) => {
			const errorMessage = setErrorMessage(error);
			dispatch(requestFail(errorMessage));
		});
};

export const copyEvents = (payload) => async(dispatch) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.post("/copy-events", payload);
		if (response.data && response.status === 200) {
			dispatch(requestComplete(response.data.message));
			return response.data.data;
		} else {
			dispatch(requestFail());
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
};

export const getProductsDetailsOfPromo = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promos/${promo_id}/products`)
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response.data?.message));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}
}

export const getStoresDetailsOfPromo = (promo_id) => async (dispatch, getState) => {
	dispatch(requestStart());
	try {
		const response = await APIV3.get(`/promos/${promo_id}/stores`)
		if (response.data && response.data.status === 200) {
			dispatch(requestComplete(response.data?.message));
			return response.data?.data;
		} else {
			dispatch(requestFail(response?.data?.error || response?.data?.message));
			return false;
		}
	} catch (error) {
		const errorMessage = setErrorMessage(error);
		dispatch(requestFail(errorMessage));
		return false;
	}

}
