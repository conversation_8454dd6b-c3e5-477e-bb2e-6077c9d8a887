import React, { useState, useEffect, useRef } from "react";
import _ from "lodash";
import { useSelector, useDispatch } from "react-redux";
import { Tooltip, Badge, Input, DateRangePicker, Select, Button, DatePicker } from "impact-ui";
import NumbersIcon from "@mui/icons-material/Numbers";
import infoIconSvg from "../../../assets/imageAssets/infoIcon.svg?.url";
import errorAlertIconSvg from "../../../assets/imageAssets/errorIcon.svg?.url";
import warningAlertIconSvg from "../../../assets/imageAssets/warningIcon.svg?.url";
import LockedIcon from "../../../assets/imageAssets/locked.svg?.url";
import LockOpenIcon from "../../../assets/imageAssets/lock-open.svg?.url";
import { workbenchCopyIAEditDataSave } from "../../../store/features/workbenchReducer/workbenchReducer";
import moment from "moment";
import { DEFAULT_DATE_FORMAT, global_labels } from "../../../constants/Constants";
import {
	replaceSpecialCharacter,
	replaceSpecialCharToCharCode,
} from "../utility_helpers";
import "react-dates/initialize";
import ProcessingIcon from "../../../assets/imageAssets/processingIcon.svg?.url";

import "./tableCellRendererHelpers.scss";

const iconsMapping = {
	processing: <img src={ProcessingIcon} />,
};

export const CellsWithLinkandIcon = (props) => {
	const { icons = {}, data = {}, disableInput = false } = props;
	const buttonValRef = useRef(null);  // Ref to access the box element
	const [isOverflowing, setIsOverflowing] = useState(false);

	const onLinkClick = () => {
		props.onClick(_.cloneDeep([data]));
	};

	const checkOverflow = () => {
		if (buttonValRef.current) {
			const isOverflowing = buttonValRef.current.scrollWidth > buttonValRef.current.clientWidth;
			setIsOverflowing(isOverflowing);
		}
	};

	useEffect(() => {
		// Initial check when component mounts
		checkOverflow();

		// Attach the resize listener to the AG Grid API
		const gridApi = props?.api;
		gridApi.addEventListener('columnResized', checkOverflow);

		return () => {
			gridApi.removeEventListener('columnResized', checkOverflow);
		};
	}, [props.api, props.value]);

	return (
		<div className="flexContentAround">
			{isOverflowing ? <Tooltip
				title={replaceSpecialCharacter(props.value)}
				orientation="right"
				trigger="hover"
				variant="tertiary"
			>
				<button
					ref={buttonValRef}
					disabled={disableInput}
					className="textWithLink maxWidth-70"
					onClick={onLinkClick}
				>
					{replaceSpecialCharacter(props.value)}
				</button>
			</Tooltip> : <button
				ref={buttonValRef}
				disabled={disableInput}
				className="textWithLink maxWidth-70"
				onClick={onLinkClick}
			>
				{replaceSpecialCharacter(props.value)}
			</button>}
			<div className="tableIconsContainer">
				{!_.isEmpty(icons) &&
					_.map(Object.keys(icons), (iconKey) => {
						return data[icons[iconKey]?.key] ===
							icons[iconKey]?.trueValue
							? iconsMapping[iconKey]
							: null;
					})}
			</div>
		</div>
	);
};

export const cellsWithBadge = (props) => {
	return props.value ? (
		<Badge
			color={props.color?.[props.value] || "default"}
			variant={props.variant || "stroke"}
			label={replaceSpecialCharacter(props.value)}
			size="default"
		/>
	) : (
		"-"
	);
};

export const TableWithInputBoxOption = (props) => {
	const [showInput, setShowInput] = useState(props?.defaultFlag ?? true);
	const [inputValue, setInputValue] = useState(
		replaceSpecialCharacter(props.value)
	);
	const storeData = useSelector((store) => store?.pricesmartPromoReducer);

	//if enableInput is not provided, show input box by default
	const enableFlag =
		storeData?.[props?.enableInput?.screen]?.[props?.enableInput?.enableKey] ??
		true;
	//get selected row data from store
	const data =
		storeData?.[props?.enableInput?.screen]?.[
		props?.enableInput?.storeDataKey
		] || [];

	useEffect(() => {
		//if enable flag updates, and store data matches, show input box
		_.forEach(data, (item) => {
			if (
				enableFlag !== showInput &&
				item?.[props?.enableInput?.uniqueKey] ===
				props.data?.[props?.enableInput?.uniqueKey]
			) {
				setShowInput(enableFlag);
			}
			if (!enableFlag) {
				setInputValue(replaceSpecialCharacter(props.value));
			}
		});
	}, [enableFlag]);

	const onBlurUpdate = () => {
		//on blur update send data to redux store
		props.onDataUpdate({
			key: props.colDef.field,
			data: replaceSpecialCharToCharCode(inputValue),
			uniqueKeyVal: props.data?.[props.enableInput.uniqueKey],
		});
	};

	return (
		<div className="center-align-cell-content">
			{showInput ? (
				<Input
					id="store_detail_description"
					inputProps={{}}
					name=""
					onChange={(e) => setInputValue(e.target.value)}
					onBlur={onBlurUpdate}
					placeholder="Comment..."
					type="text"
					isRequired={true}
					value={inputValue}
				/>
			) : (
				replaceSpecialCharacter(props.value)
			)}
		</div>
	);
};

export const OfferNameCellRenderer = (props) => {
	const buttonValRef = useRef(null);  // Ref to access the box element
	const [isOverflowing, setIsOverflowing] = useState(false);
	const { data = {} } = props;

	const checkOverflow = () => {
		if (buttonValRef.current) {
			const isOverflowing = buttonValRef.current.scrollWidth > buttonValRef.current.clientWidth;
			setIsOverflowing(isOverflowing);
		}
	};

	useEffect(() => {
		// Initial check when component mounts
		checkOverflow();

		// Attach the resize listener to the AG Grid API
		const gridApi = props?.api;
		gridApi.addEventListener('columnResized', checkOverflow);

		return () => {
			gridApi.removeEventListener('columnResized', checkOverflow);
		};
	}, [props.api, props.value]);

	//get redux store data
	const storeData = useSelector((store) => store?.pricesmartPromoReducer);

	//get show input box flag from redux store, default is false
	let enableFlag =
		storeData?.[props.enableInput.screen]?.[props.enableInput.enableKey] ??
		false;

	//get selected row data from store
	const selectedOffers =
		storeData?.[props.enableInput.screen]?.[
		props.enableInput.storeDataKey
		] || [];

	//check if data exists in store matches with current row data
	let isDataExists = selectedOffers.some(
		(item) =>
			item[props.enableInput.uniqueKey] ===
			props.data[props.enableInput.uniqueKey]
	);

	isDataExists = isDataExists ? enableFlag : false;

	const onLinkClick = () => {
		props.onClick && props.onClick(_.cloneDeep([data]));
	};

	const autoResimMapping = {
		"-1": {
			message:
				"Auto resimulation has failed. Please resimulate manually.",
			fillColor: "#da1e28",
		},
		1: {
			fillColor: "#ff832b",
			message:
				`The values against this ${global_labels?.promo_alias} have changed due to a change in the ${global_labels.event_primary}. Please review the numbers`, 
		},
		2: {
			fillColor: "#fabd4d",
			message:
				`The values against this ${global_labels?.promo_alias} have changed due to a change in the ${global_labels.event_primary} and the optimized numbers maybe inaccurate. Please review the numbers and re-optimize for the updated forecast`,
		},
	};

	return isDataExists ? (
		<TableWithInputBoxOption {...props} />
	) : (
		<div className="flexContentAround">
			{isOverflowing ? <Tooltip
				title={replaceSpecialCharacter(props.value)}
				orientation="right"
				trigger="hover"
				variant="tertiary"
			>
				<button
					ref={buttonValRef}
					disabled={enableFlag}
					className={`maxWidth-70 ${props?.onClick ? "textWithLink" : "notClickable"}`}
					onClick={onLinkClick}
				>
					{replaceSpecialCharacter(props.value)}
				</button>
			</Tooltip> : <button
				ref={buttonValRef}
				disabled={enableFlag}
				className={`maxWidth-70 ${props?.onClick ? "textWithLink" : "notClickable"}`}
				onClick={onLinkClick}
			>
				{replaceSpecialCharacter(props.value)}
			</button>}
			<div className="flexWithGap8">
				{data.is_overridden === 1 && (
					<Tooltip
						title={
							<div>
								<div>Reason: {props.data?.override_reason || "-"}</div>
								<div>Comment: {props.data?.override_comment || "-"} </div>
							</div>
						}
						orientation="right"
						trigger="hover"
						variant="tertiary"
					>
						<div>
							<Badge
								size="small"
								label="Edited"
								color="default"
								isIcon
								variant="stroke"
								icon={<NumbersIcon className="numberIcon" />}
							/>
						</div>
					</Tooltip>
				)}
				{data.is_auto_resimulated !== 0 && (
					<Tooltip
						title={`Warning: ${autoResimMapping[data?.is_auto_resimulated]?.message
							}`}
						orientation="right"
						trigger="hover"
						variant="tertiary"
					>
						<div className="flexWithGap8">
							{data.is_auto_resimulated === -1 && <img src={errorAlertIconSvg} alt="error" width={"16px"} />}
							{(data.is_auto_resimulated === 1 || data.is_auto_resimulated === 2) && <img src={warningAlertIconSvg} alt="warning" width={"16px"} />}

						</div>
					</Tooltip>
				)}
				<Badge color="info" label={data.status?.[0]} variant="stroke" />
			</div>
		</div>
	);
};

export const CellWithInfoIcon = (props) => {
	return (
		<div className="flexWithGap8 flexContentAround">
			<span className="text-ellipsis">
				{replaceSpecialCharacter(props.value)}
			</span>
			{props.data?.[props.flag] && (
				<Tooltip
					title={replaceSpecialCharacter(props.info)}
					orientation="right"
					trigger="hover"
					variant="tertiary"
				>
					<img src={infoIconSvg} alt="info" width={"16px"} />
				</Tooltip>
			)}
		</div>
	);
};

export const CellWithWarningIcon = (props) => {
	return (
		<div className="flexWithGap8 flexContentAround">
			<span className="text-ellipsis maxWidth-70">
				<Tooltip
					title={replaceSpecialCharacter(props.value)}
					orientation="bottom"
					trigger="hover"
					variant="tertiary"
				>
					{replaceSpecialCharacter(props.value)}
				</Tooltip>
			</span>
			{props.data?.[props.flag] && (
				<Tooltip
					title={replaceSpecialCharacter(props.data.message)}
					orientation="right"
					trigger="hover"
					variant="tertiary"
				>
					<img src={warningAlertIconSvg} alt="warning" width={"16px"} />
				</Tooltip>
			)}
		</div>
	);
};

export const CellWithLockIcon = (props) => {

	const handleLockUnclock = () => {
		console.log(props.data);
		props.onLockUnlockUpdate(props.data)
	}
	return (
		<div className="flexWithGap8 flexContentAround">
			<span className="text-ellipsis maxWidth-70">
				<Tooltip
					title={replaceSpecialCharacter(props.value)}
					orientation="bottom"
					trigger="hover"
					variant="tertiary"
				>
					{replaceSpecialCharacter(props.value)}
				</Tooltip>
			</span>
				<Tooltip
					title={`${props?.data?.is_locked ? "Unlock" : "Lock"} ${global_labels.event_primary}`} 
					orientation="right"
					trigger="hover"
					variant="tertiary"
				>
					<img 
						src={props?.data?.is_locked ? LockedIcon : LockOpenIcon} 
						alt={props?.data?.is_locked ? "Locked" : "Unlocked"} 
						width={"16px"} 
						height={"16px"}
						onClick={handleLockUnclock}
					/>
				</Tooltip>
		</div>
	);
};

export const OfferStatusCellRenderer = (props) => {
	
	// -1	Placeholder
	// 0	Draft/Copied
	// 2	To Finalize
	// 4	Finalized
	// 6	Archived
	// 8	Execution Approved

	const { status_id = 0 } = props?.data || {};

	const statusMapping = {
		"-1": "default",
		"0": "default",
		"6": "error",
		"2": "info",
		"4": "success",
		"8": "success"
	}

	return (
		<Badge color={statusMapping[status_id]} label={props.value} variant="stroke" size="default" />
	)
}

export const copyDateRangeCellRenderer = (props) => {
	const dispatch = useDispatch();
	const {
		events,
	} = useSelector((store) => store?.pricesmartPromoReducer.promo);
	const [startDate, setStartDate] = useState(null);
	const [endDate, setEndDate] = useState(null);
	const [selectedEvent, setSelectedEvent] = useState(null);

	const ref = useRef(null);

	useEffect(() => {
		if(props.data.start_date){
			setStartDate(props.data.start_date);
		}
	},[props.data.start_date]);

	useEffect(() => {
		if(props.data.end_date){
			setEndDate(props.data.end_date);
		}
	},[props.data.end_date])

   useEffect(() => {
	 if(events?.length && props.data.event_id ){
		const matchingEvent = events.find(
						   option => option.event_id === props.data.event_id
					   );
				//    Update the selected event if a match was found
		   if (matchingEvent) {
			   setSelectedEvent(matchingEvent);
		   }
	 }
   }, [events, props.data]);

	const onCancelClick = () => {
		setStartDate(null);
		setEndDate(null);
	};

	const resetDateRange = () => {
		setStartDate(null);
		setEndDate(null);
		//reset the date range to original date
		_.forEach(selectedRowData, (row) => {
			const payload = {
				data: {
					start_date: row.start_date,
					end_date: row.end_date,
				},
				uniqueKeyVal: row.promo_id,
			};
			dispatch(workbenchCopyIAEditDataSave(payload));
		});
	};

	const onPrimaryButtonClick = () => {
		const payload = {
			data: {
				event_id: selectedEvent?.event_id,
				start_date: moment(startDate),
				end_date: moment(endDate),
			},
			uniqueKeyVal: props.data.promo_id,
		};
		dispatch(workbenchCopyIAEditDataSave(payload));
	}

	return (
		<div ref={ref} className="center-align-cell-content">
			<DateRangePicker
				showRangeSelector={false}
				startDate={startDate}
				setStartDate={setStartDate}
				endDate={endDate}
				setEndDate={setEndDate}
				startDateInputProps={{
					label: "StartDate",
					name: "start_date",
				}}
				endDateInputProps={{
					label: "EndDate",
					name: "end_date",
				}}
				onSecondaryButtonClick={
					onCancelClick
				}
				onResetClick={resetDateRange}
				displayFormat="MM-DD-YYYY"
				withPortal={true}
				orientation="horizontal"
				// isDisabled={_.isEmpty(selectedEvent?.event_id)}
				isOutsideRange={(date) =>
					moment(selectedEvent?.start_date).isAfter(date.startOf("day")) ||
					moment(selectedEvent?.end_date).isBefore(date.startOf("day"))
				}
				onPrimaryButtonClick={onPrimaryButtonClick}
			/>
		</div>
	)
}

export const eventSelectCellRenderer = (props) => {

	const dispatch = useDispatch();
	const {
		events,
	} = useSelector((store) => store?.pricesmartPromoReducer.promo);

	const [eventOptions, setEventOptions] = useState([]);
	const [selectedEvent, setSelectedEvent] = useState(null);
	const [isOpen, setIsOpen] = useState(false);
	const [filteredOptions, setFilteredOptions] = useState([]);

	useEffect(() => {
		 // Only proceed if we have valid event options and an event_id
		 if (eventOptions?.length && props.data.event_id) {
			// Find the event that matches the current event_id
			const matchingEvent = eventOptions.find(
				option => option.value === props.data.event_id
			);
			
			// Update the selected event if a match was found
			if (matchingEvent) {
				setSelectedEvent(matchingEvent);
			}
		}
	}, [props.data, eventOptions]);

	useEffect(() => {
		console.log(props)
		if (events?.length) {
			const tempOptions = events.map((event) => {
				return {
					label: replaceSpecialCharacter(event.event_name),
					value: event.event_id,
					start_date: event.start_date,
					end_date: event.end_date,
				};
			});
			setEventOptions(tempOptions);
		}
	}, [events]);

	const eventNameChangeHandler = (selectedOptions) => {
		setSelectedEvent(selectedOptions);
		const payload = {
			data: {
				event_id: selectedOptions?.value,
				start_date: moment(selectedOptions?.start_date),
				end_date: moment(selectedOptions?.end_date),
			},
			uniqueKeyVal: props.data.promo_id,
		};
		dispatch(workbenchCopyIAEditDataSave(payload));
	}

	return (
		<div className="center-align-cell-content">
			<Select
				currentOptions={filteredOptions?.length ? filteredOptions : eventOptions}
				initialOptions={eventOptions}
				setSelectedOptions={eventNameChangeHandler}
				setCurrentOptions={setFilteredOptions}
				placeholder={`Select ${global_labels?.event_primary}`}
				isRequired={true}
				isWithSearch={true}
				isMulti={false}
				selectedOptions={selectedEvent}
				isOpen={isOpen}
				setIsOpen={setIsOpen}
				isCloseWhenClickOutside={true}
				withPortal={true}
			/>
		</div>
	)
}

export const CellOnClickButton = (props) => {
	return (
		<div>
			<div className="center-align-cell-content">
				<Button
					onClick={() => props?.clickHandler && props?.clickHandler(props.data, props.colDef, props)}
					size="large"
					variant="url"
				>
					{props.value}
				</Button>
			</div>
		</div>
	)
}

export const CellWithDateRange = (props) => {
	const mutatedData = props.tableData.find(item => item.event_id === props.data.event_id);
	const [submitOfferByDate, setSubmitOfferByDate] = useState(mutatedData.submit_offers_by || null);



	const isOutsideRange = (date) => {
		const startDate = moment(mutatedData.start_date).add(-1, "days");
		const today = moment();
		return (
		  moment(date).isAfter(startDate) ||
		  moment(date).isBefore(today)
		);
	  };
	  
	return (
		<div className="center-align-cell-content copy-event-date-picker">
			 <DatePicker
			 withPortal={true}
              isRequired={true}
              selectedDate={submitOfferByDate}
              setSelectedDate={(date) => {
                setSubmitOfferByDate(date);
              }}
			  isOutsideRange={isOutsideRange}
              isDisabled={_.isEmpty(mutatedData.start_date)}
              displayFormat="MM-DD-YYYY"
			  onPrimaryButtonClick={() => {
				props.onCellValueChange({
					field: "submit_offers_by",
					value: submitOfferByDate,
					id: props.data.event_id
				});
			  }}
            />
		</div>
	)
}

export const CopyEventDateRangeCellRenderer = (props) => {
	
	const {onCellValueChange} = props;

	const [dates, setDates] = useState({
		start_date: props.data.start_date || null,
		end_date: props.data.end_date || null
	});

	const handleDateChange = () => {
		
		onCellValueChange({
			field: "start_date",
			value: dates.start_date,
			id: props.data.event_id
		});
		onCellValueChange({
			field: "end_date",
			value: dates.end_date,
			id: props.data.event_id
		});
	}

	const isOutsideRange = (date) => {
		const today = moment();
		return moment(date).isBefore(today, 'day');
	  };

	return (
		<div  className="center-align-cell-content">
			<DateRangePicker
				displayFormat="MM-DD-YYYY"
				showRangeSelector={false}
				withPortal={true}
				orientation="horizontal"
				startDate={dates.start_date}
				setStartDate={(date) => {
					setDates(prev => ({...prev, start_date: date}))
				}}
				endDate={dates.end_date}
				setEndDate={(date) =>{
					setDates(prev => ({...prev, end_date: date}))
				}}
				isOutsideRange={isOutsideRange}
				onPrimaryButtonClick={handleDateChange}
			/>
		</div>
	)
}

