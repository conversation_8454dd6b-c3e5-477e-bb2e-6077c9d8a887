import { createPortal } from 'react-dom';
import { useEffect, useState } from 'react';

const ToastPortal = ({ children }) => {
  const [portalContainer, setPortalContainer] = useState(null);

  useEffect(() => {
    // Create portal container if it doesn't exist
    let element = document.getElementById('toast-root');
    if (!element) {
      element = document.createElement('div');
      element.setAttribute('id', 'toast-root');
      element.style.position = 'fixed';
      element.style.zIndex = '9999';
      element.style.top = '0';
      element.style.left = '0';
      element.style.right = '0';
      element.style.pointerEvents = 'none';
      document.body.appendChild(element);
    }
    setPortalContainer(element);

    // Cleanup on unmount
    return () => {
      if (element && element.parentElement) {
        element.parentElement.removeChild(element);
      }
    };
  }, []);

  if (!portalContainer) return null;

  return createPortal(children, portalContainer);
};

export default ToastPortal;
