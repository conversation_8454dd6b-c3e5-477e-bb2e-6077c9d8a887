import React, { Suspense, useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import _ from "lodash";
import { Routes as RouterRoutes, Route } from "react-router-dom-v5-compat";
import { Skeleton } from "@mui/material";
// import { CompatRouter } from "react-router-dom-v5-compat";

/* 404 Page */
import NotFound from "./NotFound";

/* Layout */
import Layout from "../../components/common/layout/Layout";

/* Components */

import { getProtectedRoutes } from "../../constants/RouteConstants";
import { getGlobalConfigs } from "../../store/features/global/global";

/* Decision Dashboard */

const loadComponent = (componentName) => {
    const Component = React.lazy(() =>
        import(`../../components/${componentName}`)
    );
    return Component;
};

const AppRoutes = () => {
    const dispatch = useDispatch();
    const [isGlobalConfigsLoaded, setIsGlobalConfigsLoaded] = useState(false);

    useEffect(() => {
        // get global configs for the first time the app is loaded
        // this includes all the configurations for the app, like event, promo, etc. flags for those
        callGlobalConfigs();
    }, []);

    const callGlobalConfigs = async () => {
        const res = await dispatch(getGlobalConfigs());
        if (res) {
            setIsGlobalConfigsLoaded(res);
            // setRoutes(getProtectedRoutes());
        }
    };

    const protectedRoutes = getProtectedRoutes();
    return (
        <main className="App">
            <Suspense
                fallback={
                    <div>
                        <h1>...Loading</h1>
                        <Skeleton />
                    </div>
                }
            >
                {isGlobalConfigsLoaded && (
                    <RouterRoutes>
                        {!_.isEmpty(protectedRoutes) &&
                            protectedRoutes.map((args) => {
                                if (!args.component)
                                    return (
                                        <Route
                                            path="/*"
                                            key="notFound"
                                            element={<NotFound />}
                                        />
                                    );
                                const Component = loadComponent(args.component);
                                const { ...rest } = args;
                                return (
                                    <Route
                                        exact
                                        {...rest}
                                        element={
                                            <Suspense>
                                                <Layout>
                                                    <Component {...rest} />
                                                </Layout>
                                            </Suspense>
                                        }
                                    />
                                );
                            })}
                        <Route path="/*" element={<NotFound />} />
                        <Route
                            path="/under-maintenance"
                            element={<NotFound type="under_maintenance" />}
                        />
                    </RouterRoutes>
                )}
            </Suspense>
        </main>
    );
};

export default AppRoutes;
