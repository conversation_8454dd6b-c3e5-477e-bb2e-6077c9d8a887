.App {
	position: fixed;
	height: 100vh;
	width: 100vw;
}

.positionRelative {
	position: relative;
}

.display_flex {
	display: flex;
	flex-direction: row;
	justify-content: flex-start;
	align-items: center;
}

.display-flex-end{
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.base_color {
	background-color: #eff2fa;
}

.width100 {
	width: 100%;
}

.width-500 {
    width: 500px;
}

.height-full {
	height: 100vh;
}

.content_container {
	padding: 24px;
	border-radius: 8px;
	background: #ffffff;
}

.content_container_border {
	@extend .content_container;
	border: 1px solid #d9dde7;
}

.carousel_container {
	@extend .content_container;
	padding: 12px;
}

.layoutWidth {
	width: calc(100% - 60px);
}

.screen_container {
	display: flex;
	flex-direction: column;
	height: calc(100vh - 56px);
}

.screen_data_container {
	padding: 0px 24px 24px 24px;
}

.flexWithCenterAlign {
	display: flex;
	align-items: center;
}

.flexWithGap12 {
	display: flex;
	gap: 12px;
	align-items: center;
}

.flexWithGap8 {
	display: flex;
	gap: 8px;
	align-items: center;
}

.flexWithGap16 {
	display: flex;
	gap: 16px;
	align-items: center;
}

.centerFlexWithGap12 {
	@extend .flexWithGap12;
	align-items: center;
	justify-content: center;
}

.flex24 {
	display: flex;
	align-items: center;
	gap: 24px;
}

.flex1 {
	flex: 1;
}

.flexContentAround {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.flexColumn {
	flex-direction: column;
	align-items: unset !important;
}

.footer_section {
	padding: 16px 24px;
	border-top: 1px solid #d9dde7;
	background: #ffffff;
	position: fixed;
	bottom: 0;
	width: calc(100% - 60px);
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.horizontal-line {
	height: 12px;
	width: 1px;
	background-color: #d9dde7;
}

.horizontal-divider-line {
    border-top: 1px solid #d9dde7;
}

.buttons_container {
	margin-top: 28px;
	display: flex;
	gap: 16px;
}

.ag-aria-description-container {
	display: none !important;
}

.tableGlobalSearchContainer {
	position: absolute;
	right: 40px;
	bottom: 0px;
	z-index: 2;
	background: white;
	opacity: 0;
	animation: slideIn 0.5s forwards;
}

@keyframes slideIn {
	0% {
		opacity: 0;
		transform: width(0);
	}
	100% {
		opacity: 1;
		transform: width(100%);
	}
}

.maxWidth-60 {
	max-width: 60% !important;
}

.maxWidth-70 {
	max-width: 70% !important;
}

.maxWidth-90 {
	max-width: 90% !important;
}

/* label css */

.label-14px-normal {
	font-size: 14px;
	color: #60697d;
}

.label-12px-normal {
	font-size: 12px;
	color: #60697d;
}

/* primary and secondary text css */

.text-12-500 {
	color: #1f2b4d;
	font-size: 12px;
	line-height: 125%;
	font-weight: 500;
}

.text-12-600 {
	color: #1f2b4d;
	font-size: 12px;
	line-height: 125%;
	font-weight: 600;
}

.text-12-800 {
	color: #1f2b4d;
	font-size: 12px;
	line-height: 125%;
	font-weight: 800;
}

.text-14-500 {
	color: #0d152c;
	font-size: 14px;
	line-height: 21px;
	font-weight: 500;
}

.text-14-600 {
	color: #0d152c;
	font-size: 14px;
	font-weight: 600;
	line-height: 21px;
}

.text-14-700 {
	color: #1f2b4d;
	font-size: 14px;
	font-weight: 700;
	line-height: 21px;
}

.text-14-800 {
	color: #1f2b4d;
	font-size: 14px;
	font-weight: 800;
	line-height: 21px;
}

.text-16-500 {
	color: #1f2b4d;
	font-size: 16px;
	font-weight: 500;
	line-height: 20px;
}

.text-16-600 {
	color: #1f2b4d;
	font-size: 16px;
	font-weight: 600;
	line-height: 20px;
}

.text-16-800 {
	color: #1f2b4d;
	font-size: 16px;
	font-weight: 800;
	line-height: 24px;
}

.text-20-800 {
	color: #0d152c;
	font-size: 20px;
	font-weight: 800;
	line-height: 30px;
}

.secondaryText-12-400 {
	color: #60697d;
	font-size: 12px;
	line-height: 20px;
	font-weight: 400;
}

.secondaryText-12-500 {
	color: #60697d;
	font-size: 12px;
	line-height: 20px;
	font-weight: 500;
}

.secondaryText-14-500 {
	color: #60697d;
	font-size: 14px;
	line-height: 20px;
	font-weight: 500;
}

.secondaryText-14-700 {
	color: #60697d;
	font-size: 14px;
	line-height: 20px;
	font-weight: 700;
}

.secondaryText-16-500 {
	color: #60697d;
	font-size: 16px;
	line-height: 20px;
	font-weight: 500;
}

.secondaryText-24-700 {
    color: #c3c8d4;
    font-size: 24px;
    font-weight: 700;
}

.primaryText-12-500{
	color: #3649C6;
	font-size: 12px;
	font-weight: 500;
	line-height: 20px;
}

.primaryText-14-500{
	color: #3649C6;
	font-size: 14px;
	font-weight: 500;
	line-height: 20px;
}

.primaryText-20-800{
	color: #3649C6;
	font-size: 20px;
	font-weight: 800;
	line-height: 30px;
}

.text-nowrap {
	text-wrap: nowrap;
}

.textAlignRight {
	text-align: right;
}

.textAlignCenter {
    text-align: center;
}


/* padding and margins */

.padding-8 {
	padding: 8px;
}

.padding-16 {
	padding: 16px;
}

.padding-24 {
	padding: 24px;
}

.paddingBottom-8 {
	padding-bottom: 8px;
}

.paddingTop-8 {
	padding-top: 8px;
}

.paddingTop-12 {
	padding-top: 12px;
}

.paddingTop-16 {
	padding-top: 16px;
}

.paddingTop-20 {
	padding-top: 20px;
}

.paddingTop-24 {
	padding-top: 24px;
}

.paddingBottom-24 {
	padding-bottom: 24px;
}

.paddingLeft-24 {
	padding-left: 24px;
}

.paddingRight-8 {
	padding-right: 8px;
}

.marginTop-8 {
	margin-top: 8px;
}

.marginTop-12 {
	margin-top: 12px;
}

.marginTop-16 {
	margin-top: 16px;
}

.marginTop-20 {
	margin-top: 20px;
}

.marginTop-24 {
	margin-top: 24px;
}

.marginBottom-24 {
	margin-bottom: 24px;
}

.marginBottom-8 {
	margin-bottom: 8px;
}

.marginBottom-12 {
	margin-bottom: 12px;
}

.marginBottom-16 {
	margin-bottom: 16px;
}

.marginBottom-20 {
	margin-bottom: 20px;
}

.margin-16 {
	margin: 16px;
}

.margin-20 {
	margin: 20px;
}

.marginLeft-8 {
	margin-left: 8px !important;
}

.marginLeft-12 {
	margin-left: 12px;
}

.marginLeft-16 {
	margin-left: 16px !important;
}

.row-checkbox-disabled {
	pointer-events: none;
}


.dotted-divider {
	border-top: 1px dotted #D9DDE7;
}
.grid-40-60 {
	display: grid;
	grid-template-columns: 40% 60%;
}

.grid-row-gap-8 {
	grid-row-gap: 8px;
}

.grid-row-gap-16 {
	grid-row-gap: 16px;
}

.grid-row-gap-24{
	grid-row-gap: 24px;
}

.horizontalCenterContent {
	justify-content: center;
}

.greyed-out {
	background: #f2f2f2 !important;
    color: #B4BAC7 !important;
}

.text-ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.create-event-container{
	.impact_drawer_filter_container_right_panel{
		.impact_drawer_filter_container_right_panel{
			left: 0;
		}
	}
}


/* To be removed post demo and Design system release */

.info-badge-stroke {
	height: 24px !important;
	span{
		font-size: 14px !important;
	}
}

.bold-text-800{
	font-weight: 800;
}

.flex-wrap-wrap{
	flex-wrap: wrap;
}

.vertical-divider {
	border-left: 1px solid #D9DDE7;
	height: 15px;
}
/* Hotfix by Impact UI Jaymin/Himanshu */
.ag-theme-alpine .ag-pinned-left-cols-container .ag-row {
    background-color: #ffffff;
}
.svg-blue-icon {
	filter: brightness(0) saturate(100%) invert(27%) sepia(79%) saturate(5046%) hue-rotate(232deg) brightness(82%) contrast(87%);
}

.svg-blue-icon-hover {
	&:hover {
		filter: brightness(0) saturate(100%) invert(27%) sepia(79%) saturate(5046%) hue-rotate(232deg) brightness(82%) contrast(87%);
	}
}

.delete-button {
	&:hover {
		img {
			filter: brightness(0) saturate(100%) invert(45%) sepia(92%) saturate(3836%) hue-rotate(339deg) brightness(122%) contrast(76%);
		}
	}
}

.svg-grey-icon {
	filter: brightness(0) saturate(100%) invert(43%) sepia(7%) saturate(1118%) hue-rotate(183deg) brightness(90%) contrast(87%);
}

.impact-date-picker-dropdown-container{
	z-index: 1200 !important;
}

.product-store-detail-table {
    box-shadow: none !important;
}

.product-store-detail-modal {
    min-width: 1000px !important;
    min-height: 650px !important;
    .impact-table-main-header {
        height: 60px !important;
    }
	.impact-table-main-container {
		margin: unset !important;
		padding: unset !important;
		box-shadow: none !important;
	}
}

.metrics_display_mode_container {
    display: flex !important;
    align-items: center !important;
    gap: 24px !important;
}

.metrics_radio_button_group {
    display: flex !important;
    flex-direction: row !important;
    gap: 8px !important;

    .ia-radioButton {
        border: 1px solid #D9DDE7;
        border-radius: 12px;
        padding: 0px 10px 0px 2px;
    }
}
.ia-radioButton-selected:not(.Mui-disabled)  {
    border-color: #4259EE !important;
    .MuiFormControlLabel-label {
        color: #4259EE !important;
    }
}