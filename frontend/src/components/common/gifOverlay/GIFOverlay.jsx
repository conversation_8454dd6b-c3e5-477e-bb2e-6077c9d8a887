import React, { useState, useEffect } from "react";
import { Button } from "impact-ui";
import _ from "lodash";

import "./GIFOverlay.scss";

import FailureGIF from "../../../assets/imageAssets/waitingSVGFailure.svg?.url";
import waitingGIF from "../../../assets/imageAssets/waitingGIF.gif";
import { formatThousands } from "../../../utils/helpers/formatter";
import { gifDataPointsConfig, optimizationGIFConfig } from "./GIFConstants";
import LinearProgressBar from "../linearProgressBar/LinearProgressBar";

function GIFOverlay(props) {

	const [isProcessOn, setIsProcessOn] = useState(true);
	const [isFailed, setIsFailed] = useState(false);
	const [gifConfig, setGifConfig] = useState(gifDataPointsConfig);

	const {
		headerText = null,
		showActionButtons = true,
		primaryButtonLabel = null,
		secondaryButtonLabel = null,
		tertiaryButtonLabel = null,
		footerText = null,
		processStatus = null,
		autoCloseOnSuccess = true,
		data = {},
		timerData = {},
	} = props;

	useEffect(() => {
		if (processStatus === "success") {
			setIsProcessOn(false);
			setIsFailed(false);
			if (autoCloseOnSuccess) {
				setTimeout(() => {
					props.closeGIFModal();
				}, 5000);
			}
		}
		if (processStatus === "failure") {
			setIsProcessOn(false);
			setIsFailed(true);
		}
	}, [processStatus]);

	useEffect(() => {
		if (data.hasOwnProperty("discount_points") && data.discount_points != undefined) {
			setGifConfig(_.cloneDeep(optimizationGIFConfig));
		} else {
			setGifConfig(_.cloneDeep(gifDataPointsConfig));
		}
	}, [data]);

	return (
		<div className="gifContainer">
			<div className="gifDataContainer">
				<div className="primaryText-20-800">{headerText}</div>
				<div>
					{isProcessOn || !isFailed ? (
						<img
							className="gifImageContainer"
							src={waitingGIF}
							alt={`waiting`}
						/>
					) : (
						<div className="positionRelative">
							<img src={FailureGIF} alt="waiting GIF" />
							<span className="failureCross">X</span>
						</div>
					)}
				</div>
				{!isProcessOn ? (
					<div
						className={
							isFailed
								? "gifFailedContainer"
								: "gifSuccessContainer"
						}
					>
						{isFailed ? "Failed" : "Ready !"}
					</div>
				) : null}
				{!_.isEmpty(data) ? (
					<div>
						<div className="flexWithGap16">
							{_.map(gifConfig, (config) => {
								return config.isDataBox ? (
									<div className={config.className}>
										{config.icon}
										<div>
											<div className="gifDataValue">
												{formatThousands(
													data[config.key]
												)}
											</div>
											<div className="text-18-700">
												{config.label}
											</div>
										</div>
									</div>
								) : (
									<span className={config.className}>
										{config.label}
									</span>
								);
							})}
						</div>
						{!_.isEmpty(timerData) ? (
							<div className="gifTimerContainer">
								<div className="text-16-500">
									It'll take approximately{" "}
									{timerData?.["time_in_mins"]} {timerData?.["time_in_mins"] > 1 ? "minutes" : "minute"}. You can wait here.
								</div>
								<LinearProgressBar
									showFullBar={false}
									barColor={isFailed ? "#DA1E28" : "#1E6C4B"}
									timeInSec={
										(timerData?.["time_in_mins"] * 60) || 0
									}
								/>
							</div>
						) : null}
						<div className="secondaryText-24-700 textAlignCenter marginTop-20">
							OR
						</div>
					</div>
				) : null}
				{showActionButtons ? (
					<div className="flexWithGap16 horizontalCenterContent marginTop-16">
						{primaryButtonLabel ? (
							<Button
								onClick={props.primaryButtonAction}
								size="large"
								variant="primary"
							>
								{primaryButtonLabel}
							</Button>
						) : null}
						{secondaryButtonLabel ? (
							<Button
								onClick={props.secondaryButtonAction}
								size="large"
								variant="secondary"
							>
								{secondaryButtonLabel}
							</Button>
						) : null}
						{tertiaryButtonLabel ? (
							<Button
								onClick={props.tertiaryButtonAction}
								size="large"
								variant="secondary"
							>
								{tertiaryButtonLabel}
							</Button>
						) : null}
					</div>
				) : null}
				{footerText ? (
					<div className="gifFooterContainer marginTop-20">
						<div className="gifHorizontalLine" />
						<div className="secondaryText-14-500 ">{footerText}</div>
					</div>
				) : null}
			</div>
		</div>
	);
}

export default GIFOverlay;

// required props:-

// headerText --> (default null), if not sent, we won't show the header text
// showActionButtons --> (default true), if false, we won't show the action buttons.
// primaryButtonLabel --> (default null), if not sent, we won't show the primary button.
// secondaryButtonLabel --> (default null), if not sent, we won't show the secondary button.
// tertiaryButtonLabel --> (default null), if not sent, we won't show the tertiary button.
// footerText --> (default null), if not sent, we won't show the footer text.
// processStatus --> (default null), send "success" or "failure" to show the success or failure state, after process is done
// autoCloseOnSuccess --> (default true), if true, the modal will close automatically after 5 seconds of success state
// closeGIFModal -->  function to close the GIF modal
// primaryButtonAction --> function to be called on primary button click
// secondaryButtonAction --> function to be called on secondary button click
// tertiaryButtonAction --> function to be called on tertiary button click

// Example:-(

// const [processStatus, setProcessStatus] = useState(null);

// useEffect(() => {
//     if(processStatus === "success"){
//     setProcessStatus("success");
//     }else{
//         setProcessStatus("failure");
//     }
// }, [isOptimisationSuccss]);

// 			<GIFOverlay
// 				headerText="Optimization is running for"
// 				primaryButtonLabel="Create Strategy"
// 				primaryButtonAction={() => {
// 					console.log("Create Strategy");
// 				}}
// 				secondaryButtonLabel="Return to Workbench"
// 				secondaryButtonAction={() => {
// 					console.log("Return to Workbench");
// 				}}
// 				tertiaryButtonLabel="Edit scenario"
// 				tertiaryButtonAction={() => {
// 					console.log("Return to Workbench");
// 				}}
// 				footerText="Don't worry. You'll get notification once optimisation is completed."
// 				processStatus={processStatus}
// 				closeGIFModal={() => {
// 					console.log("Close GIF Modal");
// 				}}
// 			/>
// 		);
