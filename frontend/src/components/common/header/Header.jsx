import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import uuid from "react-uuid";
import moment from "moment";
import { Header } from "impact-ui";
import Smartbot from "core/commonComponents/smartBot";
import { firebaseobj } from "../../../auth/firebase";
import {
    removeLoader,
    requestCompleteNoLoader,
    toastError,
    toggle<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oa<PERSON>,
    setNewSseConnection,
    setSseRecievedEvents,
    setNewNotificationIndicator,
} from "../../../store/features/global/global";
import {
    setSimulatedPromoId,
    setIsOptimise,
} from "../../../store/features/promoReducer/promoReducer";
import { replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";

import Notifications from "../notifications/Notifications";

import "./Header.scss";

const REACT_APP_API_BASE_URL = process.env.REACT_APP_API_BASE_URL;

const getGuid = () => {
    if (sessionStorage.getItem("UNIQ_SSE_KEY")) {
        return sessionStorage.getItem("UNIQ_SSE_KEY");
    }
    const id = uuid(); // generate unique UUID
    sessionStorage.setItem("UNIQ_SSE_KEY", id);
    return id;
};

function HeaderComponent() {
    const [isNotificationOpen, setIsNotificationOpen] = useState(false);
    const [isChatBotOpen, setIsChatBotOpen] = useState(false);

    const dispatch = useDispatch();

    const { newSseConnection, newNotificationIndicator = false } = useSelector(
        (store) => store?.pricesmartPromoReducer.global
    );

    const handleMojo = () => {
        window.open(
            "https://impactanalytics.mojohelpdesk.com/mc/up/new-ticket/guest/76717",
            "_blank"
        );
    };

    // Adding event listener for SSE - Lock/Unlock & Override/Finalize
    useEffect(() => {
        screenMount();
    }, []);

    useEffect(() => {
        if (isNotificationOpen && newNotificationIndicator) {
            dispatch(setNewNotificationIndicator(false));
        }
    }, [isNotificationOpen]);

    const screenMount = async () => {
        const uniqueId = getGuid();
        const token = await getValidToken();
        const sseListener = new EventSource(
            REACT_APP_API_BASE_URL +
                "sse-output?guid=" +
                uniqueId +
                "&token=" +
                token,
            {
                authorizationHeader: `Bearer ${token}`,
            }
        );
        establishSseConnection(sseListener);

        return () => {
            sseListener.close();
            sessionStorage.removeItem("UNIQ_SSE_KEY");
        };
    };

    useEffect(() => {
        sseConnectionHandler();
    }, [newSseConnection]);

    const sseConnectionHandler = async () => {
        if (newSseConnection) {
            // const uniqueId = sessionStorage.getItem("UNIQ_SSE_KEY");
            const uniqueId = uuid();
            const token = await getValidToken();
            const sseListener = new EventSource(
                REACT_APP_API_BASE_URL +
                    "sse-output?guid=" +
                    uniqueId +
                    "&token=" +
                    token,
                {
                    // withCredentials: true,
                }
            );
            establishSseConnection(sseListener);
            dispatch(setNewSseConnection(false));
        }
    };

    const getValidToken = async () => {
        // Check if SAML token is valid. If expiry time is less than 5 mins, generate new token
        let token = await firebaseobj
            ?.auth()
            .currentUser?.getIdTokenResult()
            .then(async (authResult) => {
                const expirationTime = authResult.expirationTime;
                const fiveMinsFromNow = moment().add(5, "minutes");
                if (
                    moment.utc(expirationTime).local().isBefore(fiveMinsFromNow)
                ) {
                    console.log("Token about to expire. Generating new");
                    // Generate new token and save
                    try {
                        let tokenGenerated = await firebaseobj
                            .auth()
                            .currentUser.getIdToken(true);

                        localStorage.setItem("token", tokenGenerated);
                        return tokenGenerated;
                    } catch (error) {
                        console.error(
                            "Unable to generate refresh token",
                            error
                        );
                    }
                } else if (authResult.token !== localStorage.getItem("token")) {
                    localStorage.setItem("token", authResult.token);
                    return authResult.token;
                } else {
                    return localStorage.getItem("token");
                }
            });
        return token;
    };

    const renewSseConnection = async () => {
        // const uniqueId = sessionStorage.getItem("UNIQ_SSE_KEY");
        const uniqueId = uuid();

        const token = await getValidToken();
        const sseListener = new EventSource(
            REACT_APP_API_BASE_URL +
                "sse-output?guid=" +
                uniqueId +
                "&token=" +
                token,
            {
                // withCredentials: true,
            }
        );
        establishSseConnection(sseListener);
    };

    const establishSseConnection = (sseListener) => {
        sseListener.onmessage = (e) => {
            // Check event type and parse accordingly
            const parsedResponse = JSON.parse(e.data);
            !_.isEmpty(parsedResponse) &&
                !_.isEmpty(parsedResponse.messages) &&
                parsedResponse.messages.map((eventResponse) => {
                    // Replace special characters in eventResponse.message
                    eventResponse.message = replaceSpecialCharacter(
                        eventResponse.message
                    );

                    if (
                        (eventResponse.status == 200 ||
                            eventResponse.status == true) &&
                        (eventResponse.action === "override" ||
                            eventResponse.action === "finalize" ||
                            eventResponse.action === "approve_scenario")
                    ) {
                        dispatch(setNewNotificationIndicator(true));
                        dispatch(toggleLengthyOpLoader(false));
                        dispatch(
                            requestCompleteNoLoader(eventResponse.message)
                        );
                    } else if (
                        (eventResponse.status !== 200 ||
                            eventResponse.status === false) &&
                        (eventResponse.action === "finalize" ||
                            eventResponse.action === "override" ||
                            eventResponse.action === "approve_scenario")
                    ) {
                        dispatch(setNewNotificationIndicator(true));
                        dispatch(toastError(eventResponse.message));
                        dispatch(toggleLengthyOpLoader(false));
                    } else if (
                        (eventResponse.status === 200 ||
                            eventResponse.status == true) &&
                        (eventResponse.action === "resimulate" ||
                            eventResponse.action === "optimise")
                    ) {
                        // Resimulate OR Optimization complete
                        dispatch(setNewNotificationIndicator(true));
                        dispatch(setSimulatedPromoId(eventResponse.promo_id));
                        dispatch(setIsOptimise(false));
                        dispatch(toggleLengthyOpLoader(false));
                    } else if (
                        (eventResponse.action === "resimulate" ||
                            eventResponse.action === "optimise") &&
                        eventResponse.status !== 200
                    ) {
                        dispatch(setNewNotificationIndicator(true));
                        dispatch(setIsOptimise(false));
                        dispatch(toggleLengthyOpLoader(false));
                        dispatch(toastError(eventResponse.message));
                    } else if (
                        eventResponse.action === "bulk_resimulate" &&
                        eventResponse.status === 200
                    ) {
                        const editedEvent = _.cloneDeep(eventResponse);
                        editedEvent.read_at = null;
                        editedEvent.read_by = null;
                        editedEvent.is_broadcast_message = 0;
                        editedEvent.is_sse_notification = true;
                        dispatch(setNewNotificationIndicator(true));
                        dispatch(
                            requestCompleteNoLoader(eventResponse.message)
                        );
                    } else if (
                        eventResponse.action === "bulk_resimulate" &&
                        eventResponse.status !== 200
                    ) {
                        dispatch(toastError(eventResponse.message));
                    } else if (
                        eventResponse.action === "execution_approved" &&
                        (eventResponse.status === 400 ||
                            eventResponse.status === false)
                    ) {
                        dispatch(setNewNotificationIndicator(true));
                        dispatch(toastError(eventResponse.message));
                    } else if (
                        eventResponse.action === "download" ||
                        eventResponse.status === 200 ||
                        eventResponse.status === true
                    ) {
                        switch (eventResponse.module) {
                            case "REPORT":
                                const downloadEvent = _.cloneDeep(
                                    eventResponse
                                );
                                downloadEvent.read_by = null;
                                downloadEvent.is_broadcast_message = 0;
                                break;
                            case "EVENT_AUTO_RESIMULATION":
                                const editedEvent = _.cloneDeep(eventResponse);
                                editedEvent.read_by = null;
                                editedEvent.is_broadcast_message = 0;
                                editedEvent.is_sse_notification = true;
                                break;
                            default:
                                console.log(eventResponse);
                                break;
                        }
                        // Dispatch request complete event to show the success message
                        dispatch(setNewNotificationIndicator(true));
                        dispatch(
                            requestCompleteNoLoader(eventResponse.message)
                        );
                    } else {
                        console.log("Error", eventResponse);
                        dispatch(removeLoader());
                        dispatch(toastError(eventResponse.message));
                    }

                    dispatch(
                        setSseRecievedEvents({
                            eventId: eventResponse.action,
                            eventData: eventResponse,
                            triggeredStatus: false,
                        })
                    );
                });
            // props.sseCompleteEvents(eventsComplete);
        };
        sseListener.onerror = (e) => {
            console.log(e);
            // Check here for token validity
            // If there's an authentication error, close the SSE connection and reopen it again
            console.log("Reconnecting....");
            sseListener.close();
            renewSseConnection();
        };
    };

    const userID = localStorage.getItem("name");
    let userName = userID ? userID.split("@")[0] : "User";
    userName = userName
        .split(".")
        .map((item) => item.charAt(0).toUpperCase() + item.slice(1))
        .join(" ");
    return (
        <div className="headers_container">
            <Header
                dropMenuOptions={[]}
                handleHelpClick={handleMojo}
                handleChatBotClick={() => setIsChatBotOpen(!isChatBotOpen)}
                handleNotificationClick={() => setIsNotificationOpen(true)}
                notificationIndicator={newNotificationIndicator}
                title="Pricesmart / Promo"
                userName={userName}
                showNotificationIcon={true}
                showHelpIcon={true}
                showChatBotIcon={true}
                showMessageIcon={false}
                handleLogoClick={() => {
                    window.location.href = "/home";
                }}
            />
            <Notifications
                isOpen={isNotificationOpen}
                setIsOpen={setIsNotificationOpen}
            />
            <Smartbot
                invokeBot={isChatBotOpen}
                closeBot={() => setIsChatBotOpen(false)}
            />
        </div>
    );
}

export default HeaderComponent;
