.impact-notification-container {
    .ia-tabs-horizontal {
        .MuiTabs-root {
            display: none;
        }
    }
    .impact-notification-task-list-header {
        display: none;
    }
    .impact-notification-list-container {
        padding-top: 0px;
    }
    .impact-tab-panel,
    .ia-tabPanel {
        padding-top: 0px !important;
    }
    .impact-notification-list-header {
        padding-bottom: 10px;
    }
    .impact-notification-body-container {
        padding-bottom: 10px;
    }
    .impact-notification-list-items-container {
        position: absolute;
        left: 0;
        right: 0;
        top: 45px;
        bottom: 0;
        height: auto !important;
    }

    .impact-notification-list-item-header-left img {
        display: none !important;
    }

    .impact-notification-list-item-header-left {
        display: none;
    }

    .impact-notification-list-item-title {
        max-width: 36ch;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .impact-notification-separator {
        display: none;
    }
    .impact-notification-list-item-icon {
        height: 16px !important;
        width: 16px !important;
    }
    .impact-notification-list-item-icon-line {
        height: 8px !important;
        width: 1px !important;
    }

    .impact-notification-list-item {
        align-items: baseline !important;
    }

    .impact-notification-list-item-header {
        .impact-notification-list-item-header-right {
            .ia-styles.ia-btn:nth-child(1),
            .ia-styles.ia-btn:nth-child(2) {
                visibility: hidden;
            }
        }
    }
}
