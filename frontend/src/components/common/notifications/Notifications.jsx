import React, { useEffect, useState, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Notification } from "impact-ui";
import _ from "lodash";

import "./Notifications.scss";

import {
    getNotifications,
    toastError,
    updateNotificationReadStatus,
} from "../../../store/features/global/global";
import { useNavigate } from "react-router-dom-v5-compat";
import { setActivePromoId, setMaxStepCount } from "../../../store/features/promoReducer/promoReducer";
import { replaceSpecial<PERSON>haracter } from "../../../utils/helpers/utility_helpers";
function Notifications(props) {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const { notifications = {} } = useSelector(
        (store) => store?.pricesmartPromoReducer.global
    );

    const [notificationList, setNotificationList] = useState([
        {
            id: 1,
            value: "task-list",
            notificationList: [],
        },
    ]);

    useEffect(() => {
        dispatch(getNotifications());
    }, []);

    useEffect(() => {
        if (props.isOpen) {
            dispatch(getNotifications());
        }
    }, [props.isOpen]);

    useEffect(() => {
        //if notifications is not empty, build the notification data as per impact ui notification component
        if (!_.isEmpty(notifications)) {
            const notificationData = _.map(
                notifications.notifications,
                (notification) => {
                    const notitificationCardData = {
                        id: notification.notification_id,
                        status: "success",
                        read: notification.read_at ? true : false, // Set read based on the backend's `read_at` field
                        label: notification.header_text,
                        date: new Date(notification.created_at),
                        time: new Date(notification.created_at).getTime(),
                        description: replaceSpecialCharacter(notification.message),

                        handleMarkAsRead: () =>
                            handleMarkNotificationRead(notification),
                        handleSelectChange: () =>
                            handleNotificationClick(notification),
                    };
                    //if the notification is of type download, add the download handler
                    //also set the status to fail if the notification execution status is not present
                    //and set the read status to true if the notification is already read
                    if (notification.action === "download") {
                        notitificationCardData.handleDownloadNotification = () =>
                            downloadNotificationHandler(notification);
                        notitificationCardData.handleDeleteNotification = () => { };
                        notitificationCardData.handleBookMark = () => { };
                        if (!notification.execution_status)
                            notitificationCardData.status = "fail";
                        if (notification.read_at)
                            notitificationCardData.read = true;
                    }

                    return notitificationCardData;
                }
            );
            const tempData = _.cloneDeep(notificationList);
            tempData[0].notificationList = _.cloneDeep(notificationData);
            setNotificationList(tempData);
        }
    }, [notifications]);

    const handleNotificationClick = (notification) => {
        if (notification.module === "promotions" && notification.action === "copy") {
            const promoIds = notification.promo_ids || [];
            if (notification.execution_status === false) {
                dispatch(toastError(`Copy ${global_labels?.promo_alias} failed for the promotion.`));
            } else if (promoIds.length > 0) {
                navigate(`/pricesmart-promo/workbench/?promoIds=${promoIds}`, {
                    replace: false,
                });
                props.setIsOpen(false);
            } else {
                dispatch(toastError(`No copied ${global_labels?.promo_plural} found.`));
            }
        } else if (notification.module === "event" && notification.action === "copy") {
            const eventIds = notification.promo_ids || [];
            if (notification.execution_status === false) {
                dispatch(toastError(`Copy ${global_labels?.event_primary} failed.`));
            } else if (eventIds.length > 0) {
                navigate(`/pricesmart-promo/marketing-calendar/?eventIds=${eventIds}`, {
                    replace: false,
                });
                props.setIsOpen(false);
            } else {
                dispatch(toastError(`No copied ${global_labels?.event_plural} found.`));
            }
        }
        else if (notification.navigate_to && (notification.step_count || notification.step_count == 0)) {
            navigate(`/pricesmart-promo/workbench/create-offer?promo=${notification.navigate_to}`, { replace: false });
            dispatch(setMaxStepCount(notification.step_count));
            dispatch(setActivePromoId(notification.navigate_to));
            props.setIsOpen(false);
        } else if (notification.action === "download") {
            downloadNotificationHandler(notification);
        }
    }

    const downloadNotificationHandler = useCallback((notification) => {
        //get the current notification list
        //update the read status to true, and update the local state
        // using this approach as where the callback function is "close over" 
        // the state value from when they were defined.
        setNotificationList(currentList => {
            const tempList = _.cloneDeep(currentList);
            const tempNotificationList = tempList[0].notificationList;

            //find the index of the selected notification in the list
            const index = _.findIndex(
                tempNotificationList,
                (item) => item.id === notification.notification_id
            );

            tempNotificationList[index].read = true;
            tempList[0].notificationList = tempNotificationList;
            return tempList;
        });

        //if the download link is expired, return
        if (notification.download_link_expired) return;

        //open the download link in a new tab
        if (notification?.download_url) {
            window.open(notification.download_url, "_blank");
        }
    }, []);

    const handleMarkNotificationRead = (notification) => {
        // Dispatch the action to update the backend
        dispatch(
            updateNotificationReadStatus({
                mark_all_as_read: false,
                notification_ids: [notification.notification_id],
                application: "promo",
            })
        ).then((success) => {
            if (success === true) {
                dispatch(getNotifications());// Fetch notifications only if API succeeded
            }
        });
    };

    const markAllAsReadHandler = async () => {
        // Dispatch the action to update the backend
        dispatch(
            updateNotificationReadStatus({
                notification_ids: [],
                mark_all_as_read: true,
                application: "promo",
            })
        ).then((success) => {
            if (success === true) {
                dispatch(getNotifications());// Fetch notifications only if API succeeded
            }
        });
    };

    return (
        <div className="ps_notifications">
            <Notification
                title="Notification"
                isOpen={props.isOpen}
                setIsOpen={props.setIsOpen}
                handleClose={() => props.setIsOpen(false)}
                notificationPanels={notificationList}
                setNotificationPanels={setNotificationList}
                notificationTabs={[
                    {
                        label: "Task List",
                        value: "task-list",
                    },
                ]}
                handleMarkReadAll={markAllAsReadHandler}
            />
        </div>
    );
}

export default Notifications;