import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom-v5-compat";
import { useDispatch } from "react-redux";
import { Sidebar } from "impact-ui";
import _ from "lodash";

import { signOut } from "../../../store/features/auth/auth";

import "./Sidebar.scss";

import { sideBarRoutes } from "../../../constants/RouteConstants";

function SidebarComponent() {
    const [activeRoute, setActiveRoute] = useState("decision-dashboard");
    const [activeTempRoute, setActiveTempRoute] = useState(
        "decision-dashboard"
    );
    const [activeChildRoute, setActiveChildRoute] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const currentRoute = window.location.pathname;

    useEffect(() => {
        // on path change, check the current route and set the active route

        _.forEach(sideBarRoutes, (route) => {
            //if the route has no children
            if (currentRoute.includes(route.link)) {
                setActiveRoute(route.value);
                setActiveTempRoute(route.value);
                setActiveChildRoute("");
            }
            if (route.children.length) {
                //if the route has children, find the child and set both parent and child route
                return _.forEach(route.children, (child) => {
                    if (currentRoute.includes(child.link)) {
                        setActiveRoute(route.value);
                        setActiveTempRoute(route.value);
                        setActiveChildRoute(child.value);
                    }
                });
            }
        });
    }, [currentRoute]);

    const handleChildRouteChange = (parent, child) => {
        setActiveChildRoute(child.value);
        setActiveTempRoute(parent.value);
        navigate(child.link);
        setIsOpen(false);
    };
    const handleParentRouteChange = (route) => {
        setActiveRoute(route.value);
        //if parent route has no children, navigate to the link
        if (!route.children?.length) {
            navigate(route.link);
            setActiveTempRoute(route.value);
            setIsOpen(false);
        }
    };

    const onSideBarClose = () => {
        //if you've clicked on sidebar option which has children, but closed the sidebar, the active route will be moved back to current screen
        if (activeRoute !== activeTempRoute) {
            setActiveRoute(activeTempRoute);
        }
        setIsOpen((prev) => !prev);
    };

    return (
        <div className="sidebar_container">
            <Sidebar
                actionRoutes={[]}
                childActive={activeChildRoute}
                handleChildRouteChange={handleChildRouteChange}
                handleLogOut={() => dispatch(signOut())}
                handleParentRouteChange={handleParentRouteChange}
                parentActive={activeRoute}
                routes={_.cloneDeep(sideBarRoutes) || []}
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                handleClose={onSideBarClose}
                isCloseWhenClickOutside={true}
            />
        </div>
    );
}

export default SidebarComponent;
