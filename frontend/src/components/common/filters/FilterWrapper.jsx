import React, { useState, useEffect } from "react";
import { FiltersStrip } from "impact-ui";
import { useDispatch, useSelector } from "react-redux";
import _ from "lodash";
import moment from "moment";

import FilterGroup from "./FilterGroup";

import {
	getFilterOptions,
	setDateRangeToStore,
} from "../../../store/features/filters/filters";
import { global_labels } from "../../../constants/Constants";
import "./Filters.scss";
import { wholeProductFilterConfig, wholeStoreFilterConfig } from "../../../constants/FilterConfigConstants";

function FilterWrapper(props) {
	const {
		callAPIonLoad = false,
		filterConfig = [],
		showFiltersSection = true,
	} = props;
	const [filterGroupFlag, setFilterGroupFlag] = useState(false);
	const [filtersOnLoadFlag, setFiltersOnLoadFlag] = useState(false);
    const [selectedFilter, setSelectedFilter] = useState("Not selected");

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	const { hierachy_keys } = useSelector(
		(store) => store?.pricesmartPromoReducer.global
	);

	const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

	const dispatch = useDispatch();

	const { screen = "", requiredFiltersOnLoad = [] } = props;

	useEffect(() => {
		// if screen has on load API set, "if" statement will be called. This will be only called once.
		if (!filtersOnLoadFlag && callAPIonLoad) {
			let callAPIFlag = true;
			//checks if all required filters are selected, if not, callAPIFlag will be false.
			_.forEach(requiredFiltersOnLoad, (filterID) => {
				if (
					filterID !== "dateRange" &&
					!filtersData[screen]?.[filterID]?.selectedOptionsArray
						?.length
				) {
					callAPIFlag = false;
				} else if (filterID === "dateRange") {
					if (!filtersData[screen]?.[filterID]) callAPIFlag = false;
				}
			});
			//if callAPIFlag is true, call the screen's filterApply method.
			if (callAPIFlag) {
				props.onFilterApply();
				setFiltersOnLoadFlag(true);
			}
		}
	}, [filtersData[screen]]);

	useEffect(() => {
		// if screen has onLoad Filters, this useEffect will be called.
		//callAPIonLoad is passed from screen.
		if (callAPIonLoad) {
			//iterates through all the filter groups, and checks for filters which has "selectOnLoad" key as true
			_.forEach(filterConfig, (config) => {
				const group_ids = config?.groupConfig.map(item => item.filterId);
				_.forEach(config?.groupConfig, (group) => {
					if (group?.selectOnLoad) {
						//checks if filter is a dropdown, and if options are already available in store
						if (
							group?.filterType === "dropdown" &&
							!filtersData?.[screen]?.[group?.filterId]?.options
						) {
							//if options are not present, creates the payload based on apiEndpoint type, and calls the API
							let payload;
							if (group?.filterId === "event" || group?.filterId === "promo") {
								payload = {
									product_hierarchies: {},
									store_hierarchies: {},
									show_metrics: false
								};
								const product_hierarchy_keys = wholeProductFilterConfig.map(item => item.filterId);
								const store_hierarchy_keys = wholeStoreFilterConfig.map(item => item.filterId);
								Object.keys(filtersData[props?.screen]).forEach((key) => {
									if (
										key !== "dateRange" &&
										!_.isEmpty(filtersData[props?.screen][key]?.selectedOptionsArray)
									) {
										let arr =
											filtersData[props?.screen][key].selectedOptionsArray;
										if (product_hierarchy_keys.includes(key)) 
											payload["product_hierarchies"][key] = arr;
										else if (store_hierarchy_keys.includes(key))
											payload["store_hierarchies"][key] = arr;
										else if (key === "event")
											payload["event_ids"] = arr;
										else if (key === "promo")
											payload["promo_ids"] = arr;
										else
										 payload[key] = arr;
									} else if (key === "dateRange") {
										payload["start_date"] = moment(
											filtersData[props?.screen].dateRange.start_date
										).format("YYYY-MM-DD");
										payload["end_date"] = moment(
											filtersData[props?.screen].dateRange.end_date
										).format("YYYY-MM-DD");
									}
								});
							} else {
								payload = {
									hierarchy_filters: {},
									query_column: group.filterId,
								};
								for (let i = 0; i < group_ids.length; i++) {
									const key = group_ids[i];
									const data = filtersData?.[props?.screen]?.[key];
									if (i >= group_ids.indexOf(props.filterId)) 
										break;
									if (
										key !== "dateRange" 
										&& group_ids.includes(key)
										&& !_.isEmpty(data?.selectedOptionsArray)
									) {
										payload["hierarchy_filters"][key] = _.cloneDeep(
											data?.selectedOptionsArray
										);
									}
								}
							}

							if (!_.isEmpty(group?.extraParams)) {
								payload = {
									...payload,
									...group.extraParams,
								};
							}

							dispatch(
								getFilterOptions({
									requestObject: _.cloneDeep(payload),
									filterEndpoint: group?.apiEndpoint,
									from: screen || "",
									selectedItems: group?.selection || null,
									selectOnLoad: group?.selectOnLoad || false,
									filterName: group.filterId,
								})
							);
						}
						//if filters is a dropdown, sets the start and end date in redux store.
						if (group?.filterType === "dateRange") {
							const dateRange = {
								start_date: group?.start_date || null,
								end_date: group?.end_date || null,
							};
							dispatch(
								setDateRangeToStore({
									dateRange: _.cloneDeep(dateRange),
									from: screen || "",
									key: group.filterId,
								})
							);
						}
					}
				});
			});
		}
	}, []);

    // Transforms filterConfig array into tag objects for FiltersStrip component display
    const filterTags = filterConfig.flatMap((config) =>
        config.groupConfig
            .map((group) => {
                // Get filter data for current screen and filter ID from Redux store
                const filterData = filtersData?.[screen]?.[group.filterId];
                if (!filterData) return null;

                // Create base tag object with common properties
                const baseTag = {
                    id: group.filterId,
                    label: hierarchyGlobalKeys?.[group.filterId],
                    required: group.isMandatory,
                    handleViewAll: () => setFilterGroupFlag(true),
                };

                // For dropdown filters, map selected options to tag values
                if (
                    group.filterType === "dropdown" &&
                    filterData.selectedOptions?.length
                ) {
                    return {
                        ...baseTag,
                        values: filterData.selectedOptions.map((option) => ({
                            id: option.value,
                            label: option.label,
                        })),
                    };
                }

                // For date range filters, format start and end dates as a single tag value
                if (group.filterType === "dateRange" && filterData.start_date) {
                    const dateFormat = "MM-DD-YYYY";
                    return {
                        ...baseTag,
                        values: [
                            {
                                id: group.filterId,
                                label: `${moment(filterData.start_date).format(
                                    dateFormat
                                )} - ${moment(filterData.end_date).format(
                                    dateFormat
                                )}`,
                            },
                        ],
                    };
                }

                return null;
            })
            // Remove any null values from the mapped array
            .filter(Boolean)
    );

    return (
        <div>
			{showFiltersSection ? (
				<div className="marginBottom-16">
					<FiltersStrip
						filterButtonClick={() => setFilterGroupFlag(true)}
						filterButtonLabel="All Filters"
						filterTags={filterTags}
						handleApplyFilter={props.onFilterApply}
						handleCancelFilter={() => setFilterGroupFlag(false)}
						selectedFilter={selectedFilter}
						setSelectedFilter={setSelectedFilter}
						savedFilterLists={[
							{
								id: 1,
								label: "All",
							},
						]} // to be removed after the filter strip is updated
						savedFiltersBadge={[
							{
								id: 1,
								label: "All",
							},
						]} // to be removed after the filter strip is updated
						hideSelectedFilterBadge={true}
					/>
				</div>
			) : null}
			{filterGroupFlag ? (
				<FilterGroup
					{...props}
					closeFilterAction={setFilterGroupFlag}
					isOpen={filterGroupFlag}
				/>
			) : null}
		</div>
	);
}

export default FilterWrapper;
