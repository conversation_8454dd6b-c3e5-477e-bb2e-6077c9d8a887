import React, { useState, useEffect } from "react";
import { FilterPanel } from "impact-ui";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";

import FiltersSelect from "./FiltersSelect";
import FiltersDateRange from "./FiltersDateRange";

import ProductSelectionIcon from "../../../assets/imageAssets/productSelectionFilterIcon.svg?.url";
import StoreSelectionIcon from "../../../assets/imageAssets/storeSelectionFilterIcon.svg?.url";
import DateSelectionIcon from "../../../assets/imageAssets/dateSelectionFilterIcon.svg?.url";

import { toastError } from "../../../store/features/global/global";

import "./Filters.scss";

const filterIcons = {
	product_hierarchy: <img src={ProductSelectionIcon} />,
	store_hierarchy: <img src={StoreSelectionIcon} />,
	date_selection: <img src={DateSelectionIcon} />,
};

function FilterGroup(props) {
	const dispatch = useDispatch();
	const { filterConfig = [], defaultOpen } = props;
	const [panelFiltersData, setPanelFiltersData] = useState([]);
	const [activeGroup, setActiveGroup] = useState(defaultOpen);

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	useEffect(() => {
		//checks if filterConfig is present, and configures the children for each section. Sets the config to respective useState.
		//This configuration is according to Impact UI v3 FilterPanel
		if (filterConfig.length) {
			let panelFiltersData = [];
			_.forEach(filterConfig, (config) => {
				panelFiltersData.push({
					...config,
					icon: filterIcons[config?.value],
					children: filterSection(config?.groupConfig),
					numberOfFilter: 0,
				});
			});
			setPanelFiltersData(_.cloneDeep(panelFiltersData));
			setFilterGroupCount(panelFiltersData);
		}
	}, [filterConfig]);

	useEffect(() => {
		if (!panelFiltersData.length) return;
		setFilterGroupCount();
	}, [filtersData[props.screen]]);

	const setFilterGroupCount = (panelConfigData = panelFiltersData) => {
		//iterates through all the filters in a group, checks if filterType is dropdown or dateRange.

		const newPanelFiltersData = _.cloneDeep(panelConfigData);
		_.forEach(newPanelFiltersData, (panelFilter) => {
			let numberOfFilter = 0;
			_.forEach(panelFilter?.groupConfig, (child) => {
				//if selectedOptionsArray is present, increments the numberOfFilter.
				if (child?.filterType === "dropdown") {
					const { filterId } = child;
					const selectedOptions =
						filtersData[props.screen]?.[filterId]
							?.selectedOptionsArray;
					if (selectedOptions?.length) {
						numberOfFilter += 1;
					}
					//if dateRange is present, increments the numberOfFilter.
				} else if (
					child?.filterType === "dateRange" &&
					child?.start_date &&
					child?.end_date
				) {
					numberOfFilter += 1;
				}
			});
			//sets the numberOfFilter to the respective group.
			panelFilter.numberOfFilter = numberOfFilter;
		});
		setPanelFiltersData(_.cloneDeep(newPanelFiltersData));
	};

	const filterSection = (groupData) => {
		// iterates through all the filters in a group, checks if filterType is dropdown or dateRange.
		const sections = [[]];
		_.forEach(groupData, (data) => {
			const section = data.row || 0; 
			if (!sections[section]) {
				sections[section] = [];
			}
			if (data?.filterType === "dropdown") {
				sections[section]?.push(
					<FiltersSelect
						{...props}
						{...data}
						groupData={groupData}
					/>
				);
			}
			if (data?.filterType === "dateRange") {
				sections[section]?.push(
					<FiltersDateRange
						{...props}
						{...data} 
						groupData={groupData}
					/>
				);
			}
			if (data?.filterType === "custom") {
				sections[section]?.push(
					data.component || null
				)
			}
		})	
		return (
			<div>
				{sections.map((section, index) => (
					<div className="filters_section" key={`filter-section-${index}-${section.length}`}>
						{section}
					</div>
				))}
			</div>
		);
	};

	const onFilterApply = () => {
		const screenFilters = _.cloneDeep(filtersData?.[props.screen]);
		const { requiredFiltersOnLoad = [] } = props;
		let callAPI = true;
		//checks if all required filters are selected, if not, sets callAPI to false.
		if (requiredFiltersOnLoad.length) {
			_.forEach(requiredFiltersOnLoad, (filter) => {
				if (filter === "dateRange") {
					if (
						!screenFilters[filter]?.start_date ||
						!screenFilters[filter]?.end_date
					) {
						callAPI = false;
					}
				} else {
					if (!screenFilters[filter]?.selectedOptionsArray?.length) {
						callAPI = false;
					}
				}
			});
		}
		//if all required filters are selected, calls onFilterApply function.
		if (callAPI) {
			props.onFilterApply();
			props.closeFilterAction(false);
		} else {
			// else, show toast error.
			dispatch(toastError("Please select all required filters"));
		}
	};

	return panelFiltersData.length ? (
		<div className="filterGroup_wrapper">
			<FilterPanel
				active={activeGroup}
				anchor="right"
				isOpen={props.isOpen}
				className=""
				filters={_.cloneDeep(panelFiltersData)}
				handleClose={() => props.closeFilterAction(false)}
				onPrimaryButtonClick={onFilterApply}
				onSecondaryButtonClick={() => props.closeFilterAction(false)}
				primaryButtonLabel="Submit"
				secondaryButtonLabel="Cancel"
				setActive={setActiveGroup}
				size="large"
				title="All filters"
			/>
		</div>
	) : null;
}

export default FilterGroup;
