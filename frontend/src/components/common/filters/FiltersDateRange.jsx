import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import "react-dates/initialize";
import { DateRangePicker } from "impact-ui";
import _ from "lodash";

import {
	setDateRangeToStore,
	resetFiltersDataForId,
} from "../../../store/features/filters/filters";

function FiltersDateRange(props) {
	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);
	const [startDate, setStartDate] = useState(
		filtersData?.[props?.screen]?.[props?.filterId]?.start_date || null
	);
	const [endDate, setEndDate] = useState(
		filtersData?.[props?.screen]?.[props?.filterId]?.end_date || null
	);

	const dispatch = useDispatch();

	const saveSelectedDateRange = () => {
		//create payload for dateRange and set it to redux store
		const dateRange = {
			start_date: moment(startDate),
			end_date: moment(endDate),
		};
		dispatch(
			setDateRangeToStore({
				dateRange: _.cloneDeep(dateRange),
				from: props?.screen || "",
				key: props?.filterId,
			})
		);
		//in current group, find the index of current filter.
		const filterIndex = _.findIndex(
			props.groupData,
			(data) => data.filterId === props.filterId
		);
		//to check if any filter(s) are there, which should reset by changing any filter from any group.
		let deleteDataIds = [];
		//to get all filter(s) id in a group, which are after current filter.
		for (let i = filterIndex + 1; i < props.groupData.length; i++) {
			const { resetData = true } = props.groupData[i];
			if (resetData) {
				deleteDataIds.push(props.groupData[i].filterId);
			}
		}		
		_.forEach(props?.filterConfig, (config) => {
			_.forEach(config?.groupConfig, (group) => {
				if (
					"allMandatoryFiltersRequired" in group &&
					group?.allMandatoryFiltersRequired &&
					group.filterId !== props.filterId
				) {
					deleteDataIds.push(group.filterId);
				}
			});
		});
		// if there are id(s) present in deleteDataIds send it to store. Store has functionality written to clear out
		// all filters data whose id will be present in deleteDataIds
		if (deleteDataIds.length) {
		}
		dispatch(
			resetFiltersDataForId({
				data: _.cloneDeep(deleteDataIds),
				from: props?.screen,
			})
		);
	};

	const onCancelClick = () => {
		console.log("cancel called");
		setStartDate(
			filtersData?.[props?.screen]?.[props?.filterId]?.start_date || null
		);
		setEndDate(
			filtersData?.[props?.screen]?.[props?.filterId]?.end_date || null
		);
	};

	const resetDateRange = () => {
		console.log("reset click");
		setStartDate(null);
		setEndDate(null);
	};

	const startDateHandler = (date) => {
		console.log("start date", date);
		setStartDate(date);
	};

	const endDateHandler = (date) => {
		console.log("end date", date);
		setEndDate(date);
	};

	return (
		<DateRangePicker
			label={props?.filterName || "Date Range"}
			isRequired={props?.isMandatory || false}
			showRangeSelector={false}
			startDate={startDate}
			setStartDate={startDateHandler}
			endDate={endDate}
			setEndDate={endDateHandler}
			startDateInputProps={{
				label: "StartDate",
				name: "start_date",
			}}
			endDateInputProps={{
				label: "EndDate",
				name: "end_date",
			}}
			minDate={props?.minDate || null}
			maxDate={props?.maxDate || null}
			onPrimaryButtonClick={saveSelectedDateRange}
			onSecondaryButtonClick={onCancelClick}
			onResetClick={resetDateRange}
			displayFormat="MM-DD-YYYY"
		/>
	);
}

export default FiltersDateRange;
