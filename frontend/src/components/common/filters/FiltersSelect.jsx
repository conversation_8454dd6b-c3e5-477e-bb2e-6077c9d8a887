import React, { useState, useEffect } from "react";
import { Select } from "impact-ui";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";

import {
	setSelectedFilters,
	getFilterOptions,
	resetFiltersDataForId,
	getCompletedOffersFilters,
} from "../../../store/features/filters/filters";

import {
	checkMandatoryFiltersSelection,
	generatePayloadForModelFilters,
} from "./filtersHelper";

import {
	global_labels,
	DEFAULT_DATE_FORMAT,
} from "../../../constants/Constants";
import moment from "moment";
import { wholeProductFilterConfig, wholeStoreFilterConfig } from "../../../constants/FilterConfigConstants";
import { capitalizeFirstLetter, fabricatePayloadHierarchy } from "../../../utils/helpers/utility_helpers";

function FiltersSelect(props) {
	const dispatch = useDispatch();
	const [currentOptions, setCurrentOptions] = useState([]);
	const [isOpen, setIsOpen] = useState(false);
	const [isSelectAll, setIsSelectAll] = useState(false);
	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);
	const { hierachy_keys } = useSelector(
		(store) => store?.pricesmartPromoReducer.global
	);

	useEffect(() => {
		// Ensure completedOffers starts with empty options until API populates it
		if (
			(!filtersData?.[props?.screen]?.[props.filterId]?.options ||
				filtersData[props?.screen][props.filterId]?.options.length ===
					0)
		) {
			setCurrentOptions([]); // Set empty initially for completedOffers
		} else if (filtersData?.[props?.screen]?.[props.filterId]?.options) {
			// Re-order the options so that selected filters appear on top
			let reOrderedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.options || []
			);
			let selectedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.selectedOptionsArray
			);

			if (
				!_.isEmpty(selectedOptions) &&
				selectedOptions.length !== reOrderedOptions.length
			) {
				reOrderedOptions.sort((a, b) => {
					if (
						selectedOptions.includes(a.value) &&
						!selectedOptions.includes(b.value)
					) {
						return -1; // Keep `a` before `b`
					} else if (
						!selectedOptions.includes(a.value) &&
						selectedOptions.includes(b.value)
					) {
						return 1; // Keep `b` before `a`
					} else {
						// Secondary condition: sort by `label`
						return a.label - b.label;
					}
				});
			}
			setCurrentOptions(reOrderedOptions);
		}
	}, [filtersData?.[props?.screen]?.[props.filterId]?.options]);

	useEffect(() => {
		//to set the select all checkbox checked or unchecked based on selected options.
		if (
			filtersData?.[props?.screen]?.[props.filterId]?.selectedOptions
				?.length ===
			filtersData?.[props?.screen]?.[props.filterId]?.options?.length
		) {
			setIsSelectAll(true);
		} else {
			setIsSelectAll(false);
		}
	}, [filtersData?.[props?.screen]?.[props.filterId]?.selectedOptions]);

	const onSelectionChange = (data) => {
		//sets selected options to redux state
		dispatch(
			setSelectedFilters({
				data,
				from: props?.screen,
				filterId: props.filterId,
			})
		);
		//in current group, find the index of current filter.
		const filterIndex = _.findIndex(
			props.groupData,
			(data) => data.filterId === props.filterId
		);
		if (filterIndex < 0) return;
		let deleteDataIds = [];
		//to get all filter(s) id in a group, which are after current filter.
		for (let i = filterIndex + 1; i < props.groupData.length; i++) {
			const { resetData = true } = props.groupData[i];
			if (resetData) {
				deleteDataIds.push(props.groupData[i].filterId);
			}
		}
		//to check if any filter(s) are there, which should reset by changing any filter from any group.
		_.forEach(props?.filterConfig, (config) => {
			_.forEach(config?.groupConfig, (group) => {
				if (
					"allMandatoryFiltersRequired" in group &&
					group?.allMandatoryFiltersRequired &&
					group.filterId !== props.filterId
				) {
					deleteDataIds.push(group.filterId);
				}
			});
		});
		// find the unique of all ids, and send it to store. Store has functionality written to clear out
		// all filters data whose id will be present in deleteDataIds
		deleteDataIds = _.uniq(deleteDataIds);
		dispatch(
			resetFiltersDataForId({
				data: _.cloneDeep(deleteDataIds),
				from: props?.screen,
			})
		);
	};

	const onDropdownOpen = () => {
		//if filterId is completedOffers, call the API for completed offers filters.
		if (props.filterId === "completedOffers") {
			dispatch(
				getCompletedOffersFilters({
					req: generateCompletedOffersPayload(),
					filterName: "completedOffers",
					from: props.screen,
				})
			);
			return;
		}

		//when you open any dropdown, if options are present in store, do nothing.
		if (filtersData[props?.screen]?.[props.filterId]?.options?.length) {
			// Re-order the options so that selected filters appear on top
			let reOrderedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.options
			);
			let selectedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.selectedOptionsArray
			);

			if (
				!_.isEmpty(selectedOptions) &&
				selectedOptions.length !== reOrderedOptions.length
			) {
				reOrderedOptions.sort((a, b) => {
					if (
						selectedOptions.includes(a.value) &&
						!selectedOptions.includes(b.value)
					) {
						return -1; // Keep `a` before `b`
					} else if (
						!selectedOptions.includes(a.value) &&
						selectedOptions.includes(b.value)
					) {
						return 1; // Keep `b` before `a`
					} else {
						// Secondary condition: sort by `label`
						return a.label - b.label;
					}
				});
			}
			setCurrentOptions(reOrderedOptions);
			return;
		}

		//in current group, find the index of current filter.
		const filterIndex = _.findIndex(
			props.groupData,
			(data) => data.filterId === props.filterId
		);
		if (filterIndex > -1) {
			//if current filter is the first filter in the group, no need for any validations. Call the API.
			if (filterIndex === 0) {
				callFiltersOptionAPI();
			} else {
				//if current filter is not the first filter in the group,
				//check if current filter is mandatory filter
				const filterConfig = props.groupData[filterIndex];
				//if current filter is mandatory, check if previous filter has selected items, if yes, call the API.
				if (filterConfig.isMandatory) {
					const prevFilterId =
						props.groupData[filterIndex - 1].filterId;
					if (
						filtersData[props?.screen][prevFilterId]
							?.selectedOptions?.length || prevFilterId === "dateRange"
					) {
						callFiltersOptionAPI();
					}
				} else {
					//if current filter is not mandatory, iterate through current group.
					let callAPIFlag = true;
					_.forEach(props.groupData, (data) => {
						//if there is mandatory filter which does not have selected options, set callAPIFlag as false
						if (
							data?.isMandatory &&
							data?.filterId !== "dateRange"
						) {
							if (
								!filtersData[props?.screen][data?.filterId]
									?.selectedOptions?.length
							) {
								callAPIFlag = false;
							}
						}
						if (
							data?.filterId == "dateRange" &&
							(!filtersData[props?.screen][data?.filterId]
								.start_date ||
								!filtersData[props?.screen][data?.filterId]
									.end_date)
						) {
							callAPIFlag = false;
						}
					});
					//if callAPIFlag is true, call the API.
					if (callAPIFlag) callFiltersOptionAPI();
				}
			}
		}
	};

	const callFiltersOptionAPI = () => {
		let payload = {
			allow_only_active_products: true,
			application: "promo",
			filters: {
				product_hierarchy: {},
				store_hierarchy: {},
			},
			hierarchy_type: props.filterId,
			// screen_name: props?.screen || "",
		};
		//in current group, find the index of current filter.
		const filterIndex = _.findIndex(
			props.groupData,
			(data) => data.filterId === props.filterId
		);

		//fetch current filter config, and check apiEndpoint.
		const group = props.groupData[filterIndex];
		if (group?.filterId === "event" || group?.filterId === "promo") {
			payload = {
				product_hierarchies: {},
				store_hierarchies: {},
				show_metrics: false
			};
			const product_hierarchy_keys = wholeProductFilterConfig.map(item => item.filterId);
			const store_hierarchy_keys = wholeStoreFilterConfig.map(item => item.filterId);
			Object.keys(filtersData[props?.screen]).forEach((key) => {
				if (
					key !== "dateRange" &&
					!_.isEmpty(filtersData[props?.screen][key]?.selectedOptionsArray)
				) {
					let arr =
						filtersData[props?.screen][key].selectedOptionsArray;
					if (product_hierarchy_keys.includes(key)) 
						payload["product_hierarchies"][key] = arr;
					else if (store_hierarchy_keys.includes(key))
						payload["store_hierarchies"][key] = arr;
					else if (key === "event")
						payload["event_ids"] = arr;
					else if (key === "promo")
						payload["promo_ids"] = arr;
					else
					 payload[key] = arr;
				} else if (key === "dateRange") {
					payload["start_date"] = moment(
						filtersData[props?.screen].dateRange.start_date
					).format("YYYY-MM-DD");
					payload["end_date"] = moment(
						filtersData[props?.screen].dateRange.end_date
					).format("YYYY-MM-DD");
				}
			});
		} else {
			const group_ids = props.groupData.map(item => item.filterId);
			payload = {
				hierarchy_filters: {},
				query_column: props.filterId,
			}
			for (let i = 0; i < group_ids.length; i++) {
				const key = group_ids[i];
				const data = filtersData?.[props?.screen]?.[key];
				if (i >= group_ids.indexOf(props.filterId)) 
					break;
				if (
					key !== "dateRange" 
					&& group_ids.includes(key)
					&& !_.isEmpty(data?.selectedOptionsArray)
				) {
					payload["hierarchy_filters"][key] = _.cloneDeep(
						data?.selectedOptionsArray
					);
				}
			}
		}

		if (!_.isEmpty(group?.extraParams)) {
			payload = {
				...payload,
				...group.extraParams,
			};
		}

		// if selecteditems present for the filter, then add it to the payload
		const selectedItems = filtersData?.[props?.screen]?.[props.filterId]?.selectedOptionsArray || [];

		// call the API and set the data.
		dispatch(
			getFilterOptions({
				requestObject: _.cloneDeep(payload),
				filterEndpoint: group?.apiEndpoint,
				from: props?.screen || "",
				selectedItems: selectedItems || [],
				selectOnLoad: !!selectedItems?.length,
				filterName: group?.filterId,
			})
		);
	};

	//generate payload for completed offers filters API call.
	const generateCompletedOffersPayload = () => {
		let completedOffersFilters = {};
		Object.keys(filtersData[props?.screen]).forEach((key) => {
			if (key === "event") {
				completedOffersFilters["event_ids"] = _.cloneDeep(
					filtersData[props?.screen]?.[key]?.selectedOptionsArray
				);
			} else if (key === "promo") {
				completedOffersFilters["promo_ids"] = _.cloneDeep(
					filtersData[props?.screen]?.[key]?.selectedOptionsArray
				);
			}
		});
		if (filtersData[props?.screen]?.dateRange) {
			completedOffersFilters["start_date"] = moment(
				filtersData[props?.screen]?.dateRange?.start_date
			).format(DEFAULT_DATE_FORMAT);
			completedOffersFilters["end_date"] = moment(
				filtersData[props?.screen]?.dateRange?.end_date
			).format(DEFAULT_DATE_FORMAT);
		}

		completedOffersFilters = {
			...completedOffersFilters,
			...fabricatePayloadHierarchy(filtersData[props?.screen])
		}

		return completedOffersFilters;
	};

	const onClearAll = () => {
		onSelectionChange([]);
	};

	const onSelectAllHandler = (e) => {
		//if select all is checked, set all options as selected, else clear all selected options.
		if (e.target.checked) {
			onSelectionChange(
				filtersData?.[props?.screen]?.[props.filterId]?.options
			);
		} else {
			onSelectionChange([]);
		}
	};

	const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

	return (
		<Select
			currentOptions={_.cloneDeep(currentOptions)}
			initialOptions={_.cloneDeep(
				filtersData?.[props?.screen]?.[props.filterId]?.options || []
			)}
			label={capitalizeFirstLetter(hierarchyGlobalKeys?.[props?.filterId]) || ""}
			labelOrientation="top"
			name={capitalizeFirstLetter(hierarchyGlobalKeys?.[props?.filterId]) || ""}
			setSelectedOptions={onSelectionChange}
			onClearAll={onClearAll}
			onDropdownOpen={onDropdownOpen}
			onSelectAll={onSelectAllHandler}
			placeholder="Select.."
			isWithSearch={true}
			isRequired={props?.isMandatory || false}
			isSelectAll={isSelectAll}
			toggleSelectAll={props?.isMulti || false}
			isClearable={true}
			isMulti={props?.isMulti || false}
			selectedOptions={
				filtersData?.[props?.screen]?.[props.filterId]
					?.selectedOptions || []
			}
			setCurrentOptions={setCurrentOptions}
			setIsSelectAll={setIsSelectAll}
			isOpen={isOpen}
			setIsOpen={setIsOpen}
			isCloseWhenClickOutside={true}
		/>
	);
}

export default FiltersSelect;
