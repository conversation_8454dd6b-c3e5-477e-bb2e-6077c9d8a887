.filter_wrapper {
	background-color: #ffffff;
	padding: 8px 24px;
	display: flex;
	align-items: center;
	margin-bottom: 24px;
}

.filterGroup_wrapper {
	height: 100vh;
	width: calc(100vw - 60px);
	position: fixed;
	top: 0;
	right: 0;
}

.filters_section {
	display: flex;
	gap: 24px;
	flex-wrap: wrap;
	padding: 8px;
}

.filter_button_container {
	padding-left: 12px;
	min-width: 120px;
}

.filter_horizontal_line {
	height: 16px;
	border-left: 1px solid #d9dde7;
}

.filters_tags_container {
	height: inherit;
	width: calc(100% - 116px);
	padding-right: 12px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
}

.filtersTagsDataContainer {
	display: flex;
	align-items: center;
	gap: 12px;
	width: calc(100% - 56px);
}

.filterTagContainer {
	background: #ffffff;
	display: flex;
	align-items: center;
	gap: 4px;

	.impact-tag {
		border: none;
		border-radius: 8px;
		overflow: hidden;
		color: #1F2B4D;
		background: #ECEEFD;
		&:hover {
			background: rgba(0, 0, 0, 0.04);
			border: none;
			box-shadow: none;
		}
	}
}
.filterTags {
	display: flex;
	gap: 12px;
	max-width: 100%;
	overflow-x: scroll;
	scroll-behavior: smooth;
	scrollbar-width: none;
	cursor: all-scroll;
}

.filter-main-label {
	color: #60697D
}

.filterTags > .filterTagContainer:first-child > .filter_horizontal_line {
	display: none;
}

.filterTags::-webkit-scrollbar {
	display: none;
}

.filterTagsSlideButtonsContainer {
	display: flex;
	position: relative;
	gap: 4px;
}

.filterTagsSlideButton {
	background-color: #f5f6fa;
	padding: 4px 6px;
	cursor: pointer;
	border-radius: 4px 0 0 4px;
}

.rightChiveron {
	rotate: -180deg;
	position: relative;
	width: 17px;
	img {
		bottom: 8px;
		left: 5px;
		position: absolute;
	}
}
