export const tableDataFormatter = (data = []) => {
	let formattedData = [];
	data.forEach((item) => {
		let formattedItem = {
			...item,
			actualized_st_percent: item?.actualized?.actualized_st_percent,

			// Finalized
			finalized_margin_percent: item?.finalized?.margin_percent,
			finalized_contribution_margin: item?.finalized?.contribution_margin,
			finalized_contribution_margin_percent: item?.finalized?.contribution_margin_percent,
			finalized_sales_units: item?.finalized?.sales_units,
			finalized_revenue: item?.finalized?.revenue,
			finalized_margin: item?.finalized?.margin,
			finalized_promo_spend: item?.finalized?.promo_spend,
			finalized_st_percent: item?.finalized?.finalized_st_percent,
			finalized_inventory: item?.finalized?.total_inventory,
			//Baseline
			baseline_sales_units: item?.baseline?.sales_units,
			baseline_margin: item?.baseline?.margin,
			baseline_revenue: item?.baseline?.revenue,

			// Incremental
			incremental_sales_units: item?.incremental?.sales_units,
			incremental_revenue: item?.incremental?.revenue,
			incremental_margin: item?.incremental?.margin,
 
			// for Original selection
			original_sales_units: item?.original?.sales_units,
			original_revenue: item?.original?.revenue,
			original_margin: item?.original?.margin,
			original_margin_percent: item?.original?.margin_percent,
			original_contribution_margin: item?.original?.contribution_margin,
			original_contribution_margin_percent: item?.original?.contribution_margin_percent,

			//  Stacked
			// --- Finalized Stack
			finalized_stack_sales_units: item?.finalized_stack?.sales_units,
			finalized_stack_revenue: item?.finalized_stack?.revenue,
			finalized_stack_margin: item?.finalized_stack?.margin,
			finalized_stack_margin_percent: item?.finalized_stack?.margin_percent,
			finalized_stack_contribution_margin_percent: item?.finalized_stack?.contribution_margin_percent,
			finalized_stack_contribution_margin: item?.finalized_stack?.contribution_margin,
			finalized_stack_st_percent: item?.finalized_stack?.finalized_stack_st_percent,
			// --- Baseline Stack
			baseline_stack_sales_units: item?.baseline_stack?.sales_units,
			baseline_stack_revenue: item?.baseline_stack?.revenue,
			baseline_stack_margin: item?.baseline_stack?.margin,
			// --- Original Stack
			original_stack_sales_units: item?.original_stack?.sales_units,
			original_stack_revenue: item?.original_stack?.revenue,
			original_stack_margin: item?.original_stack?.margin,
			original_stack_margin_percent: item?.original_stack?.margin_percent,
			original_stack_contribution_margin_percent: item?.original_stack?.contribution_margin_percent,
			original_stack_contribution_margin: item?.original_stack?.contribution_margin,

		};
		formattedData.push(formattedItem);
	});
	return formattedData;
}