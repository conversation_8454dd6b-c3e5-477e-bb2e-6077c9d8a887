import moment from "moment";
import {
	capitalizeFirstLetter,
	replaceS<PERSON>ialCharacter,
	replaceSpecialCharToCharCode,
} from "../../../utils/helpers/utility_helpers";
import {
	yyyyMmDdFormatter,
	toUnit,
	toPercentage,
	toCurrencyByCurrencyId,
} from "../../../utils/helpers/formatter";

import FinalizedSalesUnitsIcon from "../../../assets/imageAssets/finSalesUnits.png";
import IncSalesUnitsIcon from "../../../assets/imageAssets/incSalesUnits.png";
import BaselineSalesUnitsIcon from "../../../assets/imageAssets/baselineSalesU.png";
import FinalRevenueIcon from "../../../assets/imageAssets/finalRevenue.png";
import BaseRevenueIcon from "../../../assets/imageAssets/baseRevenue.png";
import FinalizedGMIcon from "../../../assets/imageAssets/finGMDollar.png";
import BaselineGMIcon from "../../../assets/imageAssets/baselineGMDollar.png";
import IncrementalGMIcon from "../../../assets/imageAssets/incGMDollar.png";
import FinalizedGMPercentIcon from "../../../assets/imageAssets/finGMPercent.png";
import FinalizedCMDollarIcon from "../../../assets/imageAssets/finCMDollar.png";
import FinalizedCMPercentIcon from "../../../assets/imageAssets/finCMPercent.png";

import {
	OfferNameCellRenderer,
	OfferStatusCellRenderer,
	TableWithInputBoxOption,
	cellsWithBadge,
	copyDateRangeCellRenderer,
	eventSelectCellRenderer,
	CellWithWarningIcon
} from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { containerStore } from "../../../store";
import {
	workbenchInlineEditDataSave,
	workbenchCopyIAEditDataSave,
} from "../../../store/features/workbenchReducer/workbenchReducer";
import { global_labels, HYPERLINK_PRODUCT_TABLE_CONFIG } from "../../../constants/Constants";
import { handleOfferValidateAndEdit } from "../offer/OfferHelper";

export const workbenchTilesConfig = [
	{
		label: "Finalized Sales Units",
		key: "sales_units_finalized",
		formatter: "toUnit",
		icon: FinalizedSalesUnitsIcon,
	},
	{
		label: "Baseline Sales Units",
		key: "sales_units_baseline",
		formatter: "toUnit",
		icon: BaselineSalesUnitsIcon,
	},
	{
		label: "Incremental Sales Units",
		key: "sales_units_incremental",
		formatter: "toUnit",
		icon: IncSalesUnitsIcon,
	},
	{
		label: "Finalized Revenue",
		key: "revenue_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalRevenueIcon,
	},
	{
		label: "Baseline Revenue",
		key: "revenue_baseline",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: BaseRevenueIcon,
	},
	{
		label: "Incremental Revenue",
		key: "revenue_incremental",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalRevenueIcon,
	},
	{
		label: "Finalized GM",
		key: "gross_margin_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalizedGMIcon,
	},
	{
		label: "Baseline GM",
		key: "gross_margin_baseline",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: BaselineGMIcon,
	},
	{
		label: "Incremental GM",
		key: "gross_margin_incremental",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: IncrementalGMIcon,
	},
	{
		label: "Finalized GM %",
		key: "gross_margin_percent_finalized",
		formatter: "toUnit",
		icon: FinalizedGMPercentIcon,
	},
	{
		label: "Finalized CM $",
		key: "contribution_gross_margin_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalizedCMDollarIcon,
	},
	{
		label: "Finalized CM %",
		key: "contribution_gross_margin_percent_finalized",
		formatter: "toUnit",
		icon: FinalizedCMPercentIcon,
	},
];

export const workbenchTableConfig = [
	{
		field: "",
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true,
		suppressMenu: true,
		filter: false,
		sortable: false,
		pinned: "left",
		maxWidth: 60,
		colId: "checkbox",
		hide: false
	},
	{
		field: "promo_name",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Name`,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
		cellRenderer: OfferNameCellRenderer,
		width: 360,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRendererParams: {
			enableInput: {
				screen: "workbench",
				enableKey: "enableWorkbenchInlineEdit",
				storeDataKey: "workbenchSelectedOffers",
				uniqueKey: "promo_id",
			},
			defaultFlag: false,
			onDataUpdate: (data) =>
				containerStore.dispatch(workbenchInlineEditDataSave(data)),
			onClick: async (data) => {
				handleOfferValidateAndEdit(data);
			}
		},
		hide: false
	},
	{
		field: "event_name",
		headerName: global_labels.event_primary + " Name",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => {
			if (params.value) {
				return replaceSpecialCharacter(params.value)
			} else {
				return "-"
			}
		},
		hide: false
	},
	{
		field: "start_date",
		headerName: "Start Date",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
		hide: false
	},
	{
		field: "end_date",
		headerName: "End Date",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
		hide: false
	},
	{
		field: "created_by",
		headerName: "Created By",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
		hide: false
	},
	{
		field: "status",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Status`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 180,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
		cellRenderer: OfferStatusCellRenderer,
		hide: false
	},
	{
		field: "offer_comment",
		headerName: "Comments",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: TableWithInputBoxOption,
		cellRendererParams: {
			enableInput: {
				screen: "workbench",
				enableKey: "enableWorkbenchInlineEdit",
				storeDataKey: "workbenchSelectedOffers",
				uniqueKey: "promo_id",
			},
			defaultFlag: false,
			onDataUpdate: (data) =>
				containerStore.dispatch(workbenchInlineEditDataSave(data)),
		},
		hide: false
	},
	{
		field: "products_count",
		headerName: "Products",
		minWidth: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: false,
		type: "number",
	},
	{
		field: "stores_count",
		headerName: "Stores",
		minWidth: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: false,
		type: "number",
	},
	{
		field: "performance",
		headerName: "Performance",
		width: 160,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
		hide: false
	},
	{
		field: "discount_type",
		headerName: "Discount Type",
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: false
	},
	{
		field: "discount_level",
		headerName: "Discount Level - Products",
		width: 182,
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: false
	},
	{
		field: "markdown_budget",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} $`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 140,
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_margin_percent",
		headerName: "Finalized GM %",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_stack_margin_percent",
		headerName: "Finalized GM %",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		hide: true,
		type: "number",
	},
	{
		field: "original_margin_percent",
		headerName: "Original GM %",
		width: 176,
		hide: true,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		type: "number",
	},
	{
		field: "original_stack_margin_percent",
		headerName: "Original GM %",
		width: 176,
		hide: true,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		type: "number",
	},
	{
		field: "finalized_inventory",
		headerName: "Inventory",
		width: 190,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		type: "number",
	},
	{
		field: "finalized_st_percent",
		headerName: "Finalized ST %",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_stack_st_percent",
		headerName: "Finalized ST %",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		hide: true,
		type: "number",
	},
	{
		field: "finalized_contribution_margin",
		headerName: "Finalized CM $",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_stack_contribution_margin",
		headerName: "Finalized CM $",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "original_contribution_margin",
		headerName: "Original CM $",
		width: 176,
		isSearchable: true,
		hide: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		type: "number",
	},
	{
		field: "original_stack_contribution_margin",
		headerName: "Original CM $",
		width: 176,
		isSearchable: true,
		hide: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		type: "number",
	},
	{
		field: "finalized_contribution_margin_percent",
		headerName: "Finalized CM %",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_stack_contribution_margin_percent",
		headerName: "Finalized CM %",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		hide: true,
		type: "number",
	},
	{
		field: "original_contribution_margin_percent",
		headerName: "Original CM %",
		width: 176,
		isSearchable: true,
		hide: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		type: "number",
	},
	{
		field: "original_stack_contribution_margin_percent",
		headerName: "Original CM %",
		width: 176,
		isSearchable: true,
		hide: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		type: "number",
	},
	{
		field: "finalized_sales_units",
		headerName: "Finalized Sales Units",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_stack_sales_units",
		headerName: "Finalized Sales Units",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		hide: true,
		type: "number",
	},
	{
		field: "original_sales_units",
		headerName: "Original Sales Units",
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: true,
		valueFormatter: (params) => toUnit(params),
		type: "number",
	},
	{
		field: "original_stack_sales_units",
		headerName: "Original Sales Units",
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: true,
		valueFormatter: (params) => toUnit(params),
		type: "number",
	},
	{
		field: "baseline_sales_units",
		headerName: "Baseline Sales Units",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		hide: false,
		type: "number",
	},
	{
		field: "baseline_stack_sales_units",
		headerName: "Original Sales Units",
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: true,
		valueFormatter: (params) => toUnit(params),
		type: "number",
	},
	{
		field: "target_sales_units",
		headerName: "Target Sales Units",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_revenue",
		headerName: "Finalized Revenue",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_stack_revenue",
		headerName: "Finalized Revenue",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "original_revenue",
		headerName: "Original Revenue",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "original_stack_revenue",
		headerName: "Original Revenue",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "baseline_revenue",
		headerName: "Baseline Revenue",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "baseline_stack_revenue",
		headerName: "Original Revenue",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "target_revenue",
		headerName: "Target Revenue",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_margin",
		headerName: "Finalized Margin",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "finalized_stack_margin",
		headerName: "Finalized Margin",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "original_margin",
		headerName: "Original Margin",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "original_stack_margin",
		headerName: "Original Margin",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "baseline_margin",
		headerName: "Baseline Margin",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "baseline_stack_margin",
		headerName: "Original Margin",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: true,
		type: "number",
	},
	{
		field: "target_margin",
		headerName: "Target Margin",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		hide: false,
		type: "number",
	},
	{
		field: "product_selection_type",
		headerName: "Product Selection Type",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 195,
		hide: false
	},
	{
		field: "customer_type",
		headerName: "Customer Type",
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: false
	},
	{
		field: "offer_distribution_channel",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Distribution Channel`,
		width: 215,
		isSearchable: true,
		filter: "agTextColumnFilter",
		hide: false
	},
];

export const finalizeOverlapTableConfig = [
	{
		field: "source_promo_name",
		headerName: `Source ${capitalizeFirstLetter(global_labels?.promo_primary)}`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "conflicted_promo_name",
		headerName: `Conflicted ${capitalizeFirstLetter(global_labels?.promo_primary)}`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "conflicted_promo_products_count",
		headerName: `Conflicted ${capitalizeFirstLetter(global_labels?.promo_primary)} Product Count`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		width: 280,
		type: "number",
	},
	{
		field: "conflicted_promo_status",
		headerName: `Conflicted ${capitalizeFirstLetter(global_labels?.promo_primary)} Status`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
		width: 250,
	},
	{
		field: "conflicted_promo_start_date",
		headerName: `Conflicted ${capitalizeFirstLetter(global_labels?.promo_primary)} Start Date`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
		width: 260,
	},
	{
		field: "conflicted_promo_end_date",
		headerName: `Conflicted ${capitalizeFirstLetter(global_labels?.promo_primary)} End Date`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
		width: 260,
	},
];

export const copyIATableConfig = [
	{
		field: "",
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true,
		suppressMenu: true,
		filter: false,
		sortable: false,
		pinned: "left",
		maxWidth: 60,
	},
	{
		field: "promo_name",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Name`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: CellWithWarningIcon,
		cellRendererParams: {
			flag: "showWarning"
		}
	},
	{
		field: "date_range",
		headerName: "Date",
		isSearchable: true,
		filter: "agTextColumnFilter",
	},
	{
		field: "new_promo_name",
		headerName: `New ${capitalizeFirstLetter(global_labels?.promo_alias)} Name`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: TableWithInputBoxOption,
		width: 300,
		cellRendererParams: {
			enableInput: {
				screen: "workbench",
				storeDataKey: "copyOfferTableData",
				uniqueKey: "promo_id",
			},
			defaultFlag: true,
			onDataUpdate: (data) =>
				containerStore.dispatch(
					workbenchCopyIAEditDataSave({
						...data,
						data: {
							new_promo_name: replaceSpecialCharToCharCode(data.data),
						},
					})
				),
		},
	},
	{
		field: "event_name",
		headerName: `${capitalizeFirstLetter(global_labels?.event_primary)}`,
		isSearchable: true,
		colId: "event_name",
		filter: "agTextColumnFilter",
		width: 300,
		cellRenderer: eventSelectCellRenderer,
		hide: false
	},
	{
		field: "modified_date_range",
		headerName: "Modified date range",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: copyDateRangeCellRenderer,
		width: 300,
	},
];

export const viewColsOptions = [
	{ value: "stacked", label: "Stacked" },
	{ value: "original", label: "Original #" },
];

export const productTableConfig = (hierarchyGlobalKeys) => ([
    {
        field: "client_product_id",
        headerName: "Product ID",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "product_name",
        headerName: "Description",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 300,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    // {
    //     field: "msrp",
    //     headerName: "MSRP",
    //     isSearchable: true,
    //     filter: "agTextColumnFilter",
    //     width: 150,
    //     valueFormatter: toCurrencyByCurrencyId,
    //     type: "number",
    // },
    {
        field: "current_price",
        headerName: "Current Price",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
        type: "number",
    },
	{
		field: "cost",
		headerName: "Cost",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 140,
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		type: "number",
	},
    {
        field: "total_inventory",
        headerName: "Inventory",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
    },
    // {
    //     field: "launch_price",
    //     headerName: "Launch Price",
    //     isSearchable: true,
    //     filter: "agTextColumnFilter",
    //     flex: 1,
    //     valueFormatter: toCurrencyByCurrencyId,
    //     type: "number",
    // },
	...HYPERLINK_PRODUCT_TABLE_CONFIG(hierarchyGlobalKeys),
	{
		headerName: "Finalized",
		colId: "finalized",
		children: [
			{
				field: "finalized_revenue",
				headerName: "Revenue",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_margin",
				headerName: "Margin",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_units",
				headerName: "Units",
				type: "number",
			},
			{
				field: "finalized_gm_percent",
				headerName: "GM %",
				valueFormatter: toPercentage,
				type: "number",
			},
		],
	},
	{
		headerName: "Actual",
		colId: "actual",
		children: [
			{
				field: "actual_revenue",
				headerName: "Revenue",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actual_margin",
				headerName: "Margin",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actual_units",
				headerName: "Units",
				type: "number",
			},
			{
				field: "actual_gm_percent",
				headerName: "GM %",
				valueFormatter: toPercentage,
				type: "number",
			},
		]
	},
])

export const storeTableConfig = (hierarchyGlobalKeys) => ([
	{
		field: "store_id",
		headerName: "Store ID",
		valueFormatter: (params) => parseInt(params.value),
	},
	{
		field: "store_name",
		headerName: "Store Name",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s0_name",
		headerName: hierarchyGlobalKeys?.s0_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s1_name",
		headerName: hierarchyGlobalKeys?.s1_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s2_name",
		headerName: hierarchyGlobalKeys?.s2_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s3_name",
		headerName: hierarchyGlobalKeys?.s3_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s4_name",
		headerName: hierarchyGlobalKeys?.s4_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	// {
	// 	field: "s5_name",
	// 	headerName: "City",
	// 	valueFormatter: (params) => replaceSpecialCharacter(params.value),
	// },
])