import React, { useState, useCallback, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";
import { Input, Button, Select, Modal, Prompt } from "impact-ui";
import { Table } from "impact-ui-v3";
import _ from "lodash";
import moment from "moment";

import CopyOfferPanel from "./CopyOfferPanel";

import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import ViewIcon from "../../../assets/imageAssets/viewIcon.svg?.url";
import CopyIcon from "../../../assets/imageAssets/copyIcon.svg?.url";
import ContentCopyOutlinedIcon from "@mui/icons-material/ContentCopyOutlined";

import {
    workbenchDownload,
    setSelectedWorkbenchOffers,
    setWorkbenchEnableInlineEdit,
    workbenchFinalizeAPICall,
    callWorkbenchTilesAPI,
    setCopyOfferTableData,
    finalizePromo,
    deletePromo,
    setIsPromoEdit,
    getStoresDetailsOfPromo,
    getProductsDetailsOfPromo,
} from "../../../store/features/workbenchReducer/workbenchReducer";
import {
    workbenchTableConfig,
    finalizeOverlapTableConfig,
    viewColsOptions,
    storeTableConfig,
    productTableConfig,
} from "./WorkbenchConstants";
import {
    toastError,
    validateOperation,
} from "../../../store/features/global/global";
import {
    inlineEditAPICall,
    withdrawOffer
} from "../../../store/features/decisionDashboardReducer/decisionDashboardReducer";

import {
    setActivePromoId,
    setMaxStepCount,
    setPromoDetails
} from "../../../store/features/promoReducer/promoReducer";
import { CellOnClickButton } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { setSelectedFilters } from "../../../store/features/filters/filters";
import { capitalizeFirstLetter,fabricatePayloadHierarchy, labelCurrencyHandlerForTableDefs } from "../../../utils/helpers/utility_helpers";
import { global_labels } from "../../../constants/Constants";

function WorkbenchTable(props) {
    const workbenchTableRef = useRef(null);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const {
        workbenchTableData = [],
        workbenchSelectedOffers = [],
        workbenchPromoTypes = [],
        enableWorkbenchInlineEdit = false,
        editedWorkbenchSelectedRowData = [],
        isPromoEdit = false,
    } = useSelector((store) => store?.pricesmartPromoReducer.workbench);

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const {
        global_configs,
        hierachy_keys,
        currency_detail
    } = useSelector((store) => store?.pricesmartPromoReducer?.global);

    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [deleteOffersModalText, setDeleteOffersModalText] = useState(null);
    const [showCopyPanel, setShowCopyPanel] = useState(false);
    const [showFinalizeButton, setShowFinalizeButton] = useState(false);
    const [finalizeOverlapOffers, setFinalizeOverlapOffers] = useState([]);
    const [finalizeWarningModalFlag, setFinalizeWarningModalFlag] = useState(
        false
    );
    const [promoType, setPromoType] = useState(
        _.filter(workbenchPromoTypes, (status) => status.value !== 6)
    );
    const [showWithdrawPrompt, setShowWithdrawPrompt] = useState(false);
    const [
        filteredWorkbenchTableData,
        setFilteredWorkbenchTableData,
    ] = useState([]);
    const [isSelectAll, setIsSelectAll] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [tableColDefs, setTableColDefs] = useState(workbenchTableConfig);
    const [selectedViewCols, setSelectedViewCols] = useState([]);
    const [isViewColsOpen, setIsViewColsOpen] = useState(false);
    const [isSelectAllForViewCols, setIsSelectAllForViewCols] = useState(false);
    const [productDetailTableData, setProductDetailTableData] = useState([]);
    const [storeDetailTableData, setStoreDetailTableData] = useState([]);
    const [productDetailModalOpen, setProductDetailModalOpen] = useState(false);
    const [storeDetailModalOpen, setStoreDetailModalOpen] = useState(false);
    const productDetailTableRef = useRef(null);
    const storeDetailTableRef = useRef(null);

    useEffect(() => {
        let col_def = _.cloneDeep(workbenchTableConfig);
        if (!global_configs?.event?.use_event) {
            col_def = col_def.filter((col) => col.field !== "event_name");
        }
        const col_products = col_def.find((ele) => ele.field == "products_count");
        const col_stores = col_def.find((ele) => ele.field == "stores_count");
        if (col_products) {
            col_products["cellRenderer"] = (props) => (
                <CellOnClickButton {...props} clickHandler={clickHandler} />
            );
        }
        if (col_stores) {
            col_stores["cellRenderer"] = (props) => (
                <CellOnClickButton {...props} clickHandler={clickHandler} />
            );
        }
        setTableColDefs(col_def);
    }, [global_configs?.event?.use_event]);

    useEffect(() => {
        return () => {
            dispatch(setSelectedWorkbenchOffers([]));
        }
    }, []);

    useEffect(() => {
        if (isPromoEdit) {
            handleEditOfferClick();
        }
    }, [isPromoEdit]);

    useEffect(() => {
        //if inline edit is ongoing, hide the checkbox column
        workbenchTableRef?.current?.api?.applyColumnState({
            state: [
                {
                    colId: "checkbox",
                    hide: enableWorkbenchInlineEdit,
                },
            ],
            applyOrder: true,
        });
    }, [enableWorkbenchInlineEdit]);

    const clickHandler = async (data, colDef) => {
        if (colDef.field === "products_count") {
            let productsDetails = [];
            productsDetails = await dispatch(getProductsDetailsOfPromo(data.promo_id));
            if (productsDetails) {
                setProductDetailTableData(productsDetails);
                setProductDetailModalOpen(true);
                setStoreDetailModalOpen(false);
            }
        } else if (colDef.field === "stores_count") {
            let storesDetails = [];
            storesDetails = await dispatch(getStoresDetailsOfPromo(data.promo_id));
            if (storesDetails) {
                setStoreDetailTableData(storesDetails);
                setStoreDetailModalOpen(true);
                setProductDetailModalOpen(false);
            }
        }
    }

    const onFilterTextBoxChanged = useCallback((text) => {
        setGlobalSearchText(text);
        workbenchTableRef.current.api.setGridOption("quickFilterText", text);
        return () => {
            dispatch(setSelectedWorkbenchOffers([]));
        }
    }, []);

    const handlePromoTypeChange = (selectedOptions) => {
        setPromoType(selectedOptions);
    };

    const onSelectAllHandler = () => {
        if (isSelectAll) {
            // Deselect all options
            setPromoType([]);
        } else {
            // Select all options
            setPromoType(workbenchPromoTypes);
        }
        setIsSelectAll(!isSelectAll);
    };

    useEffect(() => {
        if (promoType.length > 0) {
            const selectedStatusValues = promoType.map(
                (status) => status.value
            );
            const filteredData = _.filter(workbenchTableData, (promo) =>
                selectedStatusValues.includes(promo.status_id)
            );
            setFilteredWorkbenchTableData(filteredData);
        } else {
            setFilteredWorkbenchTableData(workbenchTableData);
        }
    }, [workbenchTableData, promoType]);

    const onDownloadOffersTableClick = () => {
        //build the payload for download
        let payload = {
            action: "get",
            report_file_name: "workbench_report_extensive_data",
            report_name: "workbench_report_extensive_data",
            report_type: "excel",
        };

        const workbenchFilters = _.cloneDeep(filtersData.WORKBENCH);

        _.forEach(Object.keys(workbenchFilters), (key) => {
            if (key === "dateRange") {
                payload.start_date = moment(
                    workbenchFilters.dateRange?.start_date
                ).format("MM/DD/YYYY");
                payload.end_date = moment(
                    workbenchFilters.dateRange?.end_date
                ).format("MM/DD/YYYY");
            }
        });

        payload = {
            ...payload,
            ...fabricatePayloadHierarchy(workbenchFilters),
            target_currency_id: currency_detail?.currency_id,
        }
        //call the api
        dispatch(workbenchDownload(payload));
    };

    const onRowSelection = () => {
        //get selected product groups and set in product
        const selectedRows = workbenchTableRef.current.api.getSelectedRows();
        dispatch(setSelectedWorkbenchOffers(_.cloneDeep(selectedRows)));
        let showFinalizeButton = true;

        if (selectedRows?.length) {
            //check if any selected offer has status other than To Finalize
            _.forEach(selectedRows, (row) => {
                if (row.status_id !== 2) {
                    showFinalizeButton = false;
                }
            });
            //if all selected offers have status as To Finalize, show finalize button
            if (showFinalizeButton) {
                setShowFinalizeButton(true);
            } else {
                setShowFinalizeButton(false);
            }
        }
    };

    const onInlineEditButtonClick = async () => {
        let isSelectionValid = true;
        let isOfferArchived = false;
        let selectedPromos = [];

        //check if selected offers have start date as today or past date
        _.forEach(workbenchSelectedOffers, (ele) => {
            selectedPromos.push(ele.promo_id);
            if (moment().isSameOrAfter(moment(ele.start_date))) {
                isSelectionValid = false;
            }
            if (ele.status_id === 6) {
                isOfferArchived = true;
            }
        });

        // if start date is today or past date, show error message and return

        if (!isSelectionValid) {
            dispatch(toastError(`Ongoing/Completed ${global_labels?.promo_alias_plural} can not be edited!`));
            return;
        }
        //if selected offers are archived, show error message and return
        if (isOfferArchived) {
            dispatch(toastError(`Archived ${global_labels?.promo_alias_plural} can not be edited!`));
            return;
        }
        //check if selected offers are allowed to be edited by calling the api
        const isOpAllowed = await dispatch(
            validateOperation({
                promo_id: [...selectedPromos],
            })
        );
        //if selected offers are not allowed to be edited, show error message and return
        if (!isOpAllowed.is_valid) {
            dispatch(toastError(isOpAllowed.message));
            return;
        }
        //set enable inline edit key to true
        dispatch(setWorkbenchEnableInlineEdit(true));
    };

    const onInlineEditUpdateClick = async () => {
        //check if any changes are made, if not show error message and return
        if (!editedWorkbenchSelectedRowData.length) {
            dispatch(toastError("No changes are made."));
            return;
        }
        let errMsg = "";

        //check if promo name is empty, if empty show error message and return
        _.forEach(editedWorkbenchSelectedRowData, (promo) => {
            if (!promo.promo_name || promo.promo_name === "") {
                errMsg = `${capitalizeFirstLetter(global_labels?.promo_primary)} name cannot be empty.`;
            }
        });
        if (errMsg) {
            dispatch(toastError(errMsg));
            return;
        }
        //build payload for inline edit
        const payloadData = _.map(editedWorkbenchSelectedRowData, (data) => {
            return {
                promo_id: data.promo_id,
                comments: data.offer_comment,
                promo_name: data.promo_name,
            };
        });
        //call the api for inline edit, once done, refresh screen data
        const inlineEditResponse = await dispatch(
            inlineEditAPICall({ promos: _.cloneDeep(payloadData) })
        );

        if (inlineEditResponse) {
            props.resetScreenData();
        }
        dispatch(setWorkbenchEnableInlineEdit(false));
    };

    const onInlineEditCancelClick = () => {
        dispatch(setWorkbenchEnableInlineEdit(false));
        workbenchTableRef.current.api.deselectAll();
    };

    const onFinalizeButtonClick = async () => {
        const selectedPromoIds = [];
        let isFinalizeAllowed = true;
        //check if selected offers have start date as today or past date and get all the promo ids
        _.forEach(workbenchSelectedOffers, (promo) => {
            selectedPromoIds.push(promo.promo_id);
            if (moment().isSameOrAfter(moment(promo?.start_date)))
                isFinalizeAllowed = false;
        });

        //if selected offers have date as today or past date, show error message and return
        if (!isFinalizeAllowed) {
            dispatch(toastError(`${capitalizeFirstLetter(global_labels?.promo_alias_plural)} cannot be finalized`));
            return;
        }
        //check if selected offers have any overlap with other finalized offers
        const payload = {
            promo_ids: selectedPromoIds,
        };

        const finalizeResponse = await dispatch(
            workbenchFinalizeAPICall(payload)
        );
        //if there is any overlap, show warning message
        if (finalizeResponse?.length) {
            setFinalizeOverlapOffers(finalizeResponse);
            setFinalizeWarningModalFlag(true);
        } else {
            //if there is no overlap, call the finalize api
            handleFinalizePromo();
        }
    };

    const handleFinalizePromo = async () => {
        const promoIds = [];
        //get all promo ids of selected offers
        _.forEach(workbenchSelectedOffers, (row) => {
            promoIds.push(row.promo_id);
        });
        //build payload for finalize
        const finalizePayload = {
            promo_ids: promoIds,
        };
        setFinalizeWarningModalFlag(false);
        setFinalizeOverlapOffers([]);
        //call the api and reset screen data on success
        const finalizeRes = await dispatch(finalizePromo(finalizePayload));
        if (finalizeRes) {
            props.resetScreenData();
        }
    };

    const onFinalizeCancelClick = () => {
        setFinalizeOverlapOffers([]);
        setFinalizeWarningModalFlag(false);
    };

    const handleUpdateKPI = () => {
        let payload = {
            screen_type: "workbench",
        };
        //get all promo ids of selected offers
        payload.promo_ids = _.map(
            workbenchSelectedOffers,
            (row) => row.promo_id
        );
        const workbenchFilters = _.cloneDeep(filtersData.WORKBENCH);
        //build payload for update KPI
        _.forEach(Object.keys(workbenchFilters), (key) => {
            if (key === "dateRange") {
                payload.start_date = moment(
                    workbenchFilters.dateRange?.start_date
                ).format("MM/DD/YYYY");
                payload.end_date = moment(
                    workbenchFilters.dateRange?.end_date
                ).format("MM/DD/YYYY");
            }
        });
        payload = {
            ...payload,
            ...fabricatePayloadHierarchy(workbenchFilters),
        }
        //call the api for update KPI and deselect all the selected offers
        dispatch(callWorkbenchTilesAPI(_.cloneDeep(payload)));
        workbenchTableRef.current.api.deselectAll();
    };

    const onDeleteButtonClick = () => {
        let isSelectionValid = true;
        let execution_approved_promos = 0;
        let finalized_promos = 0;

        let reason = "";
        _.forEach(workbenchSelectedOffers, (ele) => {
            //check if selected offers are Execution Approved
            // -1	Placeholder
            // 0	Draft/Copied
            // 2	To Finalize
            // 4	Finalized
            // 6	Archived
            // 8	Execution Approved

            if (ele.status_id === 8 && moment().isBefore(moment(ele.start_date))) { // check future execution approved offers, which can be deleted but need to show warning
                execution_approved_promos++;
            }
            if (ele.status_id === 4 && moment().isBefore(moment(ele.start_date))) { // check future finalized offers, which can be deleted but need to show warning
                finalized_promos++;
            }
            //check if selected offers have start date as today or past date
            if (
                (
                    moment().isSameOrAfter(moment(ele.start_date)) && moment().isSameOrBefore(moment(ele.end_date)) // check ongoing offers which should't be deleted
                )
                || ele.status_id === 6 // check archived offers which should't be deleted
                || (ele.status_id === 8 && moment().isAfter(moment(ele.end_date))) // check past execution approved offers which should't be deleted
                || (ele.status_id === 4 && moment().isAfter(moment(ele.end_date))) // check past finalized offers which should't be deleted
            ) {
                isSelectionValid = false;
                if (ele.status_id === 8 && moment().isAfter(moment(ele.end_date))) {
                    reason = "Execution Approved & in the past";
                } else if (ele.status_id === 4 && moment().isAfter(moment(ele.end_date))) {
                    reason = "Finalized & in the past";
                } else if (ele.status_id === 6) {
                    reason = "Archived";
                } else {
                    reason = "Ongoing";
                }
            }
        });
        //if selected offers have date as today or past date, show error message and return
        if (!isSelectionValid) {
            const errorMessage = reason
                ? `${capitalizeFirstLetter(global_labels?.promo_alias_plural)} cannot be deleted, \n Reason: as one of the ${global_labels?.promo_alias} is ${reason}`
                : `${capitalizeFirstLetter(global_labels?.promo_alias_plural)} cannot be deleted`;
            dispatch(toastError(errorMessage));
            return false;
        }

        let warningNote = null;
        //if selected offers are Execution Approved, show warning message
        if (execution_approved_promos > 0 || finalized_promos > 0) {
            warningNote = <div>
                {execution_approved_promos ? `${execution_approved_promos} of the selected ${global_labels?.promo_alias_plural} are Execution Approved and` : ""}
                <br />
                {finalized_promos ? `${finalized_promos} of the selected ${global_labels?.promo_alias_plural} are Finalized` : ""}
            </div>
            setDeleteOffersModalText(warningNote);
        } else {
            //if selected offers are not Execution Approved, call delete api
            onDeleteHandle();
        }
    };

    const cancelDeleteHandler = () => {
        //clear delete modal text, and deselect all the selected promos
        setDeleteOffersModalText(null);
        workbenchTableRef.current.api.deselectAll();
    };

    const onDeleteHandle = async () => {
        //get all promo ids of selected offers
        const selectedPromoIds = workbenchSelectedOffers.map(
            (promo) => promo.promo_id
        );
        //build the payload and call the api to delete the selected offers
        const deletePromoPayload = {
            promo_ids: selectedPromoIds,
        };
        const deleteRes = await dispatch(deletePromo(deletePromoPayload));
        //if delete is successful, refresh screen data
        if (deleteRes) {
            props.resetScreenData();
        }
        //close the delete modal, clear delete modal text, if any
        cancelDeleteHandler();
    };

    const onCopyOffer = () => {
        //on copy IA button click, create table data for selected offers
        const selectedPromoData = _.map(workbenchSelectedOffers, (promo) => {
            const dateRange = `${moment(promo.start_date).format(
                "MM-DD-YYYY"
            )} - ${moment(promo.end_date).format("MM-DD-YYYY")}`;
            return {
                promo_id: promo.promo_id,
                promo_name: promo.promo_name,
                new_promo_name: promo.promo_name,
                start_date: null,
                end_date: null,
                date_range: dateRange,
                modified_date_range: "-",
                event_id: null,
            };
        });
        //save the table data for copy IA in redux store
        dispatch(setCopyOfferTableData(_.cloneDeep(selectedPromoData)));
        //open copy IA panel
        setShowCopyPanel(true);
    };

    const onCopyPanelClose = (flag) => {
        //if copy IA is successful, refresh screen data
        if (flag) {
            props.resetScreenData();
        }
        setShowCopyPanel(false);
        workbenchTableRef?.current?.api?.deselectAll();
    };

    const onCreateNewOfferClick = () => {
        //navigate to create product group screen
        navigate("/pricesmart-promo/workbench/create-offer");
        dispatch(setPromoDetails(null))
    };

    const handleEditOfferClick = async () => {
        if (workbenchSelectedOffers?.[0]?.status_id === 8) {
            setShowWithdrawPrompt(true);
            return;
        }
        const res = await dispatch(validateOperation({
            promo_id: [workbenchSelectedOffers?.[0]?.promo_id]
        }));

        if (!res.is_valid) {
            dispatch(toastError(res.message));
            dispatch(setIsPromoEdit(false));
            return;
        }

        dispatch(setActivePromoId(workbenchSelectedOffers?.[0].promo_id));
        dispatch(setMaxStepCount(workbenchSelectedOffers?.[0].step_count));

        if (isPromoEdit) {
            dispatch(setIsPromoEdit(false));
        }

        if (res) {
            dispatch(setSelectedWorkbenchOffers([]));
            navigate(`/pricesmart-promo/workbench/create-offer?promo=${workbenchSelectedOffers?.[0]?.promo_id}`);
        }
    };

    const handleViewOfferClick = async () => {
        dispatch(setActivePromoId(workbenchSelectedOffers?.[0].promo_id));
        dispatch(setMaxStepCount(workbenchSelectedOffers?.[0].step_count));

        dispatch(setSelectedWorkbenchOffers([]));
        navigate(`/pricesmart-promo/workbench/create-offer?promo=${workbenchSelectedOffers?.[0]?.promo_id}&view=true`);
    };

    const handleViewColsChange = (selectedOptions) => {
        setSelectedViewCols(selectedOptions);
        const selectedValues = selectedOptions.map((ele) => ele.value);
        setIsSelectAllForViewCols(selectedOptions.length === viewColsOptions.length);
        const tableColDef = _.cloneDeep(workbenchTableConfig);
        const colsForOriginal = [
            "original_margin_percent",
            "original_contribution_margin",
            "original_contribution_margin_percent",
            "original_sales_units",
            "original_revenue",
            "original_margin",
        ];
        const colsForStacked = [
            "finalized_stack_margin_percent",
            "finalized_stack_contribution_margin",
            "finalized_stack_contribution_margin_percent",
            "finalized_stack_sales_units",
            "finalized_stack_revenue",
            "finalized_stack_margin",
            "baseline_stack_sales_units",
            "baseline_stack_revenue",
            "baseline_stack_margin",
            "finalized_stack_st_percent",
        ];
        const colsForBothSelected = [
            ...colsForStacked,
            "original_stack_margin_percent",
            "original_stack_contribution_margin",
            "original_stack_contribution_margin_percent",
            "original_stack_sales_units",
            "original_stack_revenue",
            "original_stack_margin",
        ];
        const colsToBeHiddenForStacked = [
            "baseline_sales_units",
            "baseline_revenue",
            "baseline_margin",
            "finalized_sales_units",
            "finalized_revenue",
            "finalized_margin",
            "finalized_margin_percent",
            "finalized_promo_spend",
            "finalized_contribution_margin",
            "finalized_contribution_margin_percent",
            "finalized_st_percent",
        ];
        if (selectedValues.includes("stacked") || selectedValues.includes("original")) {
            tableColDef.forEach((col) => {
                if (
                    (colsForBothSelected.includes(col.field) && selectedValues.includes("stacked") && selectedValues.includes("original"))
                    || (colsForStacked.includes(col.field) && selectedValues.includes("stacked") && !selectedValues.includes("original"))
                    || (colsForOriginal.includes(col.field) && selectedValues.includes("original") && !selectedValues.includes("stacked"))
                ) {
                    col.hide = false;
                }
                if (colsToBeHiddenForStacked.includes(col.field) && selectedValues.includes("stacked")) {
                    col.hide = true;
                }
            });

            setTableColDefs(
                !global_configs?.event?.use_event
                    ? tableColDef.filter((col) => col.field !== "event_name")
                    : _.cloneDeep(tableColDef)
            );
        } else {
            setTableColDefs(
                !global_configs?.event?.use_event
                    ? workbenchTableConfig.filter((col) => col.field !== "event_name")
                    : _.cloneDeep(workbenchTableConfig)
            );
        }
    };

    const onSelectAllHandlerForViewCols = (e) => {
        if (e.target.checked) {
            handleViewColsChange(viewColsOptions);
        } else {
            handleViewColsChange([]);
        }
    };

    const submitCopyPromos = data => {
        onCopyPanelClose();
        dispatch(
            setSelectedFilters({
                data: data?.promo_ids,
                from: "WORKBENCH",
                filterId: "promo",
            })
        );
        props.resetScreenData({
            ...data,
            promo_ids: data?.promo_ids?.map(promo => promo?.value),
        });
    }

    const onWithdrawButtonClick = () => {
        //set prompt to false
        setShowWithdrawPrompt(false);
        const promoIds = [];
        let withdrawPromoFlag = false;
        //get all promo ids of selected offers where status id is 8
        //if start date is today or future date, set withdrawPromoFlag to true
        _.forEach(workbenchSelectedOffers, (row) => {
            if (row.status_id === 8) {
                if (moment().isSameOrAfter(moment(row.start_date)))
                    withdrawPromoFlag = true;
                promoIds.push(row.promo_id);
            }
        });
        //if withdrawPromoFlag is true, show error message and return
        if (withdrawPromoFlag) {
            dispatch(
                toastError(`Ongoing/Completed ${global_labels?.promo_alias_plural} cannot be withdrawn!`)
            );
            return;
        }
        //else call withdrawPromo function
        withdrawPromo(promoIds);
    };

    const withdrawPromo = async (promoIds = []) => {
        //build payload for withdraw
        const withdrawPayload = {
            action: "withdraw",
            promo_ids: promoIds,
            guid: sessionStorage.getItem("UNIQ_SSE_KEY"),
        };
        const withdrawRes = await dispatch(withdrawOffer(withdrawPayload));
        //if withdraw is successful, refresh screen data
        if (withdrawRes) {
            dispatch(setActivePromoId(promoIds[0]));
            dispatch(setMaxStepCount(workbenchSelectedOffers?.[0].step_count));
            navigate(`/pricesmart-promo/workbench/create-offer?promo=${promoIds[0]}`);
            return;
        }
    };

    const onDownloadProductButtonClick = () => {
        // download products data as excel
        const params = {
            fileName: "products.xlsx",
        };
        productDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const onDownloadStoreButtonClick = () => {
        // download stores data as excel
        const params = {
            fileName: "stores.xlsx",
        };
        storeDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

    return (
        <div className="marginTop-20">
            <Table
                ref={workbenchTableRef}
                tableHeader={`All ${capitalizeFirstLetter(global_labels?.promo_standard_plural)}`}
                suppressMenuHide={true}
                rowData={filteredWorkbenchTableData}
                columnDefs={labelCurrencyHandlerForTableDefs(tableColDefs, currency_detail?.currency_symbol || "$")}
                rowSelection="multiple"
                onSelectionChanged={onRowSelection}
                suppressRowClickSelection={true}
                topRightOptions={
                    enableWorkbenchInlineEdit ? (
                        <div className="centerFlexWithGap12">
                            <Button
                                onClick={onInlineEditUpdateClick}
                                size="large"
                                variant="primary"
                            >
                                Update
                            </Button>
                            <Button
                                onClick={onInlineEditCancelClick}
                                size="large"
                                variant="primary"
                            >
                                Cancel
                            </Button>
                        </div>
                    ) : (
                        <div className="centerFlexWithGap12">
                            {!workbenchSelectedOffers.length ? (
                                <div className="centerFlexWithGap12">
                                    <Select
                                        currentOptions={workbenchPromoTypes}
                                        initialOptions={workbenchPromoTypes}
                                        label={`${capitalizeFirstLetter(global_labels?.promo_alias)} Status`}
                                        labelOrientation="left"
                                        setSelectedOptions={
                                            handlePromoTypeChange
                                        }
                                        setCurrentOptions={() => { }}
                                        placeholder="Select..."
                                        isRequired={false}
                                        isWithSearch={false}
                                        isMulti={true}
                                        isSelectAll={isSelectAll}
                                        onSelectAll={onSelectAllHandler}
                                        setIsSelectAll={setIsSelectAll}
                                        selectedOptions={promoType}
                                        isOpen={isOpen}
                                        setIsOpen={setIsOpen}
                                        isCloseWhenClickOutside={true}
                                        toggleSelectAll
                                    />
                                    <Select
                                        currentOptions={viewColsOptions}
                                        initialOptions={viewColsOptions}
                                        label="Show"
                                        labelOrientation="left"
                                        setSelectedOptions={handleViewColsChange}
                                        setCurrentOptions={() => { }}
                                        placeholder="Select.."
                                        isRequired={false}
                                        isWithSearch={false}
                                        isMulti={true}
                                        isSelectAll={isSelectAllForViewCols}
                                        onSelectAll={onSelectAllHandlerForViewCols}
                                        setIsSelectAll={setIsSelectAllForViewCols}
                                        selectedOptions={selectedViewCols}
                                        isOpen={isViewColsOpen}
                                        setIsOpen={setIsViewColsOpen}
                                        isCloseWhenClickOutside={true}
                                        toggleSelectAll
                                    />
                                    <div className="horizontal-line" />
                                    <Button
                                        onClick={onCreateNewOfferClick}
                                        size="large"
                                        variant="primary"
                                    >
                                        Create New {capitalizeFirstLetter(global_labels?.promo_primary)}
                                    </Button>
                                    <div className="horizontal-line" />
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={DownloadIcon} />}
                                        onClick={onDownloadOffersTableClick}
                                        size="large"
                                        variant="tertiary"
                                        className='viewOfferButton'
                                    />
                                </div>
                            ) : null}

                            {workbenchSelectedOffers.length ? (
                                <div className="centerFlexWithGap12">
                                    <Button
                                        onClick={handleUpdateKPI}
                                        size="large"
                                        variant="secondary"
                                    >
                                        Update KPI's
                                    </Button>
                                    <Button
                                        onClick={onInlineEditButtonClick}
                                        size="large"
                                        variant="secondary"
                                    >
                                        Inline Edit
                                    </Button>
                                    {showFinalizeButton ? (
                                        <Button
                                            onClick={onFinalizeButtonClick}
                                            size="large"
                                            variant="primary"
                                        >
                                            Finalize
                                        </Button>
                                    ) : null}
                                    <div className="horizontal-line" />
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={DeleteIcon} />}
                                        onClick={onDeleteButtonClick}
                                        size="large"
                                        variant="secondary"
                                        type="destructive"
                                        className='viewOfxferButton delete-button'
                                    />
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={CopyIcon} />}
                                        onClick={onCopyOffer}
                                        size="large"
                                        variant="tertiary"
                                        className='viewOfferButton copy'
                                    />
                                </div>
                            ) : null}

                            {workbenchSelectedOffers.length === 1 ? (
                                <div className="centerFlexWithGap12">
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={ViewIcon} />}
                                        onClick={() => {
                                            handleViewOfferClick()
                                        }}
                                        size="large"
                                        variant="tertiary"
                                        className="viewOfferButton"
                                    />
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={EditIcon} />}
                                        onClick={() => {
                                            handleEditOfferClick();
                                        }}
                                        size="large"
                                        variant="tertiary"
                                        className='viewOfferButton'
                                    />
                                </div>
                            ) : null}
                            <div className="positionRelative">
                                {showGlobalSearch ? (
                                    <div className="tableGlobalSearchContainer">
                                        <Input
                                            onChange={(e) =>
                                                onFilterTextBoxChanged(
                                                    e.target.value
                                                )
                                            }
                                            placeholder="Search"
                                            rightIcon={<img src={SearchIcon} />}
                                            type="text"
                                            value={globalSearchText}
                                        />
                                    </div>
                                ) : null}
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={SearchIcon} />}
                                    onClick={() => {
                                        setShowGlobalSearch((prev) => !prev);
                                    }}
                                    size="large"
                                    variant="tertiary"
                                    className='viewOfferButton'
                                />
                            </div>
                        </div>
                    )
                }
            />
            <Modal
                onClose={onFinalizeCancelClick}
                onPrimaryButtonClick={handleFinalizePromo}
                onSecondaryButtonClick={onFinalizeCancelClick}
                primaryButtonLabel="Continue"
                secondaryButtonLabel="Cancel"
                size="medium"
                title={`Finalize ${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`}
                open={finalizeWarningModalFlag}
            >
                <div className="invalidProductsGrpModalContainer">
                    <span className="secondaryText-14-500">
                        There is an overlap with other finalized {global_labels?.promo_alias_plural}.
                    </span>
                    <Table
                        tableHeader={"Effected Plans"}
                        suppressMenuHide
                        rowData={finalizeOverlapOffers}
                        columnDefs={finalizeOverlapTableConfig}
                    />
                </div>
            </Modal>
            <Prompt
                handleClose={cancelDeleteHandler}
                onPrimaryButtonClick={onDeleteHandle}
                onSecondaryButtonClick={cancelDeleteHandler}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title="Are you Sure?"
                variant="warning"
                isOpen={deleteOffersModalText}
            >
                {deleteOffersModalText}
            </Prompt>
            <Prompt
                handleClose={() => {
                    setShowWithdrawPrompt(false);
                }}
                onPrimaryButtonClick={onWithdrawButtonClick}
                onSecondaryButtonClick={() => {
                    setShowWithdrawPrompt(false);
                }}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title={`Confirm withdrawal and edit of selected ${global_labels?.promo_alias}`}
                variant="warning"
                isOpen={showWithdrawPrompt}
            >
                Are you sure you want to withdraw this {global_labels?.promo_alias} and continue editing?
            </Prompt>
            {showCopyPanel ? (
                <CopyOfferPanel
                    isOpen={showCopyPanel}
                    closePanel={onCopyPanelClose}
                    screen="WORKBENCH"
                    submitCopyPromos={submitCopyPromos}
                />
            ) : null}
            <Modal
                open={productDetailModalOpen}
                onClose={() => {
                    setProductDetailModalOpen(false);
                }}
                onPrimaryButtonClick={() => {
                    setProductDetailModalOpen(false);
                }}
                primaryButtonLabel="Ok"
                size="medium"
                title="Product Details"
                className="product-store-detail-modal"
            >
                <Table
                    ref={productDetailTableRef}
                    suppressMenuHide
                    rowData={productDetailTableData}
                    columnDefs={productTableConfig(hierarchyGlobalKeys)}
                    suppressRowClickSelection={true}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <Button
                            iconPlacement="left"
                            icon={<img src={DownloadIcon} />}
                            onClick={onDownloadProductButtonClick}
                            size="large"
                            variant="tertiary"
                        />
                    }
                />
            </Modal>
            <Modal
                open={storeDetailModalOpen}
                onClose={() => {
                    setStoreDetailModalOpen(false);
                }}
                onPrimaryButtonClick={() => {
                    setStoreDetailModalOpen(false);
                }}
                primaryButtonLabel="Ok"
                size="medium"
                title="Store Details"
                className="product-store-detail-modal"
            >
                <Table
                    ref={storeDetailTableRef}
                    suppressMenuHide
                    rowData={storeDetailTableData}
                    columnDefs={storeTableConfig(hierarchyGlobalKeys)}
                    suppressRowClickSelection={true}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <Button
                            iconPlacement="left"
                            icon={<img src={DownloadIcon} />}
                            onClick={onDownloadStoreButtonClick}
                            size="large"
                            variant="tertiary"
                        />
                    }
                />
            </Modal>
        </div>
    );
}

export default WorkbenchTable;