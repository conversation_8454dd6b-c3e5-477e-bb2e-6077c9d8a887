import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Carousel from "../../ui/carousel/Carousel";
import _ from "lodash";

import { tilesDataFormatter } from "../../../utils/helpers/utility_helpers";

import InfoCard from "../../ui/infoCard/InfoCard";

import { workbenchTilesConfig } from "./WorkbenchConstants";

function WorkbenchTiles() {
    const [tilesData, setTilesData] = useState([]);
    const { workbenchTilesData = [] } = useSelector(
        (store) => store?.pricesmartPromoReducer.workbench
    );

    useEffect(() => {
		if (workbenchTilesData.length) {
			//metrics data from the api response in reducer
			const tiles = workbenchTilesData[0]?.metrics;
            const currency_detail = workbenchTilesData[0]?.currency_detail;
            const formatters_params = {
                ...currency_detail
            } 
			const tilesData = tilesDataFormatter(
				tiles,
				workbenchTilesConfig,
				formatters_params
			);

			setTilesData(_.cloneDeep(tilesData));
		}
	}, [workbenchTilesData]);


    return (
        <div className="carousel_container">
            <Carousel>
                {tilesData.length
                    ? _.map(tilesData, (data) => {
                            return <InfoCard {...data} />;
                      })
                    : null}
            </Carousel>
        </div>
    );
}

export default WorkbenchTiles;