import React, { useState, useEffect } from "react";
import moment from "moment";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import { <PERSON>ton, Checkbox, RadioButtonGroup } from "impact-ui";

import FilterWrapper from "../../common/filters/FilterWrapper";
import ScreenBreadcrumb from "../../common/breadCrumb/ScreenBreadcrumb";
import WorkbenchTiles from "./WorkbenchTiles";
import WorkbenchTable from "./WorkbenchTable";
import EmptyData from "../../ui/emptyData/EmptyData";

import { 
    workbenchRequiredFiltersOnLoad as requiredFiltersOnLoad,
    workbenchFilterConfig as filterConfig,
    wholeStoreFilterConfig,
    wholeProductFilterConfig
} from "../../../constants/FilterConfigConstants";
import { breadcrumbRoutes } from "../../../constants/RouteConstants";
import {
    callWorkbenchTilesAPI,
    callWorkbenchTableAPI,
    getPromoTypes,
} from "../../../store/features/workbenchReducer/workbenchReducer";

import "./Workbench.scss";
import { DEFAULT_DATE_FORMAT, global_labels } from "../../../constants/Constants";
import { useNavigate } from "react-router-dom-v5-compat";
import { resetFiltersForNextIds, setSelectedGenricFilters } from "../../../store/features/filters/filters";
import { capitalizeFirstLetter } from "../../../utils/helpers/utility_helpers";

function Workbench(props) {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const { global_configs, currency_detail } = useSelector((store) => store?.pricesmartPromoReducer.global);

    const [showFiltersSection, setShowFiltersSection] = useState(true);
    const [workbenchFilterConfig, setWorkbenchFilterConfig] = useState(filterConfig);

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const { workbenchTableData = [] } = useSelector(
        (store) => store?.pricesmartPromoReducer.workbench
    );

    useEffect(() => {
        dispatch(getPromoTypes({ screenName: "workbench" }));
    }, []);

    useEffect(() => {
        const promoIds = new URLSearchParams(window.location.search).get("promoIds");
        if (promoIds) {
            onFilterApply({ promo_ids: promoIds.split(",") });
        }
    }, [window.location.href]);

    useEffect(() => {
        // set default values for the filters
        // show_partially_overlapping_events
        dispatch(setSelectedGenricFilters({
            data: true,
            from: "WORKBENCH",
            filterId: "show_partially_overlapping_events",
        }));
        // metrics_display_mode
        dispatch(setSelectedGenricFilters({
            data: "entire_offer_duration",
            from: "WORKBENCH",
            filterId: "metrics_display_mode",
        }));
    }, [])

    useEffect(() => {
        const config = [];
        filterConfig.forEach((filter) => {
            let newFilter = _.cloneDeep(filter);
            if (!global_configs?.event?.use_event) {
                newFilter = {
                    ...filter,
                    groupConfig: filter.groupConfig?.filter((group) => group.filterId !== "event"),
                };
            }
            // to handle custom filter
            newFilter.groupConfig.forEach((group) => {
                if (group.filterId === "promo" || group.filterId === "event") {
                    group["extraParams"] = {
                        ...group?.extraParams,
                        show_partially_overlapping_events: filtersData?.WORKBENCH?.show_partially_overlapping_events || false,
                    }
                }
                if (group.filterType === "custom") {
                    if (group.filterId === "show_partially_overlapping_events") {
                        group["component"] = (
                            <Checkbox
                                checked={
                                    filtersData?.WORKBENCH?.show_partially_overlapping_events || false
                                }
                                label={group?.label}
                                onChange={(val) => {
                                    dispatch(setSelectedGenricFilters({
                                        data: val.target.checked,
                                        from: "WORKBENCH",
                                        filterId: group?.filterId,
                                    }));
                                    dispatch(resetFiltersForNextIds({
                                        activeScreen: "WORKBENCH",
                                        payload: ["event", "promo"],
                                    }))
                                }}
                                required={group?.isMandatory || false}
                                variant="default"
                            />
                        )
                    } else if (group.filterId === "metrics_display_mode") {
                        group["component"] = (
                            <div className="metrics_display_mode_container">
                                <p className="text-14-600">Show metrics of:&nbsp;</p>
                                <RadioButtonGroup
                                    options={group?.options}
                                    selectedOption={filtersData?.WORKBENCH?.metrics_display_mode}
                                    isDisabled={!filtersData?.WORKBENCH?.show_partially_overlapping_events}
                                    onChange={(_e, val) => {
                                        dispatch(setSelectedGenricFilters({
                                            data: val,
                                            from: "WORKBENCH",
                                            filterId: group?.filterId,
                                        }));
                                    }}
                                    orientation="row"
                                    className="metrics_radio_button_group"
                                />
                            </div>
                        )
                    }
                }
            });

            config.push(newFilter);
        });
        setWorkbenchFilterConfig(config);
    }, [global_configs?.event?.use_event, filtersData?.WORKBENCH]);

    const onFilterApply = (updatedFilters = {}) => {
        const workbenchFilters = _.cloneDeep(filtersData.WORKBENCH);
        // set all selected filters in the payload
        const product_hierarchy_keys = wholeProductFilterConfig.map(item => item.filterId);
        const store_hierarchy_keys = wholeStoreFilterConfig.map(item => item.filterId);
        let payload = {
            product_hierarchies: {},
            store_hierarchies: {},
        };
        _.forEach(Object.keys(workbenchFilters || {}), (key) => {
            if (key === "dateRange") {
                payload.start_date = moment(
                    workbenchFilters.dateRange?.start_date
                ).format(DEFAULT_DATE_FORMAT);
                payload.end_date = moment(
                    workbenchFilters.dateRange?.end_date
                ).format(DEFAULT_DATE_FORMAT);
            } else if (key === "event") {
                payload["event_ids"] = _.cloneDeep(
                    workbenchFilters[key]?.selectedOptionsArray
                );
            } else if (key === "promo") {
                payload["promo_ids"] = _.cloneDeep(
                    workbenchFilters[key]?.selectedOptionsArray
                );
            } else if (product_hierarchy_keys.includes(key)) {
                payload["product_hierarchies"][key] = _.cloneDeep(
                    workbenchFilters[key]?.selectedOptionsArray
                );
            } else if (store_hierarchy_keys.includes(key)) {
                payload["store_hierarchies"][key] = _.cloneDeep(
                    workbenchFilters[key]?.selectedOptionsArray
                );
            } else {
                payload[key] = workbenchFilters[key].hasOwnProperty("selectedOptionsArray")
                    ? _.cloneDeep(workbenchFilters[key]?.selectedOptionsArray)
                    : _.cloneDeep(workbenchFilters[key]);
            }
        });

        // for table API, add action in payload
        payload.action = "get";
        payload.target_currency_id = currency_detail?.currency_id;
        if (!_.isEmpty(updatedFilters)) {
            payload = {
                ...payload,
                ...updatedFilters
            }
        }
        dispatch(callWorkbenchTableAPI(_.cloneDeep(payload)));
        // for tiles API, add screen_type in payload
        let payloadForTilesAndTable = _.cloneDeep(payload);
        payloadForTilesAndTable.screen_type = "workbench";
        payloadForTilesAndTable = {
            ...payloadForTilesAndTable,
        }
        dispatch(callWorkbenchTilesAPI(_.cloneDeep(payloadForTilesAndTable)));
        navigate(location.pathname, { replace: true, state: {} });
    };

    return (
        <div className="screen_container">
            <ScreenBreadcrumb breadcrumbList={breadcrumbRoutes()?.["workbench"]}>
                <Button
                    iconPlacement="left"
                    onClick={() => {
                        setShowFiltersSection((prev) => !prev);
                    }}
                    size="large"
                    variant="secondary"
                >
                    {showFiltersSection ? "Hide" : "Show"} Filters
                </Button>
            </ScreenBreadcrumb>
            <FilterWrapper
                defaultOpen="product_hierarchy"
                screen="WORKBENCH"
                callAPIonLoad={!new URLSearchParams(window.location.search).get("promoIds")}
                filterConfig={workbenchFilterConfig}
                requiredFiltersOnLoad={requiredFiltersOnLoad}
                onFilterApply={onFilterApply}
                showFiltersSection={showFiltersSection}
            />
            {workbenchTableData.length ? (
                <div className="screen_data_container flex1">
                    <div className="workbenchDataContainer">
                        <WorkbenchTiles />
                        <WorkbenchTable resetScreenData={onFilterApply} />
                    </div>
                </div>
            ) : (
                <>
                    <div className="display-flex-end padding-16">
                        <Button
                            variant="primary"
                            onClick={() => {
                                navigate("/pricesmart-promo/workbench/create-offer");
                            }}
                        >
                            Create New {capitalizeFirstLetter(global_labels?.promo_primary)}
                        </Button>
                    </div>
                    <EmptyData text={`Please Select Filters To View ${capitalizeFirstLetter(global_labels?.promo_plural)}`} />
                </>
            )}
        </div>
    );
}

export default Workbench;