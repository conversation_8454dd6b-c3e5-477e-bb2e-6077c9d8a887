import React, { useState, useEffect, useRef, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON>, <PERSON><PERSON>, DateRangePicker, Select, Toast } from "impact-ui";
import { Table } from "impact-ui-v3";
import _ from "lodash";
import moment from "moment";

import { copyIATableConfig } from "./WorkbenchConstants";

import {
	workbenchCopyIAEditDataSave,
	validateCopyPromo,
	copyPromos,
	setCopyOfferTableData,
	setCopyOfferEditedData
} from "../../../store/features/workbenchReducer/workbenchReducer";
import CopyIcon from "../../../assets/imageAssets/copyIcon.svg?.url";
import { getEvents } from "../../../store/features/promoReducer/promoReducer";
import InactiveCloseIcon from "../../../assets/imageAssets/inactiveCloseIcon.svg?url";
import warningAlertIconSvg from "../../../assets/imageAssets/warningIcon.svg?.url";
import infoIconSvg from "../../../assets/imageAssets/ISymbol.svg?.url";
import { capitalizeFirstLetter, replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";
import { DEFAULT_DATE_FORMAT, global_labels } from "../../../constants/Constants";

function CopyOfferPanel(props) {
	const copyIATableRef = useRef(null);
	const dateRangeRef = useRef(null);
	const dispatch = useDispatch();

	const [toast, setToast] = useState({
		isOpen: false,
		message: "",
		variant: "success",
	});

	const [selectedRowData, setSelectedRowData] = useState([]);
	const [showBulkEdit, setShowBulkEdit] = useState(false);
	const [startDate, setStartDate] = useState(null);
	const [endDate, setEndDate] = useState(null);
	const [selectedevent, setSelectedevent] = useState([]);
	const [isOpen, setIsOpen] = useState(false);
	const [eventOptions, setEventOptions] = useState([]);
	const [overallWarningMessage, setOverallWarningMessage] = useState(null);
	const { copyOfferTableData = [], copyOfferEditedData = [] } = useSelector(
		(store) => store?.pricesmartPromoReducer.workbench
	);
	const [filteredOptions, setFilteredOptions] = useState([]);

	const {
		events,
	} = useSelector((store) => store?.pricesmartPromoReducer.promo);

	const {
		global_configs
	} = useSelector((store) => store?.pricesmartPromoReducer?.global);

	useEffect(async () => {
		if (global_configs?.event?.use_event) {
			await dispatch(getEvents({ is_locked: false }));
		}
	}, [])

	useEffect(() => {
		if (global_configs?.event?.use_event && events?.length) {
			const tempOptions = events.map((event) => {
				return {
					label: replaceSpecialCharacter(event.event_name),
					value: event.event_id,
					start_date: event.start_date,
					end_date: event.end_date,
				};
			});
			setEventOptions(tempOptions);
		}
	}, [events]);

	useEffect(() => {
		//add close dateRange popup event listener on outside click
		const handleClickOutside = (event) => {
			if (
				dateRangeRef.current &&
				!dateRangeRef.current.contains(event.target)
			) {
				setShowBulkEdit(false); // Close popup if click is outside
			}
		};

		// Add event listener to detect clicks outside of the popup
		document.addEventListener("mousedown", handleClickOutside);

		// Cleanup the event listener when component unmounts
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [setShowBulkEdit]);

	const onRowSelection = useCallback(() => {
		// set selected offer data
		setSelectedRowData(copyIATableRef.current.api.getSelectedRows());
	});

	const handleBulkEditApply = () => {
		//save date range for selected offers into redux
		_.forEach(selectedRowData, (row) => {
			const payload = {
				data: {
					// event_id: selectedevent?.value,
					start_date: moment(startDate),
					end_date: moment(endDate),
				},
				uniqueKeyVal: row.promo_id,
			};
			if (global_configs?.event?.use_event) {
				payload["data"]["event_id"] = selectedevent?.value
			}
			dispatch(workbenchCopyIAEditDataSave(payload));
		});
		//clear selected offers and date range
		copyIATableRef.current.api.deselectAll();
		setShowBulkEdit(false);
		setStartDate(null);
		setEndDate(null);
	};

	const onCancelClick = () => {
		setShowBulkEdit(false);
		setStartDate(null);
		setEndDate(null);
	};

	const resetDateRange = () => {
		setStartDate(null);
		setEndDate(null);
		setShowBulkEdit(false);
		//reset the date range to original date
		_.forEach(selectedRowData, (row) => {
			const payload = {
				data: {
					start_date: row.start_date,
					end_date: row.end_date,
				},
				uniqueKeyVal: row.promo_id,
			};
			dispatch(workbenchCopyIAEditDataSave(payload));
		});
	};

	const handleValidateCopy = async () => {
		if (!copyOfferEditedData.length) {
			setToast({
				isOpen: true,
				message: `Please update ${global_labels?.promo_alias_plural} for copy.`,
				variant: "error",
			});
			return;
		}
		// Check if all edited offers have modified-offer-name
		const hasInvalidOffers = copyOfferEditedData.some(data => !data['new_promo_name']);
		if (hasInvalidOffers) {
			setToast({
				isOpen: true,
				message: `Please provide modified ${global_labels?.promo_alias} name for all selected ${global_labels?.promo_alias_plural}.`,
				variant: "error",
			});
			return;
		}

		if (global_configs?.event?.use_event) {

			const copyIAData = _.map(copyOfferEditedData, (data) => {
				return {
					event_id: data.event_id,
					promo_id: data.promo_id
				};
			});

			const validateCopyPayload = {
				offer_event_mappings: _.cloneDeep(copyIAData),
			}

			const validateResponse = await dispatch(validateCopyPromo(validateCopyPayload));
			if (validateResponse?.promo_data?.length) {
				const tempTableData = _.cloneDeep(copyOfferTableData);

				const overallError = validateResponse?.summary_message;
				// Check if there are any validation errors to process
				if (overallError) {
					setOverallWarningMessage(overallError);
				} else {
					setOverallWarningMessage(null)
				}
				// Iterate through each row in the table data
				tempTableData.forEach(data => {
					// Find matching validation error for current promo_id
					const validationError = validateResponse?.promo_data?.find(
						error => error.promo_id === data.promo_id  // Use strict equality
					);

					// If validation error exists, add error message to the data row
					if (validationError) {
						data.message = validationError.validation_message;
						data.showWarning = true
					}
				});
				//update table data
				dispatch(setCopyOfferTableData(tempTableData));
			} else {
				onSubmitClick()
			}
		} else {
			onSubmitClick()
		}
	};

	const onSubmitClick = async () => {

		//modify the date range format to YYYY-MM-DD
		const copyIAData = _.map(copyOfferEditedData, (data) => {
			return {
				...data,
				start_date: moment(data.start_date).format("YYYY-MM-DD"),
				end_date: moment(data.end_date).format("YYYY-MM-DD"),
			};
		});

		const copyPromoPayload = {
			promos: _.cloneDeep(copyIAData),
		}

		const result = await dispatch(copyPromos(copyPromoPayload));

		if (result?.promo_ids) {
			// Get min start date and max end date
			const minStartDate = moment.min(copyOfferEditedData.map(data => moment(data.start_date)));
			const maxEndDate = moment.max(copyOfferEditedData.map(data => moment(data.end_date)));

			const dataObj = {
				promo_ids: result?.promo_ids,
				start_date: moment(minStartDate).format(DEFAULT_DATE_FORMAT),
				end_date: moment(maxEndDate).format(DEFAULT_DATE_FORMAT)
			}
			onClosePanel()

			props.submitCopyPromos(dataObj);

		}
		if(!result){
			setToast({
				isOpen: true,
				message: `Failed to copy ${global_labels?.promo_alias_plural}.`,
				variant: "error",
			});
		}
	}
	const onGridReady = useCallback(() => {
		// select all effected plans on grid ready for modal table

		if (global_configs?.event?.use_event) {
			const savedState = copyIATableRef.current.api.getColumnState();

			_.forEach(savedState, (config) => {
				if (config?.colId == "event_name") {
					config.hide = false;
				}
			});
			copyIATableRef.current.api.applyColumnState({
				state: _.cloneDeep(savedState),
				applyOrder: true,
			});
		}
	});

	const eventNameChangeHandler = (selectedOptions) => {
		setSelectedevent(selectedOptions);
		setStartDate(moment(selectedOptions?.start_date));
		setEndDate(moment(selectedOptions?.end_date));
	}

	const bulkEditCloseHandler = () => {
		setShowBulkEdit(false);
		setStartDate(null);
		setEndDate(null);
		setSelectedevent([]);
	}

	const onClosePanel = () => {
		setSelectedRowData([]);
		setSelectedevent([]);
		setStartDate(null);
		setEndDate(null);
		dispatch(setCopyOfferTableData([]));
		dispatch(setCopyOfferEditedData([]));
		props.closePanel(false);
	}

	return (
		<Panel
			anchor="right"
			open={true}
			className="copyOfferPanel"
			onClose={onClosePanel}
			onPrimaryButtonClick={handleValidateCopy}
			onSecondaryButtonClick={onClosePanel}
			primaryButtonLabel="Submit"
			secondaryButtonLabel="Cancel"
			size="large"
			title={
				<div className="centerFlexWithGap12">
					<Button
						iconPlacement="left"
						icon={<img src={CopyIcon} />}
						disabled
						size="large"
						variant="secondary"
					/>
					<p>Copy {capitalizeFirstLetter(global_labels?.promo_alias_plural)}</p>
				</div>
			}
		>
			<div className="copy-offer-panel-toast">
				<Toast
					autoHideDuration={3000}
					message={toast.message}
					onClose={(_e, _reason) => {
						setToast({
							isOpen: false,
							message: "",
							variant: "",
						});
					}}
					position="top-right"
					variant={toast.variant}
					isOpen={toast.isOpen}
				/>
			</div>
			<div>
				<Table
					tableHeader={`${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`}
					ref={copyIATableRef}
					suppressMenuHide
					rowData={copyOfferTableData}
					columnDefs={copyIATableConfig}
					suppressRowClickSelection={true}
					rowSelection="multiple"
					onSelectionChanged={onRowSelection}
					topRightOptions={
						<div className="centerFlexWithGap12">
							<Button
								onClick={() =>
									setShowBulkEdit((prev) => !prev)
								}
								size="large"
								variant="primary"
								disabled={
									!selectedRowData.length || showBulkEdit
								}
							>
								Bulk edit
							</Button>
						</div>
					}
				/>
				<div className="flexWithGap12 marginTop-16 marginBottom-16">
					<img src={infoIconSvg} alt="info" width={"18px"} />
					<p className="text-14-600">
						Update {global_labels?.event_primary} dates individually or in bulk.
					</p>
				</div>
				{showBulkEdit ? (
					<div>
						<div
							className="bulkCopyContainer"
							ref={dateRangeRef}
						>
							<div className="bulkCopyHeader">
								<p className="text-16-800">Bulk Edit</p>
								<Button
									iconPlacement="left"
									icon={<img src={InactiveCloseIcon} />}
									size="large"
									variant="url"
									onClick={() => bulkEditCloseHandler()}
								/>
							</div>
							<div className="bulkCopyBody">
								{global_configs?.event?.use_event ? <Select
									currentOptions={filteredOptions?.length ? filteredOptions : eventOptions}
									initialOptions={eventOptions}
									label={`${capitalizeFirstLetter(global_labels?.event_primary)} name`}
									labelOrientation="top"
									setSelectedOptions={eventNameChangeHandler}
									setCurrentOptions={setFilteredOptions}
									placeholder={`Select ${global_labels?.event_primary}`}
									isRequired={true}
									isWithSearch={true}
									isMulti={false}
									selectedOptions={selectedevent}
									isOpen={isOpen}
									setIsOpen={setIsOpen}
									isCloseWhenClickOutside={true}
								// isDisabled={!props.allowPromoEdit}
								/>
									: null}
								<DateRangePicker
									label={"Date Range"}
									isRequired={true}
									showRangeSelector={false}
									startDate={startDate}
									setStartDate={setStartDate}
									endDate={endDate}
									setEndDate={setEndDate}
									labelOrientation="top"
									startDateInputProps={{
										label: "StartDate",
										name: "start_date",
									}}
									endDateInputProps={{
										label: "EndDate",
										name: "end_date",
									}}
									minDate={selectedevent?.start_date || null}
									maxDate={selectedevent?.end_date || null}
									isOutsideRange={(date) =>
										(global_configs?.event?.use_event && 
											(
												moment(selectedevent?.start_date).isAfter(date.startOf("day")) ||
												moment(selectedevent?.end_date).isBefore(date.startOf("day"))
											)
										) || moment().isAfter(date)
									}
									isDisabled={global_configs?.event?.use_event && _.isEmpty(selectedevent)}
									displayFormat="MM-DD-YYYY"
									// orientation="horizontal"
									// withPortal={true}
								// anchorDirection="right"
								/>
							</div>
							<div className="bulkCopyFooter">
								<Button
									size="large"
									variant="url"
									onClick={onClosePanel}
								>
									Cancel
								</Button>
								<Button
									size="large"
									variant="primary"
									onClick={handleBulkEditApply}
								>
									Apply
								</Button>
							</div>
						</div>
					</div>
				) : null}
				{
					overallWarningMessage ? (
						<div className="warningMessageContainer">
							<div className="flexWithGap8">
								<img src={warningAlertIconSvg} alt="warning" width={"16px"} />
								<p className="text-14-600">
									{overallWarningMessage}
								</p>
							</div>
							<Button
								iconPlacement="left"
								icon={<img src={InactiveCloseIcon} />}
								size="large"
								variant="url"
								onClick={() => setOverallWarningMessage(null)}
							/>
						</div>
					) : null
				}
			</div>
		</Panel>
	);
}

export default CopyOfferPanel;
