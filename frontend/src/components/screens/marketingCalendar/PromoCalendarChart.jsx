import React, { useState, useEffect } from "react";
import { useSelector, useDispatch, connect } from "react-redux";
import moment from "moment";
import _ from "lodash";
import Calendar from "../../ui/calendar/Calendar";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "impact-ui";
import { formatChartData } from "./MarketingCalendarHelper";
import { capitalizeFirstLetter, replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";
import { toastError } from "../../../store/features/global/global";
import { DEFAULT_DATE_FORMAT, global_labels } from "../../../constants/Constants";
import RefreshIcon from "../../../assets/imageAssets/refreshIcon.svg?.url";
import CopyIcon from "../../../assets/imageAssets/copyIcon.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import ViewIcon from "../../../assets/imageAssets/eyeIcon.svg?.url";
import * as formatters from "../../../utils/helpers/formatter";

function PromoCalendarChart(props) {
	const dispatch = useDispatch();

	const {
        fiscalCalendar,
		currency_detail
    } = useSelector((store) => store?.pricesmartPromoReducer.global);

	const {
		marketingCalendarData = [],
	} = useSelector((store) => store?.pricesmartPromoReducer.marketingCalendar);

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	const [
		filteredMarketingCalendarChartData,
		setFilteredMarketingCalendarChartData,
	] = useState({});

	useEffect(() => {
		if (props.promoType.length > 0) {
			const selectedStatusValues = props.promoType.map(
				(status) => status.value
			);
			let filteredData = {};
			Object.keys(marketingCalendarData).forEach((key) => {
				filteredData[key] = formatChartData(
					marketingCalendarData[key].filter((promo) =>
						selectedStatusValues.includes(promo.status_id)
					)
				);
			});
			setFilteredMarketingCalendarChartData(filteredData);
		} else {
			setFilteredMarketingCalendarChartData([]);
		}
	}, [marketingCalendarData, props.promoType]);

	const getDetailContent = (item) => {
		return (
			<div className="event-detail-content">
				<div className="event-detail-title-container">
					<p className="event-detail-title">
						{item.name &&
						replaceSpecialCharacter(item.name).length > 34 ? (
							<Tooltip
								title={replaceSpecialCharacter(item.name)}
								placement="right"
								variant="tertiary"
							>
								<span title={item.name}>
									{replaceSpecialCharacter(
										item.name
									).substring(0, 34)}
									...
								</span>
							</Tooltip>
						) : (
							replaceSpecialCharacter(item.name)
						)}
					</p>
					<div className="event-detail-cta">
						<div className="centerFlexWithGap12">
							<Tooltip title={`View ${capitalizeFirstLetter(global_labels?.promo_alias)}`} variant="tertiary">
								{/* wrapper div for button to avoid the button property conflict with the tooltip */}
								<div>
									<Button
										iconPlacement="left"
										icon={<img src={ViewIcon} alt="view" />}
										onClick={() => {
											props.handleViewOffer([item]);
										}}
										size="small"
										variant="secondary"
									/>
								</div>
							</Tooltip>
							<Tooltip title={`Edit ${capitalizeFirstLetter(global_labels?.promo_alias)}`} variant="tertiary">
								<div>
									<Button
										iconPlacement="left"
										icon={<img src={EditIcon} alt="edit" />}
										onClick={() => {
											props.handleEditOffer([item]);
										}}
										size="small"
										variant="secondary"
									/>
								</div>
							</Tooltip>
							<Tooltip title={`Resimulate ${capitalizeFirstLetter(global_labels?.promo_alias)}`} variant="tertiary">
								<div>
									<Button
										iconPlacement="left"
										icon={<img src={RefreshIcon} alt="resimulate" />}
										onClick={() => {
											props.handleResimulate([item]);
										}}
										size="small"
										variant="secondary"
									/>
								</div>
							</Tooltip>
							{
								props.handleCopyOffer && (
									<Tooltip title={`Copy ${capitalizeFirstLetter(global_labels?.promo_alias)}`} variant="tertiary">
										<div>
											<Button
												iconPlacement="left"
												icon={<img src={CopyIcon} alt="copy" />}
												onClick={() => {
													props.handleCopyOffer([item]);
												}}
												size="small"
												variant="secondary"
											/>
										</div>
									</Tooltip>
								)
							}

							<Tooltip title={`Delete ${capitalizeFirstLetter(global_labels?.promo_alias)}`} variant="tertiary">
								<div>
									<Button
										iconPlacement="left"
										icon={<img src={DeleteIcon} alt="delete" />}
										onClick={() => {
											props.handleDelete([item]);
										}}
										size="small"
										variant="secondary"
										type="destructive"
										className='delete-button'
									/>
								</div>
							</Tooltip>
						</div>
					</div>
				</div>
				<div className="event-details">
					<div className="event-detail-column">
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">
								Date Range
							</p>
							<p className="event-detail-value">
								{moment(item.start_date).format(
									DEFAULT_DATE_FORMAT
								)}{" "}
								-{" "}
								{moment(item.end_date).format(
									DEFAULT_DATE_FORMAT
								)}
							</p>
						</div>
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">
								{capitalizeFirstLetter(global_labels?.promo_alias)} Comments
							</p>
							<p className="event-detail-value">
								{item.offer_comment}
							</p>
						</div>
					</div>

					<div className="event-detail-column">
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">Promo {currency_detail?.currency_symbol || "$"}</p>
							<p className="event-detail-value">
								{formatters["toCurrencyByCurrencyIdWithFormatThousands"]({
									...item,
									value: item.timeline_status === "Completed" ? item.actualized?.promo_spend : item.finalized?.promo_spend,
								})}
							</p>
						</div>
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">{capitalizeFirstLetter(global_labels?.promo_alias)}</p>
							<p className="event-detail-value">
								{item.timeline_status === "Completed" ? item.actualized?.discount : item.finalized?.discount}
							</p>
						</div>
					</div>

					<div className="event-detail-column">
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">
								Sales Units
							</p>
							<p className="event-detail-value">
								{formatters["toUnit"]({
									value: item.timeline_status === "Completed" ? item.actualized?.sales_units : item.finalized?.sales_units,
								})}
							</p>
						</div>
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">Revenue</p>
							<p className="event-detail-value">
								{formatters["toCurrencyByCurrencyIdWithFormatThousands"]({
									...item,
									value: item.timeline_status === "Completed" ? item.actualized?.revenue : item.finalized?.revenue,
								})}
							</p>
						</div>
					</div>

					<div className="event-detail-column">
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">Margin</p>
							<p className="event-detail-value">
								{formatters["toCurrencyByCurrencyIdWithFormatThousands"]({
									...item,
									value: item.timeline_status === "Completed" ? item.actualized?.margin : item.finalized?.margin,
								})}
							</p>
						</div>
						<div className="event-detail-value-container">
							<p className="event-detail-value-title">
								Performance
							</p>
							<p className="event-detail-value">
								{item.performance}
							</p>
						</div>
					</div>
				</div>
			</div>
		);
	};

	const getMinMaxDate = (type, year) => {
		let marketingCalendarFilters = filtersData.MARKETING_CALENDAR;
		const calCurrentStateTy = props?.calCurrentState?.ty;
		switch (type) {
			case "min":
				if (year === "ty") {
					return moment(
						marketingCalendarFilters?.dateRange?.start_date
					);
				} else if (year === "ly") {
					const fy = fiscalCalendar.find((y) => y.year === calCurrentStateTy.yr);
                    const fq = fy.fiscal_data?.quarters.find((q) => q.fiscal_quarter === calCurrentStateTy.qtr);
                    const fm = fq.months?.find((m) => m.fiscal_month === calCurrentStateTy.mo) || fq.months[0];
                    const fw = fm.weeks?.find((w) => w.fiscal_week === calCurrentStateTy.wk) || fm.weeks[0];
                    const f_date = fw.min_date;

                    return moment(f_date).subtract(1, "years");
				}
				break;
			case "max":
				if (year === "ty") {
					return moment(
						marketingCalendarFilters?.dateRange?.end_date
					);
				} else if (year === "ly") {
					return moment(
						marketingCalendarFilters?.dateRange?.end_date
					).subtract(1, "years");
				}
		}
	};

	return (
		<div>
			{props.showLastYear ? (
				<div className="marginTop-20 marginBottom-24">
					<p className="calendar-year-title">Last Year</p>
					<Calendar
						eventList={
							!_.isEmpty(filteredMarketingCalendarChartData["ly"])
								? filteredMarketingCalendarChartData["ly"]
								: []
						}
						isTrackingDeviceExternal={
							props.isTrackingDeviceExternal
						}
						calendarFrequency={props.aggregation}
						fiscalCalendar={props.fiscalCalendar}
						onCalEventClick={(e) =>
							props.onCalendarItemClick(e, "ly")
						}
						toastError={(error) => dispatch(toastError(error))}
						calCurrentState={props.calCurrentState}
						setCalCurrentState={props.setCalCurrentState}
						tab="promo"
						calDateRange="ly"
						calMinDate={getMinMaxDate("min", "ly")}
						calMaxDate={getMinMaxDate("max", "ly")}
						// handleAction={props.handleAction}
						// roleActions={props.roleActions}
						getDetailContent={getDetailContent}
						onBoundaryChange={(args) => props.onBoundaryChange(true, args)}
					/>
				</div>
			) : null}
			<div className="marginTop-20 marginBottom-24">
				<p className="calendar-year-title">This Year</p>
				<Calendar
					eventList={
						!_.isEmpty(filteredMarketingCalendarChartData["ty"])
							? filteredMarketingCalendarChartData["ty"]
							: []
					}
					isTrackingDeviceExternal={props.isTrackingDeviceExternal}
					calendarFrequency={props.aggregation}
					fiscalCalendar={props.fiscalCalendar}
					onCalEventClick={(e) => props.onCalendarItemClick(e, "ty")}
					toastError={(error) => dispatch(toastError(error))}
					calCurrentState={props.calCurrentState}
					setCalCurrentState={props.setCalCurrentState}
					tab="promo"
					calDateRange="ty"
					calMinDate={getMinMaxDate("min", "ty")}
					calMaxDate={getMinMaxDate("max", "ty")}
					// handleAction={props.handleAction}
					// roleActions={props.roleActions}
					getDetailContent={getDetailContent}
					onBoundaryChange={(args) => props.onBoundaryChange(false, args)}
				/>
			</div>
		</div>
	);
}

const mapStateToProps = (store) => {
	return {
		fiscalCalendar: store.pricesmartPromoReducer.global.fiscalCalendar,
	};
};

const mapDispatchToProps = (dispatch) => {
	return {};
};

export default connect(
	mapStateToProps,
	mapDispatchToProps
)(PromoCalendarChart);
