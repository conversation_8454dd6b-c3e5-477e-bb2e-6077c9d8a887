import React, { useState, useEffect, useRef, useCallback } from "react";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import {
	ButtonGroup,
	FileUpload,
	TextArea,
	Prompt,
	Modal,
	Button,
	Input,
	Badge,
} from "impact-ui";
import { Table } from "impact-ui-v3";
import ComponentFilters from "../../../ui/componentFilters/ComponentFilters";
import EmptyData from "../../../ui/emptyData/EmptyData";
import {
	resetAllFiltersData,
	overwriteFilters,
} from "../../../../store/features/filters/filters";

import {
	filterTypeButtonStore,
	createStoreFilterConfig,
	createStoreTableConfig,
	invalidDataColumnConfigStore,
} from "./createEventConstants";

import {
	callStoreTableDataApi,
	getCreateStoreGrpDataFromExcel,
	getCreateStoreDataFromCopyPaste,
	setStoreFilterTableData,
	setTableDataFromUploadOrCopyPasteStore,
} from "../../../../store/features/eventReducer/eventReducer";

import {
	mergeFiltersData,
	downloadCSV,
	fabricatePayloadHierarchy,
} from "../../../../utils/helpers/utility_helpers";
import SearchIcon from "../../../../assets/imageAssets/searchIcon.svg?.url";

import {
	excelTemplateDownload,
	requestStart,
	requestComplete,
	toastError,
} from "../../../../store/features/global/global";
import StoreGroupSelection from "./StoreGroupSelection";

function SpecificStoreSelection(props) {
	const dispatch = useDispatch();
	const tableRef = useRef();

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);
	const {
		createEventStoreTableData = [],
		tableDataFromUploadOrCopyPasteStore = [],
	} = useSelector(
		(store) => store?.pricesmartPromoReducer.event
	);

	const {
		setSelectedStores,
		setSelectedSpecificStoreType,
		clearOnUnmount = true,
	} = props

	const [selectedFilterType, setSelectedFilterType] = useState(props?.selectedSpecificStoreType || "hierarchy");

	const [showGlobalSearch, setShowGlobalSearch] = useState(false);
	const [globalSearchText, setGlobalSearchText] = useState("");
	const [storeTableData, setStoreTableData] = useState([]);
	const [selectedStoresTableData, setSelectedStoresTableData] = useState(props?.selectedStores || []);
	const [uploadedExcel, setUploadedExcel] = useState([]);
	const [lastFiltersData, setLastFiltersData] = useState({});
	const [showDataOverridePrompt, setShowDataOverridePrompt] = useState(false);
	const [showInvalidDataModal, setShowInvalidDataModal] = useState(false);
	const [invalidTableData, setInvalidTableData] = useState([]);
	const [copyPasteData, setCopyPasteData] = useState("");
	const [tempTableData, setTempTableData] = useState([]);
	const [flagToStopRepeatedSetData, setFlagToStopRepeatedSetData] = useState(false);
	const [storeColumnDefs, setStoreColumnDefs] = useState(createStoreTableConfig);

	useEffect(() => {
		return () => {
			dispatch(
				resetAllFiltersData({
					from: "CREATE_EVENT_STORE_CONFIG_COMPONENT",
				})
			);
			if (clearOnUnmount) {
				dispatch(setStoreFilterTableData([]));
				dispatch(setTableDataFromUploadOrCopyPasteStore([]));
			}
		};
	}, []);

	useEffect(() => {
		// if store data is fetched from API by selecting the filters, set it in table
		if (!_.isEmpty(createEventStoreTableData)) {
			// if there is no previous data in table, set the fetched data
			handleSetStoreTableData(createEventStoreTableData);
		}
	}, [createEventStoreTableData]);

	useEffect(() => {
		if (props?.selectedStores?.length > 0 && _.isEmpty(createEventStoreTableData)) {
			setSelectedStoresTableData(props?.selectedStores);
			setStoreFilterTableData(props?.selectedStores);
		}
	}, [props?.selectedStores]);

	useEffect(() => {
		// if store data is fetched from API by uploading excel or copy paste, set it in table
		if (
			(selectedFilterType === "upload" ||
				selectedFilterType === "copy_paste") &&
			!_.isEmpty(tableDataFromUploadOrCopyPasteStore)
		) {
			// if there are invalid or inactive stores, show modal with invalid and inactive stores
			if (
				tableDataFromUploadOrCopyPasteStore?.invalid?.length ||
				tableDataFromUploadOrCopyPasteStore?.inactive?.length
			) {
				const newTableData = [
					...tableDataFromUploadOrCopyPasteStore?.invalid || [],
					...tableDataFromUploadOrCopyPasteStore?.inactive || [],
				];
				setInvalidTableData(_.cloneDeep(newTableData));
				setShowInvalidDataModal(true);
			} else if (tableDataFromUploadOrCopyPasteStore?.valid?.length) {
				// if there are only valid stores, set it in table
				handleSetStoreTableData(tableDataFromUploadOrCopyPasteStore.valid);
			}
		}
	}, [tableDataFromUploadOrCopyPasteStore]);

	useEffect(() => {
		if (props?.viewMode) {
			let def = _.cloneDeep(createStoreTableConfig);
			def = _.filter(def, (item) => item.field !== "" && !item.checkboxSelection);
			setStoreColumnDefs(def);
		} else {
			setStoreColumnDefs(createStoreTableConfig);
		}
	}, [props?.viewMode]);


	const onComponentFiltersApply = () => {
		let callAPI = true;
		// check if all mandatory filters are selected
		const filtersDataSelected = _.cloneDeep(
			filtersData["CREATE_EVENT_STORE_CONFIG_COMPONENT"]
		);
		_.forEach(createStoreFilterConfig, (config) => {
			const key = config.filterId;
			if (
				config.isMandatory &&
				!filtersDataSelected?.[key]?.selectedOptionsArray?.length
			) {
				callAPI = false;
			}
		});
		if (!callAPI) {
			dispatch(toastError("Please select all mandatory filters"));
			return;
		}
		// set last filters data for replace and retain prompt
		if (_.isEmpty(lastFiltersData))
			setLastFiltersData(_.cloneDeep(filtersDataSelected));
		// if all mandatory filters are selected, call API to get store data
		const payload = fabricatePayloadHierarchy(filtersDataSelected);

		dispatch(callStoreTableDataApi(payload));
		setFlagToStopRepeatedSetData(false);
	};

	const onClearFilter = () => {
		// clear filters data for store configuration
		dispatch(
			resetAllFiltersData({
				from: "CREATE_EVENT_STORE_CONFIG_COMPONENT",
			})
		);
	};

	const handleSetStoreTableData = (data) => {
		if (flagToStopRepeatedSetData) {
			return;
		}
		if (storeTableData?.length) {
			setShowDataOverridePrompt(true);
			setTempTableData(data);
		} else if (
			(selectedFilterType === "upload" || selectedFilterType === "copy_paste")
		) {
			dispatch(setStoreFilterTableData(_.cloneDeep(data)));
			dispatch(setTableDataFromUploadOrCopyPasteStore({}));
			setStoreTableData(data);
		} else
			setStoreTableData(data);
		setFlagToStopRepeatedSetData(true);
	};

	const onRowSelection = useCallback(() => {
		// set selected stores data
		const selectedRows = tableRef.current.api.getSelectedRows();
		setSelectedStoresTableData(_.cloneDeep(selectedRows));
		setSelectedStores(_.cloneDeep(selectedRows));
	});

	const rowDataChanged = () => {
		// whenever row data updates in AG Grid, select all rows
		if (tableRef?.current?.api) tableRef.current.api.selectAll();
	};

	const onReplaceAndOverwriteClick = () => {
		// replace table data with current selection and set last filters data same as current selection
		setStoreTableData(tempTableData);
		setShowDataOverridePrompt(false);
		// adding this as the dispatch will trigger the useEffect then the handleSetStoreTableData will be called
		// and it will again set the same data in table, so we need to stop that
		setFlagToStopRepeatedSetData(true);
		dispatch(setStoreFilterTableData(_.cloneDeep(tempTableData)));
		if (selectedFilterType === "upload" || selectedFilterType === "copy_paste") {
			dispatch(setTableDataFromUploadOrCopyPasteStore({}));
		}
		setTempTableData([]);
		setLastFiltersData(filtersData["CREATE_EVENT_STORE_CONFIG_COMPONENT"]);
	};

	const onRetainClick = () => {
		//concat current data with previous data, and set it in table
		let currentRows = _.cloneDeep(storeTableData);
		const tempRows = _.cloneDeep(tempTableData || []).map(item => ({
			...item,
			// convert store_id to number as on upload it is string, and _.uniqBy is not working with string
			store_id: Number(item.store_id)
		}));
		currentRows = [...currentRows, ...tempRows];
		currentRows = _.uniqBy(currentRows, "store_id");
		setStoreTableData(_.cloneDeep(currentRows));
		setShowDataOverridePrompt(false);
		setTempTableData([]);
		// adding this as the dispatch will trigger the useEffect then the handleSetStoreTableData will be called
		// and it will again set the same data in table, so we need to stop that
		setFlagToStopRepeatedSetData(true);
		dispatch(
			setStoreFilterTableData(_.cloneDeep(currentRows))
		);
		if (
			(selectedFilterType === "upload" || selectedFilterType === "copy_paste")
		) {
			dispatch(setTableDataFromUploadOrCopyPasteStore({}));
		}
		// concat currennt data with previous data and set both last filters and current filters as same
		const updatedFiltersData = mergeFiltersData(
			_.cloneDeep(lastFiltersData),
			_.cloneDeep(filtersData["CREATE_EVENT_STORE_CONFIG_COMPONENT"])
		);
		setLastFiltersData(_.cloneDeep(updatedFiltersData));
		dispatch(
			overwriteFilters({
				filtersData: _.cloneDeep(updatedFiltersData),
				activeScreen: "CREATE_EVENT_STORE_CONFIG_COMPONENT",
			})
		);
	};

	const onFilterTypeTabChange = (e) => {
		// set selected filter type and reset all filters data if filter type is not select filters
		setSelectedFilterType(e.target.value);
		setSelectedSpecificStoreType(e.target.value);
		if (e.target.value !== "hierarchy") {
			dispatch(
				resetAllFiltersData({
					from: "CREATE_EVENT_STORE_CONFIG_COMPONENT",
				})
			);
		}
		setUploadedExcel([]);
		setCopyPasteData("");
	};

	const onUploadExcelNextClick = async (file) => {
		// on next click of excel upload, call API to get store data
		if (!file.file) return;
		const formData = new FormData();
		formData.append("file", file.file);
		setFlagToStopRepeatedSetData(false);
		const responseFlag = await dispatch(
			getCreateStoreGrpDataFromExcel(formData)
		);
		// if store data is not fetched, reset uploaded excel data
		if (!responseFlag) {
			setUploadedExcel([]);
		}
	};

	const onActiveInactiveStoreProceed = () => {
		// on proceed click of invalid stores modal, set valid and inactive stores in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		const newTableData = [
			...tableDataFromUploadOrCopyPasteStore?.valid || [],
			...tableDataFromUploadOrCopyPasteStore?.inactive || [],
		];
		handleSetStoreTableData(newTableData);
	};

	const onActiveStoreProceed = () => {
		// on proceed click of invalid stores modal, set only valid stores in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		handleSetStoreTableData(tableDataFromUploadOrCopyPasteStore.valid || []);
	};

	const onCopyPasteChange = (e) => {
		// set copy paste data in state
		const data = e.target.value;
		let array = _.filter(data.split("\n"), (item) => item !== "");
		let val = array.join(",");
		setCopyPasteData(val);
	};

	const onCopyPasteSubmitClick = () => {
		// on submit click of copy paste, call API to get store data
		let storeIds = copyPasteData.split(",");
		storeIds = _.map(storeIds, (value) => value.trim());
		storeIds = _.filter(storeIds, (item) => item !== "").map(item => [...item.split(" ")]);
		const payload = { stores_data: storeIds };
		dispatch(getCreateStoreDataFromCopyPaste(payload));
		setFlagToStopRepeatedSetData(false);
		setCopyPasteData("");
	};

	const excelDownloadHandler = () => {
		// download excel template for store selection
		const payload = {
			type: "store_group_upload",
		};
		dispatch(
			excelTemplateDownload(
				{
					params: payload,
					responseType: "blob",
				},
				"store_selection_template"
			)
		);
	};

	const csvDownloadHandler = () => {
		// download csv template for store selection
		dispatch(requestStart());
		downloadCSV(
			[
				{
					["Store ID"]: "123",
				},
			],
			"store_selection_template",
			","
		);
		dispatch(requestComplete("Template downloaded successfully"));
	};

	const onFilterTextBoxChanged = useCallback((text) => {
		setGlobalSearchText(text);
		tableRef.current.api.setGridOption("quickFilterText", text);
	}, []);

	const onFirstDataRendered = (params) => {
		const nodesToSelect = [];
		const selected_ids = props?.selectedStores?.map((group) => group.store_id);
		params.api.forEachNode((node) => {
			if (selected_ids.includes(node.data?.store_id)) {
				nodesToSelect.push(node);
			}
		});
		params.api.setNodesSelected({ nodes: nodesToSelect, newValue: true });
	};

	return (
		<div>
			<div className="storeConfigSelectionContainer marginTop-24">
				<ButtonGroup
					selectedOption={selectedFilterType}
					onChange={onFilterTypeTabChange}
					options={filterTypeButtonStore}
				/>
				<p className="text-12-500 marginTop-12">
					Please select one of the methods for uploading data to get
					results
				</p>
				<div className="marginTop-24">
					{selectedFilterType === "hierarchy" && (
						<div className="flexColumn flex24">
							<ComponentFilters
								filterConfig={createStoreFilterConfig}
								callAPIonLoad={false}
								screen="CREATE_EVENT_STORE_CONFIG_COMPONENT"
								onPrimaryButtonClick={onComponentFiltersApply}
								onSecondaryButtonClick={onClearFilter}
								primaryButtonText="Submit"
								secondaryButtonText="Clear Filters"
							/>
						</div>
					)}
					{selectedFilterType === "upload" && (
						<FileUpload
							fileList={uploadedExcel}
							numberOfFiles={1}
							onSecondaryButtonClick={() => setUploadedExcel([])}
							onFileListChange={(files) => {
								setUploadedExcel(files);
								onUploadExcelNextClick(files?.[0] || {});
							}}
							validFileTypes={[
								{
									fileType: "xlsx",
									templateDownloader: excelDownloadHandler,
									typeOverride: false,
								},
								{
									fileType: "csv",
									templateDownloader: csvDownloadHandler,
									typeOverride: false,
								},
							]}
						/>
					)}
					{selectedFilterType === "copy_paste" && (
						<div>
							<TextArea
								onChange={onCopyPasteChange}
								placeholder="Paste IDs here..."
								value={copyPasteData}
								width={"575px"}
							/>
							<div className="buttons_container">
								<Button
									onClick={() => setCopyPasteData("")}
									size="large"
									variant="url"
								>
									Reset
								</Button>
								<Button
									onClick={onCopyPasteSubmitClick}
									size="large"
									variant="primary"
									disabled={!copyPasteData?.length}
								>
									Submit
								</Button>
							</div>
						</div>
					)}
					{selectedFilterType === "store_group" && (
						<StoreGroupSelection
							setSelectedStoreGroups={props?.setSelectedStoreGroups}
							selectedStoreGroups={props?.selectedStoreGroups}
							clearOnUnmount={false}
							viewMode={props?.viewMode}
						/>
					)}
				</div>
				{selectedFilterType !== "store_group" && (
					<div className="marginTop-24">
						{storeTableData?.length > 0 ? (
							<div>
								<Table
									tableHeader={"Stores"}
									ref={tableRef}
									suppressMenuHide
									rowData={storeTableData}
									columnDefs={storeColumnDefs}
									rowSelection="multiple"
									onSelectionChanged={onRowSelection}
									// onRowDataUpdated={rowDataChanged}
									onFirstDataRendered={onFirstDataRendered}
									topRightOptions={
										<div className="centerFlexWithGap12">
											<div className="positionRelative">
												{showGlobalSearch ? (
													<div className="tableGlobalSearchContainer">
														<Input
															onChange={(e) =>
																onFilterTextBoxChanged(
																	e.target.value
																)
															}
															placeholder="Search"
															rightIcon={
																<img
																	src={SearchIcon}
																/>
															}
															type="text"
															value={globalSearchText}
														/>
													</div>
												) : null}
												<Button
													iconPlacement="left"
													icon={<img src={SearchIcon} />}
													onClick={() =>
														setShowGlobalSearch(
															(prev) => !prev
														)
													}
													size="large"
													variant="tertiary"
												/>
											</div>
										</div>
									}
									topLeftOptions={
										<Badge
											color="default"
											label={`Selected Stores: ${selectedStoresTableData?.length} / ${storeTableData?.length}`}
											size="default"
											variant="subtle"
										/>
									}
								/>
							</div>
						) : (
							<EmptyData text="Please Select Filters to view Stores" />
						)}
					</div>
				)}
			</div>
			<Prompt
				handleClose={() => {
					setTempTableData([]);
					setShowDataOverridePrompt(false);
				}}
				onPrimaryButtonClick={onRetainClick}
				onSecondaryButtonClick={onReplaceAndOverwriteClick}
				primaryButtonLabel="Retain"
				secondaryButtonLabel="Replace & overwrite"
				title="Confirm Selection"
				variant="warning"
				isOpen={showDataOverridePrompt}
			>
				Do you want to Retain and Continue or Replace table with current
				selection
			</Prompt>
			<Modal
				onClose={() => {
					setShowInvalidDataModal(false);
					setInvalidTableData([]);
				}}
				onPrimaryButtonClick={onActiveInactiveStoreProceed}
				onSecondaryButtonClick={onActiveStoreProceed}
				primaryButtonLabel="Proceed with all Active and Inactive Stores"
				secondaryButtonLabel="Proceed with only Active Stores"
				size="medium"
				title="Stores Detail"
				open={showInvalidDataModal}
			>
				<div className="invalidStoresGrpModalContainer">
					<span className="secondaryText-14-500">
						Based on the data feed Pricesmart received, the uploaded
						list contains some inactive/invalid Stores
					</span>
					<Table
						tableHeader={"Stores"}
						suppressMenuHide
						rowData={invalidTableData}
						columnDefs={invalidDataColumnConfigStore}
					/>
				</div>
			</Modal>
		</div>
	);
}

export default SpecificStoreSelection;
