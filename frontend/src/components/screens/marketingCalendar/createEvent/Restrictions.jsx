import React, { useEffect, useState } from "react";
import {
    FilterPanel,
    Button,
    DateRange<PERSON>icker,
    DatePicker,
    ButtonGroup,
    Input,
    Tooltip,
} from "impact-ui";
import {
    productFilterConfig,
    restrictionsFilters,
} from "./createEventConstants";
import SetRestrictionImage from "../../../../assets/imageAssets/setRestrictionImage.png";

import Save from "../../../../assets/imageAssets/save.svg?.url";

import LockOpen from "../../../../assets/imageAssets/lock-open.svg?.url";
import Locked from "../../../../assets/imageAssets/locked.svg?.url";
import CalendarIcon from "../../../../assets/imageAssets/calendarIcon.svg?.url";
import Store from "../../../../assets/imageAssets/store.svg?.url";
import Product from "../../../../assets/imageAssets/product.svg?.url";
import PercentOff from "../../../../assets/imageAssets/percentOff.svg?.url";
import GroupPeople from "../../../../assets/imageAssets/groupPeople.svg?.url";

import moment from "moment";
import ComponentFilters from "../../../ui/componentFilters/ComponentFilters";
import { useDispatch, useSelector } from "react-redux";
import {
    resetAllFiltersData,
    setSelectedFilters,
} from "../../../../store/features/filters/filters";
import SpecficProductSelection from "./SpecficProductSelection";
import {
    setEventProductGroupData,
    setEventProductTableData,
    setEventStoreGroupData,
    setStoreFilterTableData,
    setTableDataFromUploadOrCopyPaste,
    setTableDataFromUploadOrCopyPasteStore,
    updateEventDetails,
} from "../../../../store/features/eventReducer/eventReducer";
import SpecificStoreSelection from "./SpecificStoreSelection";
import ProductGroupSelection from "./ProductGroupSelection";
import _ from "lodash";
import { global_labels } from "../../../../constants/Constants";
import { productSelectionOptions, storeSelectionOptions } from "../../offer/OfferConstants";
import ISymbol from "../../../../assets/imageAssets/ISymbol.svg?.url";

const filterIcons = {
    date_restriction: <img src={CalendarIcon} alt="calendar" />,
    store_restriction: <img src={Store} alt="store" />,
    product_restriction: <img src={Product} alt="product" />,
    customer_restriction: <img src={GroupPeople} alt="group people" />,
    promotion_restriction: <img src={PercentOff} alt="percent off" />,
};

const Restrictions = (props) => {
    const dispatch = useDispatch();

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const { 
        eventDetails,
    } = useSelector(
        (store) => store?.pricesmartPromoReducer.event
    );

    const  {
        start_date,
        end_date,
    } = props;


    const {
        date_restriction,
        product_restriction,
        store_restriction,
    } = useSelector((store) => {
        return store?.pricesmartPromoReducer.event?.eventDetails;
    });

    const [activeGroup, setActiveGroup] = useState("product_restriction");
    const [errMsg, setErrMsg] = useState("");

    // Date Restriction specific states
    const [isRestrictionPanelOpen, setIsRestrictionPanelOpen] = useState(false);
    const [panelFiltersData, setPanelFiltersData] = useState([]);
    const [sameAsEvent, setSameAsEvent] = useState("");
    const [minPromotionDays, setMinPromotionDays] = useState("");
    const [maxPromotionDays, setMaxPromotionDays] = useState("");
    const [promotionStartDay, setPromotionStartDay] = useState(null);
    const [promotionEndDay, setPromotionEndDay] = useState(null);
    const [eventDays, setEventDays] = useState(0);

    // Product Restriction specific states
    const [productRestrictionOption, setProductRestrictionOption] = useState();
    const [productRestrictionLock, setProductRestrictionLock] = useState(false);
    const [selectedSpecificProducts, setSelectedSpecificProducts] = useState([]);
    const [selectedProductGroups, setSelectedProductGroups] = useState([]);
    const [selectedSpecificProductType, setSelectedSpecificProductType] = useState("hierarchy");

    // Store Restriction specific states
    const [storeRestrictionOption, setStoreRestrictionOption] = useState();
    const [storeRestrictionLock, setStoreRestrictionLock] = useState(false);
    const [selectedSpecificStores, setSelectedSpecificStores] = useState([]);
    const [selectedStoreGroups, setSelectedStoreGroups] = useState([]);
    const [selectedSpecificStoreType, setSelectedSpecificStoreType] = useState("hierarchy");

    const lockToolTip = `Lock ${global_labels?.promo_alias} values to match ${global_labels?.event_primary} values, preventing changes during ${global_labels?.promo_alias} creation`;

    useEffect(() => {
        createFilters();
    }, []);

    useEffect(() => {
        if (!isRestrictionPanelOpen) {
            clearAll();
        } else {
            if (!_.isEmpty(date_restriction?.sameAsEvent)) {
                setMinPromotionDays(date_restriction?.minPromotionDays || "");
                setSameAsEvent(date_restriction?.sameAsEvent || null);
                setMaxPromotionDays(date_restriction?.maxPromotionDays || "");
                setPromotionStartDay(date_restriction?.promotionStartDay ? 
                    moment(date_restriction?.promotionStartDay) : null);
                setPromotionEndDay(date_restriction?.promotionEndDay
                        ? moment(date_restriction?.promotionEndDay): null);
            }
            if (!_.isEmpty(product_restriction?.product_restriction_level)) {
                const {
                    product_restriction_level,
                    lock,
                    specific_product_type,
                    products,
                    product_groups,
                    product_hierarchy,
                } = _.cloneDeep(product_restriction);
                setProductRestrictionLock(lock);
                setProductRestrictionOption(product_restriction_level || null);
                if (product_restriction_level == "whole_category") {
                    Object.keys(
                        product_hierarchy || {}
                    ).forEach((key) => {
                        dispatch(
                            setSelectedFilters({
                                data: product_hierarchy?.[key] || [],
                                filterId: key,
                                from: "PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT",
                            })
                        );
                    });
                } else if (
                    product_restriction_level == "specific_products"
                ) {
                    setSelectedSpecificProducts(products);
                    dispatch(
                        setEventProductTableData(products)
                    );
                } else if (product_restriction_level == "product_group") {
                    setSelectedProductGroups(product_groups);
                    dispatch(
                        setEventProductGroupData(product_groups)
                    );
                } else {
                    setSelectedSpecificProducts([]);
                    setSelectedProductGroups([]);
                }
                setSelectedSpecificProductType(specific_product_type || "hierarchy");
            }

            if (!_.isEmpty(store_restriction)) {
                const {
                    store_restriction_level,
                    lock,
                    specific_store_type,
                    stores,
                    store_groups,
                } = _.cloneDeep(store_restriction);
                let specificStoreSelection = specific_store_type || "hierarchy";
                let storeRestrictionLevel = store_restriction_level || null;
                if (store_restriction_level === "store_group") {
                    storeRestrictionLevel = "specific_stores";
                    specificStoreSelection = "store_group";
                }
                setStoreRestrictionLock(lock);
                setStoreRestrictionOption(storeRestrictionLevel);
                setSelectedSpecificStoreType(specificStoreSelection);
                if (store_restriction_level == "specific_stores") {
                    setSelectedSpecificStores(stores);
                    dispatch(
                        setStoreFilterTableData(stores)
                    )
                } else if (
                    store_restriction_level == "store_group" ||
                    specific_store_type == "store_group"
                ) {
                    setSelectedStoreGroups(store_groups)
                    setSelectedSpecificStoreType("hierarchy");
                    dispatch(
                        setEventStoreGroupData(store_groups)
                    );
                    setSelectedSpecificStoreType("store_group");
                } else {
                    setSelectedSpecificStores([]);
                    setSelectedStoreGroups([]);
                }
            }
        }
    }, [isRestrictionPanelOpen]);

    useEffect(() => {
        calculateDays();
    }, [
        start_date, 
        end_date, 
        promotionStartDay, 
        promotionEndDay
    ]);

    useEffect(() => {
        createFilters();
    }, [
        start_date,
        end_date,
        sameAsEvent,
        minPromotionDays,
        maxPromotionDays,
        promotionStartDay,
        promotionEndDay,

        productRestrictionOption,
        productRestrictionLock,
        selectedSpecificProducts,
        selectedProductGroups,
        selectedSpecificProductType,
        selectedProductGroups,

        storeRestrictionOption,
        storeRestrictionLock,
        selectedSpecificStores,
        selectedStoreGroups,
        selectedSpecificStoreType,
        selectedStoreGroups,

        errMsg,
    ]);

    const calculateDays = () => {
        const startDate = promotionStartDay || start_date;
        const endDate = promotionEndDay || end_date;
        let noOfDays = 0;
        if (startDate && endDate) {
            let start = moment(startDate);
            let end = moment(endDate);
            noOfDays = end.diff(start, "days") + 1;
        }
        setEventDays(noOfDays);
        if (minPromotionDays > noOfDays) {
            setMinPromotionDays(noOfDays);
        }
        if (maxPromotionDays > noOfDays) {
            setMaxPromotionDays(noOfDays);
        }
    };

    const createFilters = () => {
        if (restrictionsFilters.length) {
            let panelFiltersData = [];
            _.forEach(restrictionsFilters, (config, ind) => {
                panelFiltersData.push({
                    ...config,
                    icon: filterIcons[config?.value],
                    children:
                        <div>
                            <div className="label-12px-normal error-msg paddingBottom-8">{errMsg}</div>
                            {filterContent(config?.value)}
                        </div>,
                    // numberOfFilter: getCount(config?.value),
                    // If the need arises for a custom filter title, use the below code and some style changes in scss file
                    // title:
                    //     <div className="display_flex filter-title-container">
                    //         <span className="filter-title">{config.title}</span>
                    //         <div className="flexWithGap8">
                    //             <div className="impact_drawer_filter_tab_badges" style={{display: "unset"}}> {Math.floor(Math.random() * 10)} </div>
                    //             {hasLock(config.value) &&
                    //                 <img src={Locked} width={"12px"}/>
                    //             }
                    //             {hasSavedData(config.value) &&
                    //                 <img src={Save} width={"12px"}/>
                    //             }
                    //         </div>
                    //     </div>
                });
            });
            setPanelFiltersData(panelFiltersData);
            addSavedLockedIcon();
        }
    };

    const getCount = (filterKey) => {
        let count = 0;

        const validKeys = {
            date_restriction: ["sameAsEvent","minPromotionDays","maxPromotionDays","promotionStartDay","promotionEndDay",],
            product_restriction: ["productRestrictionOption", "productRestrictionLock"],
            store_restriction: ["storeRestrictionOption", "storeRestrictionLock"]
        }

        const filter = eventDetails?.[filterKey] || null
        Object.keys(filter || {}).forEach(key => {
            if (
                (filter?.[key] || filter?.[key] === 0)
                && validKeys?.[filterKey].includes(key)
            ) count += 1;
        })
        return count
    }
    
    const addSavedLockedIcon = () => {
        const filterTabs = document.querySelectorAll(".impact_drawer_filter_tab");
        filterTabs.forEach((element, ind) => {
            const config = restrictionsFilters?.[ind]
            if (config) {
                if (hasLock(config.value)) 
                    element.classList.add('locked-filter');
                if (hasSavedData(config.value))
                    element.classList.add("saved-filter")
            }
        })
    }

    const clearAll = (type = "all") => {
        // Date Restriction 
        if (type == "date_restriction" || type == "all") clearDateRestriction();

        // Product Restriction
        if (type == "product_restriction" || type == "all") clearProductRestriction();

        // Store Restriction
        if (type == "store_restriction" || type == "all") clearStoreRestriction();

        // Common
        setErrMsg("");
    };

    const clearDateRestriction = () => {
        setSameAsEvent("");
        setMinPromotionDays("");
        setMaxPromotionDays("");
        setPromotionStartDay(null);
        setPromotionEndDay(null);
    }

    const clearProductRestriction = () => {
        setProductRestrictionOption();
        setProductRestrictionLock(false);
        setSelectedSpecificProducts([]);
        setSelectedProductGroups([]);
        setSelectedSpecificProductType("hierarchy");
        dispatch(
            resetAllFiltersData({
                from: "PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT",
            })
        );
        dispatch(setEventProductTableData([]));
        dispatch(setTableDataFromUploadOrCopyPaste([]));
        dispatch(setEventProductGroupData([]));
        dispatch(
            resetAllFiltersData({
                from: "CREATE_EVENT_PRODUCT_GROUP",
            })
        );
    }

    const clearStoreRestriction = () => {
        setStoreRestrictionOption();
        setStoreRestrictionLock(false);
        setSelectedSpecificStores([]);
        setSelectedStoreGroups([]);
        setSelectedSpecificStoreType("hierarchy");
        dispatch(
            resetAllFiltersData({
                from: "CREATE_EVENT_STORE_GROUP_RESTRICITON",
            })
        );
        dispatch(setStoreFilterTableData([]));
        dispatch(setEventStoreGroupData([]));
        dispatch(setTableDataFromUploadOrCopyPasteStore([]));
    }

    // if need arises for a custom filter title
    const hasLock = (value) => {
        if (value == "product_restriction") {
            return productRestrictionLock || product_restriction?.lock;
        } else if (value == "store_restriction") {
            return storeRestrictionLock || store_restriction?.lock;
        }
        return false;
    }
    // if need arises for a custom filter title
    const hasSavedData = (value) => {
        if (value == "date_restriction" && !_.isEmpty(date_restriction)) {
            const {
                sameAsEvent = false,
                minPromotionDays = false,
                maxPromotionDays = false,
                promotionStartDay = false,
                promotionEndDay = false,
            } = date_restriction;
            if (sameAsEvent || minPromotionDays || maxPromotionDays || promotionStartDay || promotionEndDay) {
                return true;
            }
        } else if (value == "product_restriction" && !_.isEmpty(product_restriction)) {
            const {
                product_restriction_level = false,
                lock = false,
                products = [],
                product_groups = [],
                selectedSpecificProductType = false,
            } = product_restriction;
            if (product_restriction_level || lock || products?.length > 0 || product_groups?.length > 0) {
                return true;
            }
        } else if (value == "store_restriction" && !_.isEmpty(store_restriction)) {
            const {
                store_restriction_level = false,
                lock = false,
                stores = [],
                store_groups = [],
                selectedSpecificStoreType = false,
            } = store_restriction;
            if (store_restriction_level || lock || stores?.length > 0 || store_groups?.length > 0) {
                return true;
            }
        }
        return false
    }

    const dateRestrictionFilter = () => {
        return (
            <div>
                <div className="flexContentAround">
                    <p className="secondaryText-16-500">Select attributes</p>
                    <div className="flexWithGap8">
                        <Button 
                            variant="text"
                            size="large"
                            onClick={() => handleIndividualRestrictionClear("date_restriction")}
                            iconPlacement="left"
                        >
                            Clear
                        </Button> 
                        <Button 
                            variant="tertiary"
                            size="large"
                            onClick={() => handleIndividualRestrictionSave("date_restriction")}
                            iconPlacement="left"
                        >
                            Save
                        </Button> 
                    </div>
                </div>
                <div className="marginTop-20">
                    {(!start_date || !end_date) && (
                        <div className="label-12px-normal paddingBottom-8 error-msg">
                            * Please select the {global_labels.event_primary} date range for date restriction. 
                        </div>
                    )}
                    <DateRangePicker
                        label="Date Range"
                        startDate={
                            start_date ?
                                moment(start_date)
                                : null
                        }
                        endDate={
                            end_date ?
                                moment(end_date)
                                : null
                        }
                        isDisabled
                        startDateInputProps={{
                            label: "Start Date",
                            name: "startDate",
                        }}
                        endDateInputProps={{
                            label: "End Date",
                            name: "endDate",
                        }}
                        displayFormat="MM-DD-YYYY"
                    />
                </div>
                <p className="secondaryText-14-500 marginTop-20">Restrict {global_labels?.promo_alias} date</p>
                <div className="marginTop-20 grid-row-gap-24 grid-40-60">
                    <span className="text-14-500">Same as {global_labels.event_primary}</span> 
                    <ButtonGroup
                        onChange={(_e, parms) => {
                            setSameAsEvent(parms);
                        }}
                        options={[
                            {
                                label: "Yes",
                                value: "yes",
                            },
                            {
                                label: "No",
                                value: "no",
                            },
                        ]}
                        selectedOption={sameAsEvent}
                        isDisabled={_.isEmpty(start_date) || _.isEmpty(end_date)}
                    />
                    <span className="text-14-500">Minimum promotion days</span>
                    <Input
                        id="min_promotion_days"
                        inputProps={{}}
                        onChange={(e) => {
                            let value = e.target.value;
                            setMinPromotionDays(value);
                        }}
                        onBlur={(e) => {
                            let value = e.target.value;
                            if (+e.target.value < 0) value = 0;
                            else if (+e.target.value > eventDays) value = eventDays;
                            setMinPromotionDays(value);
                            if (value !== "" && maxPromotionDays < value && maxPromotionDays !== "") setMaxPromotionDays(value);
                        }}
                        placeholder="Please enter number"
                        type="number"
                        value={minPromotionDays}
                        disabled={
                            _.isEmpty(start_date) || _.isEmpty(end_date) || sameAsEvent == "yes"
                        }
                    />
                    <span className="text-14-500">Maximum promotion days</span>
                    <Input
                        id="max_promotion_days"
                        inputProps={{}}
                        onChange={(e) => {
                            let value = e.target.value;
                            setMaxPromotionDays(value);
                        }}
                        onBlur={(e) => {
                            let value = e.target.value;
                            if (+e.target.value > eventDays) {
                                value = eventDays;
                            } else if (+e.target.value < 0) {
                                value = 0;
                            } else if (+e.target.value < +minPromotionDays && value !== "") {
                                value = minPromotionDays;
                            }
                            setMaxPromotionDays(value);
                        }}
                        placeholder="Please enter number"
                        type="number"
                        value={maxPromotionDays}
                        disabled={
                            _.isEmpty(start_date) || _.isEmpty(end_date) || sameAsEvent == "yes"
                        }
                    />
                    <span className="text-14-500">Promotion start day</span>
                    <DatePicker
                        selectedDate={promotionStartDay}
                        setSelectedDate={(date) => {
                            setPromotionStartDay(date);
                            setPromotionEndDay(null);
                        }}
                        isOutsideRange={(date) => {
                            return (
                                moment(date).isBefore(moment(start_date)) ||
                                moment(date).isAfter(moment(end_date))
                            );
                        }}
                        isDisabled={
                            _.isEmpty(start_date) || _.isEmpty(end_date) || sameAsEvent == "yes"
                        }
                        displayFormat="MM-DD-YYYY"
                    />
                    <span className="text-14-500">Promotion end day</span>
                    <DatePicker
                        selectedDate={promotionEndDay}
                        setSelectedDate={(date) => {
                            setPromotionEndDay(date);
                        }}
                        isOutsideRange={(date) => {
                            return (
                                moment(date).isBefore(moment(start_date)) ||
                                moment(date).isAfter(moment(end_date)) ||
                                moment(date).isBefore(moment(promotionStartDay))
                            );
                        }}
                        isDisabled={
                            _.isEmpty(start_date) || _.isEmpty(end_date) || sameAsEvent == "yes"
                        }
                        displayFormat="MM-DD-YYYY"
                    />
                </div>
            </div>
        );
    };

    const productRestrictionFilter = () => {
        return (
            <div>
                <div className="flexContentAround">
                    <p className="secondaryText-16-500">Select attributes</p>
                    <div className="flexWithGap8">
                        <Button 
                            variant="text"
                            size="large"
                            iconPlacement="left"
                            onClick={() => handleIndividualRestrictionClear("product_restriction")}
                        >
                            Clear
                        </Button> 
                        <Button 
                            variant="tertiary"
                            size="large"
                            iconPlacement="left"
                            onClick={() => handleIndividualRestrictionSave("product_restriction")}
                        >
                            Save
                        </Button> 
                    </div>
                </div>
                <div className="marginTop-20">
                    <div className="marginBottom-16 display_flex">
                        <ButtonGroup
                            onChange={(_e, parms) => setProductRestrictionOption(parms)}
                            options={productSelectionOptions}
                            selectedOption={productRestrictionOption}
                        />
                        <Tooltip
                            orientation="left"
                            title={lockToolTip}
                            variant="tertiary"
                        >
                            <div className="marginLeft-16">
                                {productRestrictionLock ?
                                    <img src={Locked} alt="lock" className="lock-icon" onClick={() => setProductRestrictionLock(!productRestrictionLock)} />
                                    : <img src={LockOpen} alt="lock" className="lock-icon" onClick={() => setProductRestrictionLock(!productRestrictionLock)} />
                                }
                            </div>
                        </Tooltip>
                    </div>
                    {productRestrictionOption != "sitewide" && (
                        <div className="marginBottom-16">
                            {productRestrictionOption == "whole_category" && (
                                <ComponentFilters
                                    filterConfig={productFilterConfig}
                                    callAPIonLoad={false}
                                    screen="PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT"
                                    onSecondaryButtonClick={() => {
                                        dispatch(
                                            resetAllFiltersData({
                                                from: "PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT",
                                            })
                                        );
                                    }}
                                    secondaryButtonText="Clear Filters"
                                />
                            )}
                            {productRestrictionOption == "specific_products" && (
                                <SpecficProductSelection
                                    setSelectedProducts={setSelectedSpecificProducts}
                                    selectedProducts={selectedSpecificProducts}
                                    setSelectedSpecificProductType={setSelectedSpecificProductType}
                                    selectedSpecificProductType={selectedSpecificProductType}
                                    clearOnUnmount={true}
                                />
                            )}
                            {productRestrictionOption == "product_group" && (
                                <ProductGroupSelection
                                    setSelectedProductGroups={setSelectedProductGroups}
                                    selectedProductGroups={selectedProductGroups}
                                    clearOnUnmount={false}
                                />
                            )}
                        </div>
                    )}
                    {productRestrictionOption && (
                        <div className="add-drag-message">
                            <img src={ISymbol} alt="Note" />
                            <div>
                                <p className="secondaryText-14-500">
                                    {`You have selected ${productSelectionOptions.filter(option => {
                                        return option.value == productRestrictionOption
                                    })?.[0]?.label} as product restriction`}
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        );
    };

    const storeRestrictionFilter = () => {
        return (
            <div>
                <div className="flexContentAround">
                    <p className="secondaryText-16-500">Select attributes</p>
                    <div className="flexWithGap8">
                        <Button 
                            variant="text"
                            size="large"
                            iconPlacement="left"
                            onClick={() => handleIndividualRestrictionClear("store_restriction")}
                        >
                            Clear
                        </Button> 
                        <Button 
                            variant="tertiary"
                            size="large"
                            iconPlacement="left"
                            onClick={() => handleIndividualRestrictionSave("store_restriction")}
                        >
                            Save
                        </Button> 
                    </div>
                </div>
                <div className="marginTop-20">
                    <div className="marginBottom-16 display_flex">
                        <ButtonGroup
                            onChange={(_e, parms) => setStoreRestrictionOption(parms)}
                            options={storeSelectionOptions}
                            selectedOption={storeRestrictionOption}
                        />
                        <div className="marginLeft-16">
                            <Tooltip
                                orientation="left"
                                title={lockToolTip}
                                variant="tertiary"
                            >
                                {storeRestrictionLock ?
                                    <img src={Locked} alt="lock" className="lock-icon" onClick={() => setStoreRestrictionLock(!storeRestrictionLock)} />
                                    : <img src={LockOpen} alt="lock" className="lock-icon" onClick={() => setStoreRestrictionLock(!storeRestrictionLock)} />
                                }
                            </Tooltip>
                        </div>
                    </div>
                    {storeRestrictionOption != "all_stores" && (
                        <div className="marginBottom-16">
                            {storeRestrictionOption == "specific_stores" && (
                                <SpecificStoreSelection
                                    setSelectedStores={setSelectedSpecificStores}
                                    setSelectedSpecificStoreType={setSelectedSpecificStoreType}
                                    setSelectedStoreGroups={setSelectedStoreGroups}
                                    selectedSpecificStoreType={selectedSpecificStoreType}
                                    selectedStoreGroups={selectedStoreGroups}
                                    selectedStores={selectedSpecificStores}
                                    clearOnUnmount={false}
                                />
                            )}
                        </div>
                    )}
                    {storeRestrictionOption && (
                        <div className="add-drag-message">
                            <img src={ISymbol} alt="Note" />
                            <div>
                                <p className="secondaryText-14-500">
                                    {`You have selected ${storeSelectionOptions.filter(option => {
                                        return option.value == storeRestrictionOption
                                    })?.[0]?.label} as store restriction`}
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        )
    };

    const customerRestrictionFilter = () => {
        return <div>Customer Restriction Under Construction</div>;
    };

    const promotionRestrictionFilter = () => {
        return <div>Promotion Restriction Under Construction</div>;
    };

    const filterContent = (filter) => {
        if (filter == "date_restriction") 
            return dateRestrictionFilter();
        else if (filter == "product_restriction") 
            return productRestrictionFilter();
        else if (filter == "store_restriction") 
            return storeRestrictionFilter();
        else if (filter == "customer_restriction")
            return customerRestrictionFilter();
        else if (filter == "promotion_restriction")
            return promotionRestrictionFilter();
        return null;
    };

    const onSubmitHandler = () => {
        const payload = {
            date_restriction: {
                ...(date_restriction || {}),
            },
            product_restriction: {
                ...(product_restriction || {}),
            },
            store_restriction: {
                ...(store_restriction || {}),
            },
            // customer_restriction: {
            //     ...(customer_restriction || {}),
            // },
            // promotion_restriction: {
            //     ...(promotion_restriction || {}),
            // },
        };

        if (!validateRestriction()) return;
        // Create date restriction payload
        payload.date_restriction = createDateRestrictionPayload();

        // Create product restriction payload
        payload.product_restriction = createProductRestrictionPayload();

        // Create store restriction payload 
        payload.store_restriction = createStoreRestrictionPayload();

        dispatch(
            updateEventDetails(payload)
        );
        setIsRestrictionPanelOpen(false);
        createFilters();
        props?.setIsEditedFlag(true);
    };

    const validateRestriction = () => {
        return validateDateRestriction() && validateProductRestriction() && validateStoreRestriction();
    }

    const validateDateRestriction = () => {
        // Since in the tkt description it was mentioned that the date restriction is optional
        // If SameAsEvent is No: Enable the following non-mandatory options: Minimum promotion days, Maximum promotion days, Promotion start day, Promotion end day
        // as they are optional, so not validating them
        // If validation is required, then add the validation here

        return true;
    }

    const createDateRestrictionPayload = () => {
        if (sameAsEvent == "no") {
            return {
                sameAsEvent,
                minPromotionDays,
                maxPromotionDays,
                promotionStartDay: moment.isMoment(promotionStartDay)
                    ? promotionStartDay.format("YYYY-MM-DD")
                    : null,
                promotionEndDay: moment.isMoment(promotionEndDay)
                    ? promotionEndDay.format("YYYY-MM-DD")
                    : null,
            };
        } else {
            return {
                sameAsEvent,
                minPromotionDays: null,
                maxPromotionDays: null,
                promotionStartDay: null,
                promotionEndDay: null,
            };
        }

    }

    const createProductRestrictionPayload = () => {
        let productRestriction = {};
        if (productRestrictionOption != "sitewide") {
            productRestriction = {
                product_restriction_level: productRestrictionOption,
                lock: productRestrictionLock,
                specific_product_type: productRestrictionOption === "specific_products" ? selectedSpecificProductType : null,
                products: selectedSpecificProducts,
                product_groups: selectedProductGroups,
                product_hierarchy: {},
            };
            if (productRestrictionOption == "whole_category") {
                productRestriction.product_hierarchy = {}
                    Object.keys(filtersData["PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT"]).map(key => {
                        productRestriction.product_hierarchy[key] = filtersData["PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT"][key].selectedOptions;
                    });
            }
        } else if (productRestrictionOption == "sitewide") {
            productRestriction = {
                product_restriction_level: productRestrictionOption,
                lock: productRestrictionLock,
            };
        }
        return productRestriction;
    }

    const createStoreRestrictionPayload = () => {
        let storeRestriction = {};
        if (
            !(["all_stores", "ecom_stores", "bnm_stores",].includes(storeRestrictionOption))
        ) {
            storeRestriction = {
                store_restriction_level: storeRestrictionOption,
                lock: storeRestrictionLock,
                specific_store_type: selectedSpecificStoreType,
                stores: selectedSpecificStores,
                store_groups: selectedStoreGroups,
            };
        } else if (
            storeRestrictionOption == "all_stores" ||
            storeRestrictionOption == "ecom_stores" ||
            storeRestrictionOption == "bnm_stores"
        ) {
            storeRestriction = {
                store_restriction_level: storeRestrictionOption,
                lock: storeRestrictionLock,
            };
        }
        return storeRestriction;
    }

    const validateProductRestriction = () => {
        if (productRestrictionOption == "whole_category") {
           const arr = Object.keys(filtersData?.["PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT"] || {}).map(key => {
                return !!filtersData["PRODUCT_RESTRICTION_WHOLE_CATEGORY_CREATE_EVENT"][key].selectedOptions.length
           });
           if (!arr.includes(true)) {
               setErrMsg("Please select product categories");
               return false;
           }
        } else if (productRestrictionOption == "specific_products") {
            if (!selectedSpecificProducts?.length) {
                setErrMsg("Please select specific products");
                return false;
            }
        } else if (productRestrictionOption == "product_group") {
            if (!selectedProductGroups?.length) {
                setErrMsg("Please select product groups");
                return false;
            }
        }
        return true;
    }

    const validateStoreRestriction = () => {
        if (
            !["all_stores", "ecom_stores", "bnm_stores",].includes(storeRestrictionOption)
        ) {
            if (storeRestrictionOption == "specific_stores") {
                if (selectedSpecificStoreType !== "store_group" && !selectedSpecificStores?.length) {
                    setErrMsg("Please select specific stores");
                    return false;
                } else if (selectedSpecificStoreType === "store_group" && !selectedStoreGroups?.length) {
                    setErrMsg("Please select store groups");
                    return false;
                }
            } 
        }
        return true
    }

    const getSummaryDetails = () => {
        const arr = [];
        restrictionsFilters.forEach((config, ind) => {
            if (hasSavedData(config.value)) {
                arr.push(
                    <div key={config.value} className="create-event-card-summary-chip">
                        <div className="flexWithGap8">
                            {filterIcons[config?.value]}
                            <p className="secondaryText-12-500">
                                {config.title}
                            </p>
                        </div>
                        <div className="flexWithGap8">
                            {hasLock(config.value) && (
                                <div className="create-event-card-summary-chip-icon">
                                    <img src={Locked} width={"11px"} alt="locked icon" />
                                </div>
                            )}
                            {hasSavedData(config.value) && (
                                <div className="create-event-card-summary-chip-icon">
                                    <img src={Save} width={"11px"} alt="save icon" />
                                </div>
                            )}
                        </div>
                    </div>
                );
            }
        });
        if (arr.length > 0) {
            return (
                <div className="create-event-card-summary-container">
                    {arr}
                </div>
            )
        }
        return false;
    }

    const handleIndividualRestrictionClear = (filter) => {
        clearAll(filter);
        if (filter == "date_restriction") {
            dispatch(updateEventDetails({
                date_restriction: {},
            }));
        } else if (filter == "product_restriction") {
            dispatch(updateEventDetails({
                product_restriction: {},
            }));
        } else if (filter == "store_restriction") {
            dispatch(updateEventDetails({
                store_restriction: {},
            }));
        }
        props?.setIsEditedFlag(true);
    }

    const handleIndividualRestrictionSave = (filter) => {
        let valid = true;
        if (filter == "date_restriction") valid = validateDateRestriction();
        else if (filter == "product_restriction") valid = validateProductRestriction();
        else if (filter == "store_restriction") valid = validateStoreRestriction();

        if (valid) {
            const payload = {
                date_restriction: {
                    ...(date_restriction || {}),
                },
                product_restriction: {
                    ...(product_restriction || {}),
                },
                store_restriction: {
                    ...(store_restriction || {}),
                },
            }

            if (filter == "date_restriction") {
                payload.date_restriction = createDateRestrictionPayload();

            } else if (filter == "product_restriction") {
                payload.product_restriction = createProductRestrictionPayload();
            } else if (filter == "store_restriction") {
                payload.store_restriction = createStoreRestrictionPayload();
            }

            dispatch(
                updateEventDetails(payload)
            );
        }
    }

    const handleClearAll = () => {
        dispatch(updateEventDetails({
            date_restriction: {},
            product_restriction: {},
            store_restriction: {},
        }));
        setIsRestrictionPanelOpen(false);
        props?.setIsEditedFlag(true);
        clearAll("all");
    }

    return (
        <div className="card-conatainer">
            <div className="content_container create-event-card">
                <div className="create-event-card-text-container">
                    <p className="text-16-800">Restriction</p>
                    {getSummaryDetails() ||
                        <>
                            <p className="text-14-800 marginTop-20">
                                This is where you can restrict {global_labels.event_primary} scope, and set {global_labels.event_primary} rules
                            </p>
                            <p className="label-12px-normal marginTop-8">
                                Click on “set restriction” button to start applying restriction
                            </p>
                        </>
                    }
                    <Button
                        onClick={() => setIsRestrictionPanelOpen(true)}
                        size="large"
                        variant="secondary"
                        className="create-event-card-button"
                    >
                        Set Restrictions
                    </Button>
                </div>
                <img src={SetRestrictionImage} width={"152px"} alt="set restriction"/>
            </div>
            {panelFiltersData?.length > 0 && (
                <FilterPanel
                    active={activeGroup}
                    anchor="right"
                    isOpen={isRestrictionPanelOpen}
                    className="create-event-container promo-restrictions"
                    filters={_.cloneDeep(panelFiltersData)}
                    handleClose={() => setIsRestrictionPanelOpen(false)}
                    onPrimaryButtonClick={onSubmitHandler}
                    onSecondaryButtonClick={() => handleClearAll()}
                    primaryButtonLabel="Save All"
                    secondaryButtonLabel="Clear All"
                    setActive={(val) => {
                        setActiveGroup(val);
                        addSavedLockedIcon();
                    }}
                    size="large"
                    title={"Set Restrictions"}
                />
            )}
        </div>
    );
};

export default Restrictions;
