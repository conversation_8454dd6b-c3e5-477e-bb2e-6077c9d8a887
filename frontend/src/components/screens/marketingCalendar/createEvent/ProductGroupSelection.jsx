import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import ComponentFilters from '../../../ui/componentFilters/ComponentFilters';
import {
    Table,
    Input,
    Button,
    Prompt,
    Badge,
} from 'impact-ui'
import { useDispatch, useSelector } from 'react-redux';
import { toastError } from "../../../../store/features/global/global";
import { callProductConfigTableAPI, setEventProductGroupData } from "../../../../store/features/eventReducer/eventReducer";
import { fabricatePayloadHierarchy, mergeFiltersData } from "../../../../utils/helpers/utility_helpers";
import { overwriteFilters, resetAllFiltersData } from "../../../../store/features/filters/filters";
import { filterConfigProductGroup, productGroupTableConfiguation } from "./createEventConstants";
import SearchIcon from "../../../../assets/imageAssets/searchIcon.svg?.url";
import EmptyData from '../../../ui/emptyData/EmptyData';
import _ from "lodash";
import { CellWithInfoIcon } from '../../../../utils/helpers/tableHelpers/tableCellRendererHelpers';
import { global_labels } from '../../../../constants/Constants';

const ProductGroupSelection = (props) => {

    const tableRefProductGroup = useRef(null);
    const dispatch = useDispatch();

    const {
        filtersData,
    } = useSelector((store) => store?.pricesmartPromoReducer.filters);

    const {
        createEventProductGroupData = []
    } = useSelector((store) => store?.pricesmartPromoReducer.event);

    const {
        clearOnUnmount = true,
    } = props

    const [lastFiltersDataProductGrp, setLastFiltersDataProductGrp] = useState({});
    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [tableDataOverridePromptProductGroup, setTableDataOverridePromptProductGroup] = useState(false);
    const [productGroupTableData, setProductGroupTableData] = useState([]);
    const [selectedProductGroups, setSelectedProductGroups] = useState(props?.selectedProductGroups || []);
    const [modifiedFilterConfig, setModifiedFilterConfig] = useState([]);
    const [callAPIonLoad, setCallAPIonLoad] = useState(false);
    const [productGroupTableDef, setProductGroupTableDef] = useState(productGroupTableConfiguation);

    useEffect(() => {
        // This part added to modify the filter config is beacuse we need to pass event_id in the filter payload,
        // since this component is used in create offer screen
        // where we need to pass event_id in the filter payload, as to filter the option based on any retriction applied on event
        const modifiedFilterConfig = _.cloneDeep(filterConfigProductGroup).map((config) => {
            let newConfig = _.cloneDeep(config);
            if (props?.eventId) {
                newConfig["extraParams"] = {
                    event_id: props?.eventId
                };
            }
            return newConfig;
        });
        setModifiedFilterConfig(modifiedFilterConfig);
        if (
            _.isEmpty(props?.selectedProductGroups) &&
            _.isEmpty(createEventProductGroupData)
        )
            setCallAPIonLoad(true);
        return () => {
            setCallAPIonLoad(true);
            if (clearOnUnmount) {
                dispatch(setEventProductGroupData([]));
                dispatch(
                    resetAllFiltersData({
                        from: "CREATE_EVENT_PRODUCT_GROUP",
                    })
                );
            }
        }
    }, []);

    useEffect(() => {
        if (callAPIonLoad && _.isEmpty(props?.selectedProductGroups)) {
            let callAPIFlag = true;
            //checks if all required filters are selected, if not, callAPIFlag will be false.
            _.forEach([
                "l0_ids",
                "l1_ids",
                "l2_ids"
            ], (filterID) => {
                if (
                    filterID !== "dateRange" &&
                    !filtersData["CREATE_EVENT_PRODUCT_GROUP"]?.[filterID]?.selectedOptionsArray
                        ?.length
                ) {
                    callAPIFlag = false;
                } else if (filterID === "dateRange") {
                    if (!filtersData["CREATE_EVENT_PRODUCT_GROUP"]?.[filterID])
                        callAPIFlag = false;
                }
            });
            //if callAPIFlag is true, call the screen's filterApply method.
            if (callAPIFlag) {
                onComponentFiltersProductGroupApply();
                setCallAPIonLoad(false);
            }
        }
    }, [
        filtersData["CREATE_EVENT_PRODUCT_GROUP"],
        callAPIonLoad,
    ]);

    useEffect(() => {
        if (!_.isEmpty(props?.selectedProductGroups) && _.isEmpty(createEventProductGroupData)) {
            setSelectedProductGroups(_.cloneDeep(props?.selectedProductGroups));
            setProductGroupTableData(_.cloneDeep(props?.selectedProductGroups));
        } else if (_.isEmpty(props?.selectedProductGroups) && _.isEmpty(createEventProductGroupData)) {
            setSelectedProductGroups([]);
            setProductGroupTableData([]);
        }
    }, [props?.selectedProductGroups]);

    useEffect(() => {
        if (!_.isEmpty(createEventProductGroupData)) {
            // if there is no previous data in table, set the fetched data
            if (!productGroupTableData.length) {
                setProductGroupTableData(createEventProductGroupData);
            } else {
                // if there is previous data in table, show prompt to retain or replace
                setTableDataOverridePromptProductGroup(true);
            }
        }
    }, [createEventProductGroupData]);

    useEffect(() => {
        // if the component is used in offer exclusion screen, modify the column definition to show info icon
        // when the product is already excluded in the event, so adding a cell renderer to show info icon
        if (props?.isFromOfferExclusion) {
            let modifiedProductsGroupTabDef = _.cloneDeep(productGroupTableConfiguation) || [];
            modifiedProductsGroupTabDef = modifiedProductsGroupTabDef.map((column) => {
                if (column.field === "product_group_name") {
                    column["cellRenderer"] = CellWithInfoIcon;
                    column["cellRendererParams"] = {
                        info: `You cannot modify the selection status of products already excluded in the ${global_labels?.event_primary} section.`,
                        flag: "is_excluded_in_event"
                    }
                }
                return column;
            });
            setProductGroupTableDef(modifiedProductsGroupTabDef);
        }
        if (props?.viewMode) {
            let def = _.cloneDeep(productGroupTableConfiguation);
            def = _.filter(def, (item) => item.field !== "" && !item.checkboxSelection);
            setProductGroupTableDef(def);
        } else {
            setProductGroupTableDef(productGroupTableConfiguation);
        }
    }, [props?.isFromOfferExclusion, props?.viewMode]);

    const onComponentFiltersProductGroupApply = () => {
        let callAPI = true;
        // check if all mandatory filters are selected
        const filtersDataSelected = _.cloneDeep(
            filtersData["CREATE_EVENT_PRODUCT_GROUP"]
        );
        _.forEach(modifiedFilterConfig, (config) => {
            const key = config.filterId;
            if (
                config.isMandatory &&
                !filtersDataSelected?.[key]?.selectedOptionsArray.length
            ) {
                callAPI = false;
            }
        });
        if (!callAPI) {
            dispatch(toastError("Please select all mandatory filters"));
            return;
        }
        // set last filters data for replace and retain prompt
        if (_.isEmpty(lastFiltersDataProductGrp))
            (_.cloneDeep(filtersDataSelected));
        // if all mandatory filters are selected, call API to get product data
        const payload = fabricatePayloadHierarchy(filtersDataSelected);

        payload.event_id = props?.eventId || null;

        if (props?.isFromOfferExclusion)
            payload["with_exclusions"] = true;

        dispatch(callProductConfigTableAPI(payload));
    };

    const onRetainClickProductGroup = () => {
        //concat current data with previous data, and set it in table
        let currentRows = _.cloneDeep(productGroupTableData);
        currentRows = [
            ...currentRows,
            ...createEventProductGroupData,
        ];
        currentRows = _.uniqBy(currentRows, "product_group_id");
        setProductGroupTableData(_.cloneDeep(currentRows));
        setTableDataOverridePromptProductGroup(false);

        // concat currennt data with previous data and set both last filters and current filters as same
        const updatedFiltersData = mergeFiltersData(
            _.cloneDeep(lastFiltersDataProductGrp),
            _.cloneDeep(filtersData["CREATE_EVENT_PRODUCT_GROUP"])
        );
        setLastFiltersDataProductGrp(_.cloneDeep(updatedFiltersData));
        dispatch(
            overwriteFilters({
                filtersData: _.cloneDeep(updatedFiltersData),
                activeScreen: "CREATE_EVENT_PRODUCT_GROUP",
            })
        );
    };

    const onReplaceAndOverwriteClickProductGroup = () => {
        // replace table data with current selection and set last filters data same as current selection
        setProductGroupTableData(_.cloneDeep(createEventProductGroupData));
        setTableDataOverridePromptProductGroup(false);
        setLastFiltersDataProductGrp(filtersData["CREATE_EVENT_PRODUCT_GROUP"]);
    };

    const onRowSelectionProductGroup = useCallback(() => {
        // set selected products data
        const selectedRows = tableRefProductGroup.current.api.getSelectedRows();
        setSelectedProductGroups(_.cloneDeep(selectedRows));
        props?.setSelectedProductGroups(_.cloneDeep(selectedRows));
    });

    const onFilterTextBoxChanged = useCallback((text) => {
        setGlobalSearchText(text);
        tableRefProductGroup.current.api.setGridOption("quickFilterText", text);
    }, []);

    const rowClassRules = useMemo(() => {
        //if row is subtotal, show it in grey background
        return {
            "greyed-out": (params) => {
                return params.data.is_excluded_in_event;
            },
        };
    }, []);

    const onFirstDataRendered = (params) => {
        const nodesToSelect = [];
        const selected_group_ids = props?.selectedProductGroups?.map((group) => group.product_group_id);
        params.api.forEachNode((node) => {
            if (selected_group_ids.includes(node.data?.product_group_id)) {
                nodesToSelect.push(node);
            }
        });
        params.api.setNodesSelected({ nodes: nodesToSelect, newValue: true });
    };

    return (
        <div>
            <ComponentFilters
                filterConfig={modifiedFilterConfig}
                callAPIonLoad={callAPIonLoad}
                onPrimaryButtonClick={onComponentFiltersProductGroupApply}
                screen="CREATE_EVENT_PRODUCT_GROUP"
                onSecondaryButtonClick={() => {
                    dispatch(
                        resetAllFiltersData({
                            from: "CREATE_EVENT_PRODUCT_GROUP",
                        })
                    );
                }}
                secondaryButtonText="Clear Filters"
                primaryButtonText="Submit"
            />
            {productGroupTableData.length > 0 ? (
                <div className="marginTop-24">
                    <Table
                        tableHeader={"Product Group"}
                        ref={tableRefProductGroup}
                        suppressMenuHide
                        rowData={productGroupTableData}
                        columnDefs={productGroupTableDef}
                        rowSelection="multiple"
                        onSelectionChanged={onRowSelectionProductGroup}
                        topRightOptions={
                            <div className="centerFlexWithGap12">
                                <div className="positionRelative">
                                    {showGlobalSearch ? (
                                        <div className="tableGlobalSearchContainer">
                                            <Input
                                                onChange={(e) =>
                                                    onFilterTextBoxChanged(
                                                        e.target.value
                                                    )
                                                }
                                                placeholder="Search"
                                                rightIcon={
                                                    <img
                                                        src={SearchIcon}
                                                        alt='search'
                                                    />
                                                }
                                                type="text"
                                                value={globalSearchText}
                                            />
                                        </div>
                                    ) : null}
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={SearchIcon} alt='search' />}
                                        onClick={() =>
                                            setShowGlobalSearch(
                                                (prev) => !prev
                                            )
                                        }
                                        size="large"
                                        variant="tertiary"
                                    />
                                </div>
                            </div>
                        }
                        topLeftOptions={
                            <Badge
                                color="default"
                                label={`Selected Product Groups: ${selectedProductGroups?.length} / ${productGroupTableData?.length}`}
                                size="default"
                                variant="subtle"
                            />
                        }
                        rowClassRules={rowClassRules}
                        isRowSelectable={(rowNode) => {
                            return !rowNode.data.is_excluded_in_event;
                        }}
                        onFirstDataRendered={onFirstDataRendered}
                    />
                </div>
            ) : <EmptyData />
            }
            <Prompt
                handleClose={() => {
                    setTableDataOverridePromptProductGroup(false);
                }}
                onPrimaryButtonClick={onRetainClickProductGroup}
                onSecondaryButtonClick={onReplaceAndOverwriteClickProductGroup}
                primaryButtonLabel="Retain"
                secondaryButtonLabel="Replace & overwrite"
                title="Confirm Selection"
                variant="warning"
                isOpen={tableDataOverridePromptProductGroup}
            >
                Do you want to Retain and Continue or Replace table with current
                selection
            </Prompt>

        </div>
    )
}

export default ProductGroupSelection