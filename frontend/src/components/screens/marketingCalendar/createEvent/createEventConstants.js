import { global_labels, PRODUCT_SELECTION_VALID_INVALID_TABLE, STORE_SELECTION_VALID_INVALID_TABLE } from "../../../../constants/Constants"
import { commonProductFilterConfig, commonStoreFilterConfig, wholeStoreFilterConfig } from "../../../../constants/FilterConfigConstants"
import { 
    dateFormatter, 
    toCurrencyByCurrencyId, 
    yyyyMmDdFormatter
 } from "../../../../utils/helpers/formatter"
import { capitalizeFirstLetter, replaceSpecialCharacter } from "../../../../utils/helpers/utility_helpers"


export const restrictionsFilters = [
    {
        id: 1,
        title: "Date",
        value: "date_restriction",
    },
    {
        id: 2,
        title: "Product",
        value: "product_restriction",
    },
    {
        id: 3,
        title: "Store",
        value: "store_restriction",
    },
    // Hidden for now
    // {
    //     id: 4,
    //     title: "Customer",
    //     value: "customer_restriction",
    // },
    // {
    //     id: 5,
    //     title: "Promotional",
    //     value: "promotion_restriction",
    // },
]

export const exclusionFilters = [
    {
        id: 1,
        title: "Product",
        value: "product_exclusion",
    },
]

export const eventLabels = {
    event: "Campaign",
}

export const createEventDropdownConfig = {
    eventAdType: [
        {
          label: "Advertised",
          value: "advertised",
        },
        {
          label: "Un-Advertised",
          value: "unAdvertsied",
        },
    ],
    eventType: [
        {
            label: "Flyer",
            value: "flyer",
        },
        {
            label: "Email",
            value: "email",
        },
        {
            label: "Other",
            value: "other",
        },
    ],
    eventObjective: [
        {
            label: "Traffic/Units",
            value: "trafficUnits",
        },
        {
            label: "Revenue",
            value: "revenue",
        },
        {
            label: "Margin",
            value: "margin",
        },
        {
            label: "Other",
            value: "other",
        },
    ],
    attribute: [
        {
            label: "A1",
            value: "a1",
        },
        {
            label: "B2",
            value: "b2",
        },
        {
            label: "C3",
            value: "c3",
        },
    ],
}

// Product Restriction related constants

export const productFilterConfig = [
    ...commonProductFilterConfig.map(item => ({
        ...item,
        isMandatory: false,
        selectOnLoad: false,
        selection: null,
    })),
]

export const filterTypeButton = [
    {
        label: "Choose Filters",
        value: "hierarchy",
    },
    {
        label: "Upload XL",
        value: "upload",
    },
    {
        label: "Copy Paste Data",
        value: "copy_paste",
    },
]

export const createSpecificProductFilterConfig = [
    ...commonProductFilterConfig.map(item => ({
        ...item,
        selectOnLoad: false,
        selection: null,
    })),
]

export const createProductTableConfig = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "client_product_id",
        headerName: "Product ID",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "product_name",
        headerName: "Description",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "msrp",
        headerName: "MSRP",
        valueFormatter: (params) => {
            const { currency_id, currency_name, currency_symbol } = params.data;
            return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
        },
        type: "number",
    },
    {
        field: "current_price",
        headerName: "Current Price",
        valueFormatter: (params) => {
            const { currency_id, currency_name, currency_symbol } = params.data;
            return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
        },
        type: "number",
    },
    {
        field: "oh_inventory",
        headerName: "On Hand Inventory",
        type: "number",
    },
    {
        field: "it_inventory",
        headerName: "In transit Inventory",
        type: "number",
    },
    {
        field: "oo_inventory",
        headerName: "On order Inventory",
        type: "number",
    },
    {
        field: "cost",
        headerName: "Cost",
        valueFormatter: (params) => {
            const { currency_id, currency_name, currency_symbol } = params.data;
            return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
        },
        type: "number",
    },
    {
        field: "launch_price",
        headerName: "Launch Price",
        valueFormatter: (params) => {
            const { currency_id, currency_name, currency_symbol } = params.data;
            return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
        },
        type: "number",
    },
]

export const invalidDataColumnConfig = [
    ...PRODUCT_SELECTION_VALID_INVALID_TABLE,
    {
        field: "status",
        headerName: "Status",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
        flex: 1
    },
]

export const filterConfigProductGroup = [
    ...commonProductFilterConfig
]

export const productGroupTableConfiguation = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 60,
    },
    {
        field: "product_group_name",
        headerName: "Group Name",
        width: 250,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "product_group_description",
        headerName: "Description",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "products_count",
        headerName: "Products",
    },
    {
        field: "product_group_type",
        headerName: "Product Group Type",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l0_name",
        headerName: "Division",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l1_name",
        headerName: "Group",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l2_name",
        headerName: "Department",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_by_user",
        headerName: "Created By",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_at",
        headerName: "Created On",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "modified_by_user",
        headerName: "Modified By",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "modified_at",
        headerName: "Modified On",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "promos_count",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`,
        width: 100,
        flex: 1,
        type: "number",
    },
]

// Store Restriction related constants

export const filterTypeButtonStore = [
    {
        label: "Choose Filters",
        value: "hierarchy",
    },
    {
        label: "Upload XL",
        value: "upload",
    },
    {
        label: "Copy Paste Data",
        value: "copy_paste",
    },
    {
        label: "Store Group",
        value: "store_group",
    },
]

export const createStoreFilterConfig = [
    ...wholeStoreFilterConfig.map(item => ({
        ...item,
        selectOnLoad: false,
        selection: null,
    })),
]

export const createStoreTableConfig = [
    //  TODO once the BE is ready for stors api we aill recieve the data in s0_name, s1_name, s2_name, s3_name, s4_name, s5_name along that line
    //  as the key might for diff clients
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "store_name",
        headerName: "Store Name",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "store_id",
        headerName: "Store ID",
        valueFormatter: (params) => parseInt(params.value),
    },
    {
        field: "s1_name",
        headerName: "Channel",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s0_name",
        headerName: "Country",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s5_name",
        headerName: "City",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s3_name",
        headerName: "District",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s2_name",
        headerName: "Group",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s4_name",
        headerName: "State",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
]

export const invalidDataColumnConfigStore = [
    ...STORE_SELECTION_VALID_INVALID_TABLE,
    {
        field: "status",
        headerName: "Status",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
        flex: 1
    },
]

export const filterConfigStoreGroup = [
    ...commonStoreFilterConfig,
]

export const storeGroupTableConfiguation = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "store_group_name",
        headerName: "Group Name",
        pinned: "left",
        width: 250,
    },
    {
        field: "store_group_description",
        headerName: "Description",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "stores_count",
        headerName: "Stores",
        type: "number",
    },
    {
        field: "s1_name",
        headerName: "Channel",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s0_name",
        headerName: "Country",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s2_name",
        headerName: "Store Group",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_by_user",
        headerName: "Created By",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_at",
        headerName: "Created On",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "modified_by_user",
        headerName: "Modified By",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "modified_at",
        headerName: "Modified On",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "promos_count",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`,
        width: 100,
        // flex: 1,
        type: "number",
    },
]

export const exclusionProductTableConfig = [
    {
        headerName: "Division ID",
        field: "l0_id",
    },
    {
        headerName: "Division Name",
        field: "l0_cuq",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "Group ID",
        field: "l1_id",
    },
    {
        headerName: "Group Name",
        field: "l1_cuq",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "Department ID",
        field: "l2_id",
    },
    {
        headerName: "Department Name",
        field: "l2_cuq",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "Class ID",
        field: "l3_id",
    },
    {
        headerName: "Class Name",
        field: "l3_cuq",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "Subclass ID",
        field: "l4_id",
    },
    {
        headerName: "Subclass Name",
        field: "l4_cuq",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "MFG ID",
        field: "mfg_no",
    },
    {
        headerName: "MFG Name",
        field: "mfg_name",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "SVS ID",
        field: "l5_id",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "SVS Name",
        field: "l5_cuq",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },

]

export const effectedPromosColumnConfig = [
    {
        headerName: `${capitalizeFirstLetter(global_labels?.promo_primary)} Name`,
        field: "promo_name",
        flex: 1,
    },
    {
        headerName: `${capitalizeFirstLetter(global_labels?.promo_primary)} Status`,
        field: "promo_status",
    },
    {
        headerName: "Action",
        field: "action",
    },
        
]

export const effectedPromosColumnConfigForDeleteEvent = [
    {
        headerName: `${capitalizeFirstLetter(global_labels?.promo_primary)} Name`,
        field: "promo_name",
        flex: 1,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: `${capitalizeFirstLetter(global_labels?.event_primary)} Name`,
        field: "event_name",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        headerName: "Start Date",
        field: "start_date",
        width: 140,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
    },
    {
        headerName: "End Date",
        field: "end_date",
        width: 130,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
    },
    {
        headerName: `${capitalizeFirstLetter(global_labels?.promo_primary)} Status`,
        field: "status_name",
    },
]
