import React, { useEffect, useRef, useState, useCallback } from 'react'
import ComponentFilters from '../../../ui/componentFilters/ComponentFilters';
import {
    Table,
    Input,
    Button,
    Prompt,
} from 'impact-ui'
import { useDispatch, useSelector } from 'react-redux';
import { toastError } from "../../../../store/features/global/global";
import { getStoreGroupsFilter, setEventStoreGroupData } from "../../../../store/features/eventReducer/eventReducer";
import { fabricatePayloadHierarchy, mergeFiltersData } from "../../../../utils/helpers/utility_helpers";
import { overwriteFilters, resetAllFiltersData } from "../../../../store/features/filters/filters";
import { filterConfigStoreGroup, storeGroupTableConfiguation } from "./createEventConstants";
import SearchIcon from "../../../../assets/imageAssets/searchIcon.svg?.url";
import EmptyData from '../../../ui/emptyData/EmptyData';
import _ from "lodash";

const StoreGroupSelection = (props) => {

    const tableRefStoreGroup = useRef(null);
    const dispatch = useDispatch();
    
    const {
        filtersData,
    } = useSelector((store) => store?.pricesmartPromoReducer.filters);

    const {
        createEventStoreGroupData = []
    } = useSelector((store) => store?.pricesmartPromoReducer.event);

    const {
        clearOnUnmount = true,
    } = props

    const [lastFiltersDataStoreGrp, setLastFiltersDataStoreGrp] = useState({});
    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [tableDataOverridePromptStoreGroup, setTableDataOverridePromptStoreGroup] = useState(false);
    const [storeGroupTableData, setStoreGroupTableData] = useState([]);
    const [selectedStoreGroups, setSelectedStoreGroups] = useState(props?.selectedStoreGroups || []);
    const [storeGroupTableDef, setStoreGroupTableDef] = useState(storeGroupTableConfiguation);

    useEffect(() => {
        return () => {
            dispatch(
                resetAllFiltersData({
                    from: "CREATE_EVENT_STORE_GROUP",
                })
            );
            if (clearOnUnmount)
                dispatch(setEventStoreGroupData([]));
        }
    }, []);

    useEffect(() => {
        if (!_.isEmpty(createEventStoreGroupData)) {
            // if there is no previous data in table, set the fetched data
            if (!storeGroupTableData.length) {
                setStoreGroupTableData(createEventStoreGroupData);
            } else {
                // if there is previous data in table, show prompt to retain or replace
                setTableDataOverridePromptStoreGroup(true);
            }
        }
	}, [createEventStoreGroupData]);

    useEffect(() => {
        if (!_.isEmpty(props?.selectedStoreGroups) && _.isEmpty(createEventStoreGroupData)) {
            setSelectedStoreGroups(_.cloneDeep(props?.selectedStoreGroups));
            setStoreGroupTableData(_.cloneDeep(props?.selectedStoreGroups));
        }
    },[props?.selectedStoreGroups])

    useEffect(() => {
        if (props?.viewMode) {
            let def = _.cloneDeep(storeGroupTableConfiguation);
            def = _.filter(def, (item) => item.field !== "" && !item.checkboxSelection);
            setStoreGroupTableDef(def);
        } else {
            setStoreGroupTableDef(storeGroupTableConfiguation);
        }
    }, [props?.viewMode]);
    
    const onComponentFiltersStoreGroupApply = () => {
        let callAPI = true;
        // check if all mandatory filters are selected
        const filtersDataSelected = _.cloneDeep(
            filtersData["CREATE_EVENT_STORE_GROUP"]
        );
        _.forEach(filterConfigStoreGroup, (config) => {
            const key = config.filterId;
            if (
                config.isMandatory &&
                !filtersDataSelected?.[key]?.selectedOptionsArray.length
            ) {
                callAPI = false;
            }
        });
        if (!callAPI) {
            dispatch(toastError("Please select all mandatory filters"));
            return;
        }
        // set last filters data for replace and retain prompt
        if (_.isEmpty(lastFiltersDataStoreGrp))
                (_.cloneDeep(filtersDataSelected));
        // if all mandatory filters are selected, call API to get store data
        const payload = {
            ...fabricatePayloadHierarchy(filtersDataSelected),
        };

        dispatch(getStoreGroupsFilter(payload));
    };

    const onRetainClickStoreGroup = () => {
		//concat current data with previous data, and set it in table
		let currentRows = _.cloneDeep(storeGroupTableData);
		currentRows = [
			...currentRows,
			...createEventStoreGroupData,
		];
		currentRows = _.uniqBy(currentRows, "store_group_id");
		setStoreGroupTableData(_.cloneDeep(currentRows));
		setTableDataOverridePromptStoreGroup(false);

		// concat currennt data with previous data and set both last filters and current filters as same
		const updatedFiltersData = mergeFiltersData(
			_.cloneDeep(lastFiltersDataStoreGrp),
			_.cloneDeep(filtersData["CREATE_EVENT_STORE_GROUP"])
		);
		setLastFiltersDataStoreGrp(_.cloneDeep(updatedFiltersData));
		dispatch(
			overwriteFilters({
				filtersData: _.cloneDeep(updatedFiltersData),
				activeScreen: "CREATE_EVENT_STORE_GROUP",
			})
		);
	};

    const onReplaceAndOverwriteClickStoreGroup = () => {
		// replace table data with current selection and set last filters data same as current selection
		setStoreGroupTableData(_.cloneDeep(createEventStoreGroupData));
		setTableDataOverridePromptStoreGroup(false);
		setLastFiltersDataStoreGrp(filtersData["CREATE_EVENT_STORE_GROUP"]);
	};

    const onRowSelectionStoreGroup = useCallback(() => {
		// set selected stores data
		const selectedRows = tableRefStoreGroup.current.api.getSelectedRows();
        setSelectedStoreGroups(_.cloneDeep(selectedRows));
        props?.setSelectedStoreGroups(_.cloneDeep(selectedRows));
	});

    const onFilterTextBoxChanged = useCallback((text) => {
		setGlobalSearchText(text);
        tableRefStoreGroup.current.api.setGridOption("quickFilterText", text);
	}, []);

    const onFirstDataRendered = (params) => {
        const nodesToSelect = [];
        const selected_ids = props?.selectedStoreGroups?.map((group) => group.store_group_id);
        params.api.forEachNode((node) => {
            if (selected_ids.includes(node.data?.store_group_id)) {
                nodesToSelect.push(node);
            }
        });
        params.api.setNodesSelected({ nodes: nodesToSelect, newValue: true });
    };


    return (
        <div>
            <ComponentFilters
                filterConfig={filterConfigStoreGroup}
                callAPIonLoad={false}
                onPrimaryButtonClick={onComponentFiltersStoreGroupApply}
                screen="CREATE_EVENT_STORE_GROUP"
                onSecondaryButtonClick={() => {
                    dispatch(
                        resetAllFiltersData({
                            from: "CREATE_EVENT_STORE_GROUP",
                        })
                    );
                }}
                secondaryButtonText="Clear Filters"
                primaryButtonText="Submit"
            />
            {storeGroupTableData.length > 0 ? (
                <div>
                    <Table
                        tableHeader={"Store Group"}
                        ref={tableRefStoreGroup}
                        suppressMenuHide
                        rowData={storeGroupTableData}
                        columnDefs={storeGroupTableDef}
                        rowSelection="multiple"
                        onSelectionChanged={onRowSelectionStoreGroup}
                        onFirstDataRendered={onFirstDataRendered}
                        topRightOptions={
                            <div className="centerFlexWithGap12">
                                <div className="positionRelative">
                                    {showGlobalSearch ? (
                                        <div className="tableGlobalSearchContainer">
                                            <Input
                                                onChange={(e) =>
                                                    onFilterTextBoxChanged(
                                                        e.target.value
                                                    )
                                                }
                                                placeholder="Search"
                                                rightIcon={
                                                    <img
                                                        src={SearchIcon}
                                                        alt='search'
                                                    />
                                                }
                                                type="text"
                                                value={globalSearchText}
                                            />
                                        </div>
                                    ) : null}
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={SearchIcon} alt="Search" />}
                                        onClick={() =>
                                            setShowGlobalSearch(
                                                (prev) => !prev
                                            )
                                        }
                                        size="large"
                                        variant="secondary"
                                    />
                                </div>
                            </div>
                        }
                    />
                    <div className="secondaryText-14-500 textAlignRight">
                        Selected groups:{" "}
                        {selectedStoreGroups.length}/
                        {storeGroupTableData.length}
                    </div>
                </div>
            ) : <EmptyData />
            }
            <Prompt
                handleClose={() => {
                    setTableDataOverridePromptStoreGroup(false);
                }}
                onPrimaryButtonClick={onRetainClickStoreGroup}
                onSecondaryButtonClick={onReplaceAndOverwriteClickStoreGroup}
                primaryButtonLabel="Retain"
                secondaryButtonLabel="Replace & overwrite"
                title="Confirm Selection"
                variant="warning"
                isOpen={tableDataOverridePromptStoreGroup}
            >
                Do you want to Retain and Continue or Replace table with current
                selection
            </Prompt>

        </div>
    )
}

export default StoreGroupSelection