import React, { useState, useEffect, useRef, useCallback } from "react";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import {
	ButtonGroup,
	FileUpload,
	TextArea,
	Prompt,
	Modal,
	Button,
	Input,
	Badge,
} from "impact-ui";
import { Table } from "impact-ui-v3";
import ComponentFilters from "../../../ui/componentFilters/ComponentFilters";
import EmptyData from "../../../ui/emptyData/EmptyData";
import {
	resetAllFiltersData,
	overwriteFilters,
} from "../../../../store/features/filters/filters";
import {
	filterTypeButton,
	createSpecificProductFilterConfig,
	createProductTableConfig,
	invalidDataColumnConfig,
	exclusionProductTableConfig,
} from "./createEventConstants";
import {
	callCreateProductConfigFilterTableAPI,
	getCreateProductGrpDataFromExcel,
	getCreateProductGrpDataFromCopyPaste,
	setEventProductTableData,
	setTableDataFromUploadOrCopyPaste
} from "../../../../store/features/eventReducer/eventReducer";

import { fabricatePayloadHierarchy, mergeFiltersData } from "../../../../utils/helpers/utility_helpers";
import SearchIcon from "../../../../assets/imageAssets/searchIcon.svg?.url";
import {
	excelTemplateDownload,
	toastError,
} from "../../../../store/features/global/global";
import ProductGroupSelection from "./ProductGroupSelection";
import { getProductFromExcelForProductExclusion } from "../../../../store/features/promoReducer/promoReducer";

function SpecficProductSelection(props) {
	const dispatch = useDispatch();
	const tableRef = useRef();

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	const {
		createEventProductTableData,
		tableDataFromUploadOrCopyPaste,
	} = useSelector(
		(store) => store?.pricesmartPromoReducer.event
	)

	const {
		setSelectedProducts,
		setSelectedSpecificProductType,
		clearOnUnmount = true,
	} = props

	const [showGlobalSearch, setShowGlobalSearch] = useState(false);
	const [globalSearchText, setGlobalSearchText] = useState("");
	const [selectedFilterType, setSelectedFilterType] = useState(props?.selectedSpecificProductType);
	const [productButtonGroupOptions, setProductButtonGroupOptions] = useState(filterTypeButton);
	const [productTableData, setProductTableData] = useState([]);
	const [selectedProductTableData, setSelectedProductTableData] = useState(props?.selectedProducts || []);

	const [uploadedExcel, setUploadedExcel] = useState([]);
	const [lastFiltersData, setLastFiltersData] = useState({});
	const [showDataOverridePrompt, setShowDataOverridePrompt] = useState(false);
	const [showInvalidDataModal, setShowInvalidDataModal] = useState(false);
	const [invalidTableData, setInvalidTableData] = useState([]);
	const [copyPasteData, setCopyPasteData] = useState("");
	const [modifiedFilterConfig, setModifiedFilterConfig] = useState([]);
	const [tempTableData, setTempTableData] = useState([]);
	const [flagToStopRepeatedSetData, setFlagToStopRepeatedSetData] = useState(false);
	const [inValidData, setInValidData] = useState([]);
	const [validData, setValidData] = useState([]);
	const [productColumnDefs, setProductColumnDefs] = useState(createProductTableConfig);

	useEffect(() => {
		if (props?.includeProductGroup) {
			setProductButtonGroupOptions([
				...filterTypeButton,
				{
					label: "Product Group",
					value: "product_group",
				},
			]);
		}
		// This part added to modify the filter config is beacuse we need to pass event_id in the filter payload,
		// since this component is used in create offer screen
		// where we need to pass event_id in the filter payload, as to filter the option based on any retriction applied on event
		const modifiedFilterConfig = []
		_.cloneDeep(createSpecificProductFilterConfig).forEach((config) => {
			let newConfig = _.cloneDeep(config);
			if (props?.eventId) {
				newConfig["extraParams"] = {
					event_id: props?.eventId
				};
			}
			newConfig["isMandatory"] = props?.isFromOfferExclusion ? false : newConfig.isMandatory;
			modifiedFilterConfig.push(newConfig);
		});
		setModifiedFilterConfig(modifiedFilterConfig);

		// clear all filters data on component unmount
		return () => {
			if (clearOnUnmount) {
				dispatch(
					resetAllFiltersData({
						from: "CREATE_EVENT_SPECIFIC_PRODUCT",
					})
				);
				dispatch(setEventProductTableData([]));
				dispatch(setTableDataFromUploadOrCopyPaste({}));
			}
		};
	}, []);

	useEffect(() => {
		if (props?.viewMode) {
			let def = _.cloneDeep(createProductTableConfig);
			def = _.filter(def, (item) => item.field !== "" && !item.checkboxSelection);
			setProductColumnDefs(def);
		} else {
			setProductColumnDefs(createProductTableConfig);
		}
	}, [props?.viewMode]);

	useEffect(() => {
		if (!_.isEmpty(props?.selectedProducts) && _.isEmpty(createEventProductTableData)) {
			setSelectedProductTableData(_.cloneDeep(props?.selectedProducts));
			dispatch(setEventProductTableData(_.cloneDeep(props?.selectedProducts)));
		}
	}, [props?.selectedProducts]);

	useEffect(() => {
		if (props?.disabledOptions) {
			setProductButtonGroupOptions((prev) => {
				return prev.map((option) => {
					if (props?.disabledOptions.includes(option.value)) {
						return {
							...option,
							disabled: true,
						};
					}
					return option;
				});
			})
		}
	}, [props?.disabledOptions]);

	useEffect(() => {
		// if product data is fetched from API by selecting the filters, set it in table
		if (!_.isEmpty(createEventProductTableData)) {
			// if there is no previous data in table, set the fetched data
			handleSetProductTableData(createEventProductTableData);
		}
	}, [createEventProductTableData]);

	useEffect(() => {
		if (!_.isEmpty(props?.validData) && props?.isFromOfferExclusion) {
			setValidData(_.cloneDeep(props?.validData));
		}
	}, [props?.validData])

	const handleSetProductTableData = (data) => {
		if (flagToStopRepeatedSetData) return;
		if (productTableData?.length) {
			setShowDataOverridePrompt(true);
			setTempTableData(data);
		} else if (selectedFilterType === "upload" ||
			selectedFilterType === "copy_paste") {
			dispatch(setEventProductTableData(_.cloneDeep(data)))
			dispatch(setTableDataFromUploadOrCopyPaste({}));
			setProductTableData(data);
		} else {
			setProductTableData(data);
		}
		setFlagToStopRepeatedSetData(true);
	}

	useEffect(() => {
		// if product data is fetched from API by uploading excel or copy paste, set it in table
		if (
			(selectedFilterType === "upload" ||
				selectedFilterType === "copy_paste") &&
			!_.isEmpty(tableDataFromUploadOrCopyPaste)
		) {
			// if there are invalid or inactive products, show modal with invalid and inactive products
			if (
				tableDataFromUploadOrCopyPaste?.invalid?.length ||
				tableDataFromUploadOrCopyPaste?.inactive?.length
			) {
				const newTableData = [
					...tableDataFromUploadOrCopyPaste?.invalid || [],
					...tableDataFromUploadOrCopyPaste?.inactive || [],
				];
				setInvalidTableData(_.cloneDeep(newTableData));
				setShowInvalidDataModal(true);
			} else {
				// if there are only valid products, set it in table
				handleSetProductTableData(tableDataFromUploadOrCopyPaste.valid);
			}
		}
	}, [tableDataFromUploadOrCopyPaste]);

	const onComponentFiltersApply = () => {
		let callAPI = true;
		// check if all mandatory filters are selected
		const filtersDataSelected = _.cloneDeep(
			filtersData["CREATE_EVENT_SPECIFIC_PRODUCT"]
		);
		_.forEach(modifiedFilterConfig, (config) => {
			const key = config.filterId;
			if (
				config.isMandatory &&
				!filtersDataSelected?.[key]?.selectedOptionsArray?.length
			) {
				callAPI = false;
			}
		});
		if (!callAPI) {
			dispatch(toastError("Please select all mandatory filters"));
			return;
		}
		// set last filters data for replace and retain prompt
		if (_.isEmpty(lastFiltersData))
			setLastFiltersData(_.cloneDeep(filtersDataSelected));
		// if all mandatory filters are selected, call API to get product data
		const payload = fabricatePayloadHierarchy(filtersDataSelected);

		payload.event_id = props?.eventId || null;

		dispatch(callCreateProductConfigFilterTableAPI(payload));
		setFlagToStopRepeatedSetData(false);
	};

	const onClearFilter = () => {
		// clear filters data for product configuration
		dispatch(
			resetAllFiltersData({
				from: "CREATE_EVENT_SPECIFIC_PRODUCT",
			})
		);
	};

	const onRowSelection = useCallback(() => {
		// set selected products data
		const selectedRows = tableRef.current.api.getSelectedRows();
		setSelectedProductTableData(_.cloneDeep(selectedRows));
		setSelectedProducts(_.cloneDeep(selectedRows));
	});

	const rowDataChanged = () => {
		// whenever row data updates in AG Grid, select all rows
		if (tableRef?.current?.api) tableRef.current.api.selectAll();
	};

	const onReplaceAndOverwriteClick = () => {
		// replace table data with current selection and set last filters data same as current selection
		setProductTableData(_.cloneDeep(tempTableData));
		setShowDataOverridePrompt(false);
		// adding this as the dispatch will trigger the useEffect then the handleSetProductTableData will be called
		// and it will again set the same data in table, so we need to stop that
		setFlagToStopRepeatedSetData(true);
		dispatch(setEventProductTableData(_.cloneDeep(tempTableData)));
		if (selectedFilterType === "upload" || selectedFilterType === "copy_paste") {
			dispatch(setTableDataFromUploadOrCopyPaste({}));
		}
		setTempTableData([]);
		setLastFiltersData(filtersData["CREATE_EVENT_SPECIFIC_PRODUCT"]);
	};

	const onRetainClick = () => {
		//concat current data with previous data, and set it in table
		let currentRows = _.cloneDeep(productTableData);
		const tempRows = _.cloneDeep(tempTableData || []).map(item => ({
			...item,
			// convert product_id to number as on upload it is string, and _.uniqBy is not working with string
			product_id: Number(item.product_id)
		}));
		currentRows = [
			...currentRows,
			...tempRows,
		];
		currentRows = _.uniqBy(currentRows, "product_id");
		setFlagToStopRepeatedSetData(true);
		setProductTableData(_.cloneDeep(currentRows));
		setShowDataOverridePrompt(false);
		setTempTableData([]);
		dispatch(setEventProductTableData(_.cloneDeep(currentRows)));
		if (selectedFilterType === "upload" || selectedFilterType === "copy_paste") {
			dispatch(setTableDataFromUploadOrCopyPaste({}));
		}

		// concat currennt data with previous data and set both last filters and current filters as same
		const updatedFiltersData = mergeFiltersData(
			_.cloneDeep(lastFiltersData),
			_.cloneDeep(filtersData["CREATE_EVENT_SPECIFIC_PRODUCT"])
		);
		setLastFiltersData(_.cloneDeep(updatedFiltersData));
		dispatch(
			overwriteFilters({
				filtersData: _.cloneDeep(updatedFiltersData),
				activeScreen: "CREATE_EVENT_SPECIFIC_PRODUCT",
			})
		);
	};

	const onFilterTypeTabChange = (e) => {
		// set selected filter type and reset all filters data if filter type is not select filters
		setSelectedFilterType(e.target.value);
		setSelectedSpecificProductType(e.target.value);
		if (e.target.value !== "hierarchy") {
			dispatch(
				resetAllFiltersData({
					from: "CREATE_EVENT_SPECIFIC_PRODUCT",
				})
			);
		}
		setUploadedExcel([]);
		setCopyPasteData("");
	};

	const onUploadExcelNextClick = async (file) => {
		// on next click of excel upload, call API to get product data
		if (!file.file) return;
		const formData = new FormData();
		formData.append("file", file.file);
		setFlagToStopRepeatedSetData(false);
		if (props?.isFromOfferExclusion) {
			const response = await dispatch(
				getProductFromExcelForProductExclusion(formData)
			);
			setInValidData(response?.invalid || []);
			setValidData(response?.valid || []);
			props?.setUploadedExcel(response?.valid || []);
		} else {
			const response = await dispatch(
				getCreateProductGrpDataFromExcel(formData)
			);
			if (!response) {
				props?.setUploadedExcel([]);
				setUploadedExcel([]);
			}
		}
	};

	const onActiveInactiveProductProceed = () => {
		// on proceed click of invalid products modal, set valid and inactive products in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		const newTableData = [
			...tableDataFromUploadOrCopyPaste?.valid || [],
			...tableDataFromUploadOrCopyPaste?.inactive || [],
		];

		handleSetProductTableData(newTableData);
	};

	const onActiveProductProceed = () => {
		// on proceed click of invalid products modal, set only valid products in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		handleSetProductTableData(tableDataFromUploadOrCopyPaste.valid || []);
	};

	const onCopyPasteChange = (e) => {
		// set copy paste data in state
		const data = e.target.value;
		let array = _.filter(data.split("\n"), (item) => item !== "");
		let val = array.join(",");
		setCopyPasteData(val);
	};

	const onCopyPasteSubmitClick = () => {
		// on submit click of copy paste, call API to get product data
		let productIds = copyPasteData.split(",");
		productIds = _.map(productIds, (value) => value.trim());
		productIds = _.filter(productIds, (item) => item !== "").map(item => [...item.split(" ")]);
		if (_.isEmpty(productIds)) {
			dispatch(toastError("Please enter some product IDs"));
			return;
		}
		const payload = { products_data: productIds };
		dispatch(getCreateProductGrpDataFromCopyPaste(payload));
		setFlagToStopRepeatedSetData(false);
		setCopyPasteData("");
	};

	const excelDownloadHandler = () => {
		// download excel template for product selection
		const payload = {
			type: props?.isFromOfferExclusion ? "promo_exclusion_upload" : "promo_product_upload",
		};
		dispatch(
			excelTemplateDownload(
				{
					params: payload,
					responseType: "blob",
				},
				"product_selection_template"
			)
		);
	};

	const onFilterTextBoxChanged = useCallback((text) => {
		setGlobalSearchText(text);
		tableRef.current.api.setGridOption("quickFilterText", text);
	}, []);

	const onFirstDataRendered = (params) => {
		const nodesToSelect = [];
		const selected_ids = props?.selectedProducts?.map((group) => group.product_id);
		params.api.forEachNode((node) => {
			if (selected_ids.includes(node.data?.product_id)) {
				nodesToSelect.push(node);
			}
		});
		params.api.setNodesSelected({ nodes: nodesToSelect, newValue: true });
	};

	return (
		<div>
			<div className="productConfigSelectionContainer marginTop-24">
				<ButtonGroup
					selectedOption={selectedFilterType}
					onChange={onFilterTypeTabChange}
					options={productButtonGroupOptions}
				/>
				<p className="text-12-500 marginTop-12">
					Please select one of the methods for uploading data to get
					results
				</p>
				<div className="marginTop-24">
					{selectedFilterType === "hierarchy" && (
						<div className="flexColumn flex24">
							<ComponentFilters
								filterConfig={modifiedFilterConfig}
								callAPIonLoad={false}
								screen="CREATE_EVENT_SPECIFIC_PRODUCT"
								onPrimaryButtonClick={onComponentFiltersApply}
								onSecondaryButtonClick={onClearFilter}
								primaryButtonText={
									!props?.isFromOfferExclusion ?
										"Submit" :
										null
								}
								secondaryButtonText="Clear Filters"
							/>
						</div>
					)}
					{selectedFilterType === "upload" && (
						<FileUpload
							fileList={uploadedExcel}
							numberOfFiles={1}
							onFileListChange={(files) => {
								setUploadedExcel(files);
								onUploadExcelNextClick(files?.[0] || {});
							}}
							validFileTypes={[
								{
									fileType: "xlsx",
									templateDownloader: excelDownloadHandler,
									typeOverride: false,
								},
							]}
						/>
					)}
					{selectedFilterType === "copy_paste" && (
						<div>
							<TextArea
								onChange={onCopyPasteChange}
								placeholder="Paste IDs here..."
								value={copyPasteData}
								width={"575px"}
							/>
							<div className="buttons_container">
								<Button
									onClick={() => setCopyPasteData("")}
									size="large"
									variant="url"
								>
									Reset
								</Button>
								<Button
									onClick={onCopyPasteSubmitClick}
									size="large"
									variant="primary"
									disabled={!copyPasteData?.length}
								>
									Submit
								</Button>
							</div>
						</div>
					)}
					{selectedFilterType === "product_group" && (
						<ProductGroupSelection
							eventId={props?.eventId}
							setSelectedProductGroups={props.setSelectedProductGroups}
							clearOnUnmount={clearOnUnmount}
							selectedProductGroups={props.selectedProductGroups}
							{...props}
						/>
					)}
				</div>
				{
					(selectedFilterType !== "product_group" && !(
						(props?.isFromOfferExclusion && (selectedFilterType == "upload" || selectedFilterType == "hierarchy"))
					)) && (
						<div className="marginTop-24">
							{(productTableData?.length) ? (
								<div className="marginTop-24">
									<Table
										tableHeader={"Products"}
										ref={tableRef}
										suppressMenuHide
										rowData={productTableData}
										columnDefs={productColumnDefs}
										rowSelection="multiple"
										onSelectionChanged={onRowSelection}
										// onRowDataUpdated={rowDataChanged}
										onFirstDataRendered={onFirstDataRendered}
										topRightOptions={
											<div className="centerFlexWithGap12">
												<div className="positionRelative">
													{showGlobalSearch ? (
														<div className="tableGlobalSearchContainer">
															<Input
																onChange={(e) =>
																	onFilterTextBoxChanged(
																		e.target.value
																	)
																}
																placeholder="Search"
																rightIcon={
																	<img
																		src={SearchIcon}
																		alt="Search Icon"
																	/>
																}
																type="text"
																value={globalSearchText}
															/>
														</div>
													) : null}
													<Button
														iconPlacement="left"
														icon={
															<img
																src={SearchIcon}
																alt="Search Icon"
															/>
														}
														onClick={() =>
															setShowGlobalSearch(
																(prev) => !prev
															)
														}
														size="large"
														variant="tertiary"
													/>
												</div>
											</div>
										}
										topLeftOptions={
											<Badge
												color="default"
												label={`Selected Products: ${selectedProductTableData?.length} / ${productTableData?.length}`}
												size="default"
												variant="subtle"
											/>
										}
									/>
								</div>
							) : <EmptyData />}
						</div>
					)}
				{(props?.isFromOfferExclusion && selectedFilterType == "upload") && (
					<div className="marginTop-24">
						{!_.isEmpty(validData) && (
							<Table
								tableHeader={"Valid Data"}
								rowData={validData}
								columnDefs={exclusionProductTableConfig}
							/>
						)}
						{!_.isEmpty(inValidData) && (
							<Table
								tableHeader={"Invalid Data"}
								rowData={inValidData}
								columnDefs={exclusionProductTableConfig}
							/>
						)
						}
					</div>
				)}
			</div>
			<Prompt
				handleClose={() => {
					setTempTableData([]);
					setShowDataOverridePrompt(false);
				}}
				onPrimaryButtonClick={onRetainClick}
				onSecondaryButtonClick={onReplaceAndOverwriteClick}
				primaryButtonLabel="Retain"
				secondaryButtonLabel="Replace & overwrite"
				title="Confirm Selection"
				variant="warning"
				isOpen={showDataOverridePrompt}
			>
				Do you want to Retain and Continue or Replace table with current
				selection
			</Prompt>
			<Modal
				onClose={() => {
					setShowInvalidDataModal(false);
				}}
				onPrimaryButtonClick={onActiveInactiveProductProceed}
				onSecondaryButtonClick={onActiveProductProceed}
				primaryButtonLabel="Proceed with all Active and Inactive Products"
				secondaryButtonLabel="Proceed with only Active Products"
				size="medium"
				title="Products Detail"
				open={showInvalidDataModal}
			>
				<div className="invalidProductsGrpModalContainer">
					<span className="secondaryText-14-500">
						Based on the data feed Pricesmart received, the uploaded
						list contains some inactive/invalid SKUs
					</span>
					<Table
						tableHeader={"Products"}
						suppressMenuHide
						rowData={invalidTableData}
						columnDefs={invalidDataColumnConfig}
					/>
				</div>
			</Modal>
		</div>
	);
}

export default SpecficProductSelection;
