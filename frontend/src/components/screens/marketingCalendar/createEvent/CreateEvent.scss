.create-event-card {
	// width: 580px;
    height: 200px;
    display: flex;
    justify-content: space-between;
}

.create-event-card-text-container {
    position: relative;
    height: 100%;
    min-width: 300px;
}

.create-event-card-button {
    position: absolute;
    bottom: 0;
}

.panel-icon {
    width: 20px; 
    height: 20px;
    margin: 5px;
    border-radius: 4px;
}

.create-event-container {
    width: 900px;
    .impact_drawer_filter_container_right_panel{
        position: relative;
        overflow: hidden;
        .impact_drawer_filter_footer {
            left: 0;
            right: 0;
        }
    }
}

.filter-title {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 8px;
}

.filter-title-container {
   max-width: 196px;
   overflow: hidden;
}

.promo-restrictions {
    .impact_drawer_filter_tab {
        position: relative;
    }
    .locked-filter::after {
        width: 18px;
        height: 18px;
        background: #fff url("./../../../../assets/imageAssets/locked.svg")
            no-repeat;
        background-size: 10px;
        background-position: center;
        position: absolute;
        content: " ";
        right: 34px;
        border-radius: 20px;
        border: 1px solid #c3c8d4;
    }
    .saved-filter::before {
        width: 18px;
        height: 18px;
        background: #fff url("./../../../../assets/imageAssets/save.svg")
            no-repeat;
        background-size: 10px;
        background-position: center;
        position: absolute;
        content: " ";
        right: 8px;
        border-radius: 20px;
        border: 1px solid #c3c8d4;
    }
    .impact-drawer-filter-main-container
        .impact_drawer_filter_container_left_panel {
        width: 281px;
    }
    .impact_drawer_filter_container
        .impact_drawer_filter_container_right_panel
        .impact_drawer_filter_footer {
        left: 281px;
    }
}

.error-msg {
    color: red;
}

.lock-icon {
    padding: 8px;
    background: #F5F6FA;
    border-radius: 8px;
    height: 32px;
    width: 32px;
    cursor: pointer;
}

.create-event-card-summary-chip {
    font-size: 12px;
    display: flex;
    align-items: center;
    background: #F5F6FA;
    border-radius: 6px;
    padding: 2px 4px;
    justify-content: space-between;
    gap: 24px;
}

.create-event-card-summary-chip-icon {
    background: white;
    border: 1px solid #C3C8D4;
    border-radius: 50%;
    padding: 2px;
    width: 18px;
    height: 18px;
    text-align: center;
    aspect-ratio: 1;
}

.create-event-card-summary-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 8px 0;
}

.create-event-scroll-screen-container {
    height: calc(100vh - 160px);
    overflow: scroll;
}

.card-conatainer {
    flex: 1;
}

.promo-restrictions{
    .impact_drawer_filter_container .impact_drawer_filter_container_right_panel .impact_drawer_filter_footer{
        left: 281px !important;
    }
}
.add-drag-message {
    display: flex;
    border-radius: 8px;
    border: 1px solid #7BCFFF;
    background: #E2F4FF;
    padding: 12px;
    margin: 16px 0;
    gap: 12px;
    align-items: center;
}