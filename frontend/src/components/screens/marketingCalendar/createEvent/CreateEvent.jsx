import React, { useEffect, useMemo, useState } from "react";
import "react-dates/initialize";
import moment from "moment";
import {
    Select,
    Input,
    DateRangePicker,
    DatePicker,
    But<PERSON>,
    Modal,
} from "impact-ui";
import { Table } from "impact-ui-v3";
import NotifyImage from "../../../../assets/imageAssets/notifyImage.png";
import "./CreateEvent.scss";
import {
    createEventDropdownConfig,
    effectedPromosColumnConfig,
} from "./createEventConstants";
import {
    DEFAULT_DATE_FORMAT,
    global_labels,
} from "../../../../constants/Constants";
import Restrictions from "./Restrictions";
import { useDispatch, useSelector } from "react-redux";
import {
    createEvent,
    getEffectedPromos,
    getEventDetails,
    getEventObjectiveOptions,
    getEventTypeOptions,
    setActiveEventId,
    setEventDetails,
    setEventObjectives,
    setEventTypes,
    updateEvent,
    updateEventDetails,
} from "../../../../store/features/eventReducer/eventReducer";
import { toastError } from "../../../../store/features/global/global";
import { useNavigate } from "react-router-dom-v5-compat";
import Exclusion from "./Exclusion";
import ScreenBreadcrumb from "../../../common/breadCrumb/ScreenBreadcrumb";
import { breadcrumbRoutes } from "../../../../constants/RouteConstants";
import _ from "lodash";

const CreateEvent = (props) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const {
        eventDetails = {},
        eventTypes = [],
        eventObjectives = [],
        activeEventId,
    } = useSelector((store) => {
        return store?.pricesmartPromoReducer.event;
    });

    const { global_configs } = useSelector(
        (store) => store?.pricesmartPromoReducer?.global
    );

    const defaultOptions = {
        eventAdType: {
            label: "Advertised",
            value: "advertised",
        },
    };

    const [eventName, setEventName] = useState("");
    const [marketingNotes, setMarketingNotes] = useState("");
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [submitOfferByDate, setSubmitOfferByDate] = useState(null);
    const [eventType, setEventType] = useState("");
    const [eventAdType, setEventAdType] = useState(defaultOptions.eventAdType);
    const [eventObjective, setEventObjective] = useState("");

    const [isEventDropdownOpen, setIsEventDropdownOpen] = useState(false);
    const [isEventTypeDropdownOpen, setIsEventTypeDropdownOpen] = useState(
        false
    );
    const [
        isEventObjectiveDropdownOpen,
        setIsEventObjectiveDropdownOpen,
    ] = useState(false);
    const [effectedPromos, setEffectedPromos] = useState([]);
    const [isEffectedPromosModalOpen, setIsEffectedPromosModalOpen] = useState(
        false
    );
    const [isEditedFlag, setIsEditedFlag] = useState(false);
    const [isEditedForBasicDetails, setIsEditedForBasicDetails] = useState(
        false
    );

    useEffect(() => {
        if (!global_configs?.event?.use_event) {
            navigate("/pricesmart-promo/not-found");
            return;
        }
        dispatch(
            updateEventDetails({
                eventAdType: defaultOptions.eventAdType.value,
            })
        );
        callGetEventTypesApi();
        callGetEventObjectivesApi();

        const query = new URLSearchParams(window.location.search);
        const event_id = query.get(global_labels.event_primary)
            ? parseInt(query.get(global_labels.event_primary))
            : null;
        if (event_id) {
            callGetEventDetailApi(event_id);
        }
        return () => {
            clearAll(true);
        };
    }, []);

    useEffect(() => {
        if (activeEventId) {
            setEventName(eventDetails.name || "");
            setMarketingNotes(eventDetails.marketing_notes || "");
            setStartDate(
                eventDetails.start_date ? moment(eventDetails.start_date) : null
            );
            setEndDate(
                eventDetails.end_date ? moment(eventDetails.end_date) : null
            );
            setSubmitOfferByDate(
                eventDetails.submit_offers_by
                    ? moment(eventDetails.submit_offers_by || null)
                    : null
            );
            setEventType(
                eventTypes.find(
                    (type) => type.value === eventDetails?.event_type
                )
            );
            setEventAdType(
                createEventDropdownConfig.eventAdType.find(
                    (type) => type.value === eventDetails?.event_ad_type
                )
            );
            setEventObjective(
                eventObjectives.find(
                    (type) => type.value === eventDetails?.event_objective
                )
            );
        }
    }, [activeEventId, eventTypes, eventObjectives]);

    const callGetEventDetailApi = async (event_id) => {
        await dispatch(
            getEventDetails({
                event_id: event_id,
            })
        );
        dispatch(setActiveEventId(event_id));
    };

    const callGetEventTypesApi = async () => {
        await dispatch(getEventTypeOptions());
    };

    const callGetEventObjectivesApi = async () => {
        await dispatch(getEventObjectiveOptions());
    };

    const startDateHandler = (date) => {
        setSubmitOfferByDate(null);
        setStartDate(date);
        detectBasicDetailsChanges({
            start_date_of_event: moment(date).format(DEFAULT_DATE_FORMAT),
        });
    };

    const endDateHandler = (date) => {
        setSubmitOfferByDate(null);
        setEndDate(date);
        detectBasicDetailsChanges({
            end_date_of_event: moment(date).format(DEFAULT_DATE_FORMAT),
        });
    };

    const isOutsideRange = (date) => {
        return (
            moment(date).isAfter(moment(startDate).add(-1, "days")) ||
            moment(date).isBefore(moment())
        );
    };

    const clearAll = (all) => {
        setEventName("");
        setMarketingNotes("");
        setStartDate(null);
        setEndDate(null);
        setSubmitOfferByDate(null);
        setEventType("");
        setEventAdType();
        setEventObjective("");
        dispatch(
            setEventDetails({
                eventName: "",
                marketingNotes: "",
                startDate: null,
                endDate: null,
                submitOffersBy: null,
                eventType: "",
                eventAdType: defaultOptions.eventAdType.value,
                eventObjective: "",
                attribute: "",
                product_exclusion: {},
                date_restriction: {},
                product_restriction: {},
                store_restriction: {},
                customer_restriction: {},
                promotion_restriction: {},
            })
        );
        dispatch(setActiveEventId(null));
        if (all === true) {
            dispatch(setEventTypes([]));
            dispatch(setEventObjectives([]));
        }
    };

    const allowSave = () => {
        if (
            _.isEmpty(eventName) ||
            _.isEmpty(marketingNotes) ||
            _.isEmpty(startDate) ||
            _.isEmpty(endDate) ||
            _.isEmpty(submitOfferByDate) ||
            _.isEmpty(eventAdType)
        ) {
            dispatch(toastError("Please fill all the required fields"));
            return false;
        }

        return true;
    };

    const onSaveHandler = async () => {
        if (!allowSave() || disabledSaveButton) return;

        const payload = createEventPayload();
        let res = false;
        if (activeEventId) {
            payload.event_id = activeEventId;
            res = await dispatch(updateEvent(payload));
        } else {
            res = await dispatch(createEvent(payload));
        }
        if (res) {
            clearAll(true);
            navigate("/pricesmart-promo/marketing-calendar");
        }
    };

    const createProductRestrictionPayload = () => {
        const { product_restriction = {} } = eventDetails;

        let productRestrictionPayload = {};
        if (!_.isEmpty(product_restriction?.product_restriction_level)) {
            const {
                product_restriction_level,
                lock,
                specific_product_type,
                products = [],
                product_groups = [],
                product_hierarchy = {},
            } = _.cloneDeep(product_restriction);

            productRestrictionPayload = {
                product_restriction_level,
                lock,
                specific_product_type: specific_product_type || "",
                products:
                    product_restriction_level !== "product_group"
                        ? products?.map((product) => product.product_id)
                        : [],
                product_groups:
                    product_restriction_level === "product_group"
                        ? product_groups?.map(
                              (productGroup) => productGroup.product_group_id
                          )
                        : [],
                product_hierarchy: {},
            };
            if (product_hierarchy) {
                Object.keys(product_hierarchy).forEach((key) => {
                    if (product_hierarchy?.[key].length) {
                        productRestrictionPayload.product_hierarchy[
                            key
                        ] = product_hierarchy[key]?.map((item) => item.value);
                    }
                });
            }
        } else {
            productRestrictionPayload = {
                product_restriction_level: "",
                lock: false,
                products: [],
                product_groups: [],
                specific_product_type: "",
            };
        }

        return productRestrictionPayload;
    };

    const createStoreRestrictionPayload = () => {
        const { store_restriction = {} } = eventDetails;

        let storeRestrictionPayload = {};

        if (!_.isEmpty(store_restriction?.store_restriction_level)) {
            const {
                store_restriction_level,
                lock,
                specific_store_type,
                stores = [],
                store_groups = [],
            } = _.cloneDeep(store_restriction);

            storeRestrictionPayload = {
                store_restriction_level,
                lock,
                specific_store_type,
                stores:
                    specific_store_type !== "store_group"
                        ? stores?.map((store) => store.store_id)
                        : [],
                store_groups:
                    specific_store_type === "store_group" // only in the case of selectedSpecificStoreType as storeGroup, we need to change the store_restriction_level to store_group
                        ? store_groups?.map(
                              (storeGroup) => storeGroup.store_group_id
                          )
                        : [],
            };
            // only in the case of selectedSpecificStoreType as storeGroup, we need to change the store_restriction_level to store_group
            // as the API expects store_restriction_level as store_group when specific_store_type is storeGroup
            if (specific_store_type === "store_group") {
                storeRestrictionPayload.store_restriction_level = "store_group";
            }
        } else {
            storeRestrictionPayload = {
                store_restriction_level: "",
                lock: false,
                stores: [],
                store_groups: [],
            };
        }

        return storeRestrictionPayload;
    };

    const createProductExclusionPayload = () => {
        const { product_exclusion = {} } = eventDetails;

        let productExclusionPayload = {
            products: [],
            product_groups: [],
            product_hierarchy: {},
            product_exclusion_level: "",
            specific_product_type: null,
        };

        if (!_.isEmpty(product_exclusion)) {
            const {
                product_exclusion_level,
                products,
                product_groups,
                selectedSpecificProductType,
                hierarchy,
            } = _.cloneDeep(product_exclusion);

            productExclusionPayload = {
                product_exclusion_level: product_exclusion_level,
                products: products?.map((product) => product.product_id) || [],
                product_groups:
                    product_groups?.map(
                        (productGroup) => productGroup.product_group_id
                    ) || [],
                specific_product_type: selectedSpecificProductType || "",
                product_hierarchy: {},
            };

            if (hierarchy) {
                Object.keys(hierarchy).forEach((key) => {
                    if (hierarchy?.[key]?.length > 0) {
                        productExclusionPayload.product_hierarchy[
                            key
                        ] = hierarchy[key]?.map((item) => item.value);
                    }
                });
            }
        }

        return productExclusionPayload;
    };

    const basicEventPayload = () => {
        const basicEventPayload = {
            name: eventName,
            marketing_notes: marketingNotes,
            start_date: startDate.format("YYYY-MM-DD"),
            end_date: endDate.format("YYYY-MM-DD"),
            submit_offers_by: submitOfferByDate.format("YYYY-MM-DD"),
            event_type: eventType?.value || "",
            event_ad_type: eventAdType?.value || "",
            event_objective: eventObjective?.value || "",
            product_exclusion: null,
            // attribute: attribute || "",
        };

        if (activeEventId) {
            basicEventPayload.event_id = activeEventId;
        }

        return basicEventPayload;
    };

    const createEventPayload = () => {
        const { date_restriction } = eventDetails;

        const payload = basicEventPayload();

        // ----------------------- Restriction data -----------------------
        // ---- Date
        if (!_.isEmpty(date_restriction?.sameAsEvent)) {
            payload.date_restriction = date_restriction;
        } else {
            payload.date_restriction = {
                sameAsEvent: "no",
                minPromotionDays: null,
                maxPromotionDays: null,
                promotionStartDay: null,
                promotionEndDay: null,
            };
        }
        // ---- Product
        const productRestrictionPayload = createProductRestrictionPayload();
        payload.product_restriction = productRestrictionPayload;
        // ---- Store
        const storeRestrictionPayload = createStoreRestrictionPayload();
        payload.store_restriction = storeRestrictionPayload;

        // ----------------------- Exclusion data -----------------------
        const productExclusionPayload = createProductExclusionPayload();
        payload.product_exclusion = productExclusionPayload;

        return payload;
    };

    const getEffectedPromosHandler = async () => {
        if (!allowSave() || disabledSaveButton) return;
        const payload = createEventPayload();
        const effectedPromos = await dispatch(getEffectedPromos(payload));
        if (effectedPromos === false) {
            dispatch(toastError(`Error fetching effected ${global_labels?.promo_standard_plural}`));
            return;
        }
        if (effectedPromos?.length > 0) {
            setIsEffectedPromosModalOpen(true);
            setEffectedPromos(effectedPromos);
        } else {
            onSaveHandler();
        }
    };

    const detectBasicDetailsChanges = (changes) => {
        const {
            name,
            end_date,
            start_date,
            submit_offers_by,
            event_type = "",
            event_ad_type = "",
            event_objective = "",
            marketing_notes = "",
        } = eventDetails;

        const {
            name_of_event = eventName,
            start_date_of_event = moment(startDate).format(DEFAULT_DATE_FORMAT),
            end_date_of_event = moment(endDate).format(DEFAULT_DATE_FORMAT),
            submit_offers_by_date = moment(submitOfferByDate).format(
                DEFAULT_DATE_FORMAT
            ),
            marketing_notes_of_event = marketingNotes || "",
            event_ad_type_of_event = eventAdType?.value || "",
            event_type_of_event = eventType?.value || "",
            event_objective_of_event = eventObjective?.value || "",
        } = changes;

        if (
            name !== name_of_event ||
            end_date !== end_date_of_event ||
            start_date !== start_date_of_event ||
            event_ad_type !== event_ad_type_of_event ||
            event_objective !== event_objective_of_event ||
            marketing_notes !== marketing_notes_of_event ||
            submit_offers_by !== submit_offers_by_date ||
            event_type !== event_type_of_event
        ) {
            setIsEditedForBasicDetails(true);
        } else {
            setIsEditedForBasicDetails(false);
        }
    };

    const disabledSaveButton = useMemo(() => {
        return !(isEditedFlag || isEditedForBasicDetails);
    }, [isEditedForBasicDetails, isEditedFlag]);

    return (
        <div>
            <ScreenBreadcrumb
                breadcrumbList={breadcrumbRoutes()?.["createEvent"]}
            />
            <div className="screen_data_container paddingTop-12 create-event-scroll-screen-container">
                <div className="content_container">
                    <p className="text-16-800">Create {global_labels.event_primary}</p>
                    <div className="flex24 paddingTop-16 marginBottom-24">
                        <Input
                            id="event_name"
                            inputProps={{}}
                            label={`Name of the ${global_labels.event_primary}`}
                            name=""
                            onChange={(e) => {
                                setEventName(e.target.value);
                                detectBasicDetailsChanges({
                                    name_of_event: e.target.value,
                                });
                            }}
                            placeholder={`${global_labels.event_primary} name`}
                            type="text"
                            isRequired={true}
                            value={eventName}
                        />

                        <DateRangePicker
                            label="Please select season date range"
                            isRequired={true}
                            showRangeSelector={false}
                            startDate={startDate}
                            setStartDate={startDateHandler}
                            endDate={endDate}
                            setEndDate={endDateHandler}
                            startDateInputProps={{
                                label: "StartDate",
                                name: "startDate",
                            }}
                            endDateInputProps={{
                                label: "EndDate",
                                name: "endDate",
                            }}
                            // minDate={moment(new Date()).add(1, "days")}
                            isOutsideRange={(date) =>
                                moment(date).isBefore(moment().add(0, "days"))
                            }
                            displayFormat="MM-DD-YYYY"
                        />
                        <DatePicker
                            label={`Submit ${global_labels?.promo_alias} by`}
                            isRequired={true}
                            selectedDate={submitOfferByDate}
                            setSelectedDate={(date) => {
                                setSubmitOfferByDate(date);
                                detectBasicDetailsChanges({
                                    submit_offers_by_date: moment(date).format(
                                        DEFAULT_DATE_FORMAT
                                    ),
                                });
                            }}
                            isOutsideRange={isOutsideRange}
                            isDisabled={_.isEmpty(startDate)}
                            displayFormat="MM-DD-YYYY"
                        />
                        <Input
                            id="marketing_notes"
                            inputProps={{}}
                            label="Marketing Notes"
                            name=""
                            onChange={(e) => {
                                setMarketingNotes(e.target.value);
                                detectBasicDetailsChanges({
                                    marketing_notes_of_event: e.target.value,
                                });
                            }}
                            placeholder="Marketing Notes"
                            type="text"
                            isRequired={true}
                            value={marketingNotes}
                        />
                    </div>
                    <div className="horizontal-divider-line" />
                    <div className="flex24 paddingTop-16 marginBottom-24">
                        <Select
                            label={`${global_labels.event_primary} ad type`}    
                            setSelectedOptions={(selectedOptions) => {
                                setEventAdType(selectedOptions);
                                detectBasicDetailsChanges({
                                    event_ad_type_of_event:
                                        selectedOptions.value,
                                });
                            }}
                            selectedOptions={eventAdType}
                            setCurrentOptions={() => {}}
                            isRequired={true}
                            currentOptions={
                                createEventDropdownConfig.eventAdType
                            }
                            isOpen={isEventDropdownOpen}
                            setIsOpen={setIsEventDropdownOpen}
                            placeholder={`Select ${global_labels.event_primary} ad type`}
                        />
                        <Select
                            label={`${global_labels.event_primary} type`}
                            setSelectedOptions={(selectedOptions) => {
                                setEventType(selectedOptions);
                                detectBasicDetailsChanges({
                                    event_type_of_event: selectedOptions.value,
                                });
                            }}
                            selectedOptions={eventType}
                            setCurrentOptions={() => {}}
                            currentOptions={eventTypes || []}
                            isOpen={isEventTypeDropdownOpen}
                            setIsOpen={setIsEventTypeDropdownOpen}
                            placeholder={`Select ${global_labels.event_primary} type`}
                        />
                        <Select
                            label={`${global_labels.event_primary} objective`}
                            setSelectedOptions={(selectedOptions) => {
                                setEventObjective(selectedOptions);
                                detectBasicDetailsChanges({
                                    event_objective_of_event:
                                        selectedOptions.value,
                                });
                            }}
                            selectedOptions={eventObjective}
                            setCurrentOptions={() => {}}
                            currentOptions={eventObjectives || []}
                            isOpen={isEventObjectiveDropdownOpen}
                            setIsOpen={setIsEventObjectiveDropdownOpen}
                            placeholder={`Select ${global_labels.event_primary} objective`}
                        />
                    </div>
                    <Button
                        onClick={() => clearAll(false)}
                        size="large"
                        variant="text"
                    >
                        Clear
                    </Button>
                </div>
                <div className="flex24 marginTop-24">
                    <Restrictions
                        start_date={startDate}
                        end_date={endDate}
                        setIsEditedFlag={setIsEditedFlag}
                    />
                    <Exclusion setIsEditedFlag={setIsEditedFlag} />
                    {/* <div className="content_container create-event-card">
            <div className="create-event-card-text-container">
              <p className="text-16-800">Notify</p>
              <p className="text-14-800 marginTop-20">
                This is where you can notify others about the {global_labels.event.toLocaleLowerCase()}
              </p>
              <p className="label-12px-normal marginTop-8">
                Click on “notify” to inform individual or group
              </p>
              <Button size="large" variant="secondary" className="create-event-card-button">
                Notify Others
              </Button>
            </div>
              <img src={NotifyImage} width={"152px"} alt="notify" />
          </div> */}
                </div>
            </div>
            <div className="footer_section">
                <Button
                    onClick={() => {
                        navigate("/pricesmart-promo/marketing-calendar");
                    }}
                    size="large"
                    variant="secondary"
                >
                    Cancel
                </Button>
                <Button
                    onClick={() => {
                        activeEventId
                            ? getEffectedPromosHandler()
                            : onSaveHandler();
                    }}
                    size="large"
                    variant="primary"
                    disabled={disabledSaveButton}
                >
                    {activeEventId ? "Update" : "Save"}
                </Button>
            </div>
            <Modal
                open={isEffectedPromosModalOpen}
                onClose={() => {
                    setIsEffectedPromosModalOpen(false);
                    setEffectedPromos([]);
                }}
                onPrimaryButtonClick={() => {
                    onSaveHandler();
                    setIsEffectedPromosModalOpen(false);
                    setEffectedPromos([]);
                }}
                onSecondaryButtonClick={() => {
                    setIsEffectedPromosModalOpen(false);
                    setEffectedPromos([]);
                }}
                primaryButtonLabel="Continue"
                secondaryButtonLabel="Cancel"
                size="medium"
                title="Do you want to proceed?"
            >
                <div className="">
                    <span className="secondaryText-14-500">
                        These promotions will update due to the{" "}
                        {global_labels.event_primary} edit 
                    </span>
                    <Table
                        tableHeader={"Stores"}
                        suppressMenuHide
                        rowData={effectedPromos}
                        columnDefs={effectedPromosColumnConfig}
                    />
                </div>
            </Modal>
        </div>
    );
};

export default CreateEvent;
