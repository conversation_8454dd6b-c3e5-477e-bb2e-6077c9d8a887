import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { global_labels } from '../../../../constants/Constants'
import { breadcrumbRoutes } from '../../../../constants/RouteConstants'
import ScreenBreadcrumb from '../../../common/breadCrumb/ScreenBreadcrumb'
import { Select, Input, DateRangePicker, DatePicker, Button, Modal } from "impact-ui";
import { Table } from "impact-ui-v3";
import "react-dates/initialize";
import { useNavigate } from "react-router-dom-v5-compat";
import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';
import moment from 'moment';
import { replaceSpecialCharacter, replaceSpecialCharToCharCode } from '../../../../utils/helpers/utility_helpers';
import Restrictions from './Restrictions';
import Exclusion from './Exclusion';
import { createEvent, getEffectedPromos, getEventConfigurable, getEventDetails, setActiveEventId, setEventDetails, updateEvent } from '../../../../store/features/eventReducer/eventReducer';
import { API, APIV3 } from '../../../../utils/axios';
import { toastError } from '../../../../store/features/global/global';
import "./CreateEvent.scss";
import { effectedPromosColumnConfig } from './createEventConstants';

const CreatEventConfigurable = () => {

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {
    eventDetails = {},
    activeEventId,
  } = useSelector(
    (store) => {
      return store?.pricesmartPromoReducer.event
    }
  );

  const {
    global_configs
  } = useSelector((store) => store?.pricesmartPromoReducer?.global);

  const [details, setDetails] = useState({});
  const [dropDownStates, setDropDownStates] = useState({});
  const [apiData, setApiData] = useState({});
  const [isEditedFlag, setIsEditedFlag] = useState(false);
  const [isEditedForBasicDetails, setIsEditedForBasicDetails] = useState(false);
  const [modifiedConfig, setModifiedConfig] = useState([]);

  const [effectedPromos, setEffectedPromos] = useState([]);
  const [isEffectedPromosModalOpen, setIsEffectedPromosModalOpen] = useState(false);
  const [eventConfigurable, setEventConfigurable] = useState([]);
  const [listOfFieldThatHaveAPIData, setListOfFieldThatHaveAPIData] = useState([]);
  const [isAllDataLoaded, setIsAllDataLoaded] = useState(false);

  const disabledSaveButton = useMemo(() => {
    return !(isEditedFlag || isEditedForBasicDetails)
  }, [isEditedForBasicDetails, isEditedFlag]);

  useEffect(() => {
    if (!global_configs?.event?.use_event) {
      navigate("/pricesmart-promo/not-found");
      return;
    }

    getEventConfigurableHandler();

    const query = new URLSearchParams(window.location.search);
    const event_id = query.get("event_id") ? parseInt(query.get("event_id")) : null;
    if (event_id) {
      callGetEventDetailApi(event_id);
    }
    return () => {
      clearAll();
    }
  }, []);

  useEffect(() => {
    if (!_.isEmpty(eventConfigurable)) {
      const createEventBaseAttributeConfig = _.cloneDeep(eventConfigurable);
      createEventBaseAttributeConfig.forEach((group) => {
        group.attributes.forEach((attribute) => {
          if (attribute.type === "dropdown" && attribute.extraConfig.optionFromApi && attribute.extraConfig.apiUrl) {
            setListOfFieldThatHaveAPIData((prev) => [...prev, attribute.id]);
            getApiData(attribute);
          }
        })
      })
      const modifiedConfig = createEventBaseAttributeConfig.map((group) => {
        group.attributes.sort((a, b) => a.order - b.order);
        return group;
      });
      setModifiedConfig(modifiedConfig);
    }
  }, [eventConfigurable]);

  useEffect(() => {
    if (listOfFieldThatHaveAPIData.length > 0) {
      const isAllDataLoaded = listOfFieldThatHaveAPIData.every((field) => apiData?.[field]);
      if (isAllDataLoaded) {
        setIsAllDataLoaded(true);
      }
    }
  }, [listOfFieldThatHaveAPIData, apiData]);

  useEffect(() => {
    if (activeEventId && !_.isEmpty(modifiedConfig) && isAllDataLoaded) {
      const details = {};
      modifiedConfig.forEach((group) => {
        group.attributes.forEach((attribute) => {
          if (attribute.type === "dropdown") {
            if (attribute?.extraConfig?.options) {
              details[attribute.id] = attribute?.extraConfig?.options?.find((option) => option.value === eventDetails?.[attribute.id]);
            } else {
              details[attribute.id] = apiData?.[attribute.id]?.find((option) => option.value === eventDetails?.[attribute.id]);
            }
          } else if (attribute.type === "date_range_picker") {
            details[attribute.extraConfig.start_date_id] = moment(eventDetails?.[attribute.extraConfig.start_date_id]);
            details[attribute.extraConfig.end_date_id] = moment(eventDetails?.[attribute.extraConfig.end_date_id]);
          } else if (attribute.type === "date_picker") {
            details[attribute.extraConfig.date_id] = moment(eventDetails?.[attribute.extraConfig.date_id]);
          } else if (attribute.type === "text") {
            if (attribute.extraConfig?.inputType === "number") {
              details[attribute.id] = eventDetails?.[attribute.id];
            } else {
              details[attribute.id] = replaceSpecialCharacter(eventDetails?.[attribute.id] || "");
            }
          } else {
            details[attribute.id] = eventDetails?.[attribute.id] || "";
          }
        })
      })
      setDetails(details);
    }
    
  }, [activeEventId, isAllDataLoaded]);

  const getEventConfigurableHandler = async () => {
    const eventConfigurable = await dispatch(getEventConfigurable());
    if (eventConfigurable) {
      setEventConfigurable(eventConfigurable);
    }
  }

  const callGetEventDetailApi = async (event_id) => {
    await dispatch(
      getEventDetails({
        event_id: event_id,
      })
    );
    dispatch(setActiveEventId(event_id))
  }

  const clearAll = () => {
    setDetails({});
    setDropDownStates({});
    setIsEditedFlag(false);
    setIsEditedForBasicDetails(false);
    dispatch(setEventDetails({}));
    dispatch(setActiveEventId(null));
  }

  const isOutsideRange = (date, attribute) => {
    const {
      default_date_restriction = true,
      no_of_days_before = 0,
      no_of_days_after = 0,
      dependency_start_date_id = null,
      dependency_end_date_id = null,
    } = attribute.extraConfig;

    const dependencyStartDate = _.isEmpty(details[dependency_start_date_id]) 
                              ? moment() 
                              : moment(details[dependency_start_date_id]);

    const dependencyEndDate = _.isEmpty(details[dependency_end_date_id]) 
                              ? moment() 
                              : moment(details[dependency_end_date_id]);

    const arr = [];
    if (default_date_restriction) {
      arr.push(moment(date).isBefore(moment().startOf("day")))
    }

    if ("no_of_days_before" in attribute.extraConfig) {
      arr.push(moment(date).isAfter(dependencyStartDate.subtract(no_of_days_before, "day")))
    }

    if ("no_of_days_after" in attribute.extraConfig) {
      arr.push(moment(date).isBefore(dependencyEndDate.add(no_of_days_after, "day")));
    }

    return arr.some((item) => item);
  }
  
  const textOnChangeHandler = (e, attribute) => {
    let value = e.target.value;
    if (attribute.extraConfig?.inputType === "number") {
      value = parseInt(value.replace(/[^0-9-]/g, ''));
      if (value < attribute.extraConfig.min) {
        value = attribute.extraConfig.min;
      } else if (value > attribute.extraConfig.max) {
        value = attribute.extraConfig.max;
      }
    }
    setDetails((prev) => ({...prev, [attribute.id]: value}))
  }

  const renderComponent = useCallback((attribute) => {
    switch (attribute.type) {
      case "text":
        return <Input
          id={attribute.id}
          inputProps={{}}
          label={`${attribute.label}`}
          name={attribute.id}
          onChange={(e) => {
            setIsEditedForBasicDetails(true);
            setDetails((prev) => ({...prev, [attribute.id]: e.target.value}));
          }}
          onBlur={(e) => {
            textOnChangeHandler(e, attribute);
          }}
          placeholder={`${attribute.placeholder}`}
          type={attribute.extraConfig?.inputType || "text"}
          isRequired={attribute.required}
          value={details?.[attribute.id] || ""}
        />
      case "date_range_picker":
        return <DateRangePicker
          label={`${attribute.label}`}
          isRequired={attribute.required}
          showRangeSelector={false}
          startDate={details?.[attribute.extraConfig.start_date_id]}
          setStartDate={(date) => {
            startDateHandler(date, attribute.extraConfig.start_date_id);
            setIsEditedForBasicDetails(true);
          }}
          endDate={details?.[attribute.extraConfig.end_date_id]}
          setEndDate={(date) => {
            endDateHandler(date, attribute.extraConfig.end_date_id);
            setIsEditedForBasicDetails(true);
          }}
          startDateInputProps={{
            label: "StartDate",
            name: "startDate",
          }}
          endDateInputProps={{
            label: "EndDate",
            name: "endDate",
          }}
          isOutsideRange={(date) => isOutsideRange(date, attribute)}
          displayFormat="MM-DD-YYYY"
        />
      case "date_picker":
        return <DatePicker
          label={`${attribute.label}`}
          isRequired={attribute.required}
          selectedDate={details?.[attribute.extraConfig.date_id]}
          setSelectedDate={(date) => {
            setDetails((prev) => ({ ...prev, [attribute.extraConfig.date_id]: date }));
            setIsEditedForBasicDetails(true);
          }}
          isOutsideRange={(date) => isOutsideRange(date, attribute)}
          isDisabled={
            ("dependency_start_date_id" in attribute.extraConfig && _.isEmpty(details?.[attribute.extraConfig.dependency_start_date_id]))
            || ("dependency_end_date_id" in attribute.extraConfig && _.isEmpty(details?.[attribute.extraConfig.dependency_end_date_id]))
          }
          displayFormat="MM-DD-YYYY"
        />
      case "dropdown":
        // if the attribute has a default option and the details object does not have the attribute id, then set the default option
        // this is to ensure that the default option is selected when the component is rendered and avoid re-rendering the component
        if (attribute?.extraConfig?.defaultOption && !details?.[attribute.id]) {
          setDetails((prev) => ({ ...prev, [attribute.id]: attribute?.extraConfig?.defaultOption }));
        }
        return <Select
          label={`${attribute.label}`}
          setSelectedOptions={(selectedOptions) => {
            setDetails((prev) => ({ ...prev, [attribute.id]: selectedOptions }));
            setIsEditedForBasicDetails(true);
          }}
          selectedOptions={details?.[attribute.id] || attribute?.extraConfig?.defaultOption}
          setCurrentOptions={() => { }}
          isRequired={attribute.required}
          currentOptions={apiData?.[attribute.id] || attribute.extraConfig.options}
          isOpen={dropDownStates?.[attribute.id] || false}
          setIsOpen={(isOpen) => {
            setDropDownStates((prev) => {
              return {
                ...prev,
                [attribute.id]: typeof isOpen === 'function' ? isOpen(prev[attribute.id]) : isOpen
              }
            })
          }}
          placeholder={`Select ${attribute.label}`}
        />
    }
  },[details, dropDownStates]);

  const getApiData = async (attribute) => {
      try {
        const APITOUSE = attribute.extraConfig.apiVersion === "v3" ? APIV3 : API;
        const response = await APITOUSE.get(attribute.extraConfig.apiUrl);
        if (response.data && response.data.status === 200) {
          setApiData((prev) => ({...prev, [attribute.id]: response.data.data}));
        }
      } catch (error) {
        console.log(error);
      }
  }

  const startDateHandler = (date, id) => {
    setDetails((prev) => ({...prev, [id]: date}))
  }

  const endDateHandler = (date, id) => {
    setDetails((prev) => ({...prev, [id]: date}))
  }

  const basicEventPayload = () => {
    const basicEventPayload = {
      additional_attributes: {}
    };

    const addToPayload = (key, value, isMasterAttribute) => {
      if (isMasterAttribute) {
        basicEventPayload[key] = value || "";
      } else {
        basicEventPayload.additional_attributes[key] = value || "";
      }
    }

    modifiedConfig.forEach((group) => {
      group.attributes.forEach((attribute) => {
        if (attribute.type === "date_range_picker") {
          addToPayload(attribute.extraConfig.start_date_id, details?.[attribute.extraConfig.start_date_id]?.format("YYYY-MM-DD") || null, attribute.is_master_attr);
          addToPayload(attribute.extraConfig.end_date_id, details?.[attribute.extraConfig.end_date_id]?.format("YYYY-MM-DD") || null, attribute.is_master_attr);
        } else if (attribute.type === "date_picker") {
          addToPayload(attribute.extraConfig.date_id, details?.[attribute.extraConfig.date_id]?.format("YYYY-MM-DD") || null, attribute.is_master_attr);
        } else if (attribute.type === "dropdown") {
          addToPayload(attribute.id, details?.[attribute.id]?.value || "", attribute.is_master_attr);
        } else if (attribute.type === "text") {
          addToPayload(attribute.id, (attribute.extraConfig?.inputType === "number" ? details?.[attribute.id] : replaceSpecialCharToCharCode(details?.[attribute.id])) || "", attribute.is_master_attr);
        } else {
          addToPayload(attribute.id, details?.[attribute.id] || "", attribute.is_master_attr);
        }
      })
    })
    
    if (activeEventId) {
      basicEventPayload.event_id = activeEventId;
    }

    return basicEventPayload;
  }

  const createProductRestrictionPayload = () => {
    const {
      product_restriction = {}
    } = eventDetails;

    let productRestrictionPayload = {};
    if (!_.isEmpty(product_restriction?.product_restriction_level)) {
      const {
        product_restriction_level,
        lock,
        specific_product_type,
        products = [],
        product_groups = [],
        product_hierarchy = {},
      } = _.cloneDeep(product_restriction);

      productRestrictionPayload = {
        product_restriction_level,
        lock,
        specific_product_type: specific_product_type || "",
        products:
          product_restriction_level !== "product_group" ?
            products?.map((product) => product.product_id)
            : [],
        product_groups:
          product_restriction_level === "product_group"
            ? product_groups?.map((productGroup) => productGroup.product_group_id)
            : [],
        product_hierarchy: {}
      };
      if (product_hierarchy) {
        Object.keys(product_hierarchy).forEach((key) => {
          if (product_hierarchy?.[key].length) {
            productRestrictionPayload.product_hierarchy[key] = product_hierarchy[key]?.map(item => item.value);
          }
        });
      }
    } else {
      productRestrictionPayload = {
        product_restriction_level: "",
        lock: false,
        products: [],
        product_groups: [],
        specific_product_type: "",
      };
    }

    return productRestrictionPayload;
  };

  const createStoreRestrictionPayload = () => {
    const {
      store_restriction = {}
    } = eventDetails;

    let storeRestrictionPayload = {};

    if (!_.isEmpty(store_restriction?.store_restriction_level)) {
      const {
        store_restriction_level,
        lock,
        specific_store_type,
        stores = [],
        store_groups = [],
      } = _.cloneDeep(store_restriction);

      storeRestrictionPayload = {
        store_restriction_level,
        lock,
        specific_store_type,
        stores:
          specific_store_type !== "store_group" ?
            stores?.map((store) => store.store_id)
            : [],
        store_groups:
          specific_store_type === "store_group" ? // only in the case of selectedSpecificStoreType as storeGroup, we need to change the store_restriction_level to store_group
            store_groups?.map((storeGroup) => storeGroup.store_group_id)
            : [],
      }
      // only in the case of selectedSpecificStoreType as storeGroup, we need to change the store_restriction_level to store_group
      // as the API expects store_restriction_level as store_group when specific_store_type is storeGroup
      if (specific_store_type === "store_group") {
        storeRestrictionPayload.store_restriction_level = "store_group";
      }
    } else {
      storeRestrictionPayload = {
        store_restriction_level: "",
        lock: false,
        stores: [],
        store_groups: [],
      };
    }

    return storeRestrictionPayload;
  }

  const createProductExclusionPayload = () => {
    const {
      product_exclusion = {}
    } = eventDetails;

    let productExclusionPayload = {
      products: [],
      product_groups: [],
      product_hierarchy: {},
      product_exclusion_level: "",
      specific_product_type: null
    };

    if (!_.isEmpty(product_exclusion)) {
      const {
        product_exclusion_level,
        products,
        product_groups,
        selectedSpecificProductType,
        hierarchy,
      } = _.cloneDeep(product_exclusion);

      productExclusionPayload = {
        product_exclusion_level: product_exclusion_level,
        products: products?.map((product) => product.product_id) || [],
        product_groups: product_groups?.map((productGroup) => productGroup.product_group_id) || [],
        specific_product_type: selectedSpecificProductType || "",
        product_hierarchy: {},
      };

      if (hierarchy) {
        Object.keys(hierarchy).forEach((key) => {
          if (hierarchy?.[key]?.length > 0) {
            productExclusionPayload.product_hierarchy[key] = hierarchy[key]?.map(item => item.value);
          }
        });
      }
    }

    return productExclusionPayload;
  }

  const createEventPayload = () => {
    const {
      date_restriction,
    } = eventDetails;

    const payload = basicEventPayload();

    // ----------------------- Restriction data -----------------------
    // ---- Date
    if (!_.isEmpty(date_restriction?.sameAsEvent)) {
      payload.date_restriction = date_restriction;
    } else {
      payload.date_restriction = {
        sameAsEvent: "no",
        minPromotionDays: null,
        maxPromotionDays: null,
        promotionStartDay: null,
        promotionEndDay: null,
      };
    }
    // ---- Product
    const productRestrictionPayload = createProductRestrictionPayload();
    payload.product_restriction = productRestrictionPayload;
    // ---- Store
    const storeRestrictionPayload = createStoreRestrictionPayload();
    payload.store_restriction = storeRestrictionPayload; 

    // ----------------------- Exclusion data -----------------------
    const productExclusionPayload = createProductExclusionPayload();
    payload.product_exclusion = productExclusionPayload;

    return payload;
  }

  const getEffectedPromosHandler = async () => {
    if (!allowSave() || disabledSaveButton) return;
    const payload = createEventPayload();
    const effectedPromos = await dispatch(getEffectedPromos(payload));
    if (effectedPromos === false) {
      dispatch(toastError(`Error fetching effected ${global_labels?.promo_standard_plural}`));
      return;
    }
    if (effectedPromos?.length > 0) {
      setIsEffectedPromosModalOpen(true);
      setEffectedPromos(effectedPromos);
    } else {
      onSaveHandler();
    }
  };

  const allowSave = () => {
    const mandatoryFields = [];
    modifiedConfig.forEach((group) => {
      group.attributes.forEach((attribute) => {
        if (attribute.required) {
          if (attribute.type === "date_range_picker") {
            attribute.extraConfig?.start_date_id && mandatoryFields.push(attribute.extraConfig.start_date_id);
            attribute.extraConfig?.end_date_id && mandatoryFields.push(attribute.extraConfig.end_date_id);
          } else if (attribute.type === "date_picker") {
            attribute.extraConfig?.date_id && mandatoryFields.push(attribute.extraConfig.date_id);

            // a validation added so that the dependency start date is not before the date id
            attribute.extraConfig?.dependency_start_date_id &&
              mandatoryFields.push(
                moment(details?.[attribute.extraConfig.dependency_start_date_id])
                  .isSameOrAfter(moment(details?.[attribute.extraConfig.date_id])
                    .subtract(attribute.extraConfig.no_of_days_before || 0, "day"))
              );

            // a validation added so that the dependency end date is not after the date id
            attribute.extraConfig?.dependency_end_date_id &&
              mandatoryFields.push(
                moment(details?.[attribute.extraConfig.dependency_end_date_id])
                  .isSameOrBefore(moment(details?.[attribute.extraConfig.date_id])
                    .add(attribute.extraConfig.no_of_days_after || 0, "day"))
              );

          } else {
            mandatoryFields.push(attribute.id);
          }
        }
      })
    })

    const isAllMandatoryFieldsFilled = mandatoryFields.every((field) => {
      // if the field is a boolean, then it is a validation and we need to return the value
      if (typeof field === "boolean") {
        return field;
      }
      // if the field is a string, then it is a field and we need to check if the value is filled
      return details?.[field];
    });
    if (!isAllMandatoryFieldsFilled) {
      dispatch(toastError("Please fill valid values for all the required fields"));
      return false;
    }
    return true;
  }

  const onSaveHandler = async () => {
    if (!allowSave() || disabledSaveButton) return;

    const payload = createEventPayload();
    
    let res = false;
    if (activeEventId) {
      payload.event_id = activeEventId;
      res = await dispatch(updateEvent(payload));
    } else {
      res = await dispatch(createEvent(payload));
    }
    if (res) {
      clearAll();
      navigate("/pricesmart-promo/marketing-calendar");
    }
  };

  const createFormConfig = () => {
    const formConfig = modifiedConfig.map((group, index) => {
      return (
        <>
          <div className="flex24 paddingTop-16 marginBottom-24 flex-wrap-wrap" key={`${group.id}`}>
            {group.attributes.map((attribute) => {
              return renderComponent(attribute)
            })}
          </div>
          {index < modifiedConfig?.length - 1 && <div className="horizontal-divider-line" />}
        </>
      )
    })
    return formConfig;
  };

  return (
    <div>
      <ScreenBreadcrumb breadcrumbList={breadcrumbRoutes()?.["createEvent"]} />
      <div className="screen_data_container paddingTop-12 create-event-scroll-screen-container">
        <div className="content_container">
          <p className="text-16-800">Create {global_labels.event_primary}</p> 
          {createFormConfig()}
           <Button onClick={() => clearAll()} size="large" variant="text">
            Reset
          </Button>
        </div>
        <div className="flex24 marginTop-24">
          <Restrictions
            start_date={details?.start_date}
            end_date={details?.end_date}
            setIsEditedFlag={setIsEditedFlag}
          />
          <Exclusion
            setIsEditedFlag={setIsEditedFlag}
          />
          {/* <div className="content_container create-event-card">
              <div className="create-event-card-text-container">
                <p className="text-16-800">Notify</p>
                <p className="text-14-800 marginTop-20">
                  This is where you can notify others about the {global_labels.event.toLocaleLowerCase()}
                </p>
                <p className="label-12px-normal marginTop-8">
                  Click on “notify” to inform individual or group
                </p>
                <Button size="large" variant="secondary" className="create-event-card-button">
                  Notify Others
                </Button>
              </div>
                <img src={NotifyImage} width={"152px"} alt="notify" />
            </div> */}
        </div>
      </div>
      <div className="footer_section">
        <Button
          onClick={() => {
            navigate("/pricesmart-promo/marketing-calendar");
          }}
          size="large"
          variant="secondary"
        >
          Cancel
        </Button>
        <Button
          onClick={() => {
            activeEventId ? getEffectedPromosHandler() : onSaveHandler();
          }}
          size="large"
          variant="primary"
          disabled={disabledSaveButton}
        >
          {activeEventId ? "Update" : "Save"}
        </Button>
      </div>
      <Modal
        open={isEffectedPromosModalOpen}
        onClose={() => {
          setIsEffectedPromosModalOpen(false);
          setEffectedPromos([]);
        }}
        onPrimaryButtonClick={() => {
          onSaveHandler();
          setIsEffectedPromosModalOpen(false);
          setEffectedPromos([]);
        }}
        onSecondaryButtonClick={() => {
          setIsEffectedPromosModalOpen(false);
          setEffectedPromos([]);
        }}
        primaryButtonLabel="Continue"
        secondaryButtonLabel="Cancel"
        size="medium"
        title="Do you want to proceed?"
      >
        <div className="">
          <span className="secondaryText-14-500">
            These promotions will update due to the {global_labels.event_primary} edit 
          </span>
          <Table
            tableHeader={"Stores"}
            suppressMenuHide
            rowData={effectedPromos}
            columnDefs={effectedPromosColumnConfig}
          />
        </div>
      </Modal>
    </div>
  )
}

export default CreatEventConfigurable