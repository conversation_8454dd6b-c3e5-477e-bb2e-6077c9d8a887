
import React, { useEffect } from 'react';
import {
    Button,
    FilterPanel,
    ButtonGroup,
    // Checkbox,
} from "impact-ui";
import ExcludeProductImage from "../../../../assets/imageAssets/excludeProductImage.png";
import { exclusionFilters, productFilterConfig } from './createEventConstants';
import Product from "../../../../assets/imageAssets/product.svg?.url";
import ProductGroupSelection from './ProductGroupSelection';
import SpecficProductSelection from './SpecficProductSelection';
import ComponentFilters from "../../../ui/componentFilters/ComponentFilters";
import { resetAllFiltersData, setSelectedFilters } from '../../../../store/features/filters/filters';
import { useDispatch, useSelector } from 'react-redux';
import { setEventProductGroupData, setEventProductTableData, setTableDataFromUploadOrCopyPaste, updateEventDetails } from '../../../../store/features/eventReducer/eventReducer';
import _ from 'lodash';

const filterIcons = {
    product_exclusion: <img src={Product} alt="product group"/>,
};

const Exclusion = (props) => {

    const dispatch = useDispatch();

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const { eventDetails } = useSelector(
        (store) => store?.pricesmartPromoReducer.event
    );

    const {
        product_exclusion = {}
    } = useSelector((store) => {
        return store?.pricesmartPromoReducer.event?.eventDetails;
    });

    const [isExclusionPanelOpen, setIsExclusionPanelOpen] = React.useState(false);
    const [activeGroup, setActiveGroup] = React.useState("product_exclusion");
    const [panelFiltersData, setPanelFiltersData] = React.useState([]);
    const [productExclusionOption, setProductExclusionOption] = React.useState("product_group");
    const [selectedSpecificProducts, setSelectedSpecificProducts] = React.useState([]);
    const [selectedSpecificProductType, setSelectedSpecificProductType] = React.useState("hierarchy");
    const [selectedProductGroups, setSelectedProductGroups] = React.useState([]);

    useEffect(() => {
        createFilters();
    }, []);

    useEffect(() => {
        createFilters();
    }, [
        productExclusionOption,
        selectedSpecificProducts,
        selectedSpecificProductType,
        selectedProductGroups
    ]);

    useEffect(() => {
        if (!isExclusionPanelOpen) {
            // reset the data if panel is closed
            createFilters();
            clearAll();
        } else {
            if (!_.isEmpty(product_exclusion)) {
                // set the data if already present
                setProductExclusionOption(product_exclusion?.product_exclusion_level || "product_group");
                setSelectedSpecificProducts(product_exclusion?.selectedSpecificProducts || []);
                setSelectedProductGroups(product_exclusion?.product_groups || []);
                setSelectedSpecificProductType(product_exclusion?.selectedSpecificProductType || "hierarchy");
                dispatch(
                    setEventProductTableData(
                        product_exclusion?.product_exclusion_level == "specific_products"
                            ? product_exclusion?.products
                            : []
                    )
                );
                dispatch(
                    setEventProductGroupData(
                        product_exclusion?.product_exclusion_level == "product_group"
                            ? product_exclusion?.product_groups
                            : []
                    )
                );
                if (product_exclusion?.product_exclusion_level == "whole_category") {
                    Object.keys(
                        product_exclusion?.hierarchy || {}
                    ).forEach((key) => {
                        dispatch(
                            setSelectedFilters({
                                data:
                                    product_exclusion?.hierarchy?.[key]
                                        ?.selectedOptions || [],
                                filterId: key,
                                from: "PRODUCT_EXCLUSION_WHOLE_CATEGORY_CREATE_EVENT",
                            })
                        );
                    });
                }
            }
        }
    }, [isExclusionPanelOpen]);

    const createFilters = () => {
        // create filters for product exclusion panel
        if (exclusionFilters.length) {
            let panelFiltersData = [];
            _.forEach(exclusionFilters, (config, ind) => {
                panelFiltersData.push({
                    ...config,
                    icon: filterIcons[config?.value],
                    children: filterContent(config?.value),
                    numberOfFilter: null
                });
            });
            setPanelFiltersData(panelFiltersData);
        }
    };

    const productGroupExclusionContent = () => {
        // return the content for product group exclusion
        return (
            <div>
                <div className="flexContentAround">
                    <p className="secondaryText-16-500">Select attributes</p>
                    <div className="flexWithGap8">
                        <Button 
                            variant="text"
                            size="large"
                            onClick={handleClear}
                            iconPlacement="left"
                        >
                            Clear
                        </Button> 
                    </div>
                </div>
                <div className="marginTop-20">
                    <div className="marginBottom-16">
                        <ButtonGroup
                            onChange={(_e, parms) => setProductExclusionOption(parms)}
                            options={[
                                // Hidden for now
                                // {
                                //     label: "Whole Category",
                                //     value: "whole_category",
                                // },
                                // {
                                //     label: "Specific Products",
                                //     value: "specific_products",
                                // },
                                {
                                    label: "Product Group",
                                    value: "product_group",
                                },
                            ]}
                            selectedOption={productExclusionOption}
                        />
                    </div>
                    <div className="marginBottom-16">
                        {productExclusionOption == "whole_category" && (
                            <ComponentFilters
                                filterConfig={productFilterConfig}
                                callAPIonLoad={false}
                                screen="PRODUCT_EXCLUSION_WHOLE_CATEGORY_CREATE_EVENT"
                                onSecondaryButtonClick={() => {
                                    dispatch(
                                        resetAllFiltersData({
                                            from: "PRODUCT_EXCLUSION_WHOLE_CATEGORY_CREATE_EVENT",
                                        })
                                    );
                                }}
                                secondaryButtonText="Clear Filters"
                            />
                        )}
                        {productExclusionOption == "specific_products" && (
                            <SpecficProductSelection
                                setSelectedProducts={setSelectedSpecificProducts}
                                selectedProducts={selectedSpecificProducts}
                                setSelectedSpecificProductType={
                                    setSelectedSpecificProductType
                                }
                            />
                        )}
                        {productExclusionOption == "product_group" && (
                            <ProductGroupSelection
                                setSelectedProductGroups={setSelectedProductGroups}
                                selectedProductGroups={selectedProductGroups}
                            />
                        )}
                    </div>
                </div>
            </div>
        );
    }

    const filterContent = (filter) => {
        // return filter content based on filter
        // only have product_exclusion filter for now add more if needed
        if (filter == "product_exclusion") 
            return productGroupExclusionContent();
        return null;
    };

    const clearAll = () => {
        // clear all the data related to exclusion
        setSelectedSpecificProducts([]);
        setSelectedProductGroups([]);
        setSelectedSpecificProductType("hierarchy");
        setProductExclusionOption("product_group");
        dispatch(
            resetAllFiltersData({
                from: "PRODUCT_EXCLUSION_WHOLE_CATEGORY_CREATE_EVENT",
            })
        );
        dispatch(setEventProductTableData([]));
        dispatch(setTableDataFromUploadOrCopyPaste([]));
        dispatch(setEventProductGroupData([]));
    }

    const onSubmHandler = () => {
        // submit the exclusion data

        const hierarchy = {}
        if (productExclusionOption == "whole_category") {
            Object.keys(filtersData["PRODUCT_EXCLUSION_WHOLE_CATEGORY_CREATE_EVENT"]).map(key => {
                hierarchy[key] = filtersData["PRODUCT_EXCLUSION_WHOLE_CATEGORY_CREATE_EVENT"][key].selectedOptions;
            });
        }

        const payload = {
            product_exclusion_level: productExclusionOption,
            products: productExclusionOption == "specific_products" ? selectedSpecificProducts : [],
            product_groups: productExclusionOption == "product_group" ? selectedProductGroups : [],
            selectedSpecificProductType: productExclusionOption == "specific_products" ? selectedSpecificProductType : null,
            hierarchy: hierarchy,
        }

        dispatch(
            updateEventDetails({
                product_exclusion: payload
            })
        );
        setIsExclusionPanelOpen(false);
        props?.setIsEditedFlag(true);
    };

    const handleClear = () => {
        clearAll();
        dispatch(
            updateEventDetails({
                product_exclusion: {}
            })
        );
    }

    return (
        <div className="card-conatainer">
            <div className="content_container create-event-card">
                <div className="create-event-card-text-container">
                    <p className="text-16-800">Exclude</p>
                    {product_exclusion?.product_groups?.length > 0
                        ?
                        <div className="paddingTop-8">
                            <div className="create-event-card-summary-chip" style={{ width: "120px" }}>
                                <div className="flexWithGap8">
                                    <img src={Product} alt="product group excluded" />
                                    <p className="secondaryText-12-500">
                                        Product Group
                                    </p>
                                </div>
                            </div>
                        </div>
                        :
                        <>
                            <p className="text-14-800 marginTop-20">
                                This is where you can exclude products
                            </p>
                            <p className="label-12px-normal marginTop-8">
                                Click on “set exclusion” button to start applying restriction
                            </p>
                        </>
                    }
                    <div className="create-event-card-button">
                        <Button
                            size="large"
                            variant="secondary"
                            onClick={() => {
                                setIsExclusionPanelOpen(true);
                            }}
                        >
                            {product_exclusion?.product_groups?.length > 0 ? "View " : "Set"} Exclusion
                        </Button>
                    </div>
                </div>
                <img src={ExcludeProductImage} width={"152px"} alt=""/>
            </div>
            {panelFiltersData?.length > 0 && (
                <FilterPanel
                    active={activeGroup}
                    anchor="right"
                    isOpen={isExclusionPanelOpen}
                    className="create-event-container"
                    filters={_.cloneDeep(panelFiltersData)}
                    handleClose={() => setIsExclusionPanelOpen(false)}
                    onPrimaryButtonClick={onSubmHandler}
                    onSecondaryButtonClick={() => setIsExclusionPanelOpen(false)}
                    primaryButtonLabel="Submit"
                    secondaryButtonLabel="Cancel"
                    setActive={(val) => {
                        setActiveGroup(val);
                    }}
                    size="large"
                    title={"Set Exclusion"}
                />
            )
            }
        </div>
    )
}

export default Exclusion