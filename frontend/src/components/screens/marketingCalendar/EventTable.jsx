import React, { useCallback, useEffect, useState } from 'react';
import { Input, Button } from "impact-ui";
import { Table } from "impact-ui-v3";
import { useSelector } from 'react-redux';
import { eventTableDataFormatter, formatChartDataEvent } from './MarketingCalendarHelper';
import { eventTableConfig } from './MarketingCalendarConstants';
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import ViewIcon from "../../../assets/imageAssets/eyeIcon.svg?.url";
import RefreshIcon from "../../../assets/imageAssets/refreshIcon.svg?.url";
import LockedIcon from "../../../assets/imageAssets/locked.svg?.url";
import LockOpenIcon from "../../../assets/imageAssets/lock-open.svg?.url";
import CopyIcon from "../../../assets/imageAssets/copyIcon.svg?.url";
import { labelCurrencyHandler, replaceSpecialCharacter, replaceSpecialCharToCharCode, useModal } from '../../../utils/helpers/utility_helpers';
import CopyEventPanel from './CopyEventpanel';
import _ from 'lodash';

const EventTable = (props) => {

    const {
        eventsCalendarData = []
    } = useSelector((store) => store?.pricesmartPromoReducer.marketingCalendar);

    const {
        currency_detail
    } = useSelector((store) => store?.pricesmartPromoReducer.global);

    const [filteredMarketingCalendarChartData, setFilteredMarketingCalendarChartData] = useState({});
    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [selectedEvents, setSelectedEvents] = useState([]);

    const [
        isCopyEventModalOpen,
        openCopyEventModal,
        closeCopyEventModal,
        copyEventModalData
    ] = useModal();

    useEffect(() => {
        // if there is no previous data in table, set the fetched data
        if (_.isEmpty(eventsCalendarData)) return;
        if (props.eventType.length > 0) {
            const selectedStatusValues = props.eventType.map(
                (status) => status.value
            );
            let filteredData = {};
            Object.keys(eventsCalendarData).forEach((key) => {
                filteredData[key] = formatChartDataEvent(
                    eventsCalendarData[key].filter((event) =>
                        selectedStatusValues.includes(event.status?.toLowerCase())
                    )
                );
            });

            //* as per current flow this "eventTableDataFormatter" formatter is not required
            setFilteredMarketingCalendarChartData({
                ly: eventTableDataFormatter(filteredData.ly),
                ty: eventTableDataFormatter(filteredData.ty)
            });
        } else {
            setFilteredMarketingCalendarChartData({
                ly: [],
                ty: []
            });
        }
    }, [eventsCalendarData, props.eventType]);

    const onFilterTextBoxChanged = useCallback((text, year) => {
        // set global search text and filter data
        const encodedText = replaceSpecialCharToCharCode(text);
        setGlobalSearchText(encodedText);
        if (year === "ly") {
            props.eventCalendartableRefly.current.api.setGridOption(
                "quickFilterText",
                encodedText
            );
        } else {
            props.eventCalendarTableRefty.current.api.setGridOption(
                "quickFilterText",
                encodedText
            );
        }
    }, []);

    const onRowSelectionLY = useCallback(() => {
        const selectedRows = props.eventCalendartableRefly?.current?.api?.getSelectedRows();
        setSelectedEvents({
            ...selectedEvents,
            ly: selectedRows
        })
        // props.eventCalendarTableRefty?.current?.api?.deselectAll();
    }, [props.eventCalendartableRefly]);

    const onRowSelectionTY = useCallback(() => {
        const selectedRows = props.eventCalendarTableRefty.current.api.getSelectedRows();
        setSelectedEvents({
            ...selectedEvents,
            ty: selectedRows
        })
        // props.eventCalendartableRefly?.current?.api?.deselectAll();
    }, [props.eventCalendarTableRefty]);

    const onCopyOffer = (year) => {
        openCopyEventModal(year)
    }


    return (
        <div>
            {props.showLastYear ? (
                <div>
                    <Table
                        ref={props.eventCalendartableRefly}
                        tableHeader={"Last Year"}
                        suppressMenuHide={true}
                        rowData={filteredMarketingCalendarChartData["ly"]}
                        columnDefs={eventTableConfig.map((col) => ({
                            ...col,
                            headerName: labelCurrencyHandler(col.headerName, currency_detail?.currency_symbol || "$")
                        }))}
                        rowSelection="multiple"
                        onSelectionChanged={() => onRowSelectionLY()}
                        suppressRowClickSelection={true}
                        topRightOptions={
                            <div className="centerFlexWithGap12">
                                {selectedEvents?.ly?.length ===
                                    1 ? (
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={ViewIcon} alt="view" />}
                                        onClick={() => { }}
                                        size="large"
                                        variant="tertiary"
                                    />
                                ) : null}
                                {selectedEvents?.ly?.length ? (
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={CopyIcon} alt="copy" />}
                                        onClick={() => onCopyOffer("ly")}
                                        size="large"
                                        variant="tertiary"
                                    />
                                ) : null}
                                {!selectedEvents?.ly
                                    ?.length && (
                                        <div className="positionRelative">
                                            {showGlobalSearch ? (
                                                <div className="marketingCalendarGlobalSearchContainer">
                                                    <Input
                                                        onChange={(e) =>
                                                            onFilterTextBoxChanged(
                                                                e.target.value,
                                                                "ly"
                                                            )
                                                        }
                                                        placeholder="Search"
                                                        rightIcon={
                                                            <img src={SearchIcon} alt="search" />
                                                        }
                                                        type="text"
                                                        value={replaceSpecialCharacter(globalSearchText)}
                                                    />
                                                </div>
                                            ) : null}
                                            <Button
                                                iconPlacement="left"
                                                icon={<img src={SearchIcon} alt="search" />}
                                                onClick={() => {
                                                    setShowGlobalSearch((prev) => {
                                                        console.log(
                                                            "Previous State:",
                                                            prev
                                                        ); // Check previous state
                                                        return !prev;
                                                    });
                                                }}
                                                size="large"
                                                variant="tertiary"
                                            />
                                        </div>
                                    )}
                            </div>
                        }
                    />
                    <div className="horizontal-divider-line" />
                </div>
            ) : null}

            <Table
                ref={props.eventCalendarTableRefty}
                tableHeader={"This Year"}
                suppressMenuHide={true}
                rowData={filteredMarketingCalendarChartData["ty"]}
                columnDefs={eventTableConfig.map((col) => ({
                    ...col,
                    headerName: labelCurrencyHandler(col.headerName, currency_detail?.currency_symbol || "$")
                }))}
                rowSelection="multiple"
                onSelectionChanged={() => onRowSelectionTY()}
                suppressRowClickSelection={true}
                topRightOptions={
                    <div className="centerFlexWithGap12">
                        {selectedEvents?.ty?.length === 1 ? (
                            <div className="centerFlexWithGap12">
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={EditIcon} alt='edit' />}
                                    onClick={() => {
                                        props.handleEditEvent(selectedEvents?.ty?.[0]);
                                    }}
                                    size="large"
                                    variant="tertiary"
                                    className='calendar-event-detail-button'
                                />
                            </div>
                        ) : null}
                        {selectedEvents?.ty?.length ? (
                            <Button
                                iconPlacement="left"
                                icon={<img src={CopyIcon} alt="copy" />}
                                onClick={() => onCopyOffer("ty")}
                                size="large"
                                variant="tertiary"
                            />
                        ) : null}
                        {selectedEvents?.ty?.length ? (
                            <div className="centerFlexWithGap12">
                                <Button
                                    onClick={() => props?.handleResimulate(selectedEvents?.ty)}
                                    size="large"
                                    variant="tertiary"
                                    icon={<img src={RefreshIcon} alt='resimulate' />}
                                    className='calendar-event-detail-button'
                                ></Button>
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={DeleteIcon} alt='delete' />}
                                    onClick={() => props?.handleDelete(selectedEvents?.ty)}
                                    size="large"
                                    variant="secondary"
                                    type="destructive"
                                    className='calendar-event-detail-button delete-button'
                                />
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={LockedIcon} alt='Lock' />}
                                    onClick={() => props?.handleLockEvent(selectedEvents?.ty, true)}
                                    size="large"
                                    variant="tertiary"
                                    className='calendar-event-detail-button'
                                />
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={LockOpenIcon} alt='Unlock' />}
                                    onClick={() => props?.handleLockEvent(selectedEvents?.ty, false)}
                                    size="large"
                                    variant="tertiary"
                                    className='unlock-button-style'
                                />
                            </div>
                        ) : null}

                        {!selectedEvents?.ty?.length && (
                            <div className="positionRelative">
                                {showGlobalSearch ? (
                                    <div className="marketingCalendarGlobalSearchContainer">
                                        <Input
                                            onChange={(e) =>
                                                onFilterTextBoxChanged(
                                                    e.target.value,
                                                    "ty"
                                                )
                                            }
                                            placeholder="Search"
                                            rightIcon={<img src={SearchIcon} alt='search' />}
                                            type="text"
                                            value={replaceSpecialCharacter(globalSearchText)}
                                        />
                                    </div>
                                ) : null}
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={SearchIcon} alt='search' />}
                                    onClick={() => {
                                        setShowGlobalSearch((prev) => {
                                            console.log(
                                                "Previous State:",
                                                prev
                                            ); // Check previous state
                                            return !prev;
                                        });
                                    }}
                                    size="large"
                                    variant="tertiary"
                                />
                            </div>
                        )}
                    </div>
                }
            />
            {isCopyEventModalOpen && (
                <CopyEventPanel
                    isOpen={isCopyEventModalOpen}
                    onClose={closeCopyEventModal}
                    rowsToCopy={selectedEvents?.[copyEventModalData]}
                    onFilterApply={props?.onFilterApply}
                />
            )}
        </div>
    )
}

export default EventTable