import React, { useEffect, useRef, useState } from 'react';
import {
    Button,
    Switch,
    Select,
    ButtonGroup,
    Modal,
    Prompt,
} from "impact-ui"
import { Table } from "impact-ui-v3";
import { eventStatus, marketingCalendarViewAggregations, marketingCalendarViewDefaultAggregation, marketingCalendarViewModes } from './MarketingCalendarConstants';
import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import { useNavigate } from "react-router-dom-v5-compat";
import EventCalendarChart from './EventCalendarChart';
import { global_labels } from '../../../constants/Constants';
import EventTable from './EventTable';
import { useSelector, useDispatch } from 'react-redux';
import { 
    checkIfIsUnderProcessing, 
    deleteEvent, 
    getEffectedPromosFromDeleteEvent, 
    lockUnlockEvent,
    resimulateEvent,
    setRefreshEventsData
} from '../../../store/features/eventReducer/eventReducer';
import moment from 'moment';
import { toastError } from '../../../store/features/global/global';
import { effectedPromosColumnConfigForDeleteEvent } from './createEvent/createEventConstants';
import { overwriteFilters } from '../../../store/features/filters/filters';
import _ from "lodash";
import { getCurrent } from '../../ui/calendar/Helpers';
import { capitalizeFirstLetter } from '../../../utils/helpers/utility_helpers';

const EventView = (props) => {

    const navigate = useNavigate();
    const dispatch = useDispatch();

    const {
        eventsCalendarData = [],
    } = useSelector((store) => store?.pricesmartPromoReducer.marketingCalendar);

    const { refreshEventsData } = useSelector((store) => store?.pricesmartPromoReducer.event);

    const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

    const {
		fiscalCalendar
	} = useSelector((store) => store?.pricesmartPromoReducer.global);

    const [showLastYear, setShowLastYear] = useState(false);
    const [aggregation, setAggregation] = useState(marketingCalendarViewDefaultAggregation);
    const [activeMode, setActiveMode] = useState("calendar");
    const [eventType, setEventType] = useState([]);
    const [isSelectAll, setIsSelectAll] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [isViewByOpen, setIsViewByOpen] = useState(false);
    const [showDownloadButton, setShowDownloadButton] = useState(false);
    const [isEffectedPromosModalOpen, setIsEffectedPromosModalOpen] = useState(false);
    const [effectedPromos, setEffectedPromos] = useState([]);
    const [selectedEventsForDelete, setSelectedEventsForDelete] = useState([]);
    const [showDeletePrompt, setShowDeletePrompt] = useState(false);
    const [calCurrentState, setCalCurrentState] = useState({});
    const eventCalendarTableRefty = useRef(null);
    const eventCalendartableRefly = useRef(null);


    useEffect(() => {
        // Set the default to all select event status
        setIsSelectAll(true);
        setEventType(eventStatus);
    }, [eventStatus]);


    useEffect(() => {
        if (props?.triggerFilterApplyEvent) {
            const currentState = getCurrent(fiscalCalendar, filtersData?.MARKETING_CALENDAR?.dateRange?.start_date, filtersData?.MARKETING_CALENDAR?.dateRange?.end_date);
            setCalCurrentState((prev) => {
                return {
                    ...prev,
                    ty: {
                        yr: currentState?.currYr,
                        qtr: currentState?.currQtr,
                        mo: currentState?.currMo,
                        wk: currentState?.currWk,
                    }
                };
            });
        }
    }, [props?.triggerFilterApplyEvent]);

    useEffect(() => {
        // Show download button only if there is data to download
        if (
            (eventsCalendarData["ly"]?.length > 0 && showLastYear) 
            || eventsCalendarData["ty"]?.length > 0
        ){
            setShowDownloadButton(true);
        } else {
            setShowDownloadButton(false);
        }
    }, [eventsCalendarData]);

    useEffect(() => {
        if (refreshEventsData) {
            props?.onFilterApply("event", false, filtersData?.MARKETING_CALENDAR);
            dispatch(setRefreshEventsData(false));
        }
    }, [refreshEventsData]);

    const handleShowLastYearChange = () => {
        // Call the API to get the data for the last year, if not already present in redux
        const min_year_allowed_from_fiscal_calendar = fiscalCalendar?.[0]?.year;
		// This is added so that if the fiscal year data is absent for last year throw err
		// and eventhouggh if we are in ty 2024-01 the min date for min year week-1 is 2023-01-29, so month able to show Jan thus the mo condition added
		if (
			min_year_allowed_from_fiscal_calendar > (calCurrentState?.["ty"]?.yr - 1)
			|| ((calCurrentState?.["ty"]?.yr - 1 <= min_year_allowed_from_fiscal_calendar) && calCurrentState?.["ty"]?.mo <= 1)
		) {
			dispatch(toastError("Fiscal year data not present for last year"));
			return;
		}
        !showLastYear && props?.onFilterApply("event", true, filtersData?.MARKETING_CALENDAR);
        setShowLastYear(!showLastYear);
    };

    const onSelectAllHandler = () => {
        // If all options are selected/deselect all options
        if (isSelectAll) {
            // Deselect all options
            setEventType([]);
        } else {
            // Select all options
            setEventType(eventStatus);
        }
        setIsSelectAll(!isSelectAll);
    };

    const onDownloadEventsTableClick = () => {
        // Download the data as excel
        if (eventsCalendarData["ty"]?.length) {
            eventCalendarTableRefty.current?.api?.exportDataAsExcel({
                fileName: "events_ty"
            });
        }
        if (eventsCalendarData["ly"]?.length) {
            eventCalendartableRefly.current?.api?.exportDataAsExcel({
                fileName: "events_ly"
            });
        }
    };

    const onCreateNewEventClick = () => {
        //navigate to create product group screen
        navigate(`/pricesmart-promo/marketing-calendar/create-event`);
    };

    const handleEditEvent = async (event) => {
        //navigate to create product group screen
        const isUnderProcessing = await dispatch(checkIfIsUnderProcessing({event_id: event?.id}));
        if (isUnderProcessing) {
            dispatch(toastError(`${capitalizeFirstLetter(global_labels?.event_primary)} is under processing`));
            return;
        }
        navigate(`/pricesmart-promo/marketing-calendar/create-event/?event_id=${event?.id}`);
    }

    const handleViewEvent = async (event) => {
        // swithc to promo view and call the filter apply to get the promo data with those event
        // uncmt the below code when needed, this sets the event in the filters.
        // await dispatch(
        //     setSelectedFilters({
        //         data: [{
        //             value: event?.event_id,
        //             label: event?.event_name
        //         }],
        //         filterId: "event",
        //         from: "MARKETING_CALENDAR",
        //     })
        // );
        props?.setSelectedTab("promo-view");
        props?.onFilterApply("promo", false, {
            event: {
                selectedOptionsArray: [event?.event_id]
            }
        });
    }

    const handleLockEvent = async (events, action=null) => {
        // Call API to lock/unlock the event
        console.log("Lock Event", events);
        const lockAction = _.isNull(action)? !events?.[0]?.is_locked : action;
        const payload = {
            event_ids: events?.map((event) => event.event_id),
            is_locked: lockAction
        }
        await dispatch(lockUnlockEvent(payload));
    }

    const handleResimulate = async (events) => {
        const payload = {
            event_ids: events?.map((event) => event.event_id)
        }
        await dispatch(resimulateEvent(payload));
    }

    const handleDelete = async (events = []) => {
        const payload = {
            event_ids: events?.map((event) => event.event_id)
        }

        console.log("Delete Event", payload);
        const res = await dispatch(deleteEvent(payload));
        if (res) {
            props?.onFilterApply("event", false, filtersData?.MARKETING_CALENDAR);
        }
    }

    const getEffectedPromos = async (events = []) => {
        const isFutureDate = events?.every((event) => {
            return moment(event.start_date).isAfter(moment());
        });
        if (!isFutureDate) {
            dispatch(toastError(`You can only delete future ${global_labels?.event_plural}`));
            return;
        }

        setSelectedEventsForDelete(events);

        const payload = {
            event_ids: events?.map((event) => event.event_id)
        }

        const effectedPromos = await dispatch(getEffectedPromosFromDeleteEvent(payload)) || [];
        if (effectedPromos.length > 0) {
            setIsEffectedPromosModalOpen(true);
            setEffectedPromos(effectedPromos);
        } else {
            setShowDeletePrompt(true);
        }
    }
    
    const handleEventStatusChange = (selectedItems) => {
        if (selectedItems.length === eventType.length) {
            setIsSelectAll(true);
        } else {
            setIsSelectAll(false);
        }
        setEventType(selectedItems);
    }

	const handleBoundaryChange = (lastyear, type) => {
		const immediateFilters = _.cloneDeep(filtersData?.MARKETING_CALENDAR);
		const startDate = moment(immediateFilters["dateRange"]?.start_date);
		const endDate = moment(immediateFilters["dateRange"]?.end_date);

		const key = lastyear ? "ly" : "ty";

		if (
			type == "prevWk" ||
			type == "prevMo" ||
			type == "prevQtr" ||
			type == "prevYr"
		) {
			immediateFilters["dateRange"] = {
				start_date: startDate.subtract(1, aggregation?.value),
				end_date: endDate
			};
			setCalCurrentState((prev) => {
				const currentState = _.cloneDeep(prev[key]) || {};
				if (type === "prevYr") {
					currentState.qtr = 4;
					currentState.mo = 12;
					currentState.yr = currentState.yr - 1;
					currentState.wk = 52;
				} else if (aggregation?.value === "quarter") {
					if (currentState.qtr === 1) {
						currentState.yr = currentState.yr - 1;
						currentState.qtr = 4;
						currentState.mo = 12;
					} else {
						currentState.qtr = currentState.qtr - 1;
						currentState.mo = (currentState.qtr - 1) * 3 + 1;
						currentState.wk = (currentState.qtr - 1) * 13 + 1;
					}
				} else if (aggregation?.value === "month") {
					if (currentState.mo === 1) {
						currentState.mo = 12;
						currentState.qtr = 4;
						currentState.yr = currentState.yr - 1;
					} else {
						currentState.mo = currentState.mo - 1;
						currentState.qtr = Math.ceil((currentState.mo) / 3);
						currentState.wk = (currentState.qtr - 1) * 13 + 1;
					}
				}
				return {
					...prev,
					[key]: currentState
				};
			});
		} else if (
			type == "nextWk" ||
			type == "nextMo" ||
			type == "nextQtr" ||
			type == "nextYr"
		) {	
			immediateFilters["dateRange"] = {
				start_date: startDate,
				end_date: endDate.add(1, aggregation?.value)
			};
			setCalCurrentState((prev) => {
				const currentState = _.cloneDeep(prev[key]) || {};
				if (type === "nextYr") {
					currentState.qtr = 1;
					currentState.mo = 1;
					currentState.yr = currentState.yr + 1;
					currentState.wk = 1;
				} else if (aggregation?.value === "quarter") {
					if (currentState.qtr === 4) {
						currentState.qtr = 1;
						currentState.mo = 1;
						currentState.yr = currentState.yr + 1;
					} else {
						currentState.qtr += 1;
						currentState.mo = (currentState.qtr - 1) * 3 + 1;
						currentState.wk = (currentState.qtr - 1) * 13 + 1;
					}
				} else if (aggregation?.value === "month") {
					if (currentState.mo === 12) {
						currentState.mo = 1;
						currentState.qtr = 1;
						currentState.yr = currentState.yr + 1;
					} else {
						currentState.mo += 1;
						currentState.qtr = Math.ceil((currentState.mo) / 3);
						currentState.wk = (currentState.qtr - 1) * 13 + 1;
					}
				}
				return {
					...prev,
					[key]: currentState
				};
			});
        }
        dispatch(
            overwriteFilters({
                filtersData: _.cloneDeep(immediateFilters),
                activeScreen: "MARKETING_CALENDAR",
            })
        );

		if (lastyear) {
			props?.onFilterApply("all", true, immediateFilters);
		}
		props?.onFilterApply("all", false, immediateFilters);
	}

    return (
        <div className="paddingTop-8">
            <div className="flex1">
                <div className="marketingCalendarDataContainer">
                    <div className="content_container">
                        <div className="marketing-calendar-subtitle-container">
                            <p className="marketing-calendar-subtitle">
                                All {global_labels.event_plural}
                            </p>
                            <div className="centerFlexWithGap12">
                                <Switch
                                    iaFallback="v3"
                                    leftLabel="Show last year"
                                    onChange={handleShowLastYearChange}
                                    value={showLastYear}
                                />

                                {activeMode === "calendar" ? (
                                    <div className='centerFlexWithGap12'>
                                        <div className="horizontal-line" />
                                        <Select
                                            currentOptions={
                                                marketingCalendarViewAggregations
                                            }
                                            initialOptions={
                                                [marketingCalendarViewDefaultAggregation]
                                            }
                                            label="View By"
                                            labelOrientation="left"
                                            setSelectedOptions={(selectedItems) => {
                                                setAggregation(selectedItems);
                                            }}
                                            setCurrentOptions={() => { }}
                                            placeholder="Select..."
                                            isRequired={false}
                                            isWithSearch={false}
                                            isMulti={false}
                                            isSelectAll={false}
                                            selectedOptions={aggregation}
                                            isOpen={isViewByOpen}
                                            setIsOpen={setIsViewByOpen}
                                            isCloseWhenClickOutside={true}
                                            toggleSelectAll
                                        />
                                    </div>
                                ) : (
                                    <div className="horizontal-line" />
                                )}
                                <Select
                                    currentOptions={
                                        eventStatus
                                    }
                                    initialOptions={
                                        eventStatus
                                    }
                                    label={`${global_labels.event_primary} Status`}
                                    labelOrientation="left"
                                    setSelectedOptions={handleEventStatusChange}
                                    setCurrentOptions={() => { }}
                                    placeholder="Select..."
                                    isRequired={false}
                                    isWithSearch={false}
                                    isMulti={true}
                                    isSelectAll={isSelectAll}
                                    onSelectAll={onSelectAllHandler}
                                    setIsSelectAll={setIsSelectAll}
                                    selectedOptions={eventType}
                                    isOpen={isOpen}
                                    setIsOpen={setIsOpen}
                                    isCloseWhenClickOutside={true}
                                    toggleSelectAll={true}
                                />
                                <div className="horizontal-line" />
                                <ButtonGroup
                                    onChange={(_e, val) =>
                                        setActiveMode(val)
                                    }
                                    options={marketingCalendarViewModes}
                                    selectedOption={activeMode}
                                />
                                {showDownloadButton && (
                                    <div className="centerFlexWithGap12">
                                        <div className="horizontal-line" />
                                        <Button
                                            iconPlacement="left"
                                            icon={<img src={DownloadIcon} alt="download" />}
                                            onClick={onDownloadEventsTableClick}
                                            size="large"
                                            variant="tertiary"
                                        />
                                    </div>
                                )}
                                <div className="horizontal-line" />
                                <Button
                                    onClick={onCreateNewEventClick}
                                    size="large"
                                    variant="primary"
                                >
                                    Create New {capitalizeFirstLetter(global_labels.event_primary)}
                                </Button>
                            </div>
                        </div>
                        <div style={{ display: activeMode === "calendar" ? "block" : "none" }}>
                            <EventCalendarChart
                                eventType={eventType}
                                aggregation={aggregation?.value}
                                showLastYear={showLastYear}
                                handleViewEvent={handleViewEvent}
                                handleEditEvent={handleEditEvent}
                                handleLockEvent={handleLockEvent}
                                handleResimulate={handleResimulate}
                                handleDelete={getEffectedPromos}
                                handleBoundaryChange={handleBoundaryChange}
                                calCurrentState={calCurrentState}
                                setCalCurrentState={setCalCurrentState}
                                onBoundaryChange={handleBoundaryChange}
                            />
                        </div>
                        <div style={{ display: activeMode === "table" ? "block" : "none" }}>
                            <EventTable
                                eventType={eventType}
                                aggregation={aggregation?.value}
                                showLastYear={showLastYear}
                                handleEditEvent={handleEditEvent}
                                handleLockEvent={handleLockEvent}
                                handleResimulate={handleResimulate}
                                handleDelete={getEffectedPromos}
                                eventCalendarTableRefty={eventCalendarTableRefty}
                                eventCalendartableRefly={eventCalendartableRefly}
                                onFilterApply={props?.onFilterApply}
                            />
                        </div>
                        {/* {activeMode === "calendar" ? (
                        ) :
                        } */}
                    </div>
                </div>
            </div>
            <Prompt
				handleClose={() => setShowDeletePrompt(false)}
				onPrimaryButtonClick={() => {
                    handleDelete(selectedEventsForDelete);
                    setShowDeletePrompt(false);
                }}
				onSecondaryButtonClick={() => setShowDeletePrompt(false)}
				primaryButtonLabel="Proceed"
				secondaryButtonLabel="Cancel"
				title={`Confirm Deletion of ${global_labels.event_plural}`}
				variant="warning"
				isOpen={showDeletePrompt}
			>
				Are you sure you want to delete these {global_labels.event_plural}?
			</Prompt>
            <Modal
                open={isEffectedPromosModalOpen}
                onClose={() => {
                    setIsEffectedPromosModalOpen(false);
                    setEffectedPromos([]);
                }}
                onPrimaryButtonClick={() => {
                    handleDelete(selectedEventsForDelete);
                    setIsEffectedPromosModalOpen(false);
                    setEffectedPromos([]);
                }}
                onSecondaryButtonClick={() => {
                    setIsEffectedPromosModalOpen(false);
                    setEffectedPromos([]);
                }}
                primaryButtonLabel="Continue"
                secondaryButtonLabel="Cancel"
                size="medium"
                title="Do you want to proceed?"
            >
                <div className="">
                    <span className="secondaryText-14-500">
                        These promotions will update due to the {global_labels.event_primary} edit
                    </span>
                    <Table
                        tableHeader={`${capitalizeFirstLetter(global_labels?.promo_plural)}`}
                        suppressMenuHide
                        rowData={effectedPromos}
                        columnDefs={effectedPromosColumnConfigForDeleteEvent}
                    />
                </div>
            </Modal>
        </div>
    )
}

export default EventView