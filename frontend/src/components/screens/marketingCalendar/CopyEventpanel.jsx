import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON>, But<PERSON>, Toast } from "impact-ui";
import {Table} from "impact-ui-v3";
import _ from "lodash";
import moment from "moment";

import { copyEventTableConfig } from "./MarketingCalendarConstants";
import CopyIcon from "../../../assets/imageAssets/copyIcon.svg?.url";
import warningAlertIconSvg from "../../../assets/imageAssets/warningIcon.svg?.url";
import infoIconSvg from "../../../assets/imageAssets/ISymbol.svg?.url";
import { CopyEventBulkEdit } from "./CopyEventBulkEdit";
import { copyEvents } from "../../../store/features/marketingCalendarReducer/marketingCalendarReducer";
import { DEFAULT_DATE_FORMAT, global_labels } from "../../../constants/Constants";
import { setSelectedFilters } from "../../../store/features/filters/filters";
import { capitalizeFirstLetter, replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";

function CopyEventPanel(props) {
    const {
        global_configs
    } = useSelector((store) => store?.pricesmartPromoReducer?.global);
    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const dispatch = useDispatch();

    const copyEventTableRef = useRef(null);

    const {
        isOpen,
        onClose,
        rowsToCopy,
        onFilterApply,
    } = props;

    const [toast, setToast] = useState({
        isOpen: false,
        message: "",
        variant: "success",
    });
    const [tableData, setTableData] = useState([]);
    const [selectedRowData, setSelectedRowData] = useState([]);

    const onRowSelection = useCallback(() => {
        setSelectedRowData(copyEventTableRef.current.api.getSelectedRows());
    });

    const getDateRange = (startDate, endDate) => {
        return `${moment(startDate).format(
            "MM-DD-YYYY"
        )} - ${moment(endDate).format("MM-DD-YYYY")}`;
    }

    const handleBulkEditApply = (dates) => {
        setTableData(prevData => {
            return prevData?.map(row => {
                if (selectedRowData.some(selectedRow => selectedRow.event_id === row.event_id)) {
                    return { ...row, ...dates };
                }
                return row;
            });
        });
    }



    useEffect(() => {
        const fabricatedStructureRowsToCopy = rowsToCopy?.map(row => {
            return {
                date_range: getDateRange(row.start_date, row.end_date),
                start_date: null,
                end_date: null,
                modified_date_range: '-',
                event_id: row.event_id,
                event_name: replaceSpecialCharacter(row.event_name),
                new_event_name: row.new_event_name || row.event_name,
                submit_offers_by: row?.submit_offers_by
            };
        });
        setTableData(fabricatedStructureRowsToCopy);
    }, [rowsToCopy]);

    const handleCellValueChange = useCallback((params) => {
        const { field, value, id } = params;
        setTableData(prevData => {
            return prevData?.map(row => {
                if (row.event_id === id) {
                    return {
                        ...row,
                        [field]: value
                    }
                } else {
                    return row;
                }
            });
        });
    }, [tableData]);




    const validationOfFields = () => {
        return tableData.every(row => {
            return row.new_event_name && row.start_date && row.end_date && row.submit_offers_by;
        });
    }
    const updateFilters = data => {
        const tempFiltersData = _.cloneDeep(filtersData?.MARKETING_CALENDAR);
        tempFiltersData["event"] = { selectedOptionsArray: data?.event_ids?.map(event => event?.value) };
        tempFiltersData["dateRange"] = {
            start_date: data?.start_date,
            end_date: data?.end_date
        }
        onFilterApply("event", false, tempFiltersData);
        dispatch(
            setSelectedFilters({
                data: data?.event_ids,
                from: "MARKETING_CALENDAR",
                filterId: "event",
            })
        );
    }

    const handleSubmit = async () => {

        if (!validationOfFields()) {
            setToast({
                isOpen: true,
                message: "Please fill in all required fields",
                variant: "error",
            });
            return;
        }

        const copyEventPayload = {
            events: tableData.map(row => ({
                event_id: row.event_id,
                start_date: moment(row.start_date).format(DEFAULT_DATE_FORMAT),
                end_date: moment(row.end_date).format(DEFAULT_DATE_FORMAT),
                submit_offers_by: moment(row.submit_offers_by).format(DEFAULT_DATE_FORMAT),
                new_event_name: row.new_event_name,
            }))
        }
        const result = await dispatch(copyEvents(copyEventPayload));
        if (result) {
            setToast({
                isOpen: true,
                message: `${capitalizeFirstLetter(global_labels?.event_plural)} copied successfully`,
            });
            onClose();
            const minStartDate = moment.min(tableData.map(data => moment(data.start_date)));
            const maxEndDate = moment.max(tableData.map(data => moment(data.end_date)));
            const dataObj = {
                event_ids: result,
                start_date: moment(minStartDate).format(DEFAULT_DATE_FORMAT),
                end_date: moment(maxEndDate).format(DEFAULT_DATE_FORMAT)
            }
            updateFilters(dataObj);
        } else {
            setToast({
                isOpen: true,
                message: `Failed to copy ${capitalizeFirstLetter(global_labels?.event_plural)}`,
                variant: "error",
            });
        }
    }



    const columnDefs = useMemo(() => {
        return copyEventTableConfig(handleCellValueChange, tableData);
    }, [handleCellValueChange, tableData]);

    const flowLabel = capitalizeFirstLetter(global_configs?.event_primary) || "Event"


    return (
        <Panel
            anchor="right"
            open={isOpen}
            className="copyEventPanel"
            onClose={onClose}
            onPrimaryButtonClick={handleSubmit}
            onSecondaryButtonClick={onClose}
            primaryButtonLabel="Submit"
            secondaryButtonLabel="Cancel"
            size="large"
            title={
                <div className="centerFlexWithGap12">
                    <Button
                        iconPlacement="left"
                        icon={<img src={CopyIcon} />}
                        disabled
                        size="large"
                        variant="secondary"
                    />
                    <p>Copy {flowLabel}s</p>
                </div>
            }
        >
            <div className="copy-offer-panel-toast">
                <Toast
                    autoHideDuration={3000}
                    message={toast.message}
                    onClose={(_e, _reason) => {
                        setToast({
                            isOpen: false,
                            message: "",
                            variant: "",
                        });
                    }}
                    position="top-right"
                    variant={toast.variant}
                    isOpen={toast.isOpen}
                />
            </div>
            <div>
                <Table
                    tableHeader={`Copy ${flowLabel}s`}
                    ref={copyEventTableRef}
                    suppressMenuHide
                    rowData={tableData}
                    columnDefs={columnDefs}
                    suppressRowClickSelection={true}
                    rowSelection="multiple"
                    onSelectionChanged={onRowSelection}
                    onCellValueChange={handleCellValueChange}
                    topRightOptions={
                        <div>
                            <CopyEventBulkEdit selectedRowData={selectedRowData} handleBulkEditApply={handleBulkEditApply} />
                        </div>
                    }
                />
                <div className="flexWithGap12 marginTop-16 marginBottom-16">
                    <img src={infoIconSvg} alt="info" width={"18px"} />
                    <p className="text-14-600">
                        Update {flowLabel.toLowerCase()} dates individually or in bulk.
                    </p>
                </div>
            </div>
        </Panel>
    );
}

export default CopyEventPanel;
