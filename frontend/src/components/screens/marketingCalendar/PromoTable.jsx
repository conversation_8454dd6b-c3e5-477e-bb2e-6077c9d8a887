import React, { useState, useCallback, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Input, Button, Select, Modal } from "impact-ui";
import { Table } from "impact-ui-v3";
import _ from "lodash";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";
import CopyIcon from "../../../assets/imageAssets/copyIcon.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import ViewIcon from "../../../assets/imageAssets/eyeIcon.svg?.url";
import RefreshIcon from "../../../assets/imageAssets/refreshIcon.svg?.url";
import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import {
    setSelectedMarketingCalendarOffers,
    getProductsDetailsOfPromo,
    getStoresDetailsOfPromo,
} from "../../../store/features/marketingCalendarReducer/marketingCalendarReducer";
import {
    marketingCalendarTableConfig,
    productTableConfig,
    storeTableConfig,
    viewColsOptions,
} from "./MarketingCalendarConstants";
import { useNavigate } from "react-router-dom-v5-compat";
import { promoTableDataFormatter } from "./MarketingCalendarHelper";
import {
    setActivePromoId,
    setMaxStepCount,
} from "../../../store/features/promoReducer/promoReducer";
import {
    toastError,
    validateOperation,
} from "../../../store/features/global/global";
import { CellOnClickButton } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { global_labels } from "../../../constants/Constants";
import { labelCurrencyHandlerForTableDefs } from "../../../utils/helpers/utility_helpers";

function PromoTable(props) {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const {
        marketingCalendarData = [],
        marketingCalendarSelectedOffers = {},
    } = useSelector((store) => store?.pricesmartPromoReducer.marketingCalendar);

    const { global_configs, hierachy_keys, currency_detail } = useSelector(
        (store) => store?.pricesmartPromoReducer?.global
    );

    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");

    const [
        filteredmarketingCalendarData,
        setFilteredmarketingCalendarData,
    ] = useState([]);
    const [selectedViewCols, setSelectedViewCols] = useState([]);
    const [isViewColsOpen, setIsViewColsOpen] = useState(false);
    const [isSelectAllForViewCols, setIsSelectAllForViewCols] = useState(false);
    const [tableColDefs, setTableColDefs] = useState(
        marketingCalendarTableConfig
    );
    const [productDetailTableData, setProductDetailTableData] = useState([]);
    const [storeDetailTableData, setStoreDetailTableData] = useState([]);
    const [productDetailModalOpen, setProductDetailModalOpen] = useState(false);
    const [storeDetailModalOpen, setStoreDetailModalOpen] = useState(false);
    const productDetailTableRef = useRef(null);
    const storeDetailTableRef = useRef(null);

    useEffect(() => {
        let col_def = _.cloneDeep(marketingCalendarTableConfig);
        if (!global_configs?.event?.use_event) {
            col_def = col_def.filter((col) => col.field !== "event_name");
        }

        const col_products = col_def.find(
            (ele) => ele.field == "products_count"
        );
        const col_stores = col_def.find((ele) => ele.field == "stores_count");
        if (col_products) {
            col_products["cellRenderer"] = (props) => (
                <CellOnClickButton
                    {...props}
                    clickHandler={getProductDetails}
                />
            );
        }
        if (col_stores) {
            col_stores["cellRenderer"] = (props) => (
                <CellOnClickButton {...props} clickHandler={getStoreDetails} />
            );
        }
        setTableColDefs(col_def);
    }, [global_configs?.event?.use_event]);

    const getProductDetails = async (data) => {
        const productDetails = await dispatch(
            getProductsDetailsOfPromo(data.event_id)
        );
        if (productDetails) {
            setProductDetailTableData(productDetails);
            setProductDetailModalOpen(true);
        }
    };

    const getStoreDetails = async (data) => {
        const storeDetails = await dispatch(
            getStoresDetailsOfPromo(data.event_id)
        );
        if (storeDetails) {
            setStoreDetailTableData(storeDetails);
            setStoreDetailModalOpen(true);
        }
    };

    const onFilterTextBoxChanged = useCallback((text, year) => {
        setGlobalSearchText(text);
        if (year === "ly") {
            props.marketingCalendarTableRefly.current.api.setGridOption(
                "quickFilterText",
                text
            );
        } else {
            props.marketingCalendarTableRefty.current.api.setGridOption(
                "quickFilterText",
                text
            );
        }
    }, []);

    useEffect(() => {
        if (props.promoType.length > 0) {
            const selectedStatusValues = props.promoType.map(
                (status) => status.value
            );
            let filteredData = {};
            Object.keys(marketingCalendarData).forEach((key) => {
                filteredData[key] = marketingCalendarData[key].filter((promo) =>
                    selectedStatusValues.includes(promo.status_id)
                );
            });
            setFilteredmarketingCalendarData({
                ly: promoTableDataFormatter(filteredData.ly),
                ty: promoTableDataFormatter(filteredData.ty),
            });
        } else {
            setFilteredmarketingCalendarData({
                ly: [],
                ty: [],
            });
        }
    }, [marketingCalendarData, props.promoType]);

    const onRowSelectionTY = useCallback(() => {
        const selectedRows = props.marketingCalendarTableRefty.current.api.getSelectedRows();
        dispatch(
            setSelectedMarketingCalendarOffers({
                data: _.cloneDeep(selectedRows),
                year: "ty",
            })
        );
        props.marketingCalendarTableRefly?.current?.api?.deselectAll();
    }, [props.marketingCalendarTableRefty]);

    const onRowSelectionLY = useCallback(() => {
        //get selected product groups and set in product
        const selectedRows = props.marketingCalendarTableRefly?.current?.api?.getSelectedRows();
        dispatch(
            setSelectedMarketingCalendarOffers({
                data: _.cloneDeep(selectedRows),
                year: "ly",
            })
        );
        // Deselect all rows in other table
        // props.marketingCalendarTableRefty?.current?.api?.deselectAll();
    }, [props.marketingCalendarTableRefly, props.marketingCalendarTableRefty]);

    const onResimulate = () => {
        props.handleResimulate(marketingCalendarSelectedOffers?.["ty"]);
    };

    const onCopyOffer = (year) => {
        props.handleCopyOffer(marketingCalendarSelectedOffers?.[year]);
    };

    const handleDelete = () => {
        props.handleDelete();
    };

    const handleViewColsChange = (selectedOptions) => {
        setSelectedViewCols(selectedOptions);
        const selectedValues = selectedOptions.map((ele) => ele.value);
        setIsSelectAllForViewCols(
            selectedOptions.length === viewColsOptions.length
        );
        const tableColDef = _.cloneDeep(marketingCalendarTableConfig);
        const colsForOriginal = [
            "original_margin_percent",
            "original_contribution_margin",
            "original_contribution_margin_percent",
            "original_sales_units",
            "original_revenue",
            "original_margin",
        ];
        const colsForStacked = [
            "finalized_stack_margin_percent",
            "finalized_stack_contribution_margin",
            "finalized_stack_contribution_margin_percent",
            "finalized_stack_sales_units",
            "finalized_stack_revenue",
            "finalized_stack_margin",
            "baseline_stack_revenue",
            "baseline_stack_sales_units",
            "baseline_stack_margin",
            "finalized_stack_st_percent",
        ];
        const colsForBothSelected = [
            ...colsForStacked,
            "original_stack_margin_percent",
            "original_stack_contribution_margin",
            "original_stack_contribution_margin_percent",
            "original_stack_sales_units",
            "original_stack_revenue",
            "original_stack_margin",
        ];
        const colsToBeHiddenForStacked = [
            "baseline_sales_units",
            "baseline_revenue",
            "baseline_margin",
            "finalized_sales_units",
            "finalized_revenue",
            "finalized_margin",
            "finalized_margin_percent",
            "finalized_promo_spend",
            "finalized_contribution_margin",
            "finalized_contribution_margin_percent",
            "finalized_st_percent",
        ];
        if (
            selectedValues.includes("stacked") ||
            selectedValues.includes("original")
        ) {
            tableColDef.forEach((col) => {
                if (col.children && col.children.length > 0) {
                    col.children.forEach((child) => {
                        if (
                            (colsForBothSelected.includes(child.field) &&
                                selectedValues.includes("stacked") &&
                                selectedValues.includes("original")) ||
                            (colsForStacked.includes(child.field) &&
                                selectedValues.includes("stacked") &&
                                !selectedValues.includes("original")) ||
                            (colsForOriginal.includes(child.field) &&
                                selectedValues.includes("original") &&
                                !selectedValues.includes("stacked"))
                        ) {
                            child.hide = false;
                        }
                        if (
                            colsToBeHiddenForStacked.includes(child.field) &&
                            selectedValues.includes("stacked")
                        ) {
                            child.hide = true;
                        }
                    });
                }
            });
            setTableColDefs(
                !global_configs?.event?.use_event
                    ? tableColDef.filter((col) => col.field !== "event_name")
                    : _.cloneDeep(tableColDef)
            );
        } else {
            setTableColDefs(
                !global_configs?.event?.use_event
                    ? marketingCalendarTableConfig.filter(
                          (col) => col.field !== "event_name"
                      )
                    : _.cloneDeep(marketingCalendarTableConfig)
            );
        }
    };

    const onSelectAllHandlerForViewCols = (e) => {
        if (e.target.checked) {
            handleViewColsChange(viewColsOptions);
        } else {
            handleViewColsChange([]);
        }
    };

    const handleEditOfferClick = async () => {
        const res = await dispatch(
            validateOperation({
                promo_id: [marketingCalendarSelectedOffers?.ty?.[0]?.promo_id],
            })
        );

        if (!res.is_valid) {
            dispatch(toastError(res.message));
            return;
        }

        dispatch(
            setActivePromoId(marketingCalendarSelectedOffers?.ty?.[0]?.promo_id)
        );
        dispatch(
            setMaxStepCount(
                marketingCalendarSelectedOffers?.ty?.[0]?.step_count
            )
        );

        if (res) {
            dispatch(setSelectedMarketingCalendarOffers({}));
            navigate(
                `/pricesmart-promo/workbench/create-offer?promo=${marketingCalendarSelectedOffers?.ty?.[0]?.promo_id}`
            );
        }
    };

    const handleViewOfferClick = async () => {
        dispatch(
            setActivePromoId(marketingCalendarSelectedOffers?.ty?.[0]?.promo_id)
        );
        dispatch(
            setMaxStepCount(
                marketingCalendarSelectedOffers?.ty?.[0]?.step_count
            )
        );

        dispatch(setSelectedMarketingCalendarOffers({}));
        navigate(
            `/pricesmart-promo/workbench/create-offer?promo=${marketingCalendarSelectedOffers?.ty?.[0]?.promo_id}&view=true`
        );
    };

    const onDownloadProductButtonClick = () => {
        // download products data as excel
        const params = {
            fileName: "products.xlsx",
        };
        productDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const onDownloadStoreButtonClick = () => {
        // download stores data as excel
        const params = {
            fileName: "stores.xlsx",
        };
        storeDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

    return (
        <div className="marginTop-20">
            {props.showLastYear ? (
                <div>
                    <Table
                        ref={props.marketingCalendarTableRefly}
                        tableHeader={"Last Year"}
                        suppressMenuHide={true}
                        rowData={filteredmarketingCalendarData["ly"]}
                        columnDefs={labelCurrencyHandlerForTableDefs(tableColDefs, currency_detail?.currency_symbol || "$")}
                        rowSelection="multiple"
                        onSelectionChanged={() => onRowSelectionLY()}
                        suppressRowClickSelection={true}
                        topRightOptions={
                            <div className="centerFlexWithGap12">
                                {marketingCalendarSelectedOffers?.ly?.length ===
                                1 ? (
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={ViewIcon} alt="view" />}
                                        onClick={() => {}}
                                        size="large"
                                        variant="tertiary"
                                    />
                                ) : null}
                                {marketingCalendarSelectedOffers?.ly?.length ? (
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={CopyIcon} alt="copy" />}
                                        onClick={() => onCopyOffer("ly")}
                                        size="large"
                                        variant="tertiary"
                                    />
                                ) : null}
                                {!marketingCalendarSelectedOffers?.ly
                                    ?.length && (
                                    <>
                                        <Select
                                            currentOptions={viewColsOptions}
                                            initialOptions={viewColsOptions}
                                            label="Show"
                                            labelOrientation="left"
                                            setSelectedOptions={
                                                handleViewColsChange
                                            }
                                            setCurrentOptions={() => {}}
                                            placeholder="Select.."
                                            isRequired={false}
                                            isWithSearch={false}
                                            isMulti={true}
                                            isSelectAll={isSelectAllForViewCols}
                                            onSelectAll={
                                                onSelectAllHandlerForViewCols
                                            }
                                            setIsSelectAll={
                                                setIsSelectAllForViewCols
                                            }
                                            selectedOptions={selectedViewCols}
                                            isOpen={isViewColsOpen}
                                            setIsOpen={setIsViewColsOpen}
                                            isCloseWhenClickOutside={true}
                                            toggleSelectAll
                                        />
                                        <div className="positionRelative">
                                            {showGlobalSearch ? (
                                                <div className="marketingCalendarGlobalSearchContainer">
                                                    <Input
                                                        onChange={(e) =>
                                                            onFilterTextBoxChanged(
                                                                e.target.value,
                                                                "ly"
                                                            )
                                                        }
                                                        placeholder="Search"
                                                        rightIcon={
                                                            <img
                                                                src={SearchIcon}
                                                                alt="search"
                                                            />
                                                        }
                                                        type="text"
                                                        value={globalSearchText}
                                                    />
                                                </div>
                                            ) : null}
                                            <Button
                                                iconPlacement="left"
                                                icon={
                                                    <img
                                                        src={SearchIcon}
                                                        alt="search"
                                                    />
                                                }
                                                onClick={() => {
                                                    setShowGlobalSearch(
                                                        (prev) => {
                                                            console.log(
                                                                "Previous State:",
                                                                prev
                                                            ); // Check previous state
                                                            return !prev;
                                                        }
                                                    );
                                                }}
                                                size="large"
                                                variant="secondary"
                                            />
                                        </div>
                                    </>
                                )}
                            </div>
                        }
                    />
                    <div className="horizontal-divider-line" />
                </div>
            ) : null}

            <Table
                ref={props.marketingCalendarTableRefty}
                tableHeader={"This Year"}
                suppressMenuHide={true}
                rowData={filteredmarketingCalendarData["ty"]}
                columnDefs={labelCurrencyHandlerForTableDefs(tableColDefs, currency_detail?.currency_symbol || "$")}
                rowSelection="multiple"
                onSelectionChanged={() => onRowSelectionTY()}
                suppressRowClickSelection={true}
                topRightOptions={
                    <div className="centerFlexWithGap12">
                        {marketingCalendarSelectedOffers?.ty?.length === 1 ? (
                            <div className="centerFlexWithGap12">
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={ViewIcon} alt="view" />}
                                    onClick={() => {
                                        handleViewOfferClick();
                                    }}
                                    size="large"
                                    variant="tertiary"
                                />
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={EditIcon} alt="edit" />}
                                    onClick={() => {
                                        handleEditOfferClick();
                                    }}
                                    size="large"
                                    variant="tertiary"
                                />
                            </div>
                        ) : null}
                        {marketingCalendarSelectedOffers?.ty?.length ? (
                            <div className="centerFlexWithGap12">
                                <Button
                                    onClick={() => onResimulate()}
                                    size="large"
                                    variant="tertiary"
                                    icon={
                                        <img
                                            src={RefreshIcon}
                                            alt="resimulate"
                                        />
                                    }
                                ></Button>
                                <Button
                                    onClick={() => onCopyOffer("ty")}
                                    size="large"
                                    variant="tertiary"
                                    icon={<img src={CopyIcon} alt="copy" />}
                                ></Button>
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={DeleteIcon} alt="delete" />}
                                    onClick={handleDelete}
                                    size="large"
                                    variant="secondary"
                                    type="destructive"
                                    className="delete-button"
                                />
                            </div>
                        ) : null}

                        {!marketingCalendarSelectedOffers?.ty?.length && (
                            <>
                                {!props?.showLastYear && (
                                    <Select
                                        currentOptions={viewColsOptions}
                                        initialOptions={viewColsOptions}
                                        label="Show"
                                        labelOrientation="left"
                                        setSelectedOptions={
                                            handleViewColsChange
                                        }
                                        setCurrentOptions={() => {}}
                                        placeholder="Select.."
                                        isRequired={false}
                                        isWithSearch={false}
                                        isMulti={true}
                                        isSelectAll={isSelectAllForViewCols}
                                        onSelectAll={
                                            onSelectAllHandlerForViewCols
                                        }
                                        setIsSelectAll={
                                            setIsSelectAllForViewCols
                                        }
                                        selectedOptions={selectedViewCols}
                                        isOpen={isViewColsOpen}
                                        setIsOpen={setIsViewColsOpen}
                                        isCloseWhenClickOutside={true}
                                        toggleSelectAll
                                    />
                                )}
                                <div className="positionRelative">
                                    {showGlobalSearch ? (
                                        <div className="marketingCalendarGlobalSearchContainer">
                                            <Input
                                                onChange={(e) =>
                                                    onFilterTextBoxChanged(
                                                        e.target.value,
                                                        "ty"
                                                    )
                                                }
                                                placeholder="Search"
                                                rightIcon={
                                                    <img
                                                        src={SearchIcon}
                                                        alt="search"
                                                    />
                                                }
                                                type="text"
                                                value={globalSearchText}
                                            />
                                        </div>
                                    ) : null}
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={SearchIcon}
                                                alt="search"
                                            />
                                        }
                                        onClick={() => {
                                            setShowGlobalSearch((prev) => {
                                                console.log(
                                                    "Previous State:",
                                                    prev
                                                ); // Check previous state
                                                return !prev;
                                            });
                                        }}
                                        size="large"
                                        variant="secondary"
                                    />
                                </div>
                            </>
                        )}
                    </div>
                }
            />
            <Modal
                open={productDetailModalOpen}
                onClose={() => {
                    setProductDetailModalOpen(false);
                }}
                onPrimaryButtonClick={() => {
                    setProductDetailModalOpen(false);
                }}
                primaryButtonLabel="Ok"
                size="medium"
                title="Product Details"
                className="product-store-detail-modal"
            >
                <Table
                    ref={productDetailTableRef}
                    suppressMenuHide
                    rowData={productDetailTableData}
                    columnDefs={productTableConfig(hierarchyGlobalKeys)}
                    suppressRowClickSelection={true}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <Button
                            iconPlacement="left"
                            icon={<img src={DownloadIcon} alt="download" />}
                            onClick={onDownloadProductButtonClick}
                            size="large"
                            variant="tertiary"
                        />
                    }
                />
            </Modal>
            <Modal
                open={storeDetailModalOpen}
                onClose={() => {
                    setStoreDetailModalOpen(false);
                }}
                onPrimaryButtonClick={() => {
                    setStoreDetailModalOpen(false);
                }}
                primaryButtonLabel="Ok"
                size="medium"
                title="Store Details"
                className="product-store-detail-modal"
            >
                <Table
                    ref={storeDetailTableRef}
                    suppressMenuHide
                    rowData={storeDetailTableData}
                    columnDefs={storeTableConfig(hierarchyGlobalKeys)}
                    suppressRowClickSelection={true}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <Button
                            iconPlacement="left"
                            icon={<img src={DownloadIcon} alt="download" />}
                            onClick={onDownloadStoreButtonClick}
                            size="large"
                            variant="tertiary"
                        />
                    }
                />
            </Modal>
        </div>
    );
}

export default PromoTable;
