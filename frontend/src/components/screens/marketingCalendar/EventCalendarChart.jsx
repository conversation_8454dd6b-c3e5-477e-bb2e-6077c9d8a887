import React, { useEffect, useState } from 'react'
import Calendar from '../../ui/calendar/Calendar'
import { useDispatch, useSelector } from 'react-redux'
import { toastError } from '../../../store/features/global/global'
import moment from 'moment'
import { formatChartDataEvent } from './MarketingCalendarHelper'
import { toCurrencyByCurrencyId, toUnit } from '../../../utils/helpers/formatter'
import { capitalizeFirstLetter, replaceSpecialCharacter } from '../../../utils/helpers/utility_helpers'
import {
    Button,
    Tooltip
} from "impact-ui"
import RefreshIcon from "../../../assets/imageAssets/refreshIcon.svg?.url";
import LockedIcon from "../../../assets/imageAssets/locked.svg?.url";
import LockOpenIcon from "../../../assets/imageAssets/lock-open.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import ViewIcon from "../../../assets/imageAssets/eyeIcon.svg?.url";
import { DEFAULT_DATE_FORMAT, global_labels } from '../../../constants/Constants'

const EventCalendarChart = (props) => {

    const dispatch = useDispatch();

    const {
        fiscalCalendar,
        currency_detail
    } = useSelector((store) => store?.pricesmartPromoReducer.global);

    const {
        eventsCalendarData = []
    } = useSelector((store) => store?.pricesmartPromoReducer.marketingCalendar);

    const {
        filtersData
    } = useSelector((store) => store?.pricesmartPromoReducer.filters);

    const [filteredMarketingCalendarChartData, setFilteredMarketingCalendarChartData] = useState({});

    useEffect(() => {
        // Filter data based on selected event types
        if (_.isEmpty(eventsCalendarData)) return;
        if (props.eventType.length > 0) {
            const selectedStatusValues = props.eventType.map(
                (status) => status.value
            );
            let filteredData = {};
            Object.keys(eventsCalendarData).forEach((key) => {
                filteredData[key] = formatChartDataEvent(
                    eventsCalendarData[key].filter((event) =>
                        selectedStatusValues.includes(event.status?.toLowerCase())
                    )
                );
            });
            setFilteredMarketingCalendarChartData(filteredData);
        } else {
            setFilteredMarketingCalendarChartData([]);
        }
    }, [eventsCalendarData, props.eventType]);


    const getMinMaxDate = (type, year) => {
        // Get min and max date for the calendar
        let marketingCalendarFilters = filtersData.MARKETING_CALENDAR;
        const calCurrentStateTy = props?.calCurrentState?.ty;
        switch (type) {
            case "min":
                if (year === "ty") {
                    return moment(
                        marketingCalendarFilters?.dateRange?.start_date
                    );
                } else if (year === "ly") {
                    const fy = fiscalCalendar.find((y) => y.year === calCurrentStateTy.yr);
                    const fq = fy.fiscal_data?.quarters.find((q) => q.fiscal_quarter === calCurrentStateTy.qtr);
                    const fm = fq.months?.find((m) => m.fiscal_month === calCurrentStateTy.mo) || fq.months[0];
                    const fw = fm.weeks?.find((w) => w.fiscal_week === calCurrentStateTy.wk) || fm.weeks[0];
                    const f_date = fw.min_date;

                    return moment(f_date).subtract(1, "years");
                }
                break;
            case "max":
                if (year === "ty") {
                    return moment(
                        marketingCalendarFilters?.dateRange?.end_date
                    );
                } else if (year === "ly") {
                    return moment(
                        marketingCalendarFilters?.dateRange?.end_date
                    ).subtract(1, "years");
                }
        }
    };

    const getDetailContent = (item) => {
        // Get the content for the event detail
        return (
            <div>
                <div className="event-detail-title-container">
                    <p className="event-detail-title">
                        {global_labels.event_primary} - {" "}
                        {item.name &&
                            replaceSpecialCharacter(item.name).length > 34 ? (
                            <Tooltip
                                title={replaceSpecialCharacter(item.name)}
                                placement="right"
                                variant="tertiary"
                            >
                                <span title={item.name}>
                                    {replaceSpecialCharacter(
                                        item.name
                                    ).substring(0, 34)}
                                    ...
                                </span>
                            </Tooltip>
                        ) : (
                            replaceSpecialCharacter(item.name)
                        )}
                    </p>
                    <div className="event-detail-cta">
                        <div className="centerFlexWithGap12">
                            <Tooltip title={`View ${global_labels.promo_plural}`} variant="tertiary">
                                {/* wrapper div for button to avoid the button property conflict with the tooltip */}
                                <div>
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={ViewIcon} alt={`view ${global_labels.promo_primary}`} />}
                                        onClick={() => {
                                            props.handleViewEvent(item);
                                        }}
                                        size="small"
                                        variant="tertiary"
                                        className='calendar-event-detail-button'
                                    />
                                </div>
                            </Tooltip>
                            <Tooltip title={`Edit ${global_labels.event_primary}`} variant="tertiary">
                                <div>
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={EditIcon} alt={`edit ${global_labels.event_primary.toLowerCase()}`} />}
                                        onClick={() => {
                                            props.handleEditEvent(item);
                                        }}
                                        size="small"
                                        variant="tertiary"
                                        className='calendar-event-detail-button'
                                    />
                                </div>
                            </Tooltip>
                            <Tooltip title={`Lock ${global_labels.event_primary}`} variant="tertiary">
                                <div>
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={item?.is_locked ? LockedIcon : LockOpenIcon} alt={`${item?.is_locked ? "lock" : "unlock"} ${global_labels.event_primary}`} />}
                                        onClick={() => {
                                            props.handleLockEvent([item]);
                                        }}
                                        size="small"
                                        variant="tertiary"
                                        className='calendar-event-detail-button'
                                    />
                                </div>
                            </Tooltip>
                            <Tooltip title={`Resimulate ${global_labels.event_primary}`} variant="tertiary">
                                <div>
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={RefreshIcon} alt={`resimulate ${global_labels.event_primary}`} />}
                                        onClick={() => {
                                            props.handleResimulate([item]);
                                        }}
                                        size="small"
                                        variant="tertiary"
                                        className='calendar-event-detail-button'
                                    />

                                </div>
                            </Tooltip>
                            <Tooltip title={`Delete ${global_labels.event_primary}`} variant="tertiary">
                                <div>
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={DeleteIcon} alt={`delete ${global_labels.event_primary}`} />}
                                        onClick={() => {
                                            props.handleDelete([item]);
                                        }}
                                        size="small"
                                        variant="secondary"
                                        type="destructive"
                                        className='calendar-event-detail-button delete-button'
                                    />
                                </div>
                            </Tooltip>
                        </div>
                    </div>
                </div>
                <div className="event-details-container">
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                {global_labels.dateRange}
                            </p>
                            <p className="event-detail-value">
                                {moment(item.start_date).format(DEFAULT_DATE_FORMAT)}
                                {" "}-{" "}
                                {moment(item.end_date).format(DEFAULT_DATE_FORMAT)}
                            </p>
                        </div>
                    </div>
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                {capitalizeFirstLetter(global_labels.promo_primary)} {currency_detail?.currency_symbol || "$"}
                            </p>
                            <p className="event-detail-value">
                                {toCurrencyByCurrencyId({
                                    ...item,
                                    value: item.status === "Completed" ? item.actualized?.promo_spend : item.finalized?.promo_spend,
                                })}
                            </p>
                        </div>
                    </div>
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                {capitalizeFirstLetter(global_labels.promo_alias_plural)} Summary
                            </p>
                            <p className="event-detail-value">
                                {item.finalised_promo_count}/{item.total_promo_count}
                            </p>
                        </div>
                    </div>
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                Marketing Notes
                            </p>
                            <p className="event-detail-value">
                                {replaceSpecialCharacter(item.marketing_notes)}
                            </p>
                        </div>
                    </div>
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                Sales Units
                            </p>
                            <p className="event-detail-value">
                                {toUnit({value: item.status === "Completed" ? item.actualized?.sales_units : item.finalized?.sales_units})}
                            </p>
                        </div>
                    </div>
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                Revenue
                            </p>
                            <p className="event-detail-value">
                                {toCurrencyByCurrencyId({
                                    ...item,
                                    value: item.status === "Completed" ? item.actualized?.revenue : item.finalized?.revenue,
                                })}
                            </p>
                        </div>
                    </div>
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                Margin
                            </p>
                            <p className="event-detail-value">
                                {toCurrencyByCurrencyId({
                                    ...item,
                                    value: item.status === "Completed" ? item.actualized?.margin : item.finalized?.margin,
                                })}
                            </p>
                        </div>
                    </div>
                    <div className="event-detail-column">
                        <div className="event-detail-value-container">
                            <p className="event-detail-value-title">
                                Performance
                            </p>
                            <p className="event-detail-value">
                                {replaceSpecialCharacter(item.performance)}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        )
    }
    
    return (
        <div>
            {props.showLastYear ? (
                <div className="marginTop-20 marginBottom-24">
                    <p className="calendar-year-title">Last Year</p>
                    <Calendar
                        eventList={
                            !_.isEmpty(filteredMarketingCalendarChartData["ly"])
                                ? filteredMarketingCalendarChartData["ly"]
                                : []
                        }
                        // isTrackingDeviceExternal={
                        //     props.isTrackingDeviceExternal
                        // }
                        calendarFrequency={props.aggregation}
                        fiscalCalendar={fiscalCalendar}
                        onCalEventClick={(e) =>
                            props.onCalendarItemClick(e, "ly")
                        }
                        toastError={(error) => dispatch(toastError(error))}
                        calCurrentState={props.calCurrentState}
                        setCalCurrentState={props.setCalCurrentState}
                        tab="promo"
                        calDateRange="ly"
                        calMinDate={getMinMaxDate("min", "ly")}
                        calMaxDate={getMinMaxDate("max", "ly")}
                        // handleAction={props.handleAction}
                        // roleActions={props.roleActions}
                        getDetailContent={getDetailContent}
                        onBoundaryChange={(args) => props.onBoundaryChange(true, args)}
                    />
                </div>
            ) : null}
            <div className="marginTop-20 marginBottom-24">
                <p className="calendar-year-title">This Year</p>
                <Calendar
                    eventList={
                        !_.isEmpty(filteredMarketingCalendarChartData["ty"])
                            ? filteredMarketingCalendarChartData["ty"]
                            : []
                    }
                    // isTrackingDeviceExternal={props.isTrackingDeviceExternal}
                    calendarFrequency={props.aggregation}
                    fiscalCalendar={fiscalCalendar}
                    onCalEventClick={(e) => props.onCalendarItemClick(e, "ty")}
                    toastError={(error) => dispatch(toastError(error))}
                    calCurrentState={props.calCurrentState}
                    setCalCurrentState={props.setCalCurrentState}
                    tab="promo"
                    calDateRange="ty"
                    calMinDate={getMinMaxDate("min", "ty")}
                    calMaxDate={getMinMaxDate("max", "ty")}
                    // handleAction={props.handleAction}
                    // roleActions={props.roleActions}
                    getDetailContent={getDetailContent}
                    onBoundaryChange={(args) => props.onBoundaryChange(false, args)}
                />
            </div>
        </div>
    )
}

export default EventCalendarChart