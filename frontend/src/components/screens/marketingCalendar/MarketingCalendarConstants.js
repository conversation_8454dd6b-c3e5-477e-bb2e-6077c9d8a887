import { capitalizeFirstLetter, replaceSpec<PERSON><PERSON><PERSON><PERSON> } from "../../../utils/helpers/utility_helpers";
import {
	yyyyMmDdFormatter,
	toUnit,
	toPercentage,
	toPercWithOneDecimal,
	toCurrencyByCurrencyId,
} from "../../../utils/helpers/formatter";
import CalendarIcon from "../../../assets/imageAssets/calendarIcon.svg?.url";
import TableIcon from "../../../assets/imageAssets/tableIcon.svg?.url";
import FinalizedSalesUnitsIcon from "../../../assets/imageAssets/finSalesUnits.png";
import IncSalesUnitsIcon from "../../../assets/imageAssets/incSalesUnits.png";
import BaselineSalesUnitsIcon from "../../../assets/imageAssets/baselineSalesU.png";
import FinalRevenueIcon from "../../../assets/imageAssets/finalRevenue.png";
import BaseRevenueIcon from "../../../assets/imageAssets/baseRevenue.png";
import FinalizedGMIcon from "../../../assets/imageAssets/finGMDollar.png";
import BaselineGMIcon from "../../../assets/imageAssets/baselineGMDollar.png";
import IncrementalGMIcon from "../../../assets/imageAssets/incGMDollar.png";
import FinalizedGMPercentIcon from "../../../assets/imageAssets/finGMPercent.png";
import FinalizedCMDollarIcon from "../../../assets/imageAssets/finCMDollar.png";
import FinalizedCMPercentIcon from "../../../assets/imageAssets/finCMPercent.png";
import { global_labels, HYPERLINK_PRODUCT_TABLE_CONFIG } from "../../../constants/Constants";
import { containerStore } from "../../../store";

import {
	OfferNameCellRenderer,
	OfferStatusCellRenderer,
	cellsWithBadge,
	CellWithLockIcon,
	TableWithInputBoxOption,
	CellWithDateRange,
	CopyEventDateRangeCellRenderer
} from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { lockUnlockEvent } from "../../../store/features/eventReducer/eventReducer";

export const marketingCalendarTilesConfig = [
	{
		label: "Finalized Sales Units",
		key: "sales_units_finalized",
		formatter: "toUnit",
		icon: FinalizedSalesUnitsIcon,
	},
	{
		label: "Baseline Sales Units",
		key: "sales_units_baseline",
		formatter: "toUnit",
		icon: BaselineSalesUnitsIcon,
	},
	{
		label: "Incremental Sales Units",
		key: "sales_units_incremental",
		formatter: "toUnit",
		icon: IncSalesUnitsIcon,
	},
	{
		label: "Finalized Revenue",
		key: "revenue_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalRevenueIcon,
	},
	{
		label: "Baseline Revenue",
		key: "revenue_baseline",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: BaseRevenueIcon,
	},
	{
		label: "Incremental Revenue",
		key: "revenue_incremental",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalRevenueIcon,
	},
	{
		label: "Finalized GM",
		key: "gross_margin_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalizedGMIcon,
	},
	{
		label: "Baseline GM",
		key: "gross_margin_baseline",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: BaselineGMIcon,
	},
	{
		label: "Incremental GM",
		key: "gross_margin_incremental",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: IncrementalGMIcon,
	},
	{
		label: "Finalized GM %",
		key: "gross_margin_percent_finalized",
		formatter: "toUnit",
		icon: FinalizedGMPercentIcon,
	},
	{
		label: "Finalized CM $",
		key: "contribution_gross_margin_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalizedCMDollarIcon,
	},
	{
		label: "Finalized CM %",
		key: "contribution_gross_margin_percent_finalized",
		formatter: "toUnit",
		icon: FinalizedCMPercentIcon,
	},
];

export const marketingCalendarTableConfig = [
	{
		field: "",
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true,
		suppressMenu: true,
		filter: false,
		sortable: false,
		pinned: "left",
		maxWidth: 60,
	},
	{
		field: "promo_name",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Name`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
		cellRenderer: OfferNameCellRenderer,
		width: 350,
		cellRendererParams: {
			enableInput: {
				screen: "decisionDashboard", // reducer name
				enableKey: "enableDDInlineEdit", //input box enable key in reducer state
				storeDataKey: "decisionDashboardSelectedOffers", // selected table data key in reducer state
				uniqueKey: "promo_id", // key to match for selective enable and disable input box
			},
			defaultFlag: false,
			// add onClick handler if needed to perform any action on click of the cell, once passed the label will appear as a link
		},
	},
	{
		field: "event_name",
		headerName: global_labels.event_primary + " Name",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => {
			if (params.value) {
				return replaceSpecialCharacter(params.value)
			} else {
				return "-"
			}
		},
	},
	{
		field: "start_date",
		headerName: "Start Date",
		width: 140,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
	},
	{
		field: "end_date",
		headerName: "End Date",
		width: 136,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
	},
	{
		field: "created_by",
		headerName: "Created By",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "status",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Status`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
		cellRenderer: OfferStatusCellRenderer
	},
	{
		field: "offer_comment",
		headerName: "Comments",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "products_count",
		headerName: "Products",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		type: "number",
	},
	{
		field: "stores_count",
		headerName: "Stores",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		type: "number",
	},
	{
		field: "actual_performance",
		width: 160,
		headerName: "Actual Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "finalized_performance",
		width: 160,
		headerName: "Finalized Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "discount_type",
		headerName: "Discount Type",
		isSearchable: true,
		filter: "agTextColumnFilter",
	},
	{
		field: "discount_level",
		headerName: "Discount Level - Products",
		width: 248,
		isSearchable: true,
		filter: "agTextColumnFilter",
	},
	{
		field: "finalized_promo_spend",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} $`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		type: "number",
	},
	{
		field: "finalized_margin_percent",
		headerName: "Finalized GM %",
		width: 172,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		type: "number",
	},
	{
		field: "finalized_contribution_margin",
		headerName: "Finalized CM $",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		type: "number",
	},
	{
		field: "finalized_contribution_margin_percent",
		headerName: "Finalized CM %",
		width: 176,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toPercentage(params),
		type: "number",
	},
	{
		field: "finalized_inventory",
		headerName: "Inventory",
		width: 190,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		type: "number",
	},
	{
		colId: "st_percent",
		headerName: "ST %",
		children: [
			{
				field: "actualized_st_percent",
				headerName: "Actualized",
				valueFormatter: (params) => toPercWithOneDecimal(params),
				type: "number",
			},
			{
				field: "finalized_st_percent",
				headerName: "Finalized",
				valueFormatter: (params) => toPercWithOneDecimal(params),
				type: "number",
			},
			{
				field: "finalized_stack_st_percent",
				headerName: "Finalized",
				valueFormatter: (params) => toPercWithOneDecimal(params),
				hide: true,
				type: "number",
			},
		]
	},
	{
		headerName: "Sales Units",
		colId: "sales_units",
		children: [
			{
				field: "finalized_sales_units",
				headerName: "Finalized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 210,
				hide: false,
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "finalized_stack_sales_units",
				headerName: "Finalized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 210,
				hide: true,
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "original_sales_units",
				headerName: "Original",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 210,
				hide: true,
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "original_stack_sales_units",
				headerName: "Original",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 210,
				hide: true,
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "baseline_sales_units",
				headerName: "Baseline",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 210,
				hide: false,
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "baseline_stack_sales_units",
				headerName: "Baseline",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 210,
				hide: true,
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "incremental_sales_units",
				headerName: "Incremental",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 210,
				hide: false,
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
		]
	},
	// {
	// 	field: "target_sales_units",
	// 	headerName: "Target Sales Units",
	// 	isSearchable: true,
	// 	filter: "agTextColumnFilter",
	// 	valueFormatter: (params) => toUnit(params),
	// },
	{
		field: "revenue",
		children: [
			{
				field: "finalized_revenue",
				headerName: "Finalized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: false,
				type: "number",
			},
			{
				field: "finalized_stack_revenue",
				headerName: "Finalized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},
			{
				field: "original_revenue",
				headerName: "Original",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},
			{
				field: "original_stack_revenue",
				headerName: "Original",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},

			{
				field: "baseline_revenue",
				headerName: "Baseline",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: false,
				type: "number",
			},
			{
				field: "baseline_stack_revenue",
				headerName: "Baseline",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},
			{
				field: "incremental_revenue",
				headerName: "Incremental",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: false,
				type: "number",
			},
		]
	},
	// {
	// 	field: "target_revenue",
	// 	headerName: "Target Revenue",
	// 	isSearchable: true,
	// 	filter: "agTextColumnFilter",
	// 	valueFormatter: (params) => toCurrencyByCurrencyId(params),
	// },
	{
		field: "margin",
		children: [
			{
				field: "finalized_margin",
				headerName: "Finalized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: false,
				type: "number",
			},
			{
				field: "finalized_stack_margin",
				headerName: "Finalized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},
			{
				field: "original_margin",
				headerName: "Original",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},
			{
				field: "original_stack_margin",
				headerName: "Original",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},
			{
				field: "baseline_margin",
				headerName: "Baseline",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: false,
				type: "number",
			},
			{
				field: "baseline_stack_margin",
				headerName: "Baseline",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: true,
				type: "number",
			},
			{
				field: "incremental_margin",
				headerName: "Incremental",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				hide: false,
				type: "number",
			},
		]
	},
	// {
	// 	field: "target_margin",
	// 	headerName: "Target Margin",
	// 	isSearchable: true,
	// 	filter: "agTextColumnFilter",
	// 	valueFormatter: (params) => toCurrencyByCurrencyId(params),
	// },
	{
		field: "product_selection_type",
		headerName: "Product Selection Type",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 228,
	},
	{
		field: "customer_type",
		headerName: "Customer Type",
		isSearchable: true,
		filter: "agTextColumnFilter",
	},
	{
		field: "offer_distribution_channel",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Distribution Channel`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 215,
	},
];

export const marketingCalendarViewModes = [
	{
		label: "",
		value: "calendar",
		icon: <img src={CalendarIcon} alt="calendar" />,
	},
	{
		label: "",
		value: "table",
		icon: <img src={TableIcon} alt="table" />,
	},
];

export const marketingCalendarViewAggregations = [
	{
		label: "Month",
		value: "month",
	},
	{
		label: "Quarter",
		value: "quarter",
	},
];

export const marketingCalendarViewDefaultAggregation = {
	label: "Month",
	value: "month",
};

export const marketingCalendarDefaultPromoTypes = [4, 8];

export const client_labels = {
	event: "Event",
	offer: "Promotion",
}

export const eventStatus = [
	{
		label: "Upcoming",
		value: "upcoming",
	},
	{
		label: "On Going",
		value: "ongoing",
	},
	{
		label: "Completed",
		value: "completed",
	},
]

export const defaultEventStatus = ["upcoming", "ongoing"];

export const eventTableConfig = [
	{
		field: "",
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true,
		suppressMenu: true,
		filter: false,
		sortable: false,
		pinned: "left",
		maxWidth: 60,
	},
	{
		field: "event_name",
		headerName:  global_labels.event_primary + " Name",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: CellWithLockIcon,
		cellRendererParams: {
			onLockUnlockUpdate: async (data) => {

				const lockAction = !data?.is_locked;
				const payload = {
					event_ids: [data?.event_id],
					is_locked: lockAction
				}	
				await containerStore.dispatch(lockUnlockEvent(payload));
			}
		}
	},
	{
		field: "start_date",
		headerName: "Start Date",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 150,
		valueFormatter: (params) => yyyyMmDdFormatter(params),
	},
	{
		field: "end_date",
		headerName: "End Date",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 150,
		valueFormatter: (params) => yyyyMmDdFormatter(params),
	},
	{
		field: "created_by_name",
		headerName: "Created By",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 150,
	},
	{
		field: "status",
		headerName: global_labels.event_primary + " Status",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Upcoming: "info",
				Ongoing: "info",
				Completed: "success",
			},
		},

	},
	{
		field: "total_promo_count",
		headerName: `No: of ${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 150,
		type: "number",
	},
	{
		field: "actual_performance",
		width: 160,
		headerName: "Actual Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "finalized_performance",
		width: 160,
		headerName: "Finalized Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "marketing_notes",
		headerName: "Comments",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "finalized_inventory",
		headerName: "Inventory",
		width: 190,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		type: "number",
	},
	{
		field: "st_percent",
		headerName: "ST %",
		children: [
			{
				field: "actualized_st_percent",
				headerName: "Actualized",
				valueFormatter: (params) => toPercWithOneDecimal(params),
				type: "number",
			},
			{
				field: "finalized_st_percent",
				headerName: "Finalized",
				valueFormatter: (params) => toPercWithOneDecimal(params),
				type: "number",
			},
		]
	},
	{
		field: "sales_units",
		headerName: "Sales Units",
		children : [
			{
				field: "actualized_sales_units",
				headerName: "Actualized Sales Units",
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "finalized_sales_units",
				headerName: "Finalized",
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "baseline_sales_units",
				headerName: "Baseline",
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "actualized_incremental_sales_units",
				headerName: "Actualized Incremental",
				valueFormatter: (params) => toUnit(params),
				type: "number",
			},
			{
				field: "finalized_incremental_sales_units",
				headerName: "Finalized Incremental",
				valueFormatter: (params) => toUnit(params),
				type: "number",
			}
		]
	},
	{
		field: "revenue",
		headerName: "Revenue",
		children: [
			{
				field: "actualized_revenue",
				headerName: "Actualized Revenue",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_revenue",
				headerName: "Finalized",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "baseline_revenue",
				headerName: "Baseline",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actualized_incremental_revenue",
				headerName: "Actualized Incremental",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_incremental_revenue",
				headerName: "Finalized Incremental",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			}
		]
	},
	{
		field: "margin",
		headerName: "Margin",
		children: [
			{
				field: "actualized_margin",
				headerName: "Actualized Margin",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_margin",
				headerName: "Finalized",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "baseline_margin",
				headerName: "Baseline",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actualized_incremental_margin",
				headerName: "Actualized Incremental",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_incremental_margin",
				headerName: "Finalized Incremental",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			}
		]
	},
	{
		field: "margin_percent",
		headerName: "GM %",
		children: [
			{
				field: "actualized_margin_percent",
				headerName: "Actualized",
				valueFormatter: (params) => toPercWithOneDecimal(params),
				type: "number",
			},
			{
				field: "finalized_margin_percent",
				headerName: "Finalized",
				valueFormatter: (params) => toPercWithOneDecimal(params),
				type: "number",
			},
		]
	},
	{
		field: "promo_spend",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_primary)} $`,
		children: [
			{
				field: "actualized_promo_spend",
				headerName: "Actualized",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_promo_spend",
				headerName: "Finalized",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
		]
	},
	{
		field: "contribution_margin",
		headerName: "Contribution Margin",
		children: [
			{
				field: "actualized_contribution_margin",
				headerName: "Actualized",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_contribution_margin",
				headerName: "Finalized",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
		]
	},
	{
		field: "contribution_margein_percent",
		headerName: "CM %",
		children: [
			{
				field: "actualized_contribution_margin_percent",
				headerName: "Actualized",
				type: "number",
			},
			{
				field: "finalized_contribution_margin_percent",
				headerName: "Finalized",
				type: "number",
			},
		]
	}
]

export const viewColsOptions = [
	{ value: "stacked", label: "Stacked" },
	{ value: "original", label: "Original #" },
];

export const copyEventTableConfig = (onCellValueChange, tableData) => [
	{
		field: "",
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true,
		suppressMenu: true,
		filter: false,
		sortable: false,
		pinned: "left",
		maxWidth: 60,
	},
	{
		field: "event_name",
		headerName: `${capitalizeFirstLetter(global_labels?.event_primary)} Name`,
		isSearchable: true,
		filter: "agTextColumnFilter",
	},
	{
		field: "date_range",
		headerName: "Date Range",
		isSearchable: true,
		filter: "agTextColumnFilter",
	},
	{
		field: "new_event_name",
		headerName: `Modified ${capitalizeFirstLetter(global_labels?.event_primary)} Name`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: TableWithInputBoxOption,
		width: 300,
		cellRendererParams: {
			enableInput: {
				screen: "marketingCalendar",
				storeDataKey: "copyEventTableData",
				uniqueKey: "event_id",
			},
			defaultFlag: true,
			onDataUpdate: (data) => {
				onCellValueChange({
					field: "new_event_name",
					value: data.data,
					id: data.uniqueKeyVal,
				});
			}
		},
	},
	{
		field: "modified_date_range",
		headerName: "Modified Date Range",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: CopyEventDateRangeCellRenderer,
		width: 300,
		cellRendererParams: {onCellValueChange},
	},
	{
		field: "submit_offers_by",
		headerName: `Submit ${capitalizeFirstLetter(global_labels?.promo_alias)} By`,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: CellWithDateRange,
		width: 300,
		cellRendererParams: {onCellValueChange, tableData},
	},
];

export const productTableConfig = (hierarchyGlobalKeys) => ([
    {
        field: "client_product_id",
        headerName: "Product ID",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "product_name",
        headerName: "Description",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 300,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    // {
    //     field: "msrp",
    //     headerName: "MSRP",
    //     isSearchable: true,
    //     filter: "agTextColumnFilter",
    //     width: 150,
    //     valueFormatter: toCurrencyByCurrencyId,
    //     type: "number",
    // },
    {
        field: "current_price",
        headerName: "Current Price",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
        type: "number",
    },
	{
		field: "cost",
		headerName: "Cost",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 140,
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		type: "number",
	},
    {
        field: "total_inventory",
        headerName: "Inventory",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
    },
    // {
    //     field: "launch_price",
    //     headerName: "Launch Price",
    //     isSearchable: true,
    //     filter: "agTextColumnFilter",
    //     flex: 1,
    //     valueFormatter: toCurrencyByCurrencyId,
    //     type: "number",
    // },
	...HYPERLINK_PRODUCT_TABLE_CONFIG(hierarchyGlobalKeys),
	{
		headerName: "Finalized",
		colId: "finalized",
		children: [
			{
				field: "finalized_revenue",
				headerName: "Revenue",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_margin",
				headerName: "Margin",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_units",
				headerName: "Units",
				type: "number",
			},
			{
				field: "finalized_gm_percent",
				headerName: "GM %",
				valueFormatter: toPercentage,
				type: "number",
			},
		],
	},
	{
		headerName: "Actual",
		colId: "actual",
		children: [
			{
				field: "actual_revenue",
				headerName: "Revenue",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actual_margin",
				headerName: "Margin",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actual_units",
				headerName: "Units",
				type: "number",
			},
			{
				field: "actual_gm_percent",
				headerName: "GM %",
				valueFormatter: toPercentage,
				type: "number",
			},
		]
	},
])

export const storeTableConfig = (hierarchyGlobalKeys) => ([
	{
		field: "store_id",
		headerName: "Store ID",
		valueFormatter: (params) => parseInt(params.value),
	},
	{
		field: "store_name",
		headerName: "Store Name",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s0_name",
		headerName: hierarchyGlobalKeys?.s0_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s1_name",
		headerName: hierarchyGlobalKeys?.s1_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s2_name",
		headerName: hierarchyGlobalKeys?.s2_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s3_name",
		headerName: hierarchyGlobalKeys?.s3_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s4_name",
		headerName: hierarchyGlobalKeys?.s4_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	// {
	// 	field: "s5_name",
	// 	headerName: "City",
	// 	valueFormatter: (params) => replaceSpecialCharacter(params.value),
	// },
])
