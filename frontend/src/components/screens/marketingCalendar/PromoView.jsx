import React, { useState, useEffect, useRef } from "react";
import moment from "moment";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";
import _ from "lodash";
import { Button, Select, ButtonGroup, Switch, Prompt } from "impact-ui";

import PromoTable from "./PromoTable";
import PromoCalendarChart from "./PromoCalendarChart";
import EmptyData from "../../ui/emptyData/EmptyData";

import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";

import {
    marketingCalendarViewAggregations,
    marketingCalendarViewDefaultAggregation,
    marketingCalendarViewModes,
    marketingCalendarDefaultPromoTypes,
} from "./MarketingCalendarConstants";
import CopyOfferPanel from "../workbench/CopyOfferPanel";
import {
    marketingCalendarDownload,
    resimulateOffers
} from "../../../store/features/marketingCalendarReducer/marketingCalendarReducer";
import { setCopyOfferTableData, deletePromo } from "../../../store/features/workbenchReducer/workbenchReducer";
import {
    toastError,
    validateOperation
} from "../../../store/features/global/global";
import {
    setActivePromoId,
    setMaxStepCount
} from "../../../store/features/promoReducer/promoReducer";

import "./MarketingCalendar.scss";
import { overwriteFilters, setSelectedFilters } from "../../../store/features/filters/filters";
import { getCurrent } from "../../ui/calendar/Helpers";
import { capitalizeFirstLetter,fabricatePayloadHierarchy } from "../../../utils/helpers/utility_helpers";
import { global_labels } from "../../../constants/Constants";


const PromoView = (props) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const {
        marketingCalendarData = [],
        marketingCalendarPromoTypes = [],
        marketingCalendarSelectedOffers = {},
    } = useSelector((store) => store?.pricesmartPromoReducer.marketingCalendar);

    const {
        fiscalCalendar,
        currency_detail
    } = useSelector((store) => store?.pricesmartPromoReducer.global);

    const [activeMode, setActiveMode] = useState("calendar"); // calendar or table
    const [isSelectAll, setIsSelectAll] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [isViewByOpen, setIsViewByOpen] = useState(false);
    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );
    const [promoType, setPromoType] = useState([]);
    const [aggregation, setAggregation] = useState(
        marketingCalendarViewDefaultAggregation
    );
    const [showLastYear, setShowLastYear] = useState(false);
    const [showCopyPanel, setShowCopyPanel] = useState(false);
    const marketingCalendarTableRefly = useRef(null);
    const marketingCalendarTableRefty = useRef(null);
    const [deleteOffersModalText, setDeleteOffersModalText] = useState(null);
    const [selectedOffers, setSelectedOffers] = useState([]);
    const [calCurrentState, setCalCurrentState] = useState({});

    useEffect(() => {
        setPromoType(
            _.filter(marketingCalendarPromoTypes, (status) =>
                marketingCalendarDefaultPromoTypes.includes(status.value)
            )
        );
    }, [marketingCalendarPromoTypes]);

    useEffect(() => {
        if (props?.triggerFilterApplyPromo) {
            const currentState = getCurrent(fiscalCalendar, filtersData?.MARKETING_CALENDAR?.dateRange?.start_date, filtersData?.MARKETING_CALENDAR?.dateRange?.end_date);
            setCalCurrentState((prev) => {
                return {
                    ...prev,
                    ty: {
                        yr: currentState?.currYr,
                        qtr: currentState?.currQtr,
                        mo: currentState?.currMo,
                        wk: currentState?.currWk,
                    }
                };
            });
        }
    }, [props?.triggerFilterApplyPromo]);

    const handleShowLastYearChange = () => {
        // Call the API to get the data for the last year, if not already present in redux
        const min_year_allowed_from_fiscal_calendar = fiscalCalendar?.[0]?.year;
        // This is added so that if the fiscal year data is absent for last year throw err
        // and eventhouggh if we are in ty 2024-01 the min date for min year week-1 is 2023-01-29, so month able to show Jan thus the mo condition added
        if (
            min_year_allowed_from_fiscal_calendar > (calCurrentState?.["ty"]?.yr - 1)
            || ((calCurrentState?.["ty"]?.yr - 1 <= min_year_allowed_from_fiscal_calendar) && calCurrentState?.["ty"]?.mo <= 1)
        ) {
            dispatch(toastError("Fiscal year data not present for last year"));
            return;
        }
        !showLastYear && props?.onFilterApply("promo", true, filtersData?.MARKETING_CALENDAR);
        setShowLastYear(!showLastYear);
    };

    const handlePromoTypeChange = (selectedOptions) => {
        // set the selected promo types
        setPromoType(selectedOptions);
    };

    const handleAggregationChange = (selectedOptions) => {
        // set the selected aggregation
        setAggregation(selectedOptions);
    };

    const onSelectAllHandler = () => {
        // Select all options
        if (isSelectAll) {
            // Deselect all options
            setPromoType([]);
        } else {
            // Select all options
            setPromoType(marketingCalendarPromoTypes);
        }
        setIsSelectAll(!isSelectAll);
    };

    const onDownloadOffersTableClick = () => {
        // Call the API to download the table data
        let payload = {
            action: "get",
            report_file_name: "calendar_view_report_extensive_data",
            report_name: "calendar_view_report_extensive_data",
            report_type: "excel",
        };

        const marketingCalendarFilters = _.cloneDeep(
            filtersData.MARKETING_CALENDAR
        );

        _.forEach(Object.keys(marketingCalendarFilters), (key) => {
            if (key === "dateRange") {
                payload.start_date = moment(
                    marketingCalendarFilters.dateRange?.start_date
                ).format("MM/DD/YYYY");
                payload.end_date = moment(
                    marketingCalendarFilters.dateRange?.end_date
                ).format("MM/DD/YYYY");
            }
        });
        payload = {
            ...payload,
            ...fabricatePayloadHierarchy(marketingCalendarFilters),
            target_currency_id: currency_detail?.currency_id,
        }
        dispatch(marketingCalendarDownload(payload));
    };

    const onCreateNewOfferClick = () => {
        //navigate to create product group screen
        navigate("/pricesmart-promo/workbench/create-offer");
    };

    const handleCopyOffer = (offerSelection) => {
        //on copy IA button click, create table data for selected offers
        const selectedPromoData = _.map(offerSelection, (promo) => {
            const dateRange = `${moment(promo.start_date).format(
                "MM-DD-YYYY"
            )} - ${moment(promo.end_date).format("MM-DD-YYYY")}`;
            return {
                event_id: null,
                promo_id: promo.promo_id,
                promo_name: promo.promo_name,
                new_promo_name: promo.promo_name,
                start_date: null,
                end_date: null,
                date_range: dateRange,
                modified_date_range: "-",
                event_id: null,
            };
        });
        //save the table data for copy IA in redux store
        dispatch(setCopyOfferTableData(_.cloneDeep(selectedPromoData)));
        //open copy IA panel
        setShowCopyPanel(true);
    };

    const onCopyPanelClose = (flag) => {
        setShowCopyPanel(false);
        marketingCalendarTableRefty?.current?.api?.deselectAll();
        marketingCalendarTableRefly?.current?.api?.deselectAll();

        //if copy IA is successful, refresh screen data
        if (flag) {
            props?.onFilterApply("promo", false, filtersData?.MARKETING_CALENDAR);
        }
    };

    const handleResimulate = async (offerSelection) => {
        let isSelectionValid = true;
        _.forEach(offerSelection, (ele) => {
            //check if selected offers have start date as today or past date
            if (
                moment().isSameOrAfter(moment(ele.start_date)) ||
                ele.status_id === 6
            ) {
                isSelectionValid = false;
            }
        });
        //if selected offers have date as today or past date, show error message and return
        if (!isSelectionValid) {
            dispatch(
                toastError(
                    `Ongoing/Completed/Deleted ${capitalizeFirstLetter(global_labels?.promo_alias)} cannot be resimulated`
                )
            );
            return false;
        }

        const selectedPromoIds = offerSelection.map((promo) => promo.promo_id);
        //build the payload and call the api to delete the selected offers
        const resimulatePromoPayload = {
            promo_id_list: selectedPromoIds,
            guid: sessionStorage.getItem("UNIQ_SSE_KEY"),
        };
        const isRequestAccepted = await dispatch(
            resimulateOffers(resimulatePromoPayload)
        );
    };

    const onDeleteButtonClick = (offerSelection = []) => {
        // check if any of the selected offers are Execution Approved
        let isSelectionValid = true;
        let execution_approved_promos = 0;

        let offersToDelete = !_.isEmpty(offerSelection)
            ? _.cloneDeep(offerSelection)
            : _.cloneDeep(marketingCalendarSelectedOffers?.["ty"]);
        setSelectedOffers(offersToDelete);

        _.forEach(offersToDelete, (ele) => {
            //check if selected offers are Execution Approved
            if (ele.status_id === 8) {
                execution_approved_promos++;
            }
            //check if selected offers have start date as today or past date
            if (
                moment().isSameOrAfter(moment(ele.start_date)) ||
                ele.status_id === 6
            ) {
                isSelectionValid = false;
            }
        });
        //if selected offers have date as today or past date, show error message and return
        if (!isSelectionValid) {
            dispatch(toastError(`${capitalizeFirstLetter(global_labels?.promo_alias_plural)} cannot be deleted`));
            return false;
        }

        let warningNote = null;
        //if selected offers are Execution Approved, show warning message
        if (execution_approved_promos > 0) {
            warningNote = `${execution_approved_promos} of the selected ${capitalizeFirstLetter(global_labels?.promo_alias_plural)} are Execution Approved.`;
            setDeleteOffersModalText(warningNote);
        } else {
            //if selected offers are not Execution Approved, call delete api
            onDeleteHandle(offersToDelete);
        }
    };

    const cancelDeleteHandler = () => {
        //clear delete modal text, and deselect all the selected promos
        setDeleteOffersModalText(null);
        marketingCalendarTableRefty?.current?.api?.deselectAll();
    };

    const onDeleteHandle = async (offersToDelete = []) => {
        let offersToBeDeleted = !_.isEmpty(offersToDelete)
            ? _.cloneDeep(offersToDelete)
            : _.cloneDeep(selectedOffers);
        //get all promo ids of selected offers
        const selectedPromoIds = offersToBeDeleted.map(
            (promo) => promo.promo_id
        );
        //build the payload and call the api to delete the selected offers
        const deletePromoPayload = {
            promo_ids: selectedPromoIds,
        };
        const deleteRes = await dispatch(deletePromo(deletePromoPayload));
        //if delete is successful, refresh screen data
        if (deleteRes) {
            props?.onFilterApply("promo", false, filtersData?.MARKETING_CALENDAR);
        }
        //close the delete modal, clear delete modal text, if any
        cancelDeleteHandler();
    };

    const onCalendarItemClick = (item, year) => {
        // TODO: Handle for Lasy Year Items - if LY items are selected, unselect TY items
    };

    const handleViewOffer = async (item) => {

        dispatch(setActivePromoId(item[0]?.promo_id));
        dispatch(setMaxStepCount(item[0]?.step_count));

        if (item[0]?.promo_id) {
            navigate(`/pricesmart-promo/workbench/create-offer?promo=${item[0].promo_id}&view=true`);
        }
    };

    const handleEditOffer = async (item) => {
        const res = await dispatch(validateOperation({
            promo_id: [item[0]?.promo_id]
        }));

        if (!res.is_valid) {
            dispatch(toastError(res.message));
            return;
        }

        dispatch(setActivePromoId(item[0]?.promo_id));
        dispatch(setMaxStepCount(item[0]?.step_count));

        if (res) {
            navigate(`/pricesmart-promo/workbench/create-offer?promo=${item[0].promo_id}`);
        }
    };

    const submitCopyPromos = data => {
        onCopyPanelClose();
        const tempFiltersData = _.cloneDeep(filtersData?.MARKETING_CALENDAR);
        tempFiltersData["promo"] = { selectedOptionsArray: data?.promo_ids?.map(promo => promo?.value) };
        tempFiltersData["dateRange"] = {
            start_date: data?.start_date,
            end_date: data?.end_date
        }
        props?.onFilterApply("promo", false, tempFiltersData);
    }

    const handleBoundaryChange = (lastyear, type) => {
        const immediateFilters = _.cloneDeep(filtersData?.MARKETING_CALENDAR);
        const startDate = moment(immediateFilters["dateRange"]?.start_date);
        const endDate = moment(immediateFilters["dateRange"]?.end_date);

        const key = lastyear ? "ly" : "ty";

        if (
            type == "prevWk" ||
            type == "prevMo" ||
            type == "prevQtr" ||
            type == "prevYr"
        ) {
            immediateFilters["dateRange"] = {
                start_date: startDate.subtract(1, aggregation?.value),
                end_date: endDate
            };
            setCalCurrentState((prev) => {
                const currentState = _.cloneDeep(prev[key]) || {};
                if (type === "prevYr") {
                    currentState.qtr = 4;
                    currentState.mo = 12;
                    currentState.yr = currentState.yr - 1;
                    currentState.wk = 52;
                } else if (aggregation?.value === "quarter") {
                    if (currentState.qtr === 1) {
                        currentState.yr = currentState.yr - 1;
                        currentState.qtr = 4;
                        currentState.mo = 12;
                    } else {
                        currentState.qtr = currentState.qtr - 1;
                        currentState.mo = (currentState.qtr - 1) * 3 + 1;
                        currentState.wk = (currentState.qtr - 1) * 13 + 1;
                    }
                } else if (aggregation?.value === "month") {
                    if (currentState.mo === 1) {
                        currentState.mo = 12;
                        currentState.qtr = 4;
                        currentState.yr = currentState.yr - 1;
                    } else {
                        currentState.mo = currentState.mo - 1;
                        currentState.qtr = Math.ceil((currentState.mo) / 3);
                        currentState.wk = (currentState.qtr - 1) * 13 + 1;
                    }
                }
                return {
                    ...prev,
                    [key]: currentState
                };
            });
        } else if (
            type == "nextWk" ||
            type == "nextMo" ||
            type == "nextQtr" ||
            type == "nextYr"
        ) {
            immediateFilters["dateRange"] = {
                start_date: startDate,
                end_date: endDate.add(1, aggregation?.value)
            };
            setCalCurrentState((prev) => {
                const currentState = _.cloneDeep(prev[key]) || {};
                if (type === "nextYr") {
                    currentState.qtr = 1;
                    currentState.mo = 1;
                    currentState.yr = currentState.yr + 1;
                    currentState.wk = 1;
                } else if (aggregation?.value === "quarter") {
                    if (currentState.qtr === 4) {
                        currentState.qtr = 1;
                        currentState.mo = 1;
                        currentState.yr = currentState.yr + 1;
                    } else {
                        currentState.qtr += 1;
                        currentState.mo = (currentState.qtr - 1) * 3 + 1;
                        currentState.wk = (currentState.qtr - 1) * 13 + 1;
                    }
                } else if (aggregation?.value === "month") {
                    if (currentState.mo === 12) {
                        currentState.mo = 1;
                        currentState.qtr = 1;
                        currentState.yr = currentState.yr + 1;
                    } else {
                        currentState.mo += 1;
                        currentState.qtr = Math.ceil((currentState.mo) / 3);
                        currentState.wk = (currentState.qtr - 1) * 13 + 1;
                    }
                }
                return {
                    ...prev,
                    [key]: currentState
                };
            });
        }
        dispatch(
            overwriteFilters({
                filtersData: _.cloneDeep(immediateFilters),
                activeScreen: "MARKETING_CALENDAR",
            })
        );

        if (lastyear) {
            props?.onFilterApply("all", true, immediateFilters);
        }
        props?.onFilterApply("all", false, immediateFilters);
    }

    return (
        <div className="paddingTop-8">
            <div className="flex1">
                <div className="marketingCalendarDataContainer">
                    <div className="content_container">
                        <div className="marketing-calendar-subtitle-container">
                            <p className="marketing-calendar-subtitle">
                                All Promotions
                            </p>
                            <div className="centerFlexWithGap12">
                                <Switch
                                    iaFallback="v3"
                                    leftLabel="Show last year"
                                    onChange={handleShowLastYearChange}
                                    value={showLastYear}
                                />

                                {activeMode === "calendar" ? (
                                    <div className="centerFlexWithGap12">
                                        <div className="horizontal-line" />
                                        <Select
                                            currentOptions={
                                                marketingCalendarViewAggregations
                                            }
                                            initialOptions={
                                                [marketingCalendarViewDefaultAggregation]
                                            }
                                            label="View By"
                                            labelOrientation="left"
                                            setSelectedOptions={
                                                handleAggregationChange
                                            }
                                            setCurrentOptions={() => { }}
                                            placeholder="Select..."
                                            isRequired={false}
                                            isWithSearch={false}
                                            isMulti={false}
                                            isSelectAll={false}
                                            selectedOptions={aggregation}
                                            isOpen={isViewByOpen}
                                            setIsOpen={setIsViewByOpen}
                                            isCloseWhenClickOutside={true}
                                            toggleSelectAll
                                        />
                                    </div>
                                ) : (
                                    <div className="horizontal-line" />
                                )}
                                <Select
                                    currentOptions={
                                        marketingCalendarPromoTypes
                                    }
                                    initialOptions={
                                        marketingCalendarPromoTypes
                                    }
                                    label={`${capitalizeFirstLetter(global_labels?.promo_alias)} Status`}
                                    labelOrientation="left"
                                    setSelectedOptions={
                                        handlePromoTypeChange
                                    }
                                    setCurrentOptions={() => { }}
                                    placeholder="Select..."
                                    isRequired={false}
                                    isWithSearch={false}
                                    isMulti={true}
                                    isSelectAll={isSelectAll}
                                    onSelectAll={onSelectAllHandler}
                                    setIsSelectAll={setIsSelectAll}
                                    selectedOptions={promoType}
                                    isOpen={isOpen}
                                    setIsOpen={setIsOpen}
                                    isCloseWhenClickOutside={true}
                                    toggleSelectAll
                                />
                                <div className="horizontal-line" />
                                <ButtonGroup
                                    onChange={(_e, val) =>
                                        setActiveMode(val)
                                    }
                                    options={marketingCalendarViewModes}
                                    selectedOption={activeMode}
                                />
                                <div className="horizontal-line" />
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={DownloadIcon} />}
                                    onClick={onDownloadOffersTableClick}
                                    size="large"
                                    variant="secondary"
                                />
                                <div className="horizontal-line" />
                                <Button
                                    onClick={onCreateNewOfferClick}
                                    size="large"
                                    variant="primary"
                                >
                                    Create New {capitalizeFirstLetter(global_labels?.promo_alias)}
                                </Button>
                            </div>
                        </div>
                        {activeMode === "calendar" ? (
                            <PromoCalendarChart
                                resetScreenData={props?.onFilterApply}
                                promoType={promoType}
                                showLastYear={showLastYear}
                                handleResimulate={handleResimulate}
                                handleCopyOffer={handleCopyOffer}
                                handleDelete={onDeleteButtonClick}
                                aggregation={aggregation?.value}
                                onCalendarItemClick={onCalendarItemClick}
                                handleViewOffer={handleViewOffer}
                                handleEditOffer={handleEditOffer}
                                onBoundaryChange={handleBoundaryChange}
                                calCurrentState={calCurrentState}
                                setCalCurrentState={setCalCurrentState}
                            />
                        ) : (
                            <PromoTable
                                resetScreenData={props?.onFilterApply}
                                promoType={promoType}
                                showLastYear={showLastYear}
                                handleResimulate={handleResimulate}
                                handleCopyOffer={handleCopyOffer}
                                handleDelete={onDeleteButtonClick}
                                marketingCalendarTableRefly={
                                    marketingCalendarTableRefly
                                }
                                marketingCalendarTableRefty={
                                    marketingCalendarTableRefty
                                }
                            />
                        )}
                    </div>
                </div>
            </div>
            {showCopyPanel ? (
                <CopyOfferPanel
                    isOpen={showCopyPanel}
                    closePanel={onCopyPanelClose}
                    screen="MARKETING_CALENDAR"
                    submitCopyPromos={submitCopyPromos}
                />
            ) : null}

            <Prompt
                handleClose={cancelDeleteHandler}
                onPrimaryButtonClick={onDeleteHandle}
                onSecondaryButtonClick={cancelDeleteHandler}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title="Are you Sure?"
                variant="warning"
                isOpen={deleteOffersModalText}
            >
                {deleteOffersModalText}
            </Prompt>
        </div>
    );
}

export default PromoView;