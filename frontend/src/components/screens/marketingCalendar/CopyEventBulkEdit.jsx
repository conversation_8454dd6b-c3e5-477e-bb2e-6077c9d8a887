import React, { useState } from 'react'
import { But<PERSON>, DatePicker, DateRangePicker } from "impact-ui";
import InactiveCloseIcon from "../../../assets/imageAssets/inactiveCloseIcon.svg?url";
import moment from "moment";
import { capitalizeFirstLetter } from '../../../utils/helpers/utility_helpers';
import { global_labels } from '../../../constants/Constants';

export const CopyEventBulkEdit = (props) => {

    const { selectedRowData, handleBulkEditApply } = props;

    const [showBulkEdit, setShowBulkEdit] = useState(false);
    const [dates, setDates] = useState({
        start_date: null,
        end_date: null,
        submit_offers_by: null
    });

    const onClosePanel = () => {
        setShowBulkEdit(false);
        setDates({
            start_date: null,
            end_date: null,
            submit_offers_by: null
        });
    }

    const handleApply = () => {
        handleBulkEditApply(dates);
        onClosePanel();
    }

    const isOutsideRangeSubmitOffersBy = (date) => {
        const startDate = moment(dates.start_date).add(-1, "days");
        const today = moment();
        return (
            moment(date).isAfter(startDate) ||
            moment(date).isBefore(today)
        );
    };

    const isOutsideRange = (date) => {
        const today = moment();
        return moment(date).isBefore(today, 'day');
    };


    return (
        <div>
            <div className="centerFlexWithGap12">
                <Button
                    onClick={() =>
                        setShowBulkEdit((prev) => !prev)
                    }
                    size="large"
                    variant="primary"
                    disabled={
                        !selectedRowData.length || showBulkEdit
                    }
                >
                    Bulk edit
                </Button>
            </div>
            {showBulkEdit ? (
                <div>
                    <div
                        className="bulkCopyContainer"
                    >
                        <div className="bulkCopyHeader">
                            <p className="text-16-800">Bulk Edit</p>
                            <Button
                                iconPlacement="left"
                                icon={<img src={InactiveCloseIcon} />}
                                size="large"
                                variant="url"
                                onClick={onClosePanel}
                            />
                        </div>
                        <div className="bulkCopyBody">
                            <DateRangePicker
                                label={"Date Range"}
                                isRequired={true}
                                showRangeSelector={false}
                                startDate={dates.start_date}
                                setStartDate={(date) => setDates(prev => ({ ...prev, start_date: date }))}
                                endDate={dates.end_date}
                                setEndDate={(date) => setDates(prev => ({ ...prev, end_date: date }))}
                                labelOrientation="top"
                                startDateInputProps={{
                                    label: "StartDate",
                                    name: "start_date",
                                }}
                                endDateInputProps={{
                                    label: "EndDate",
                                    name: "end_date",
                                }}
                                displayFormat="MM-DD-YYYY"
                                orientation="horizontal"
                                withPortal={true}
                                isOutsideRange={isOutsideRange}
                            />
                            <DatePicker
                                label={`Submit ${capitalizeFirstLetter(global_labels?.promo_alias)} By`}
                                isRequired={true}
                                selectedDate={dates.submit_offers_by}
                                isDisabled={!dates.start_date}
                                displayFormat="MM-DD-YYYY"
                                setSelectedDate={(date) => setDates(prev => ({ ...prev, submit_offers_by: date }))}
                                isOutsideRange={isOutsideRangeSubmitOffersBy}
                            />
                        </div>
                        <div className="bulkCopyFooter">
                            <Button
                                size="large"
                                variant="url"
                                onClick={onClosePanel}
                            >
                                Cancel
                            </Button>
                            <Button
                                size="large"
                                variant="primary"
                                onClick={handleApply}
                            >
                                Apply
                            </Button>
                        </div>
                    </div>
                </div>
            ) : null}
        </div>
    )
}