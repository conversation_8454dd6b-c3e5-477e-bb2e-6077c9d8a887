import React, { useEffect, useState, useMemo } from 'react'
import ScreenBreadcrumb from '../../common/breadCrumb/ScreenBreadcrumb';
import _ from 'lodash';
import {
    Button,
    Tabs,
    Checkbox,
    RadioButtonGroup
} from "impact-ui"
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
import { useNavigate } from "react-router-dom-v5-compat";
import FilterWrapper from '../../common/filters/FilterWrapper';
import { breadcrumbRoutes } from '../../../constants/RouteConstants';
import { 
    marketingCalendarFilterConfig as filterConfig, 
    marketingCalendarRequiredFiltersOnLoad as requiredFiltersOnLoad, 
    wholeProductFilterConfig, 
    wholeStoreFilterConfig
} from '../../../constants/FilterConfigConstants';
import { callMarketingCalendarTableAPI, getEventApi, getPromoTypes } from '../../../store/features/marketingCalendarReducer/marketingCalendarReducer';
import PromoView from './PromoView';
import EventView from './EventView';
import { DEFAULT_DATE_FORMAT, global_labels } from '../../../constants/Constants';
import { getFiscalCalendar } from '../../../store/features/global/global';
import "../workbench/Workbench.scss";
import { resetFiltersForNextIds, setSelectedGenricFilters } from '../../../store/features/filters/filters';
import { capitalizeFirstLetter } from '../../../utils/helpers/utility_helpers';

const MarketingCalendar = () => {

    const dispatch = useDispatch();
    const navigate = useNavigate();

    const {
        global_configs,
        currency_detail
    } = useSelector((store) => {
        return store?.pricesmartPromoReducer?.global;
    });

    const {
        filtersData
    } = useSelector((store) => store?.pricesmartPromoReducer.filters);

    const [showFiltersSection, setShowFiltersSection] = useState(true);
    const [selectedTab, setSelectedTab] = useState("event-view");
    const [triggerFilterApplyPromo, setTriggerFilterApplyPromo] = useState(false);
    const [triggerFilterApplyEvent, setTriggerFilterApplyEvent] = useState(false);
    const [marketingCalendarFilterConfig, setMarketingCalendarFilterConfig] = useState(filterConfig);

    useEffect(() => {
        dispatch(setSelectedGenricFilters({
            data: true,
            from: "MARKETING_CALENDAR",
            filterId: "show_partially_overlapping_events",
        }));
        dispatch(setSelectedGenricFilters({
            data: "entire_offer_duration",
            from: "MARKETING_CALENDAR",
            filterId: "metrics_display_mode",
        }));
    }, []);

    useEffect(() => {
        const config = [];
        filterConfig.forEach((filter) => {
            let newFilter = _.cloneDeep(filter);
            if (!global_configs?.event?.use_event) {
                newFilter = {
                    ...filter,
                    groupConfig: filter.groupConfig?.filter((group) => group.filterId !== "event"),
                };
            }

            // to handle custom filter
            newFilter.groupConfig.forEach((group) => {
                if (group.filterId === "promo" || group.filterId === "event") {
                    group["extraParams"] = {
                        ...group?.extraParams,
                        show_partially_overlapping_events: filtersData?.MARKETING_CALENDAR?.show_partially_overlapping_events || false,
                    }
                }
                if (group.filterType === "custom") {
                    if (group.filterId === "show_partially_overlapping_events") {
                        group["component"] = (
                            <Checkbox
                                checked={
                                    filtersData?.MARKETING_CALENDAR?.show_partially_overlapping_events || false
                                }
                                label={group?.label}
                                onChange={(val) => {
                                    dispatch(setSelectedGenricFilters({
                                        data: val.target.checked,
                                        from: "MARKETING_CALENDAR",
                                        filterId: group?.filterId,
                                    }));
                                    dispatch(resetFiltersForNextIds({
                                        activeScreen: "MARKETING_CALENDAR",
                                        payload: ["event", "promo"],
                                    }))
                                }}
                                required={group?.isMandatory || false}
                                variant="default"
                            />
                        )
                    } else if (group.filterId === "metrics_display_mode") {
                        group["component"] = (
                            <div className="metrics_display_mode_container">
                                <p className="text-14-600">Show metrics of:&nbsp;</p>
                                <RadioButtonGroup
                                    options={group?.options}
                                    selectedOption={filtersData?.MARKETING_CALENDAR?.metrics_display_mode}
                                    isDisabled={!filtersData?.MARKETING_CALENDAR?.show_partially_overlapping_events}
                                    onChange={(_e, val) => {
                                        dispatch(setSelectedGenricFilters({
                                            data: val,
                                            from: "MARKETING_CALENDAR",
                                            filterId: group?.filterId,
                                        }));
                                    }}
                                    orientation="row"
                                    className="metrics_radio_button_group"
                                />
                            </div>
                        )
                    }
                }
            });

            config.push(newFilter);
        });
        setMarketingCalendarFilterConfig(config);
    }, [global_configs?.event?.use_event, filtersData.MARKETING_CALENDAR]);

    const allTabPanels = useMemo(() => {
        let arr = [
            <PromoView
                key={"promo-view"}
                activeTab={selectedTab}
                onFilterApply={onFilterApply}
                triggerFilterApplyPromo={triggerFilterApplyPromo}
            />
        ]
        if (global_configs?.event?.use_event) {
            arr = [
                <EventView
                    key={"event-view"}
                    activeTab={selectedTab}
                    setSelectedTab={setSelectedTab}
                    onFilterApply={onFilterApply}
                    triggerFilterApplyEvent={triggerFilterApplyEvent}
                />
                ,
                ...arr
            ]
        }
        return arr;
    }, [global_configs?.event?.use_event, triggerFilterApplyPromo, triggerFilterApplyEvent]);

    const tabConfig = useMemo(() => {
        if (global_configs?.event?.use_event) {
            setSelectedTab("event-view");
        } else {
            setSelectedTab("promo-view");
        }
        let arr = [
            {
                label: `${capitalizeFirstLetter(global_labels?.promo_primary)} View`,
                value: 'promo-view'
            }
        ]
        if (global_configs?.event?.use_event) {
            arr = [
                {
                    label: `${capitalizeFirstLetter(global_labels?.event_primary)} View`,
                    value: "event-view"
                },
                ...arr,
            ]
        }
        return arr;
    }, [global_configs?.event?.use_event]);

    useEffect(() => {
        const eventIds = new URLSearchParams(window.location.search).get("eventIds");
        if (Object.keys(filtersData?.MARKETING_CALENDAR || {}).length > 0 && eventIds) {
            setSelectedTab("event-view");
            checkSearchParamsisAvailable();
        }
    }, [window.location.href]);

    useEffect(() => {
        // Get fiscal calendar
        dispatch(
            getFiscalCalendar({
                years: [
                    moment().format("YYYY"),
                    moment().add("years", 1).format("YYYY"),
                    moment().subtract("years", 1).format("YYYY"),
                    moment().subtract("years", 2).format("YYYY"),
                ],
            })
        );

        dispatch(getPromoTypes({ screenName: "calendar_view" }));
    }, []);

    const checkSearchParamsisAvailable = () => {
        const searchParams = new URLSearchParams(window.location.search);
        const event_ids = searchParams.get("eventIds")?.split(",");
        if (event_ids) {
            const tempFiltersData = _.cloneDeep(filtersData?.MARKETING_CALENDAR || {});
            tempFiltersData["event"] = { selectedOptionsArray: event_ids };
            onFilterApply("event", false, tempFiltersData);
        } else {
            onFilterApply();
        }
    }

    function onFilterApply(callWho = "all", getLastYearData = false, immediateFilters = {}) {
        // if callwho is all, call both prmo and event API
        // if callwho is promo, call only promo API
        // if callwho is event, call only event API
        // if getLastYearData is true, get data for last year else get data for current year
        let marketingCalendarFilters = _.cloneDeep(
            filtersData.MARKETING_CALENDAR
        );
        // set all selected filters in the pacallWhoyload
        const payload = {
            show_metrics: true,
            product_hierarchies: {},
            store_hierarchies: {},
            target_currency_id: currency_detail?.currency_id,
        };

        // for tiles API, add screen_type in payload
        payload.screen_type = "calendar_view";
        // for table API, add action in payload
        payload.action = "get";

        const product_hierarchy_keys = wholeProductFilterConfig.map(item => item.filterId);
        const store_hierarchy_keys = wholeStoreFilterConfig.map(item => item.filterId);
        // pass the immediate filters to when u find that from the state u r not getting the updated filters
        // this is to avoid the issue of filters not updating in the state
        marketingCalendarFilters = _.merge(marketingCalendarFilters, immediateFilters);

        _.forEach(Object.keys(marketingCalendarFilters), (key) => {
            if (key === "dateRange") {
                payload.start_date = getLastYearData
                    ? moment(marketingCalendarFilters.dateRange?.start_date)
                        .subtract(1, "years")
                        .format(DEFAULT_DATE_FORMAT)
                    : moment(
                        marketingCalendarFilters.dateRange?.start_date
                    ).format(DEFAULT_DATE_FORMAT);
                payload.end_date = getLastYearData
                    ? moment(marketingCalendarFilters.dateRange?.end_date)
                        .subtract(1, "years")
                        .format(DEFAULT_DATE_FORMAT)
                    : moment(
                        marketingCalendarFilters.dateRange?.end_date
                    ).format(DEFAULT_DATE_FORMAT);
            } else if (product_hierarchy_keys.includes(key)) {
                payload["product_hierarchies"][key] = _.cloneDeep(
                    marketingCalendarFilters[key]?.selectedOptionsArray
                );
            } else if (store_hierarchy_keys.includes(key)) {
                payload["store_hierarchies"][key] = _.cloneDeep(
                    marketingCalendarFilters[key]?.selectedOptionsArray
                );
            } else if (key === "event") {
                payload["event_ids"] = _.cloneDeep(
                    marketingCalendarFilters[key]?.selectedOptionsArray
                );
            } else if (key === "promo") {
                payload["promo_ids"] = _.cloneDeep(
                    marketingCalendarFilters[key]?.selectedOptionsArray
                );
            } else {
                payload[key] = marketingCalendarFilters[key].hasOwnProperty("selectedOptionsArray")
                    ? _.cloneDeep(marketingCalendarFilters[key]?.selectedOptionsArray)
                    : _.cloneDeep(marketingCalendarFilters[key]);
            }
        });

        if (callWho === "all" || callWho === "promo") {
            dispatch(
                callMarketingCalendarTableAPI({
                    payload: _.cloneDeep(payload),
                    timeline: getLastYearData ? "ly" : "ty",
                })
            );
        }

        if ((callWho === "all" || callWho === "event") && global_configs?.event?.use_event) {
            dispatch(
                getEventApi({
                    payload: _.cloneDeep(payload),
                    timeline: getLastYearData ? "ly" : "ty",
                })
            )
        }
        navigate(location.pathname, { replace: true, state: {} });

    };

    return (
        <div className="screen_container">
            <ScreenBreadcrumb
                breadcrumbList={breadcrumbRoutes()?.["marketingCalendar"]}
            >
                <Button
                    iconPlacement="left"
                    onClick={() => {
                        setShowFiltersSection((prev) => !prev);
                    }}
                    size="large"
                    variant="secondary"
                >
                    {showFiltersSection ? "Hide" : "Show"} Filters
                </Button>
            </ScreenBreadcrumb>
            <FilterWrapper
                defaultOpen="product_hierarchy"
                screen="MARKETING_CALENDAR"
                callAPIonLoad={true}
                filterConfig={marketingCalendarFilterConfig}
                requiredFiltersOnLoad={requiredFiltersOnLoad}
                onFilterApply={() => {
                    checkSearchParamsisAvailable();
                    setTriggerFilterApplyEvent(Math.random());
                    setTriggerFilterApplyPromo(Math.random());
                }}
                showFiltersSection={showFiltersSection}
            />
            <div className="screen_data_container">
                <Tabs
                    onChange={(_e, value) => setSelectedTab(value)}
                    tabNames={tabConfig}
                    tabPanels={allTabPanels}
                    value={selectedTab}
                />
            </div>

        </div>
    )
}

export default MarketingCalendar