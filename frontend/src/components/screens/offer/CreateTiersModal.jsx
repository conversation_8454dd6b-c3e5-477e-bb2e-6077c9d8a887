import React, { useState, useEffect, } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Panel, Button, Input, Select, Tooltip } from "impact-ui";
import _ from "lodash";
import {
	deleteTier,
	setNoLoader,
	saveTier
} from "../../../store/features/promoReducer/promoReducer";
import {
	toastError
} from "../../../store/features/global/global";
import {
	replaceSpecial<PERSON>haracter,
	replaceSpecialCharToCharCode
} from "../../../utils/helpers/utility_helpers";
import ActiveCloseIcon from "../../../assets/imageAssets/activeCloseIcon.svg?url";
import InactiveCloseIcon from "../../../assets/imageAssets/inactiveCloseIcon.svg?url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import AddIcon from "../../../assets/imageAssets/AddIconWhite.svg?.url";
import AddIconBlue from "../../../assets/imageAssets/AddIconBlue.svg?.url";
import {
	VALUE_LABELS
} from "./OfferConstants"
import { global_labels } from "../../../constants/Constants";

const CreateTiers = props => {

	const dispatch = useDispatch();
	const {
		promoDetails = {},
		activePromoId,
		tiersData,
		validTierOfferTypes,
		bmsmOfferData,
		savedTierId
	} = useSelector((store) => store?.pricesmartPromoReducer.promo);


	const [subTierBmsmXTypes, setSubTierBmsmXTypes] = useState([]);
	const [subTierBmsmYTypes, setSubTierBmsmYTypes] = useState([]);
	const [selectedTier, setSelectedTier] = useState({});
	const [offerTypes, setOfferTypes] = useState();
	const [selectedOfferType, setSelectedOffertype] = useState(null);
	const [isTierOfferTypeOpen, setIsTierOfferTypeOpen] = useState(false);
	const [isBuyTypeOpen, setIsBuyTypeOpen] = useState(false);
	const [isGetTypeOpen, setIsGetTypeOpen] = useState(false);
	const [subTiers, setSubTiers] = useState([]);

	const [subTierXTypes, setSubTierXTypes] = useState([]);
	const [selectedSubTierXTypes, setSelectedSubTierXTypes] = useState({});
	const [subTierYTypes, setSubTierYTypes] = useState([]);
	const [selectedSubTierYTypes, setSelectedSubTierYTypes] = useState({});
	const [selectedSubTierZTypes, setSelectedSubTierZTypes] = useState({});
	const [filteredOptions, setFilteredOptions] = useState([]);

	useEffect(() => {
		screenMount();
	}, []);

	useEffect(() => {
		if (props.selectedTierVal?.tier_id) {
			setSelectedTier(_.cloneDeep(props.selectedTierVal));
			getTier(props?.selectedTierVal);
		} else {
			setSelectedTier({});
			setSelectedOffertype({});
			setSubTiers([]);
			getOfferTypes();
			getBmsmOfferTypes();
		}
	}, [props.selectedTierVal])

	const screenMount = async () => {
		setSubTierXTypes([{ label: "Units", value: "unit" }]);
		setSubTierYTypes([{ label: "Units", value: "unit" }]);
	};

	const resetTier = () => {
		//reset the selected tier and subtier 
		setSelectedTier({
			id: null,
			tier_name: "",
			tier_id: null,
		});
		setSelectedOffertype({});
		resetSubTier();
	};


	const resetSubTier = () => {
		//reset subtier values to default
		setSubTiers(
			Array.from({ length: 1 }).fill({
				offer_x_value: {
					offer_x_value: "",
					offer_x_type: "unit",
				},
				offer_y_value: {
					offer_y_value: "",
					offer_y_type: "unit",
				},
				offer_z_value: {
					offer_z_value: "",
					offer_z_type: "percent_off",
				},
			})
		);
		setSelectedSubTierXTypes({});
		setSelectedSubTierYTypes({});
		setSelectedSubTierZTypes({});
	};

	const formatBmsmTypeOptions = (offerData) => {
		let xTypeOptions = [];
		let yOptions = {};
		offerData.forEach((data) => {
			let xOption = {
				value: data.x_value_types,
				label: VALUE_LABELS[data.x_value_types],
			};
			let yTypeOptions = [];
			data?.y_value_types.forEach((yvalue) => {
				let yOption = {
					value: yvalue,
					label: VALUE_LABELS[yvalue],
				};
				yTypeOptions.push(yOption);
			});
			xTypeOptions.push(xOption);
			yOptions[data.x_value_types] = yTypeOptions;
		});

		setSubTierBmsmXTypes(xTypeOptions);
		setSubTierBmsmYTypes(yOptions);
		return {
			subTierBmsmX_types: xTypeOptions,
			subTierBmsmY_types: yOptions
		}
	}

	useEffect(() => {
		if (selectedTier?.offer_type == "bmsm" && subTierBmsmXTypes.length > 0 && subTierBmsmYTypes.length > 0) {
			getTier(props?.selectedTierVal);
		}
	}, [subTierBmsmXTypes, subTierBmsmYTypes])

	const handleDeleteTier = async (tier) => {
		const tier_id = tier.tier_id;
		dispatch(deleteTier({
			reqObj: {
				promo_id: activePromoId,
				tier_id: tier_id,
				promo_name: promoDetails?.promo_name,
				tier_name: tier.tier_name,
			},
			isCallGetTier: true
		}));
	};

	const addTier = () => {
		//reset the data populated to default
		resetTier();
	};

	const handleSaveTier = async (apply) => {
		// tier name is mandatory
		if (_.isEmpty(selectedTier?.tier_name)) {
			dispatch(toastError("Please enter a name for the tier"));
			return;
		}
		// tier type is mandatory 
		if (_.isEmpty(selectedOfferType)) {
			dispatch(toastError(`Please select an ${global_labels?.promo_alias} type`));
			return;
		}

		//validate each subtier has values entered
		for (let i = 0; i < subTiers.length; i++) {
			let subTier = subTiers[i];
			const tier_info = {
				...subTier.offer_x_value,
				...subTier.offer_y_value,
				...subTier.offer_z_value,
			};
			if (
				(!tier_info.offer_x_value && tier_info.offer_y_value) ||
				(tier_info.offer_x_value && !tier_info.offer_y_value)
			) {
				dispatch(toastError("Value missing in one for the sub tier"));
				return;
			}
			if (selectedOfferType?.value == "bxgy_percent_off") {
				if (
					(!tier_info.offer_x_value &&
						tier_info.offer_y_value &&
						tier_info.offer_z_value) ||
					(tier_info.offer_x_value &&
						!tier_info.offer_y_value &&
						tier_info.offer_z_value) ||
					(tier_info.offer_x_value &&
						tier_info.offer_y_value &&
						!tier_info.offer_z_value)
				) {
					dispatch(toastError("Value missing in one for the sub tier"));
					return;
				}
			}
		}
		if (
			_.isEmpty(selectedSubTierXTypes) ||
			_.isEmpty(selectedSubTierYTypes)
		) {
			dispatch(toastError("Subtier type missing in one for the sub tier"));
			return;
		}

		let subTierCopy = subTiers.filter((subTier) => {
			const tier_info = {
				...subTier.offer_x_value,
				...subTier.offer_y_value,
				...subTier.offer_z_value,
			};
			if (tier_info.offer_x_value && subTier.offer_y_value) return true;
			return false;
		});
		if (subTierCopy.length == 0) {
			dispatch(toastError("No data for subtiers found"));
			return;
		}

		const processedTierName = replaceSpecialCharToCharCode(
			selectedTier?.tier_name
		);

		const payload = {
			guid: sessionStorage.getItem("UNIQ_SSE_KEY"),
			promo_id: activePromoId,
			tier_id: selectedTier?.tier_id,
			tier_name: processedTierName,
			sub_tier_count: subTiers.length,
			tier_offer_type: selectedOfferType?.value,
			tier_offer_type_id: selectedOfferType?.offer_type_id,
			tier_information: [],
			has_scenario: selectedTier?.has_scenario,
			// Added the column and row id to the payload to update the tier in the correct column and row
			// not sure if this is the best way to do it, but it works for now
			col_id: props?.colId,
			row_id: props?.rowId,
			apply
		};
		subTierCopy.forEach((subTier) => {
			let x_value = {
				offer_x_value: Number(subTier.offer_x_value?.offer_x_value),
				offer_x_type: selectedSubTierXTypes?.value,
				offer_x_label: selectedSubTierXTypes?.label,
			};
			let y_value = {
				offer_y_value: Number(subTier.offer_y_value?.offer_y_value),
				offer_y_type: selectedSubTierYTypes?.value,
				offer_y_label: selectedSubTierYTypes?.label,
			};
			let z_value = {
				offer_z_value: Number(subTier.offer_z_value?.offer_z_value),
				offer_z_type: selectedSubTierZTypes?.value,
				offer_z_label: selectedSubTierZTypes?.label,
			};

			if (!z_value?.offer_z_value) {
				z_value = {
					offer_z_label: null,
					offer_z_type: null,
					offer_z_value: null,
				};
			}
			const tier_info = {
				...x_value,
				...y_value,
				...z_value,
			};
			payload.tier_information.push(tier_info);
		});

		const res = await dispatch(saveTier(payload));

		if (res) {
			if (payload?.has_scenario) {
				props.handlePanelOpen(false);
				dispatch(setNoLoader(true));
			} else {
				dispatch(setNoLoader(false));
			}
	
			if (apply) {
				props.handlePanelOpen(false);
			}
	
			dispatch(setNoLoader(false));
		}

	};

	const handleTiersRows = (action, index) => {
		if (action == "delete") {
			const newSubArr = subTiers.filter((subTier, ind) => ind != index);
			setSubTiers(newSubArr);
		} else if (action == "add") {
			const subTierArr = subTiers;
			subTierArr.push({
				offer_x_value: {
					offer_x_value: "",
					offer_x_type: selectedSubTierXTypes?.value,
				},
				offer_y_value: {
					offer_y_value: "",
					offer_y_type: selectedSubTierYTypes?.value,
				},
				offer_z_value: {
					offer_z_value: "",
					offer_z_type: selectedSubTierZTypes?.value,
				},
			});
			setSubTiers(new Array(...subTierArr));
		}
	};

	const onChangeHandlerSubTiers = (event, index, key, max) => {
		let value = event.target.value.trim();
		let subtierCopy = _.cloneDeep(subTiers);
		if (
			(selectedSubTierYTypes?.value == "unit" &&
				key == "offer_y_value") ||
			(selectedSubTierXTypes?.value == "unit" &&
				key == "offer_x_value")
		) {
			value = parseInt(value, 10);
		}
		if (/^\d+\.\d{3,}$/.test(value)) value = parseFloat(value).toFixed(2);
		if (isNaN(value)) value = "";
		if (
			(selectedSubTierYTypes?.value == "unit" &&
				key == "offer_y_value") ||
			(selectedSubTierXTypes?.value == "unit" &&
				key == "offer_x_value")
		)
			max = 20;
		else if (
			selectedSubTierYTypes?.value == "percent_off" &&
			key == "offer_y_value"
		)
			max = 100;
		if (max && value > max) value = max;

		const sub = subtierCopy.map((item, ind) => {
			if (ind == index) {
				return {
					...item,
					[key]: {
						...item[key],
						[key]: value,
					},
				};
			}
			return item;
		});
		setSubTiers(sub);
	};

	const onUpdateHandler = async (param) => {
		const selectedItems = param.selectedItems;
		const type = param.type;
		if (selectedOfferType?.value == "bmsm") {
			if (type == "x_type") {
				setSubTierYTypes(subTierBmsmYTypes[selectedItems?.value]);
				setSelectedSubTierYTypes({});
			}
		}
		if (type == "x_type") {
			setSelectedSubTierXTypes(selectedItems);
		} else if (type == "y_type") {
			setSelectedSubTierYTypes(selectedItems);
		} else if (type == "z_type") {
			setSelectedSubTierZTypes(selectedItems);
		}

		const subTier_copy = [];
		// added as a workaround to get the updated subTiers
		await setSubTiers((prev) => {
			subTier_copy.push(...prev)
			return prev
		})

		if (type == "y_type")
			subTier_copy.forEach((subTier) => {
				let value = subTier.offer_y_value.offer_y_value;
				if (
					selectedItems?.value == "percent_off" &&
					Number(value) > 100
				)
					value = 100;
				subTier.offer_y_value.offer_y_value = value;
			});

		setSubTiers(subTier_copy);
	};

	const getOfferTypes = () => {
		const offer_types = validTierOfferTypes?.map((offerType) => {
			return {
				label: offerType.display_name,
				value: offerType.offer_type,
				offer_type_id: offerType.offer_type_id,
			};
		})
		setOfferTypes(offer_types);
		return offer_types;
	}
	
	const getBmsmOfferTypes = () => {
		const { subTierBmsmX_types, subTierBmsmY_types } = formatBmsmTypeOptions(bmsmOfferData);
		setSubTierBmsmXTypes(subTierBmsmX_types);
		setSubTierBmsmYTypes(subTierBmsmY_types);
		return { subTierBmsmX_types, subTierBmsmY_types };
	}

	const getTier = async (tier) => {
		resetTier();
		const offer_types = getOfferTypes();
		const { subTierBmsmX_types, subTierBmsmY_types } = getBmsmOfferTypes();
		setOfferTypes(offer_types);
		setSelectedTier(tier);
		if (offer_types && offer_types.length > 0) {
			setSelectedOffertype(
				offer_types.find(
					(offerType) => offerType.offer_type_id == tier.offer_type_id
				),
			);
		}

		if (tier.offer_type == "bmsm") {
			setSubTierXTypes(subTierBmsmX_types);
			setSubTierYTypes([]);
			const tier_info = tier?.tier_information[0];
			const x_value_selceted = subTierBmsmX_types.find(
				(type) => tier_info?.offer_x_type == type.value
			);
			setSelectedSubTierXTypes(x_value_selceted);
			setSubTierYTypes(subTierBmsmY_types?.[x_value_selceted?.value]);
			const y_value_selceted = subTierBmsmY_types[
				x_value_selceted.value
			].find((type) => type.value == tier_info.offer_y_type);
			setSelectedSubTierYTypes(y_value_selceted);
		} else {
			setSubTierXTypes([{ label: "Units", value: "unit" }]);
			setSelectedSubTierXTypes({ label: "Units", value: "unit" });
			setSubTierYTypes([{ label: "Units", value: "unit" }]);
			setSelectedSubTierYTypes({ label: "Units", value: "unit" });
			setSelectedSubTierZTypes({ label: "Off", value: "percent_off" });
		}

		setSubTiers(
			tier?.tier_information?.map((subTier) => {
				return {
					offer_x_value: {
						offer_x_value: subTier.offer_x_value,
						offer_x_type: subTier.offer_x_type,
						offer_x_label: "Units",
					},
					offer_y_value: {
						offer_y_value: subTier.offer_y_value,
						offer_y_type: subTier.offer_y_type,
						offer_y_label: "Units",
					},
					offer_z_value: {
						offer_z_value: subTier.offer_z_value,
						offer_z_type: subTier.offer_z_type,
						offer_z_label: "Units",
					},
				};
			})
		);
	};

	const handleOfferTypeChange = (selectedItems) => {
		setSelectedOffertype(selectedItems);
		resetSubTier();
		if (selectedItems?.value == "bmsm") {
			const yTypeOptions = subTierBmsmYTypes[subTierBmsmXTypes[0]?.value];
			setSubTierXTypes(subTierBmsmXTypes);
			setSubTierYTypes(yTypeOptions);
			setSelectedSubTierYTypes(yTypeOptions[0]);
			setSelectedSubTierXTypes(subTierBmsmXTypes[0]);
		} else {
			setSubTierXTypes([
				{ label: "Units", value: "unit" },
			]);
			setSubTierYTypes([
				{ label: "Units", value: "unit" },
			]);
			setSelectedSubTierYTypes(
				{ label: "Units", value: "unit" },
			);
			setSelectedSubTierXTypes(
				{ label: "Units", value: "unit" },
			);
			setSelectedSubTierZTypes(
				{
					label: "% Off",
					value: "percent_off",
				},
			);
		}
	}

	return (
		<Panel
			anchor="right"
			open={props.isOpen}
			className="tiered-offer-panel"
			onClose={() => props.handlePanelOpen(false)}
			onPrimaryButtonClick={() => { handleSaveTier(true) }}
			onSecondaryButtonClick={() => { handleSaveTier(false) }}
			primaryButtonLabel={`Save and ${selectedTier?.has_scenario ? "Simulate" : "Apply"}`}
			secondaryButtonLabel={!selectedTier?.has_scenario ? "Save" : null}
			size="large"
			title={
				<div className="flexWithGap8">
					<Button
						iconPlacement="left"
						icon={<img src={AddIconBlue} />}
						disabled
						size="large"
						variant="secondary"
					/>
					<p>Add/edit tiers</p>
				</div>
			}
		>
			<div>
				<div className="tier-header-container">
					<p className="text-14-800">Saved tiers</p>
					<Button
						id="add-new-tier"
						onClick={() => addTier()}
						size="medium"
						variant="url"
					>
						Add new
					</Button>
				</div>
				{tiersData.length > 0 ?<div className="tier-badge-container">
					{tiersData?.map((item) => {
						return (
							<Tooltip
								title={replaceSpecialCharacter(item.tier_name)}
								placement="top"
								key={item.tier_id}
								variant="tertiary"
							>
								<div
									className={`${item.tier_id == selectedTier?.tier_id ? "active-badge-shape-div" : "inactive-badge-shape-div"} badge-shape`}
									onClick={() => getTier(item)}
							>
								<p className="text-14-500  text-ellipsis">{replaceSpecialCharacter(item.tier_name)}</p>
								{item.tier_id == selectedTier?.tier_id ?
									<img src={ActiveCloseIcon} onClick={(e) => { e.stopPropagation(); handleDeleteTier(item) }} alt="active-close-icon" /> :
										<img src={InactiveCloseIcon} onClick={(e) => { e.stopPropagation(); handleDeleteTier(item) }} alt="inactive-close-icon" />}
								</div>
							</Tooltip>

						);
					})}
				</div>
				: null}
				<div className="tier-content-input">
					<Input
						id="tier_name"
						inputProps={{}}
						label="Tier Name"
						name="tier_name"
						onChange={(e) =>
							setSelectedTier({
								...selectedTier,
								tier_name: e.target?.value,
							})
						}
						placeholder="Enter..."
						type="text"
						isRequired={true}
						value={replaceSpecialCharacter(selectedTier?.tier_name) || ""}
					/>
					<div className="marginTop-16">
						<Select
							currentOptions={
								filteredOptions?.length ? filteredOptions : offerTypes
							}
							initialOptions={
								offerTypes
							}
							label={`Tiered ${global_labels?.promo_alias} type`}
							labelOrientation="top"
							setSelectedOptions={handleOfferTypeChange}
							setCurrentOptions={setFilteredOptions}
							placeholder="Select..."
							isRequired={true}
							isWithSearch={true}
							isMulti={false}
							selectedOptions={selectedOfferType}
							isOpen={isTierOfferTypeOpen}
							setIsOpen={setIsTierOfferTypeOpen}
							isCloseWhenClickOutside={true}
						/>
					</div>
				</div>
				<div className="tier-content-input tier-content-subtier-container">
					{subTiers?.map((item, ind) => {
						return (
							<div className="subtier-content marginTop-24">
								<div className="subtier-input-field">
									<p className="text-14-500">Buy</p>
									<Input
										id="buy_value"
										inputProps={{}}
										name="buy_value"
										onChange={(e) =>
											onChangeHandlerSubTiers(
												e,
												ind,
												"offer_x_value"
											)
										}
										placeholder="Enter..."
										type="text"
										isRequired={true}
										value={item.offer_x_value?.offer_x_value}
									/>
								</div>
								{ind == 0 &&
									selectedOfferType?.value !==
									"bxgy_percent_off" &&
									selectedOfferType?.value !== "bxgy" ? (
									<Select
										currentOptions={subTierXTypes}
										initialOptions={subTierXTypes}
										setCurrentOptions={() => { }}
										placeholder="Select..."
										isRequired={true}
										selectedOptions={selectedSubTierXTypes}
										setSelectedOptions={(selectedItems) => {
											onUpdateHandler({
												selectedItems: selectedItems,
												type: "x_type",
												ind,
											});
										}}
										isWithSearch={false}
										isMulti={false}
										isOpen={isBuyTypeOpen}
										setIsOpen={setIsBuyTypeOpen}
										isCloseWhenClickOutside={true}
									/>
								) : (
									<Input
										id="buy_type"
										placeholder="value"
										disabled
										value={selectedSubTierXTypes?.label}
									/>
								)}
								<div className="subtier-input-field">
									<p className="text-14-500 ">Get</p>
									<Input
										id="get_value"
										inputProps={{}}
										name="get_value"
										onChange={(e) =>
											onChangeHandlerSubTiers(
												e,
												ind,
												"offer_y_value"
											)
										}
										placeholder="Enter..."
										type="text"
										isRequired={true}
										value={item.offer_y_value?.offer_y_value}
									/>
								</div>
								{ind == 0 &&
									selectedOfferType?.value !==
									"bxgy_percent_off" &&
									selectedOfferType?.value !== "bxgy" ? (
									<Select
										currentOptions={subTierYTypes}
										initialOptions={subTierYTypes}
										setCurrentOptions={() => { }}
										placeholder="Select..."
										isRequired={true}
										selectedOptions={selectedSubTierYTypes}
										setSelectedOptions={(selectedItems) => {
											onUpdateHandler({
												selectedItems: selectedItems,
												type: "y_type",
												ind,
											});
										}}
										isWithSearch={false}
										isMulti={false}
										isOpen={isGetTypeOpen}
										setIsOpen={setIsGetTypeOpen}
										isCloseWhenClickOutside={true}
									/>
								) : (
									<Input
										id="get_type"
										placeholder="value"
										disabled
										value={selectedSubTierYTypes?.label}
									/>
								)}
								{selectedOfferType?.value ===
									"bxgy_percent_off" && (
										<div className="subtier-input-field">
											<p className="text-14-500">At</p>
											<Input
												id="at_value"
												placeholder="Enter..."
												value={item.offer_z_value?.offer_z_value}
												disabled={_.isEmpty(selectedSubTierZTypes?.value)}
												onChange={(e) =>
													onChangeHandlerSubTiers(
														e,
														ind,
														"offer_z_value",
														100
													)
												}
											/>
											<Input
												id="at"
												disabled
												value={selectedSubTierZTypes?.label}
											/>
										</div>
									)}
								{ind != subTiers.length - 1 ? (
									<Button
										iconPlacement="left"
										icon={<img src={DeleteIcon} />}
										onClick={() =>
											handleTiersRows("delete", ind)
										}
										size="large"
										variant="secondary"
										type="destructive"
										className="delete-button"
									/>
								) : (
									<Button
										iconPlacement="left"
										icon={<img src={AddIcon} />}
										onClick={() =>
											handleTiersRows("add", ind)
										}
										size="large"
										variant="primary"
									/>
								)}
							</div>
						);
					})}
				</div>
				<div className="horizontal-divider-line marginBottom-16 marginTop-16 " />
				<p className="text-14-800">
					Summary
				</p>
				<div className="tier-summary-container marginTop-16">
					<p
						className="tier-summary-title text-14-600"
					>
						Tier ({subTiers?.length})
					</p>
					{subTiers?.map((subTier, index) => {
						return (
							<p className="tier-summary-content" key={"subtier-"+index}>
								{`Buy ${subTier?.offer_x_value?.offer_x_value} 
               					 ${selectedSubTierXTypes?.label || ""} Get 
               					 ${subTier?.offer_y_value?.offer_y_value} 
               					 ${selectedSubTierYTypes?.label || ""}
                				 ${selectedOfferType?.value === "bxgy_percent_off" ? "At " + subTier?.offer_z_value?.offer_z_value + " " + selectedSubTierZTypes?.label : ""}`}
							</p>
						);
					})}
				</div>
			</div>
		</Panel>
	)
}

export default CreateTiers;