import { containerStore } from "../../../store";
import { setSelectedWorkbenchOffers, setIsPromoEdit } from "../../../store/features/workbenchReducer/workbenchReducer";
import { setScenarioUpdatedData } from "../../../store/features/promoReducer/promoReducer";

export const detailedSimulationMetricFormatter = (simulationData) => {
	const tempData = [];
	simulationData.forEach((data) => {
		let rowData = {
			hierarchy_name: data.product_name
				? data.product_name
				: data.promo_name,
			id: data.product_id ? data.product_id : data.promo_id
		};
		data.scenario_data.forEach((scenario) => {
			rowData[`scenario_${scenario.scenario_order_id}_name`] = scenario.scenario_name || "Scenario " + scenario.scenario_order_id;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_margin`] = scenario.margin;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_revenue`] = scenario.revenue;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_sales_units`] = scenario.sales_units;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_incremental_margin`] = scenario.incremental_margin;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_incremental_revenue`] = scenario.incremental_revenue;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_incremental_sales_units`] = scenario.incremental_sales_units;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_finalized_st_percent`] = scenario.finalized_st_percent;
			rowData[`${scenario.scenario_type}_${scenario.scenario_order_id}_total_inventory`] = scenario.total_inventory;
		});
		tempData.push(rowData);
	});
    
	return tempData;
};

export const handleOfferValidateAndEdit = async (data, navigate) => {
	containerStore.dispatch(setSelectedWorkbenchOffers([data?.[0]]));
	containerStore.dispatch(setIsPromoEdit(true));

}

export const handleSimulationTableUpdate = (data) => {
	containerStore.dispatch(setScenarioUpdatedData(data));
}

export const getOfferTypeOptions = (data) => {
	const state = containerStore.getState();
	const { validOffers } = state.pricesmartPromoReducer.promo;
	const promoID = Object.keys(validOffers)[0];

	if(promoID) {
		const offerTypeOptions = validOffers[promoID].map((offer) => {
			return {
				label: offer.offer_display_name,
				value: offer.offer_type_id
			}
		})
		return offerTypeOptions;
	}
	return [];
}
