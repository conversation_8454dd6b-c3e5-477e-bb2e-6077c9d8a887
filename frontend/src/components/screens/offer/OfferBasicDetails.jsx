import React, { useState, useEffect, useMemo, useCallback } from "react";
import "react-dates/initialize";
import { useSelector, useDispatch } from "react-redux";
import moment from "moment";
import _ from "lodash";
import {
    Button,
    Input,
    Select,
    DateRangePicker,
    Prompt,
    ButtonGroup,
} from "impact-ui";

import promotionImage from "../../../assets/imageAssets/promotion.svg?url";
import placeholderImage from "../../../assets/imageAssets/placeholder.svg?url";
import {
    setIsEditedFlag,
    getStep0Basics,
    createPromoStep0,
    getEvents,
    createPlaceholderPromo,
    editAtStep0,
    calculateGrossMarginAPI,
    editPlaceholderPromo,
    getPromoEventDetails,
} from "../../../store/features/promoReducer/promoReducer";
import { capitalizeFirstLetter, replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";
import {
    DEFAULT_DATE_FORMAT,
    global_labels,
} from "../../../constants/Constants";
import { useNavigate } from "react-router-dom-v5-compat";
import { toastError } from "../../../store/features/global/global";

const OfferBasicDetails = (props) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const { global_configs } = useSelector(
        (store) => store?.pricesmartPromoReducer?.global
    );

    const {
        promoDetails = {},
        activePromoId,
        events,
        isEdited,
        promoEventDetails,
        offerModeState,
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const [isPromo, setIsPromo] = useState(true);
    const [basicDetails, setBasicDetails] = useState({});
    const [selectedEvent, setSelectedEvent] = useState([]);
    const [isOpen, setIsOpen] = useState(false);
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [eventOptions, setEventOptions] = useState([]);
    const [isEventChangeConfirmation, setIsEventChangeConfirmation] = useState(
        false
    );
    const [
        isDateModificationDisabled,
        setIsDateModificationDisabled,
    ] = useState(false);
    const [minDate, setMinDate] = useState(moment());
    const [maxDate, setMaxDate] = useState(null);
    const [currentOptions, setCurrentOptions] = useState([]);

    useEffect(() => {
        dispatch(setIsEditedFlag(false));
        return () => {
            setBasicDetails({});
            setCurrentOptions([]);
        };
    }, []);

    useEffect(() => {
        if (events?.length) {
            const tempOptions = events.map((event) => {
                return {
                    label: replaceSpecialCharacter(event.event_name),
                    value: event.event_id,
                    start_date: event.start_date,
                    end_date: event.end_date,
                };
            });
            setEventOptions(tempOptions);
            setCurrentOptions(tempOptions);
            if (promoDetails?.event_id) {
                const tempSelectedEvent = tempOptions.find(
                    (event) => event.value === promoDetails.event_id
                );
                setSelectedEvent(tempSelectedEvent);
            }
        }
    }, [events]);

    useEffect(() => {
        const fetchData = async () => {
            if (activePromoId) {
                await dispatch(getStep0Basics(activePromoId));
            }
            if (global_configs?.event?.use_event) {
                await dispatch(getEvents({ is_locked: false }));
            }
        };
        fetchData();
    }, [activePromoId]);

    useEffect(() => {
        if (_.isEmpty(promoDetails)) return;
        const {
            promo_id,
            promo_name = "",
            start_date,
            end_date,
            metrics = {},
            status,
            event_id,
        } = promoDetails;
        const tempData = {
            promo_id: promo_id,
            promo_name: replaceSpecialCharacter(promo_name),
            start_date: start_date ? moment(start_date) : null,
            end_date: end_date ? moment(end_date) : null,
            metrics: _.cloneDeep(metrics),
            event_id: event_id,
        };
        setBasicDetails(_.cloneDeep(tempData));
        setStartDate(start_date ? moment(start_date) : null);
        setEndDate(end_date ? moment(end_date) : null);
        if (event_id) {
            dispatch(
                getPromoEventDetails({
                    event_id: event_id,
                })
            );
        }
        if (status && status.toLowerCase() === "placeholder") {
            setIsPromo(false);
        }
    }, [promoDetails]);

    useEffect(() => {
        // Skip if no promo event details
        if (_.isEmpty(promoEventDetails)) return;

        const { date_restriction, start_date, end_date } = promoEventDetails;
        const { sameAsEvent, promotionStartDay, promotionEndDay } =
            date_restriction || {};

        let tempStartDate = selectedEvent?.start_date;
        let tempEndDate = selectedEvent?.end_date;

        // Handle event-linked dates
        if (String(sameAsEvent).toLowerCase() === "yes") {
            setIsDateModificationDisabled(true);
            setMinDate(moment(start_date));
            setMaxDate(moment(end_date));
            tempStartDate = start_date;
            tempEndDate = end_date;
        } else {
            // Handle custom promotion dates
            const hasCustomDateRange = !!promotionStartDay && !!promotionEndDay;
            setIsDateModificationDisabled(hasCustomDateRange);

            if (hasCustomDateRange) {
                setMinDate(moment(promotionStartDay));
                setMaxDate(moment(promotionEndDay));
                tempStartDate = promotionStartDay;
                tempEndDate = promotionEndDay;
            } else {
                tempStartDate = promotionStartDay || tempStartDate;
                tempEndDate = promotionEndDay || tempEndDate;
                setMinDate(moment(tempStartDate));
                setMaxDate(moment(tempEndDate));
            }
        }
        if (promoDetails?.event_id !== selectedEvent?.value) {
            !_.isEmpty(tempStartDate) && setStartDate(moment(tempStartDate));
            !_.isEmpty(tempEndDate) && setEndDate(moment(tempEndDate));
        }

        const updatedBasicDetails = {
            ..._.cloneDeep(basicDetails),
            start_date: tempStartDate,
            end_date: tempEndDate,
        };

        setBasicDetails(updatedBasicDetails);
        dispatch(setIsEditedFlag(true));
    }, [promoEventDetails]);

    const switchPromoPlaceholder = () => {
        setIsPromo(!isPromo);
        dispatch(setIsEditedFlag(true));
    };

    const primaryButtonLabel = useMemo(() => {
        if (!isPromo) {
            if (basicDetails?.promo_id) {
                return "Update placeholder";
            }
            return "Create placeholder";
        } else if (basicDetails?.promo_id) {
            if (props?.allowPromoEdit) {
                return `Edit ${global_labels?.promo_alias}`;
            }
            return `View ${global_labels?.promo_alias}`;
        }

        return `Create ${global_labels?.promo_alias}`;
    }, [isPromo, basicDetails?.promo_id, props?.allowPromoEdit]);

    const promoNameChangeHandler = (e) => {
        if (!props?.allowPromoEdit) {
            return;
        }
        const { value } = e.target;
        let tempBasicDetails = basicDetails;
        tempBasicDetails["promo_name"] = value;
        setBasicDetails(_.cloneDeep(tempBasicDetails));
        dispatch(setIsEditedFlag(true));
        if (!isPromo) {
            calculateGrossMargin(tempBasicDetails);
        }
    };

    const eventNameChangeHandler = (selectedOptions) => {
        setSelectedEvent(selectedOptions);

        const tempData = {
            event_id: selectedOptions?.value,
            // start_date: selectedOptions?.start_date,
            // end_date: selectedOptions?.end_date,
        };
        setBasicDetails((prev) => {
            return _.cloneDeep({
                ...prev,
                ...tempData,
            });
        });
        dispatch(setIsEditedFlag(true));
        if (!isPromo) {
            calculateGrossMargin(tempData);
        }
        if (selectedOptions?.value) {
            dispatch(
                getPromoEventDetails({
                    event_id: selectedOptions?.value,
                })
            );
        }
    };

    const promoMetricsChangeHandler = (e, key) => {
        if (!props?.allowPromoEdit) {
            return;
        }
        const { value } = e.target;
        const tempData = {
            ...basicDetails,
        };
        tempData["metrics"] = {
            ...(basicDetails?.metrics || {}),
            [key]: value,
        };
        setBasicDetails(_.cloneDeep(tempData));
        dispatch(setIsEditedFlag(true));
        if (!isPromo) {
            calculateGrossMargin(tempData);
        }
    };

    const checkAllMetrics = (data) => {
        const { revenue_target = null, inventory = null, discount = null } =
            data?.metrics || {};

        return !!(
            revenue_target &&
            revenue_target > 0 &&
            inventory &&
            inventory > 0 &&
            discount &&
            discount > 0 &&
            discount < 100 &&
            data.start_date &&
            data.end_date
        );
    };

    const calculateGrossMargin = useCallback(
        _.debounce(async (currentValues) => {
            try {
                dispatch(setIsEditedFlag(true));
                const isValidMetrics = checkAllMetrics(currentValues);
                if (
                    isValidMetrics &&
                    currentValues.start_date &&
                    currentValues.end_date
                ) {
                    console.log(
                        "fetchGrossMarginTargetIfRequired",
                        currentValues
                    );
                    currentValues = {
                        ...currentValues,
                        start_date: moment(currentValues?.start_date).format(
                            DEFAULT_DATE_FORMAT
                        ),
                        end_date: moment(currentValues?.end_date).format(
                            DEFAULT_DATE_FORMAT
                        ),
                    };
                    delete currentValues.metrics?.gross_margin_target;
                    const grossMargin = await dispatch(
                        calculateGrossMarginAPI(currentValues)
                    );
                    setBasicDetails((prevState) => ({
                        ...prevState,
                        metrics: {
                            ...prevState.metrics,
                            gross_margin_target: parseFloat(
                                grossMargin?.gross_margin
                            ).toFixed(2),
                        },
                    }));
                }
            } catch (error) {
                console.log("calculateGrossMargin error", error);
            }
        }, 300),
        []
    );

    const checkIfIsEdited = () => {
        const start_date = moment(startDate).format("MM/DD/YYYY");
        const end_date = moment(endDate).format("MM/DD/YYYY");
        return !!(
            promoDetails?.promo_name !== basicDetails?.promo_name ||
            (promoDetails?.event_id !== selectedEvent?.value &&
                global_configs?.event?.use_event) ||
            promoDetails?.start_date !== start_date ||
            promoDetails?.end_date !== end_date ||
            !_.isEqual(promoDetails?.metrics, basicDetails?.metrics)
        );
    };

    const createOffer = async () => {
        const isEdited = checkIfIsEdited();
        if (!isEdited) {
            if (promoDetails?.step_count >= 0) {
                props.setIsBasicDetails(false);
                props.handleStep(0);
            }
            return;
        }
        //call create offer api
        const payload = {
            ...basicDetails,
            start_date: moment(startDate).format(DEFAULT_DATE_FORMAT),
            end_date: moment(endDate).format(DEFAULT_DATE_FORMAT),
            event_id: selectedEvent?.value,
        };
        if (!isPromo) {
            let res = false;
            if (basicDetails?.promo_id) {
                res = await dispatch(editPlaceholderPromo(payload));
            } else {
                res = await dispatch(createPlaceholderPromo(payload));
            }
            if (res) {
                navigate("/pricesmart-promo/workbench");
            }
        } else {
            delete payload.metrics;
            let res;
            if (basicDetails?.promo_id) {
                payload.event_id = selectedEvent?.value;
                res = await dispatch(editAtStep0(payload));
            } else res = await dispatch(createPromoStep0(payload));

            if (res) {
                props.setIsBasicDetails(false);
                props.handleStep(0);
            }
        }
    };

    const handleValidationBeforeCreate = () => {
        const label = primaryButtonLabel;
        if (label == `View ${global_labels?.promo_alias}`) {
            props.setIsBasicDetails(false);
            props.handleStep(0);
            return;
        }
        const { promo_name, event_id } = basicDetails;
        const { date_restriction } = promoEventDetails;
        const { maxPromotionDays, minPromotionDays } = date_restriction || {};
        if (!promo_name || (promo_name && promo_name?.trim().length === 0)) {
            dispatch(toastError(`Please enter ${global_labels?.promo_standard} name`));
            return;
        }
        if (!event_id && global_configs?.event?.use_event) {
            dispatch(toastError(`Please select ${global_labels?.event_primary}`));
            return;
        }
        if (!startDate || !endDate) {
            dispatch(toastError("Please select date range"));
            return;
        }
        // To include both the start and end dates in calculation add 1 to the result
        let promodays = endDate.diff(startDate, "days") + 1;

        if (minPromotionDays && promodays < minPromotionDays) {
            dispatch(
                toastError(`Please select minimum of ${minPromotionDays} days`)
            );
            return;
        }

        if (maxPromotionDays && promodays > maxPromotionDays) {
            dispatch(
                toastError(`Please select maximum of ${maxPromotionDays} days`)
            );
            return;
        }

        if (promoDetails?.promo_id) {
            // add any validation in case of edits
            if (
                promoDetails?.event_id !== selectedEvent?.value &&
                global_configs?.event?.use_event
            ) {
                setIsEventChangeConfirmation(true);
            } else {
                createOffer();
            }
        } else {
            createOffer();
        }
    };

    const handleClear = () => {
        setBasicDetails(
            _.cloneDeep({
                promo_id: basicDetails?.promo_id,
                promo_name: "",
                event_id: null,
            })
        );
        setStartDate(null);
        setEndDate(null);
        setSelectedEvent([]);
    };

    const startDateHandler = (date) => {
        const tempData = {
            ...basicDetails,
            start_date: date,
        };
        setBasicDetails(_.cloneDeep(tempData));
        setStartDate(date);
        dispatch(setIsEditedFlag(true));
    };

    const endDateHandler = (date) => {
        const tempData = {
            ...basicDetails,
            end_date: date,
        };
        setBasicDetails(_.cloneDeep(tempData));
        setEndDate(date);
        dispatch(setIsEditedFlag(true));
    };

    const isSwitchButtonDisabled = () => {
        if (!props.allowPromoEdit) {
            return true;
        }
        if (isPromo && basicDetails.promo_id) {
            return true;
        }

        return false;
    };

    const isCreateOfferButtonDisabled = useMemo(() => {
        const label = primaryButtonLabel;
        if (offerModeState === "view" && label != `View ${global_labels?.promo_alias}`) {
            return true;
        }
        if (!isPromo) {
            return !isEdited;
        }
        return false;
    }, [isPromo, basicDetails, offerModeState]);

    const isDateFieldDisabled = () => {
        const { date_restriction } = promoEventDetails;
        const { sameAsEvent = "no" } = date_restriction || {};
        return (
            (!props.allowPromoEdit) ||
            (_.isEmpty(selectedEvent) && global_configs?.event?.use_event) ||
            (String(sameAsEvent).toLowerCase() === "yes")
        );
    };

    return (
        <div className="margin-20">
            <div className="offer-mode-container">
                <ButtonGroup
                    selectedOption={props.offerMode || "edit"}
                    onChange={(e, value) =>
                        props.handleOfferModeChange(e, value)
                    }
                    options={props.modeOptions}
                />
            </div>

            <div className="step-0-container">
                <div className="basic-details content_container_border">
                    <div className="basic-details-container">
                        <p className="text-16-800">Basic details</p>
                        <div className="basic-details-content">
                            <Input
                                id="offer_name"
                                inputProps={{}}
                                label={`${capitalizeFirstLetter(global_labels?.promo_standard)} name`}
                                name="promo_name"
                                onChange={(e) => promoNameChangeHandler(e)}
                                placeholder="Please enter name"
                                type="text"
                                isRequired={true}
                                value={basicDetails?.promo_name}
                                disabled={!props.allowPromoEdit}
                            />
                            {global_configs?.event?.use_event && (
                                <Select
                                    currentOptions={currentOptions}
                                    initialOptions={eventOptions}
                                    label={`${capitalizeFirstLetter(global_labels?.event_primary)} name`}
                                    labelOrientation="top"
                                    setSelectedOptions={eventNameChangeHandler}
                                    setCurrentOptions={setCurrentOptions}
                                    placeholder={`Select ${global_labels?.event_primary}`}
                                    isRequired={true}
                                    isWithSearch={true}
                                    isMulti={false}
                                    selectedOptions={selectedEvent}
                                    isOpen={isOpen}
                                    setIsOpen={setIsOpen}
                                    isCloseWhenClickOutside={true}
                                    isDisabled={!props.allowPromoEdit}
                                />
                            )}
                            <DateRangePicker
                                label={"Date Range"}
                                isRequired={true}
                                showRangeSelector={false}
                                startDate={startDate}
                                setStartDate={startDateHandler}
                                endDate={endDate}
                                setEndDate={endDateHandler}
                                labelOrientation="top"
                                startDateInputProps={{
                                    label: "StartDate",
                                    name: "start_date",
                                }}
                                endDateInputProps={{
                                    label: "EndDate",
                                    name: "end_date",
                                }}
                                minDate={minDate || null}
                                maxDate={maxDate || null}
                                isOutsideRange={(date) => {
                                    return (
                                        !(
                                            moment(date).isSameOrAfter(
                                                minDate,
                                                "day"
                                            ) &&
                                            moment(date).isSameOrBefore(
                                                maxDate,
                                                "day"
                                            ) &&
                                            global_configs?.event?.use_event
                                        ) ||
                                        (!global_configs?.event?.use_event &&
                                            moment(date).isBefore(moment()))
                                    );
                                }}
                                isDisabled={isDateFieldDisabled()}
                                displayFormat="MM-DD-YYYY"
                            />
                        </div>
                        {!isPromo ? (
                            <div>
                                <div className="basic-details-content">
                                    <Input
                                        id="offer_name"
                                        inputProps={{}}
                                        label="Expected revenue $"
                                        name="revenue_target"
                                        onChange={(e) =>
                                            promoMetricsChangeHandler(
                                                e,
                                                "revenue_target"
                                            )
                                        }
                                        placeholder="Please enter revenue"
                                        type="number"
                                        isRequired={true}
                                        value={
                                            basicDetails?.metrics
                                                ?.revenue_target
                                        }
                                        disabled={!props.allowPromoEdit}
                                    />
                                    <Input
                                        id="offer_name"
                                        inputProps={{}}
                                        label="Expected inventory"
                                        name="inventory"
                                        onChange={(e) =>
                                            promoMetricsChangeHandler(
                                                e,
                                                "inventory"
                                            )
                                        }
                                        placeholder="Please enter inventory"
                                        type="number"
                                        isRequired={true}
                                        value={basicDetails?.metrics?.inventory}
                                        disabled={!props.allowPromoEdit}
                                    />
                                    <Input
                                        id="offer_name"
                                        inputProps={{}}
                                        label="Expected discount %"
                                        name="discount"
                                        onChange={(e) =>
                                            promoMetricsChangeHandler(
                                                e,
                                                "discount"
                                            )
                                        }
                                        placeholder="Please enter discount"
                                        type="number"
                                        isRequired={true}
                                        value={basicDetails?.metrics?.discount}
                                        disabled={!props.allowPromoEdit}
                                    />
                                    <Input
                                        id="offer_name"
                                        inputProps={{}}
                                        label="Expected gross margin $"
                                        name="gross_margin_target"
                                        type="number"
                                        disabled={true}
                                        value={
                                            basicDetails?.metrics
                                                ?.gross_margin_target
                                        }
                                    />
                                </div>
                            </div>
                        ) : null}
                        <div className="basic-details-button-container">
                            <Button
                                onClick={() => handleClear()}
                                size="large"
                                variant="secondary"
                                disabled={!props.allowPromoEdit}
                            >
                                Clear
                            </Button>
                            <Button
                                onClick={() => handleValidationBeforeCreate()}
                                size="large"
                                variant="primary"
                                disabled={isCreateOfferButtonDisabled}
                            >
                                {primaryButtonLabel}
                            </Button>
                        </div>
                    </div>
                    {isPromo ? (
                        <img
                            className="offer-image-container"
                            src={promotionImage}
                            alt={`${capitalizeFirstLetter(global_labels?.promo_standard)}`}
                        />
                    ) : (
                        <img
                            className="offer-image-container"
                            src={placeholderImage}
                            alt="Placeholder"
                        />
                    )}
                </div>
                <div className="info-container content_container_border">
                    <div className="info-container-details">
                        <p className="text-16-800">
                            {isPromo
                                ? "Create placeholder"
                                : `Create ${global_labels?.promo_standard}`}
                        </p>
                        <p className="secondaryText-14-500 marginTop-24 marginBottom-24">
                            {isPromo
                                ? "Not sure about the placeholder creation? No problem! You can simply create a placeholder now and use it later."
                                : `Not sure about the ${global_labels?.promo_standard} creation? No problem! You can simply create a ${capitalizeFirstLetter(global_labels?.promo_standard)} now and use it later.`}
                        </p>
                        <Button
                            onClick={() => switchPromoPlaceholder()}
                            size="large"
                            variant="secondary"
                            disabled={isSwitchButtonDisabled()}
                        >
                            {isPromo
                                ? "Switch to placeholder"
                                : `Switch to ${global_labels?.promo_standard}`}
                        </Button>
                    </div>
                    {isPromo ? (
                        <img
                            className="offer-image-container"
                            src={placeholderImage}
                            alt="Placeholder"
                        />
                    ) : (
                        <img
                            className="offer-image-container"
                            src={promotionImage}
                            alt={`${capitalizeFirstLetter(global_labels?.promo_standard)}`}
                        />
                    )}
                </div>
                <Prompt
                    handleClose={() => {
                        setIsEventChangeConfirmation(false);
                    }}
                    onPrimaryButtonClick={() => {
                        createOffer();
                        setIsEventChangeConfirmation(false);
                    }}
                    onSecondaryButtonClick={() => {
                        setIsEventChangeConfirmation(false);
                    }}
                    primaryButtonLabel="Proceed"
                    secondaryButtonLabel="Cancel"
                    title={`${global_labels.event_primary} Change Warning?`} 
                    variant="warning"
                    isOpen={isEventChangeConfirmation}
                >
                    <div className="event-change-model">
                        Changing the {global_labels.event_primary}{" "}
                        from “
                        <span className="bold-text-800">
                            {
                                eventOptions.find(
                                    (event) =>
                                        event.value === promoDetails?.event_id
                                )?.label
                            }
                        </span>
                        ” to “
                        <span className="bold-text-800">
                            {selectedEvent?.label}
                        </span>
                        ” will reset the {capitalizeFirstLetter(global_labels?.promo_plural)} Product, Store, and Customer
                        settings to match the new{" "}
                        {global_labels.event_primary}'s criteria.
                        <br />
                        Do you want to continue?
                    </div>
                </Prompt>
            </div>
        </div>
    );
};

export default OfferBasicDetails;
