import React, {
    useState,
    useEffect,
} from "react";
import { useSelector, useDispatch } from "react-redux";

import _ from "lodash";
import {
    Button,
    Stepper,
    ButtonGroup
} from "impact-ui";

import {
    getStep0Basics,
} from "../../../store/features/promoReducer/promoReducer";
import { capitalizeFirstLetter, replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";
import ProductStore from "./productStoreDetails";
import DiscountRules from "./DiscountRules";
import OfferSimulator from "./OfferSimulator";
import ExecutionMetadata from "./ExecutionMetadata";
import { global_labels } from "../../../constants/Constants";

const OfferDetails = props => {
    const dispatch = useDispatch();

    const editBasicDetailsHandler = () => {
        props.setIsBasicDetails(true);
    }

    const {
        promoDetails = {},
        maxStepCount,
        activePromoId,
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);
    
    const {
        handleStep,
        activeStep,    
    } = props;

    useEffect(async () => {
        if (activePromoId) {
            dispatch(getStep0Basics(activePromoId))
        }
    }, [activePromoId]);

    const handleStepper = (step) => {
        if (promoDetails?.step_count < step) {
            return;
        }
        handleStep(step);
    }

    return (
        <div className="offer_details_container">
            <div className="offer-basic-details-container flex24">
                <div className="offer-basic-details">
                    <p className="text-14-800">Basic details</p>
                    <div className="badge-shape-div secondaryText-14-500">
                        {replaceSpecialCharacter(promoDetails?.promo_name)}
                </div>
                <div className="badge-shape-div secondaryText-14-500">
                    {promoDetails?.start_date} - {promoDetails?.end_date}
                </div>
                <div className="badge-shape-div secondaryText-14-500">
                    {promoDetails?.promo_days} Days
                </div>
                <Button
                    className=""
                    onClick={() => { editBasicDetailsHandler() }}
                    size="small"
                    variant="url"
                >
                    Edit promotion
                </Button>
                </div>
                <div>
                <ButtonGroup
                    selectedOption={props.offerMode || "edit"}
                    onChange={(e, value) => props.handleOfferModeChange(e, value)}
                    options={props.modeOptions}
                />
                </div>
            </div>
            <div className="offer-stepper-container">
                <Stepper
                    activeStep={activeStep}
                    steps={[
                        {
                            description: '',
                            label: 'Products & stores'
                        },
                        {
                            description: '',
                            label: 'Discount rules'
                        },
                        {
                            description: '',
                            label: `${capitalizeFirstLetter(global_labels?.promo_alias)} simulator`
                        },
                        {
                            description: '',
                            label: 'Execution metadata'
                        },
                    ]}
                    handleStep={handleStepper}
                />
            </div>
            <ProductStore
                activeStep={activeStep}
                stepNumber={0}
                setActiveStep={handleStep}
                allowPromoEdit={props.allowPromoEdit}
            />
            <DiscountRules
                activeStep={activeStep}
                stepNumber={1}
                setActiveStep={handleStep}
                allowPromoEdit={props.allowPromoEdit}
            />
            <OfferSimulator
                activeStep={activeStep}
                stepNumber={2}
                setActiveStep={handleStep}
                allowPromoEdit={props.allowPromoEdit}
            />
            <ExecutionMetadata
		    	activeStep={activeStep}
		    	stepNumber={3}
		    	setActiveStep={handleStep}
                allowPromoEdit={props.allowPromoEdit}
			/>
        </div>)
}

export default OfferDetails;