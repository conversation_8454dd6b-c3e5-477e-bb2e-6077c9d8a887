import React, { useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import { Button, Select, Panel, TextArea, Input } from "impact-ui";
import { toastError } from "../../../store/features/global/global";
import {
	getOverrideReason,
	getOverrideForecast,
	submitOverrideForecast,
} from "../../../store/features/promoReducer/promoReducer";
import {
	toCurrencyByCurrencyIdWithDecimalFormatted,
	toPercentage,
	toUnit,
} from "../../../utils/helpers/formatter";
import { PROMO_SIMULATOR } from "./OfferConstants";
import AddIconBlue from "../../../assets/imageAssets/AddIconBlue.svg?.url";
import InfoOutlinedIcon from "../../../assets/imageAssets/infoIcon.svg?.url";

const PromoOverrideForecast = (props) => {
	const { currency = {} } = props;
	const dispatch = useDispatch();
	const {
		activePromoId,
		fromStackingView,
		overideReasons,
		overiddenForecastData,
	} = useSelector((store) => store?.pricesmartPromoReducer.promo);
	const [salesUnits, setSalesUnits] = useState("");
	const [baselineUnits, setBaselineUnits] = useState("");
	const [reason, setReason] = useState({});
	const [reasonOptions, setReasonOptions] = useState([]);
	const [distributionMethod, setDistributionMethod] = useState("multiplier");
	const [comment, setComment] = useState(null);
	const [overriddenData, setOverriddenData] = useState(null);
	const [isReasonOpen, setIsReasonOpen] = useState(false);

	useEffect(() => {
		//call reasons api and set the reasonoption
		screenMount();
	}, []);

	useEffect(() => {
		setReasonOptions(overideReasons);
	}, [overideReasons]);

	useEffect(() => {
		if (overiddenForecastData) {
			setOverriddenData(_.cloneDeep(overiddenForecastData));
		}
	}, [overiddenForecastData]);

	const screenMount = async () => {
		dispatch(getOverrideReason());
	};

	const handleSalesChange = (e) => {
		let regex = /^[1-9]\d*$/;
		if (regex.test(e.target.value)) {
			setSalesUnits(e.target.value);
		} else if (_.isEmpty(e.target.value)) {
			setSalesUnits(e.target.value);
		} else {
			setSalesUnits(salesUnits);
			dispatch(toastError("Please enter a positive integer"));
		}
	};

	const handleBaselineChange = (e) => {
		let regex = /^[1-9]\d*$/;
		if (e.target.value && regex.test(e.target.value)) {
			setBaselineUnits(e.target.value);
		} else if (_.isEmpty(e.target.value)) {
			setBaselineUnits(e.target.value);
		} else {
			setBaselineUnits(baselineUnits);
			dispatch(toastError("Please enter a positive integer"));
		}
	};

	const updateReason = useCallback((selectedItems) => {
		setReason(selectedItems);
	});

	const overrideCommentHandler = (e) => {
		setComment(e.target.value);
	};

	const onApply = async () => {
		const { scenario_id, original, stacked_original } = props.scenarioData;
		if (!salesUnits && !baselineUnits) {
			dispatch(toastError("Please enter atleast one override value"));
			return;
		}
		if (
			(!_.isEmpty(salesUnits) && salesUnits <= 0) ||
			(!_.isEmpty(baselineUnits) && baselineUnits <= 0)
		) {
			dispatch(toastError("Please enter values greater than 0"));
			return;
		}
		if (
			salesUnits &&
			baselineUnits &&
			parseInt(baselineUnits, 10) > parseInt(salesUnits, 10)
		) {
			dispatch(toastError("Please enter baseline units less than sales"));
			return;
		}
		const payload = {
			promo_id: activePromoId,
			scenario_id: scenario_id,
			old_sales_units: fromStackingView
				? stacked_original?.sales_units
				: original?.sales_units,
			old_baseline_sales_units: fromStackingView
				? stacked_original?.baseline_sales_units
				: original?.baseline_sales_units,
			new_sales_units: parseInt(salesUnits),
			new_baseline_sales_units: parseInt(baselineUnits),
			override_method: distributionMethod,
			reason: reason?.[0]?.value || null,
			comment: comment,
			from_stacking_view: fromStackingView,
		};
		dispatch(getOverrideForecast(payload));
	};

	const onSubmit = async () => {
		const payload = _.cloneDeep(overriddenData?.payload);
		payload.target_currency_id = currency?.id || null;

		if(!props.allowPromoEdit){
			closeModal();
			return
		}

		if(!(payload?.new_sales_units || payload?.new_baseline_sales_units)){
			dispatch(toastError("Please enter atleast one override value"));
			return;
		}
		dispatch(
			submitOverrideForecast({
				...payload,
				from_stacking_view: fromStackingView,
			})
		);

		closeModal();
	};

	const closeModal = () => {
		setSalesUnits(null);
		setBaselineUnits(null);
		setReason({});
		setComment(null);
		setDistributionMethod("multiplier");
		setOverriddenData(null);

		props.closeOverrideModal();
	};

	const formatCurrency = (value) => {
		return toCurrencyByCurrencyIdWithDecimalFormatted({
			value,
			...(currency || {}),
		});
	}

	return (
		<Panel
			anchor="right"
			open={props.showModal}
			onClose={() => closeModal()}
			className="promo-override-dialog-container"
			onPrimaryButtonClick={() => {
				onSubmit();
			}}
			onSecondaryButtonClick={() => {
				closeModal();
			}}
			primaryButtonLabel={`Submit`}
			secondaryButtonLabel={"Cancel"}
			size="large"
			title={
				<div className="offer-flex-container">
					<Button
						iconPlacement="left"
						icon={<img src={AddIconBlue} />}
						size="large"
						variant="secondary"
						disabled={!props.allowPromoEdit || false}
					/>
					<p>Edit scenario</p>
				</div>
			}
		>
			<p className="text-16-600 marginTop-24 marginBottom-24">Your scenario</p>
			<div className="override-data-field-container">
				<div className="override-data-field">
					<Input
						name="overidden_sales"
						value={salesUnits}
						onChange={handleSalesChange}
						className="text-input"
						id="override_sales"
						placeholder="Enter..."
						label="Sales Units"
						type="text"
					/>
					<div className="info-description flexWithCenterAlign">
						<img src={InfoOutlinedIcon} alt={"Info"} />
						<p className="secondaryText-12-400">
							{`Current total sales units is ${toUnit({
								value: fromStackingView
									? props?.scenarioData?.stacked_original?.sales_units
									: props?.scenarioData?.original?.sales_units,
							})}`}
						</p>
					</div>
				</div>
				<div className="override-data-field">
					<Input
						name="overidden_sales"
						value={baselineUnits}
						onChange={handleBaselineChange}
						className="text-input"
						id="override_baseline"
						placeholder="Enter..."
						label="Baseline"
						inputProps={{}}
						type="text"
					/>
					<div className="info-description flexWithCenterAlign">
						<img src={InfoOutlinedIcon} alt={"Info"} />
						<p className="secondaryText-12-400">
							{`Current baseline units is ${toUnit({
								value: fromStackingView
									? props?.scenarioData?.stacked_original?.baseline_sales_units
									: props?.scenarioData?.original?.baseline_sales_units,
							})}`}
						</p>
					</div>
				</div>
				<div className="override-data-field">
					<Select
						currentOptions={reasonOptions}
						initialOptions={reasonOptions}
						label="Reason"
						labelOrientation="top"
						setSelectedOptions={updateReason}
						setCurrentOptions={() => { }}
						placeholder="Select..."
						isRequired={true}
						isWithSearch={false}
						isMulti={false}
						selectedOptions={reason}
						isOpen={isReasonOpen}
						setIsOpen={setIsReasonOpen}
						isCloseWhenClickOutside={true}
					/>
				</div>
			</div>
			<div className="marginTop-12">
				<TextArea
					aria-label="comments"
					placeholder="Comments..."
					id="override-comment"
					className="override-comment"
					value={comment}
					onChange={overrideCommentHandler}
					width={"775px"}
					height={"100px"}
				/>
			</div>
			<div className="marginTop-12">
				<Button 
					size="large" 
					variant="secondary" 
					onClick={onApply} 
					disabled={!props.allowPromoEdit || false}
				>
					Apply
				</Button>
			</div>
			{overriddenData && (
				<>
					<div className="override-horizontal-divider-line" />
					<div className="override-data-container">
						{PROMO_SIMULATOR.OVERIDDEN_RESULTS.METRIC_DISPLAY_ORDER.map(
							(key) => {
								return (
									<div className="override-results-content" key={key}>
										<span className="promo-override-dialog-title-text">
											{key === "overridden"
												? "Values based on your scenario"
												: "IA Forecasted"}
										</span>
										<div className="override-horizontal-divider-line" />
										<div className="override-metrics-row">
											<div className="override-metrics-item">
												<label className="text-12-500">GM {currency?.symbol || "$"}</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.margin)}
												</p>
											</div>

											<div className="override-metrics-item">
												<label className="text-12-500">Baseline</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.baseline_margin)}
												</p>
											</div>

											<div className="override-metrics-item">
												<label className="text-12-500">Incremental</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.incremental_margin)}
												</p>
											</div>
										</div>
										<div className="override-horizontal-divider-line" />
										<div className="override-metrics-row">
											<div className="override-metrics-item">
												<label className="text-12-500">Halo GM {currency?.symbol || "$"}</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.affinity_margin)}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">Pull Forward GM {currency?.symbol || "$"}</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.pull_forward_margin)}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">
													Cannibalization GM {currency?.symbol || "$"}
												</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.cannibalization_margin)}
												</p>
											</div>
										</div>
										<div className="override-horizontal-divider-line" />
										<div className="override-metrics-row">
											<div className="override-metrics-item">
												<label className="text-12-500">Revenue</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.revenue)}
												</p>
											</div>

											<div className="override-metrics-item">
												<label className="text-12-500">Baseline</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.baseline_revenue)}
												</p>
											</div>

											<div className="override-metrics-item">
												<label className="text-12-500">Incremental</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.incremental_revenue)}
												</p>
											</div>
										</div>
										<div className="override-horizontal-divider-line" />
										<div className="override-metrics-row">
											<div className="override-metrics-item">
												<label className="text-12-500">Halo Revenue</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.affinity_revenue)}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">
													Pull Forward Revenue
												</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.pull_forward_revenue)}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">
													Cannibalization Revenue
												</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.cannibalization_revenue)}
												</p>
											</div>
										</div>
										<div className="override-horizontal-divider-line" />
										<div className="override-metrics-row">
											<div className="override-metrics-item">
												<label className="text-12-500">Sales Units</label>
												<p className="secondaryText-12-500 ">
													{toUnit({
														value: overriddenData?.[key]?.sales_units,
													})}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">Baseline</label>
												<p className="secondaryText-12-500 ">
													{toUnit({
														value: overriddenData?.[key]?.baseline_sales_units,
													})}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">Incremental</label>
												<p className="secondaryText-12-500 ">
													{toUnit({
														value:
															overriddenData?.[key]?.incremental_sales_units,
													})}
												</p>
											</div>
										</div>
										<div className="override-horizontal-divider-line" />
										<div className="override-metrics-row">
											<div className="override-metrics-item">
												<label className="text-12-500">GM %</label>
												<p className="secondaryText-12-500 ">
													{toPercentage({
														value: overriddenData?.[key]?.gm_percent,
													})}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">CM %</label>
												<p className="secondaryText-12-500 ">
													{toPercentage({
														value:
															overriddenData?.[key]
																?.contribution_margin_percent,
													})}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">CM {currency?.symbol || "$"}</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.contribution_margin)}
												</p>
											</div>
										</div>
										<div className="override-horizontal-divider-line" />
										<div className="override-metrics-row">
											<div className="override-metrics-item">
												<label className="text-12-500">AUR {currency?.symbol || "$"}</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.aur)}
												</p>
											</div>
											<div className="override-metrics-item">
												<label className="text-12-500">AUM {currency?.symbol || "$"}</label>
												<p className="secondaryText-12-500 ">
													{formatCurrency(overriddenData?.[key]?.aum)}
												</p>
											</div>
											<div/>
										</div>
									</div>
								);
							}
						)}
					</div>
				</>
			)}
		</Panel>
	);
};

export default PromoOverrideForecast;
