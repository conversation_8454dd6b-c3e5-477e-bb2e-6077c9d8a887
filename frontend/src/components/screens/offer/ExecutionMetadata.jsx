import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
	Button,
	Select,
	Input
} from "impact-ui";
import _ from "lodash";
import {
	toastError,
} from "../../../store/features/global/global";
import {
	capitalizeFirstLetter,
	replaceSpecialCharToCharCode,
} from "../../../utils/helpers/utility_helpers";
import {
	fetchExecuteMetadata,
	getTargetFolderOptions,
	getPriceFilterOptions,
	getAtsCheckOptions,
	getDropShipOptions,
	getTemplateOptions,
	saveExecutionMetaData
} from "../../../store/features/promoReducer/promoReducer";
import RemoveIcon from "../../../assets/imageAssets/RemoveIconWhite.svg?.url";
import AddIcon from "../../../assets/imageAssets/AddIconWhite.svg?.url";
import { global_labels } from "../../../constants/Constants";

export const ExecutionMetadata = (props) => {
	const dispatch = useDispatch();

	const {
		activePromoId,
		promoDetails,
	} = useSelector((store) => store?.pricesmartPromoReducer.promo);

	const [formData, setFormData] = useState({});
	const [initialData, setInitialData] = useState({});
	const [isOpenObj, setIsOpenObj] = useState({});

	const formConfig = {
		folder_id: {
			label: "Target Folder",
			isMandatory: true,
			id: "folder_id",
			type: "select",
		},
		price_filter_id: {
			label: "Price Filter",
			isMandatory: true,
			id: "price_filter_id",
			type: "select",
		},
		template_id: {
			label: "Template ID",
			isMandatory: true,
			id: "template_id",
			type: "select",
		},
		sfcc_dropship_id: {
			label: "SFCC Drop Ship Options",
			isMandatory: false,
			id: "sfcc_dropship_id",
			type: "select",
		},
		sfcc_ats_check_id: {
			label: "SFCC ATS Check",
			isMandatory: false,
			id: "sfcc_ats_check_id",
			type: "select",
		},
		promo_code: {
			label: `${capitalizeFirstLetter(global_labels?.promo_primary)} Code`,
			isMandatory: false,
			id: "promo_code",
			type: "text",
			placeholder: `Enter ${capitalizeFirstLetter(global_labels?.promo_primary)} Code`,
			regex: "^[a-zA-Z0-9,]*$",
			errorMsg:
				`Only alphanumeric value with comma separation allowed, ex: 'abc123,456xyz'`,
		},
		receipt_text_eng: {
			label: "Receipt Text Eng",
			isMandatory: false,
			id: "receipt_text_eng",
			type: "text",
			placeholder: "Enter Receipt Text Eng",
		},
		receipt_text_french: {
			label: "Receipt Text French",
			isMandatory: false,
			id: "receipt_text_french",
			type: "text",
			placeholder: "Enter Receipt Text French",
		},
		sfcc_pip_text: {
			label: "SFCC PIP Text",
			isMandatory: false,
			id: "sfcc_pip_text",
			type: "text",
			placeholder: "Enter SFCC PIP Text",
		},
		sfcc_tender_type_promo_msg: {
			label: `SFCC Tender Type ${capitalizeFirstLetter(global_labels?.promo_primary)} Msg`,
			isMandatory: false,
			id: "sfcc_tender_type_promo_msg",
			type: "text",
			placeholder: `Enter SFCC Tender Type ${capitalizeFirstLetter(global_labels?.promo_primary)} Msg`,
		},
		sfcc_pip_customer_group: {
			label: "SFCC (PIP) Customer Group",
			isMandatory: false,
			id: "sfcc_pip_customer_group",
			type: "text",
			placeholder: "Enter SFCC (PIP) Customer Group",
		},
		sfcc_customer_group: {
			label: "SFCC Customer Group",
			isMandatory: false,
			id: "sfcc_customer_group",
			type: "text",
			placeholder: "Enter SFCC Customer Group",
		},
		integerField: {
			type: "clubbed",
			fields: {
				sfcc_pip_rank: {
					label: "SFCC (PIP) Rank",
					isMandatory: false,
					id: "sfcc_pip_rank",
					type: "number",
					placeholder: "Enter SFCC Customer Group",
					regex: "^[0-9]+$",
				},
				sfcc_rank: {
					label: "SFCC Rank",
					isMandatory: false,
					id: "sfcc_rank",
					type: "number",
					placeholder: "Enter SFCC Customer Group",
					regex: "^[0-9]+$",
				},
			},
		},
	};

	useEffect(() => {
		if (props.activeStep === props.stepNumber) {
			intitalApiCalls();
		} else {
			setFormData({});
			setInitialData({});
		}
	}, [props.activeStep]);

	const intitalApiCalls = async () => {
		const payload = {
			promo_id: activePromoId
		}
		const metadata = await dispatch(fetchExecuteMetadata(payload));
		setFormData((prevState) => ({
			...prevState,
			...metadata
		}));

		const targetOption = await dispatch(getTargetFolderOptions(payload));
		setInitialData((prevState) => ({
			...prevState,
			folder_id: targetOption || [],
		}));

		const filterData = await dispatch(getPriceFilterOptions(payload));
		setInitialData((prevState) => ({
			...prevState,
			price_filter_id: filterData || [],
		}));
		setFormData((prevState) => ({
			...prevState,
			price_filter_id: prevState.price_filter_id ? prevState.price_filter_id : [filterData?.[0]],
		}));

		const atsCheckIds = await dispatch(getAtsCheckOptions(payload));
		setInitialData((prevState) => ({
			...prevState,
			sfcc_ats_check_id: atsCheckIds || [],
		}));

		const tempateOptions = await dispatch(getTemplateOptions(payload));
		setInitialData((prevState) => ({
			...prevState,
			template_id: tempateOptions || [],
		}));

		const dropShipIds = await dispatch(getDropShipOptions(payload));
		setInitialData((prevState) => ({
			...prevState,
			sfcc_dropship_id: dropShipIds || [],
		}));
	}

	if (props.activeStep !== props.stepNumber) {
		return "";
	}

	const handleIsOpen = (fieldId, val) => {
		console.log(isOpenObj);
		let tempVal = false;
		if (_.isFunction(val)) {
			tempVal = val(isOpenObj[fieldId])
		} else {
			tempVal = val;
		}
		setIsOpenObj((prevState) => ({
			...prevState,
			[fieldId]: tempVal,
		}));
	}

	const handleBack = () => {
		props.setActiveStep(props.stepNumber - 1);
	}

	const renderForm = (config, key) => {
		const formDef = config[key];
		let FormContent = null;
		if (formDef.type === "select") {
			FormContent = (
				<Select
					currentOptions={initialData[formDef.id]}
					initialOptions={initialData[formDef.id]}
					label={formDef.label}
					labelOrientation="top"
					setSelectedOptions={(e) => handleupdate(e, key)}
					setCurrentOptions={() => { }}
					placeholder="Select..."
					isRequired={formDef.isMandatory || false}
					isWithSearch={false}
					isMulti={false}
					selectedOptions={formData[formDef.id] || []}
					isOpen={isOpenObj[formDef.id] || false}
					setIsOpen={(val) => handleIsOpen(formDef.id, val)}
					isCloseWhenClickOutside={true}
					isDisabled={!props.allowPromoEdit}
				/>
			);
		} else if (formDef.type === "text") {
			const { id, label } = formDef;
			FormContent = (
				<Input
					id={id}
					inputProps={{}}
					label={label}
					name={id}
					onChange={(e) => handleChange(e, formDef)}
					placeholder="Enter..."
					type="text"
					isRequired={formDef.isMandatory}
					value={formData[id] || ""}
					disabled={!props.allowPromoEdit}
					onBlur={(e) => handleBlur(e, id)}
				/>
			);
		} else if (formDef.type === "number") {
			const { id, label } = formDef;
			FormContent = (
				<div>
					<p className="secondaryText-12-500 sfcc-label">{label}</p>
					<div className="flexWithGap8">
						<Button
							icon={<img src={RemoveIcon} alt='RemoveIcon' />}
							iconPlacement="left"
							variant="primary"
							onClick={() => {
								handleNumberChange(id, "-");
							}}
							size="small"
							disabled={!props.allowPromoEdit}
						/>
						<Input
							id={id}
							inputProps={{}}
							type="text"
							value={formData[id]}
							onChange={(e) => handleChange(e, formDef)}
							disabled={!props.allowPromoEdit}
						/>
						<Button
							variant="primary"
							icon={<img src={AddIcon} alt='RemoveIcon' />}
							iconPlacement="left"
							size="small"
							disabled={!props.allowPromoEdit}
							onClick={() => { handleNumberChange(id, "+"); }}
						/>
					</div>
				</div>
			);
		} else if (formDef.type === "clubbed") {
			if (formDef.fields) {
				FormContent = (
					<div className={`${key}-style`}>
						{Object.keys(formDef.fields).map((key) => {
							return renderForm(formDef.fields, key);
						})}
					</div>
				);
			}
		}
		return (
			<div id={key}>
				{FormContent}
			</div>
		);
	};

	const handleupdate = (selectedItems, id) => {
		const formData_copy = _.cloneDeep(formData);
		formData_copy[id] = selectedItems;
		setFormData(formData_copy);
	};

	const handleNumberChange = (id, action) => {
		const formData_copy = _.cloneDeep(formData);
		if (action === "+") {
			formData_copy[id] = String(Number(formData_copy[id] || 0) + 1);
		}
		if (action === "-" && formData_copy[id] > 0) {
			formData_copy[id] = String(Number(formData_copy[id] || 0) - 1);
		}
		setFormData(formData_copy);
	};

	const handleBlur = (e, id) => {
		const formData_copy = _.cloneDeep(formData);
		const value = e.target.value.trim();
		formData_copy[id] = value;
		setFormData(formData_copy);
	};

	const handleChange = (e, config) => {
		const formData_copy = _.cloneDeep(formData);
		const value = e.target.value;
		if (config?.regex) {
			const regex = new RegExp(config?.regex);
			if (!regex.test(value) && value !== "") {
				dispatch(toastError(config?.errorMsg || "Invalid input format"));
				return;
			}
		}
		formData_copy[config.id] = value;
		setFormData(formData_copy);
	};

	const saveexmd = async () => {
		let payload = {
			promo_id: promoDetails.promo_id,
		};
		let confirmation = true;
		Object.keys(formConfig).forEach((key) => {
			const config = formConfig[key];

			verify(config);

			function verify(config) {
				const key = config.id;
				if (config?.type === "select")
					payload[key] = formData?.[key]?.value;
				else if (config?.type === "clubbed") {
					Object.keys(config?.fields).forEach((key) => {
						let clubbed_config = config.fields[key];
						verify(clubbed_config);
					});
				} else if (config?.type === "number") {
					if (!formData?.[key] && formData?.[key] != "0")
						payload[key] = null;
					else payload[key] = Number(formData?.[key]);
				} else
					payload[key] = formData?.[key]
						? replaceSpecialCharToCharCode(formData?.[key])
						: null;
				if (config?.isMandatory) {
					if (!payload[key] && payload[key] !== 0)
						confirmation = false;
				}
			}
		});
		if (!confirmation) {
			dispatch(toastError("Fill the mandatory fields"));
			return;
		}

		dispatch(saveExecutionMetaData(payload));
	};

	return (
		<div>
			<div className="content_container margin-20">
				<div className="execution-metadata-header text-14-800 marginBottom-16">
					Metadata
				</div>
				<div className="execution-metadata-form-container">
					{Object.keys(formConfig).map((key) => {
						return (
							<div>
								{renderForm(formConfig, key)}
							</div>
						);
					})}
				</div>
			</div>
			<div className="footer_section">
				<Button
					onClick={() => { handleBack() }}
					size="large"
					variant="secondary"
				>
					Back
				</Button>
				<Button
					variant="primary"
					onClick={() => saveexmd()}
					disabled={!props.allowPromoEdit}
				>
					Save
				</Button>
			</div>
		</div>
	);
};


export default ExecutionMetadata;
