import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import {
	Input,
	Select
} from "impact-ui";
import { toastError } from "../../../../store/features/global/global";
import { isInvalidCopySimulation, setSimulationInvalid } from "../../../../store/features/promoReducer/promoReducer";

const BmsmOffers = (props) => {
	const dispatch = useDispatch();
	const [isXTypeOpen, setIsXTypeOpen] = useState(false);
	const [isYTypeOpen, setIsYTypeOpen] = useState(false);
	const [yTypeOptions, setYTtypeOptions] = useState([]);
	const [selectedXType, setSelectedXType] = useState({});
	const [selectedYType, setSelectedYType] = useState({});

	const {
		promoDetails = {},
		validOffers,
		invalidScenarios
	} = useSelector((store) => store?.pricesmartPromoReducer.promo);

	useEffect(() => {
		if (promoDetails.is_copied && !promoDetails.is_edited_after_copy && props.columnName !== "ia_recommended") {
			const xVal = {
				target: {
					value: props?.selectedOfferValue?.offer_x_value,
					id: 'offer_x_value'
				},
			};
			const yVal = {
				target: {
					value: props?.selectedOfferValue?.offer_y_value,
					id: 'offer_y_value'
				},
			};
			validateOfferValue(xVal, true);
			validateOfferValue(yVal, true);
		}
	}, []);

	useEffect(() => {
		let val = props?.selectedOfferValue?.offer_x_type;
		const selectedOption = props.xTypeOptions.filter(option => option.value == val);
		setSelectedXType(selectedOption[0] || {});
		setYTtypeOptions(props?.yTypeOptions?.[val]);
	}, [props?.selectedOfferValue?.offer_x_type, props.xTypeOptions]);

	useEffect(() => {
		let val = props?.selectedOfferValue?.offer_y_type;
		let xVal = props?.selectedOfferValue?.offer_x_type;
		let tempYTypeOptions = [];
		if (!_.isEmpty(props?.yTypeOptions)) {
			tempYTypeOptions = props?.yTypeOptions?.[xVal];
		}

		const selectedOption = tempYTypeOptions?.filter(option => option.value == val);
		setSelectedYType(selectedOption?.[0] || {});
	}, [props?.selectedOfferValue?.offer_y_type, props?.yTypeOptions])

	const handleOfferValueChange = (args) => {
		const { value, key, max } = args;
		const decimalRegex = /^\d{0,10}(\.\d{0,2})?$/;
		let regex = /^0*?[1-9]\d*$/;
		let val = value;
		if (key === "offer_x_value") {
			if (props.selectedOfferValue?.offer_x_type !== "unit") {
				regex = decimalRegex;
			} else
				val = Math.min(val, 20);
		} else {
			if (props.selectedOfferValue?.offer_y_type == "percent_off")
				val = Math.min(val, 100);
			regex = decimalRegex;
		}
		if (
			(val && !regex.test(val)) ||
			val === ""
		) {
			dispatch(setSimulationInvalid(true));
		} else {
			dispatch(setSimulationInvalid(false));
		}
		const newValue = {
			...props.selectedOfferValue,
			[key]: val,
		};
		props.handleOfferValueChange(newValue);
	};

	const handleOfferTypeChange = (args) => {
		const { selectedOption, key } = args
		const { value } = selectedOption;
		let val = {};

		if (key === "offer_x_type") {
			val = {
				offer_x_value: 1,
				offer_y_value: 1,
				offer_x_type: value,
				offer_y_type: "dollar_off"
			}
			setYTtypeOptions(props.yTypeOptions[value])
		} else {
			val = {
				offer_y_value: 1,
				offer_y_type: value
			}
		}
		const newValue = {
			...props.selectedOfferValue,
			...val,
		};
		props.handleOfferValueChange(newValue);
	};



	const validateOfferValue = (e, isCopy = false) => {
		let tempInvalidScenario = _.cloneDeep(invalidScenarios);

		const rowId = props.rowData.id;
		const colId = props.columnData.colId;
		const decimalRegex = /^\d{0,10}(\.\d{0,2})?$/;
		let regex = /^0*?[1-9]\d*$/;
		let maxLimit = null;
		const key = e.target.id;

		const val = e.target.value;

		const uniqueId = `${rowId}_${colId}_${key}`;

		if (key === "offer_x_value") {
			if (props.selectedOfferValue?.offer_x_type !== "unit") {
				regex = decimalRegex;
			}
		} else {
			regex = decimalRegex;
			if (props.selectedOfferValue?.offer_y_type === "percent_off") {
				maxLimit = 100;
			}
		}

		if (isCopy) {
			if (
				(val && !regex.test(val)) ||
				(maxLimit && val && regex.test(val) && val > maxLimit)
			) {
				if (!tempInvalidScenario[uniqueId]) {
					tempInvalidScenario[uniqueId] = true;
					dispatch(isInvalidCopySimulation(tempInvalidScenario));
				}
			} else if (props.selectedOfferValue?.offer_y_type === "dollar_off"
				&& props.selectedOfferValue?.offer_x_type === "dollar"
				&& key === "offer_y_value"
				&& (parseFloat(val) > parseFloat(props.selectedOfferValue?.offer_x_value))
			) {
				if (!tempInvalidScenario[uniqueId]) {
					tempInvalidScenario[uniqueId] = true;
					dispatch(isInvalidCopySimulation(tempInvalidScenario));
				}
			} else if (tempInvalidScenario[uniqueId]) {
				// tempInvalidScenario[uniqueId] = true;
				delete tempInvalidScenario[uniqueId];
				dispatch(isInvalidCopySimulation(tempInvalidScenario));
			}
		} else {
			if (
				(val && !regex.test(val)) ||
				(maxLimit && val && regex.test(val) && val > maxLimit) ||
				val === "" || val === 0
			) {
				if (!tempInvalidScenario[uniqueId]) {
					tempInvalidScenario[uniqueId] = true;
					dispatch(isInvalidCopySimulation(tempInvalidScenario));
				}
				dispatch(toastError(
					`Please enter a valid number.${maxLimit ? 'Number should be between 0 and 100' : ''}`
				));
				dispatch(setSimulationInvalid(true));
				return;
			}
			if (props.selectedOfferValue?.offer_y_type === "dollar_off"
				&& props.selectedOfferValue?.offer_x_type === "dollar"
				&& key === "offer_y_value"
				&& (parseFloat(val) > parseFloat(props.selectedOfferValue?.offer_x_value))
			) {
				if (!tempInvalidScenario[uniqueId]) {
					tempInvalidScenario[uniqueId] = true;
					dispatch(isInvalidCopySimulation(tempInvalidScenario));
				}
				dispatch(toastError(
					`Please enter a valid number less than ${props.selectedOfferValue?.offer_x_value}`
				));
				dispatch(setSimulationInvalid(true));
				return;
			}
			if (tempInvalidScenario[uniqueId]) {
				delete tempInvalidScenario[uniqueId];
				dispatch(isInvalidCopySimulation(tempInvalidScenario));
			}
		}
	};


	return (
		<div className="bmsm-offer-value-container">
			<p className="field-label">
				BUY
			</p>
			<Input
				disabled={props.disableFlag}
				isRequired
				value={props?.selectedOfferValue?.offer_x_value || ""}
				onChange={(e) =>
					handleOfferValueChange({
						value: e.target.value,
						key: "offer_x_value",
					})
				}
				className="text-input basic-info-number-input"
				name="buyValue"
				id="offer_x_value"
				type="number"
				placeholder="Buy"
				onBlur={(e) => validateOfferValue(e, false)}
			/>
			<Select
				currentOptions={
					props.xTypeOptions
				}
				initialOptions={
					props.xTypeOptions
				}
				setSelectedOptions={(option) => handleOfferTypeChange({
					selectedOption: option,
					key: "offer_x_type"
				})}
				setCurrentOptions={() => { }}
				placeholder="Buy type"
				isRequired={true}
				isWithSearch={false}
				isMulti={false}
				selectedOptions={selectedXType}
				isOpen={isXTypeOpen}
				setIsOpen={setIsXTypeOpen}
				isCloseWhenClickOutside={true}
				withPortal={true}
				isDisabled={props.disableFlag}
			/>
			<p className="field-label">
				GET
			</p>
			<Input
				disabled={props.disableFlag}
				isRequired
				value={props?.selectedOfferValue?.offer_y_value || ""}
				onChange={(e) =>
					handleOfferValueChange({
						value: e.target.value,
						key: "offer_y_value",
					})
				}
				className="text-input basic-info-number-input"
				name="discount"
				id="offer_y_value"
				type="number"
				placeholder="Get"
				onBlur={(e) => validateOfferValue(e, false)}
			/>
			<Select
				currentOptions={
					yTypeOptions
				}
				initialOptions={
					yTypeOptions
				}
				setSelectedOptions={(option) => handleOfferTypeChange({
					selectedOption: option,
					key: "offer_y_type"
				})}
				setCurrentOptions={() => { }}
				placeholder="Get type"
				isRequired={true}
				isWithSearch={false}
				isMulti={false}
				selectedOptions={selectedYType}
				isOpen={isYTypeOpen}
				setIsOpen={setIsYTypeOpen}
				isCloseWhenClickOutside={true}
				withPortal={true}
				isDisabled={props.disableFlag}
			/>
		</div>
	);
};


export default BmsmOffers;
