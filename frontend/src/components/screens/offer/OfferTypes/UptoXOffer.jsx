import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import {
    Input,
} from "impact-ui";
import { toastError } from "../../../../store/features/global/global";
import { isInvalidCopySimulation, setSimulationInvalid } from "../../../../store/features/promoReducer/promoReducer";

const UptoXOffer = (props) => {

    const dispatch = useDispatch();

    const {
        promoDetails = {},
        validOffers,
        invalidScenarios,
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    useEffect(() => {
        if (promoDetails?.is_copied && !promoDetails?.is_edited_after_copy && props.columnName !== "ia_recommended") {
            let val = {
                target: {
                    value: props?.selectedOfferValue,
                },
            };
            validateOfferValue(val, true);
        }
    }, []);

    const validateOfferValue = (e, isCopy = false) => {
        let tempInvalidScenario = _.cloneDeep(invalidScenarios);

        const rowId = props.rowData.id;
        const colId = props.columnData.colId;
        const uniqueId = `${rowId}_${colId}`;


        let regex = /^([0-9]+(?:[.][0-9]{0,2})?|.[0-9]+)$/;
        let val = regex.test(e.target.value)
            ? parseFloat(e.target.value)
            : e.target.value;
        if (isCopy) {
            if (
                (val && !regex.test(val)) ||
                (val && regex.test(val) && (val < props.offerTypeMinPrice || val > props.offerTypeMaxPrice))
            ) {
                if (!tempInvalidScenario[uniqueId]) {
                    tempInvalidScenario[uniqueId] = true;
                    dispatch(isInvalidCopySimulation(tempInvalidScenario));
                }
            } else {
                if (tempInvalidScenario[uniqueId]) {
                    // tempInvalidScenario[uniqueId] = true;
                    delete tempInvalidScenario[uniqueId];
                    dispatch(isInvalidCopySimulation(tempInvalidScenario));
                }
            }
        } else {
            if (
                (val && !regex.test(val)) ||
                (val && regex.test(val) && (val < props.offerTypeMinPrice || val > props.offerTypeMaxPrice)) ||
                val === ""
            ) {
                if (!tempInvalidScenario[uniqueId]) {
                    tempInvalidScenario[uniqueId] = true;
                    dispatch(isInvalidCopySimulation(tempInvalidScenario));
                }
                dispatch(toastError(
                    `Please enter a valid number between ${parseFloat(props.offerTypeMinPrice).toFixed(2)} and ${parseFloat(
                        props.offerTypeMaxPrice
                    ).toFixed(2)}. Only 2 decimals allowed`
                ));
                dispatch(setSimulationInvalid(true));
                return;
            }
            if (tempInvalidScenario[uniqueId]) {
                delete tempInvalidScenario[uniqueId];
                dispatch(isInvalidCopySimulation(tempInvalidScenario));
            }
            props.handleOfferValueChange(parseFloat(e.target.value).toFixed(2));
        }
    };

    const handleOfferValueChange = (e) => {
        /* Add Min-Max validation here */
        /* Allow only numbers in input */
        let regex = /^([0-9]+(?:[.][0-9]{0,2})?|.[0-9]+)$/;
        let val = regex.test(e.target.value)
            ? parseFloat(e.target.value)
            : e.target.value;

        if (
            (val && !regex.test(val)) ||
            (val && regex.test(val) && (val < props.offerTypeMinPrice || val > props.offerTypeMaxPrice)) ||
            val === ""
        ) {
            dispatch(setSimulationInvalid(true));
        } else {
            dispatch(setSimulationInvalid(false));
        }
        props.handleOfferValueChange(e.target.value);
    };

    return (
        <Input
            value={props.selectedOfferValue}
            onChange={handleOfferValueChange}
            onBlur={validateOfferValue}
            disabled={props.disabled || props.disableFlag}
            type="number"
        />
    );
};

export default UptoXOffer;
