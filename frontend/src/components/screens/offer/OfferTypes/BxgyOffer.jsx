import React, { useEffect, useState }  from "react";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import {
    Input,
} from "impact-ui";
import { isInvalidCopySimulation, setSimulationInvalid } from "../../../../store/features/promoReducer/promoReducer";
import { toastError } from "../../../../store/features/global/global";

const Bxgy = (props) => {

    const dispatch = useDispatch();

	const [selectedOfferXValue, setSelectedOfferXValue] = useState("");
	const [selectedOfferYValue, setSelectedOfferYValue] = useState("");

    const {
        promoDetails = {},
        validOffers,
        invalidScenarios
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

	useEffect(() => {
		setSelectedOfferXValue(String(props?.selectedOfferValue?.offer_x_value));
		setSelectedOfferYValue(String(props?.selectedOfferValue?.offer_y_value));
		if (promoDetails?.is_copied && !promoDetails?.is_edited_after_copy && props.columnName !== "ia_recommended") {
			const xVal = {
				target: {
					value: props?.selectedOfferValue?.offer_x_value,
					id: 'offer_x_value'
				},
			};
			const yVal = {
				target: {
					value: props?.selectedOfferValue?.offer_y_value,
					id: 'offer_y_value'
				},
			};
			validateOfferValue(xVal, true);
			validateOfferValue(yVal, true);
		}
	}, []);

	const handleOfferValueChange = (args) => {
		const { value, key, max } = args
		const regex = /^0*?[1-9]\d*$/;
		const val = Math.min(+value, max);
		if (
			(val && !regex.test(val)) ||
			val === ""
		) {
			dispatch(setSimulationInvalid(true));
		} else {
			dispatch(setSimulationInvalid(false));
		}
		const newValue = {
			...props.selectedOfferValue,
			[key]: parseFloat(val),
			offer_x_type: "unit",
			offer_y_type: "unit"
		};
		if (key == "offer_x_value")
			setSelectedOfferXValue(String(val));
		if (key == "offer_y_value")
			setSelectedOfferYValue(String(val));
		props.handleOfferValueChange(newValue);
	};

	const validateOfferValue = (e, isCopy = false) => {

		let tempInvalidScenario = _.cloneDeep(invalidScenarios);

		const rowId = props.rowData.id;
		const { colId } = props.columnData;
		const uniqueId = `${rowId}_${colId}_${e.target.id}`;

		const regex = /^0*?[1-9]\d*$/

		const val = regex.test(e.target.value)
			? parseFloat(e.target.value)
			: e.target.value;

		if (isCopy) {
			if (val && !regex.test(val)) {
				if (!tempInvalidScenario[uniqueId]) {
					tempInvalidScenario[uniqueId] = true;
					dispatch(isInvalidCopySimulation(tempInvalidScenario));
				}
			} else if (tempInvalidScenario[uniqueId]) {
					// tempInvalidScenario[uniqueId] = true;
					delete tempInvalidScenario[uniqueId];
					dispatch(isInvalidCopySimulation(tempInvalidScenario));
			}
		} else {
			if (
				(val && !regex.test(val)) ||
				val === "" || val === 0
			) {
				if (!tempInvalidScenario[uniqueId]) {
					tempInvalidScenario[uniqueId] = true;
					dispatch(isInvalidCopySimulation(tempInvalidScenario));
				}
				dispatch(toastError(
					`Please enter a valid positive whole number`
				));
				dispatch(setSimulationInvalid(true));
				return;
			}
			if (tempInvalidScenario[uniqueId]) {
				delete tempInvalidScenario[uniqueId];
				dispatch(isInvalidCopySimulation(tempInvalidScenario));
			}
			const newValue = {
				...props.selectedOfferValue,
				[e.target.id]: parseFloat(val),
			};
			props.handleOfferValueChange(newValue);
		}
	}

	const handleKeyDown = (event) => {
		const key = event.key;
		if (key === 'ArrowLeft' || key === 'ArrowRight') {
			event.stopPropagation();
		} 
	}


	return (
		<div className="bxgy-offer-value-container">
			<p className="field-label">
				BUY
			</p>
			<Input
				disabled={props.disableFlag}
				isRequired
				value={selectedOfferXValue}
				onChange={(e) =>
					handleOfferValueChange({
						value: e.target.value,
						key: "offer_x_value",
						max: 20
					})
				}
				className="text-input basic-info-number-input"
				name="buyValue"
				id="offer_x_value"
				type="number"
				InputProps={{
					inputProps: {
						min: 0,
						// max: 100,
					},
				}}
				placeholder="Buy"
				onBlur={validateOfferValue}
			/>
			<p className="field-label">
				GET
			</p>
			<Input
				disabled={props.disableFlag}
				isRequired
				value={selectedOfferYValue}
				onChange={(e) =>
					handleOfferValueChange({
						value: e.target.value,
						key: "offer_y_value",
						max: 20
					})
				}
				className="text-input basic-info-number-input"
				name="discount"
				id="offer_y_value"
				type="number"
				InputProps={{
					inputProps: {
						min: 0,
					},
				}}
				placeholder="Get"
				onBlur={validateOfferValue}
			/>
		</div>
	);
};

export default Bxgy;
