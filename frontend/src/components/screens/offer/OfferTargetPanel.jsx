import React, { useState, useEffect, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";

import { Button, Panel, Select, Badge, Input, Toast, Tooltip } from "impact-ui";
import { <PERSON>lider } from "@mui/material";

import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import LocationIcon from "../../../assets/imageAssets/LocationIcon.svg?.url";
import DragDropIcon from "../../../assets/imageAssets/Dotted_drag_drop.svg?.url";
import ISymbol from "../../../assets/imageAssets/ISymbol.svg?.url";
import Info from "../../../assets/imageAssets/infoIcon.svg?.url";

import {
  getTargetPromoDetail,
  getBaselineLyMetrics,
  getValidOffersDetails,
  optimizeSimulation,
  editOptimizeSimulation,
  getPromoGifData,
} from "../../../store/features/promoReducer/promoReducer";
import { capitalizeFirstLetter, setErrorMessage } from "../../../utils/helpers/utility_helpers";
import "./Offer.scss";
import { global_labels } from "../../../constants/Constants";

function OfferTargetPanel(props) {

  const dispatch = useDispatch();
  const { isPanelOpen, setIsPanelOpen } = props;

  // Toggles the visibility of the panel
  const togglePanel = () => {
    if (isPanelOpen) {
      resetPanelState(); // Reset all states when closing
    }
    setIsPanelOpen((prev) => !prev);
  };

  // Holds different discount configurations and associated metadata
  const [discountConfigurations, setDiscountConfigurations] = useState({});

  // Dropdown states for discount type
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState({});

  // Dropdown states for and scenario type
  const [isScenarioOpen, setIsScenarioOpen] = useState(false);
  const [scenarioOptions, setScenarioOptions] = useState([]);
  const [selectedScenarios, setSelectedScenarios] = useState([]);
  const [isMaxParameterOpen, setIsMaxParameterOpen] = useState(false);
  const [selectedMaxParameter, setSelectedMaxParameter] = useState({
    label: "Revenue",
    value: "revenue",
  });

  // For slider-based discount types (min to max discount range and value)
  const [sliderRange, setSliderRange] = useState({ min: 0, max: 0 });
  const [sliderValue, setSliderValue] = useState({ min: 0, max: 0 });
  const [maxInput, setMaxInput] = useState(String(sliderValue.max));

  // Objective-related states
  const [selectedObjectives, setSelectedObjectives] = useState([]);
  const [draggedObjectiveIndex, setDraggedObjectiveIndex] = useState(null);
  const [isSelectAll, setIsSelectAll] = useState(false);
  const [filteredOptions, setFilteredOptions] = useState([]);

  const [toast, setToast] = useState({
    isOpen: false,
    message: "",
    variant: "success",
  });

  // Gathers existing baseline and target details from the Redux store
  const {
    baselineLyMetrics = {},
    targetPromoDetails,
    promoDetails,
    activePromoId,
    optValidOffers,
  } = useSelector((state) => state?.pricesmartPromoReducer?.promo);

  // Stores target/lift/priority for each objective type
  const [objectiveData, setObjectiveData] = useState({
    gross_margin_target: null,
    gross_margin_lift: null,
    gross_margin_priority: null,
    revenue_target: null,
    revenue_lift: null,
    revenue_priority: null,
    units_target: null,
    units_lift: null,
    units_priority: null,
    gross_margin_percent_target: null,
    gross_margin_percent_lift: null,
    gross_margin_percent_priority: null,
  });

  //Fetch target details, baseline, and valid offers when the panel opens.
  useEffect(() => {

    if (isPanelOpen && activePromoId) {
      dispatch(getTargetPromoDetail({ promo_id: activePromoId }))
        .catch((error) => {
          setToast({
            isOpen: true,
            message: setErrorMessage(error),
            variant: "error",
          });
        });
      dispatch(getBaselineLyMetrics(activePromoId))
        .catch((error) => {
          setToast({
            isOpen: true,
            message: setErrorMessage(error),
            variant: "error",
          });
        });
      dispatch(getValidOffersDetails({ actionType: "optimise", promo_id: activePromoId }))
        .catch((error) => {
          setToast({
            isOpen: true,
            message: setErrorMessage(error),
            variant: "error",
          });
        });
    } else if (!isPanelOpen) {
      setObjectiveData((prev) => {
        const updatedObjectiveData = { ...prev };
        Object.keys(updatedObjectiveData).forEach((key) => {
          updatedObjectiveData[key] = null;
        });
        return updatedObjectiveData;
      });

    }

  }, [isPanelOpen, activePromoId]);

  //When optValidOffers changes, transform that data into discountConfigurations and build the dropdown options list.
  useEffect(() => {
    if (!_.isEmpty(optValidOffers)) {
      const configurations = mapOptValidOffers(optValidOffers);
      setDiscountConfigurations(configurations);

      // Populate dropdownOptions for discount type selection
      const options = Object.keys(configurations).map((key) => {
        const config = configurations[key];
        return {
          label: config.label,
          value: key,
          offerTypeId: config.offerTypeId,
          min: config.sliderRange?.min || 0,
          max: config.sliderRange?.max || 100,
          scenarios: config.scenarios || [],
        };
      });
      setDropdownOptions(options);
    }
  }, [optValidOffers]);

  /*
    Checks which discount type is stored in targetPromoDetails (if any),
    and matches that with our dropdown to set default selections for the user.
    */
  useEffect(() => {
    if (
      dropdownOptions.length > 0
    ) {
      const discountType = targetPromoDetails?.targets?.opt_discount_type || promoDetails?.discount_rules?.discount_type;

      // Find the selected option from the dropdown options
      const selectedOption = dropdownOptions.find(
        (option) => option.value === discountType
      );

      const selectedMaxParameter = targetPromoDetails?.targets?.maximization_parameter;

      if (selectedMaxParameter) {
        setSelectedMaxParameter(optimizationObjectives.find(objective => objective.value === selectedMaxParameter));
      }



      if (selectedOption) {
        setSelectedOptions(selectedOption);
        let tempSliderValue = {};
        let tempSliderRange = {};

        tempSliderRange.min = selectedOption?.min;
        tempSliderRange.max = selectedOption?.max;
        if (selectedOption?.value === 'upto_x_percent_off') {
          const { max_upto_percent, min_upto_percent, products_on_max_upto_percent } = promoDetails?.upto_percent
          tempSliderRange.min = min_upto_percent;
          tempSliderRange.max = max_upto_percent;
        }
        setSliderRange((prevState) => ({
          ...prevState,
          ...tempSliderRange,
        }));

        // If there's existing min/max discount in the store, apply it
        const minDiscount = targetPromoDetails?.targets?.min_discount;
        const maxDiscount = targetPromoDetails?.targets?.max_discount;

        if (+minDiscount > 0 && +maxDiscount > 0) {
          tempSliderValue.min = minDiscount;
          tempSliderValue.max = maxDiscount;
          setSliderValue((prevState) => ({
            ...prevState,
            ...tempSliderValue,
          }));
          setMaxInput(maxDiscount);
        } else {
          tempSliderValue.min = selectedOption?.min;
          tempSliderValue.max = selectedOption?.max;
          setSliderValue((prevState) => ({
            ...prevState,
            ...tempSliderValue,
          }));
          setMaxInput(selectedOption?.max);
        }

        // Update scenario options if applicable
        if (selectedOption?.scenarios && selectedOption?.scenarios?.length > 0) {
          setScenarioOptions(selectedOption?.scenarios);
          setSelectedScenarios(selectedOption?.scenarios?.filter(scenario => targetPromoDetails?.targets?.discount_type_values?.includes(scenario?.value)));
        } else {
          setScenarioOptions([]);
          setSelectedScenarios([]);
        }
      }
    }
  }, [targetPromoDetails, dropdownOptions]);

  /*
        When the targetPromoDetails change, we parse any existing objective data
        and set it to local state (objectiveData). We also figure out which
        objectives are currently selected, and in what priority order.
    */
  useEffect(() => {
    if (targetPromoDetails?.targets) {
      const { targets } = targetPromoDetails;

      // Build a new object that merges the default objective keys with the store's values
      const updatedObjectiveData = Object.keys(objectiveData).reduce(
        (acc, key) => {
          acc[key] = (targets[key] || targets[key] == 0) ? targets[key] : null; // Use value from targets if available, otherwise default to null
          return acc;
        },
        {}
      );

      setObjectiveData(updatedObjectiveData);

      // Dynamically determine objectives based on non-null target values
      const objectives = [];
      if (!_.isNull(updatedObjectiveData.gross_margin_target))
        objectives.push("gross_margin");
      if (!_.isNull(updatedObjectiveData.revenue_target))
        objectives.push("revenue");
      if (!_.isNull(updatedObjectiveData.units_target))
        objectives.push("units");
      if (!_.isNull(updatedObjectiveData.gross_margin_percent_target))
        objectives.push("gross_margin_percent");

      // Sort objectives by priority
      const sortedObjectives = objectives.sort(
        (a, b) =>
          (updatedObjectiveData[`${a}_priority`] || 0) -
          (updatedObjectiveData[`${b}_priority`] || 0)
      );

      setSelectedObjectives(sortedObjectives);
    } else {
      // If no targetPromoDetails, reset everything
      const resetObjectiveData = Object.keys(objectiveData).reduce(
        (acc, key) => {
          acc[key] = null;
          return acc;
        },
        {}
      );

      setObjectiveData(resetObjectiveData);
      setSelectedObjectives([]);
    }
  }, [targetPromoDetails]);

  /*
        Helper function to transform optValidOffers into discountConfigurations
        with label, scenario options, or slider range data.
    */
  const mapOptValidOffers = (optValidOffers) => {
    const discountConfigurations = {};

    // Extract the array from the object
    const offersArray = Object.values(optValidOffers)?.[0]; // Assuming we need the first array from the object

    // Ensure offersArray is an array
    if (Array.isArray(offersArray)) {
      offersArray.forEach((offer) => {
        const {
          offer_type,
          offer_display_name,
          offer_values,
          offer_type_id,
          min_price,
          max_price,
        } = offer;

        // If the discount type is scenario-based
        if (offer_values && offer_values.length) {
          discountConfigurations[offer_type] = {
            label: offer_display_name,
            offerTypeId: offer_type_id,
            scenarios: offer_values.map((value) => ({
              label: value.offer_value,
              value: value.effective_discount,
            })),
          };
        } else {
          // If it's slider-based (min_price/max_price)
          discountConfigurations[offer_type] = {
            label: offer_display_name,
            offerTypeId: offer_type_id,
            sliderRange: {
              min: min_price ?? 0,
              max: max_price ?? 0,
            },
          };
        }
      });
    } else {
      console.error(
        "optValidOffers does not contain a valid array:",
        optValidOffers
      );
    }

    return discountConfigurations;
  };

  /*
        Handler for when the user changes the discount type from the dropdown.
        Updates the relevant local states (slider range, scenario options, etc.)
    */
  const handleDropdownChange = (option) => {
    setSelectedOptions(option);

    // Get the configuration for the selected option
    const config = discountConfigurations[option.value];

    if (config?.sliderRange) {
      const { min = null, max = null } = config?.sliderRange;
      setSliderRange((prevState) => ({
        ...prevState,
        min: min,
        max: max,
      })); // Update the slider range
      setSliderValue((prevState) => ({
        ...prevState,
        min: min,
        max: max,
      }));
      setMaxInput(max);
      // Check if the reducer provides min/max discount values for this type
      const reducerMinDiscount = targetPromoDetails?.targets?.min_discount;
      const reducerMaxDiscount = targetPromoDetails?.targets?.max_discount;

      if (
        reducerMinDiscount !== undefined &&
        reducerMaxDiscount !== undefined &&
        option.value === targetPromoDetails?.targets?.opt_discount_type
      ) {
        setSliderValue((prevState) => ({
          ...prevState,
          min: reducerMinDiscount,
          max: reducerMaxDiscount,
        }));
        setMaxInput(reducerMaxDiscount);
      } else {
        setSliderValue((prevState) => ({
          ...prevState,
          min: min,
          max: max,
        })); // Reset slider value
        setMaxInput(max);
      }
    } else {
      // Reset the slider if no range is defined for the selected option
      setSliderRange((prevState) => ({
        ...prevState,
        min: 0,
        max: 100,
      }));
      setSliderValue((prevState) => ({
        ...prevState,
        min: 0,
        max: 100,
      }));
    }

    if (config?.scenarios) {
      setScenarioOptions(config.scenarios);
      setFilteredOptions(config.scenarios);
      setSelectedScenarios([]); // Reset scenarios
    } else {
      setScenarioOptions([]);
      setSelectedScenarios([]); // Reset scenarios
    }
  };

  // Handler for scenario-based discount types (e.g., bxgy)
  const handleScenarioChange = (option) => {
    setSelectedScenarios(option);
  };

  // Handle slider change
  const handleSliderChange = (event, newValue) => {
    setSliderValue((prevState) => ({
      ...prevState,
      min: newValue[0],
      max: newValue[1],
    }));
    // Update the raw input states so the inputs stay in sync with the slider.
    setMaxInput(String(newValue[1]));
  };

  // List of all possible objectives a user can select
  const optimizationObjectives = [
    {
      label: "Revenue",
      value: "revenue",
    },
    {
      label: "Gross Margin $",
      value: "gross_margin",
    },
    {
      label: "Gross Margin %",
      value: "gross_margin_percent",
    },
    {
      label: "Units",
      value: "units",
    },
  ];

  // Toggles which objectives are selected when user clicks a Badge
  const handleBadgeClick = (objectiveValue) => {
    if (!props.allowPromoEdit) {
      return;
    }
    setSelectedObjectives((prev) =>
      prev.includes(objectiveValue)
        ? prev.filter((item) => item !== objectiveValue)
        : [...prev, objectiveValue]
    );
    setObjectiveData((prev) => {
      const updatedObjectiveData = { ...prev };
      updatedObjectiveData[`${objectiveValue}_target`] = null;
      updatedObjectiveData[`${objectiveValue}_lift`] = null;
      updatedObjectiveData[`${objectiveValue}_priority`] = null;
      return updatedObjectiveData;
    });
  };

  // Removes a selected objective
  const deleteObjective = (objectiveToDelete) => {
    setSelectedObjectives((prev) =>
      prev.filter((item) => item !== objectiveToDelete)
    );

    setObjectiveData((prev) => {
      const updatedObjectiveData = { ...prev };
      updatedObjectiveData[`${objectiveToDelete}_target`] = null;
      updatedObjectiveData[`${objectiveToDelete}_lift`] = null;
      updatedObjectiveData[`${objectiveToDelete}_priority`] = null;
      return updatedObjectiveData;
    });
  };

  // Mapping from objective string to baseline/LY metric keys in store
  const objectiveToMetrics = {
    revenue: {
      baseline: "baseline_revenue",
      ly: "ly_revenue",
    },
    gross_margin: {
      baseline: "baseline_gross_margin",
      ly: "ly_gross_margin",
    },
    gross_margin_percent: {
      baseline: "baseline_gross_margin_percent",
      ly: "ly_gross_margin_percent",
    },
    units: {
      baseline: "baseline_units",
      ly: "ly_units",
    },
  };

  // Returns the baseline value from store for the chosen objective
  const getDefaultBaseline = (objectiveValue) => {
    const metricKey = objectiveToMetrics[objectiveValue]?.baseline;
    const value =
      metricKey && baselineLyMetrics[metricKey]
        ? baselineLyMetrics[metricKey]
        : "0";

    if (objectiveValue === "gross_margin_percent") {
      return `${value}%`;
    } else if (objectiveValue === "units") {
      return value;
    } else {
      return `$${value}`;
    }
  };

  // Returns the last year (LY) value for the chosen objective
  const getLYValue = (objectiveValue) => {
    const metricKey = objectiveToMetrics[objectiveValue]?.ly;
    const value =
      metricKey && baselineLyMetrics[metricKey]
        ? baselineLyMetrics[metricKey]
        : "0";

    if (objectiveValue === "gross_margin_percent") {
      return `${value}%`;
    } else if (objectiveValue === "units") {
      return value;
    } else {
      return `$${value}`;
    }
  };

  // Drag-and-drop handlers for reordering selected objectives
  const handleDragStart = (index) => {
    setDraggedObjectiveIndex(index);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  /*
        When a user drops an objective in a new position, we reorder selectedObjectives
        and also update their priorities in objectiveData.
    */
  const handleDrop = (index) => {
    if (draggedObjectiveIndex === null || draggedObjectiveIndex === index)
      return;

    const updatedObjectives = [...selectedObjectives];
    const [draggedObjective] = updatedObjectives.splice(
      draggedObjectiveIndex,
      1
    );
    updatedObjectives.splice(index, 0, draggedObjective);

    setSelectedObjectives(updatedObjectives);
    setDraggedObjectiveIndex(null);

    // Update priorities
    updatePriorities(updatedObjectives);
  };

  /*
        Handles numeric input changes for "lift" or "target" fields
        and recalculates the other field automatically using baseline data.
    */
  const handleInputChange = (objective, field, value) => {
    setObjectiveData((prev) => {
      const updatedObjectiveData = { ...prev };
      const baselineKey = objectiveToMetrics[objective]?.baseline;
      const baselineValue = baselineLyMetrics[baselineKey] || 0;

      // Allow empty string (clearing the input) without triggering validation errors
      if (value === "") {
        updatedObjectiveData[`${objective}_${field}`] = "";
        // Optionally, also clear the corresponding field if needed.
        if (field === "lift") {
          updatedObjectiveData[`${objective}_target`] = "";
        } else if (field === "target") {
          updatedObjectiveData[`${objective}_lift`] = "";
        }
        return updatedObjectiveData;
      }

      let regex;
      let parsedValue = parseFloat(value);

      if (field === "lift") {
        regex = /^\-?[0-9]+(?:\.[0-9]{1,2})?$/; // Allow up to 2 decimals
        if (regex.test(value)) {
          updatedObjectiveData[`${objective}_lift`] = parsedValue;
          updatedObjectiveData[`${objective}_target`] = baselineValue
            ? parseFloat((baselineValue * (1 + parsedValue)).toFixed(2))
            : null;
        } else {
          setToast({
            isOpen: true,
            message: "Maximum of 2 decimals allowed for Lift.",
            variant: "error",
          });
          return prev; // Prevent invalid updates
        }
      } else if (field === "target") {
        regex = /^\d+$/; // Allow only whole numbers
        if (objective == "gross_margin_percent") {
          regex = /^([0-9]+(?:[.][0-9]{0,2})?|.[0-9]+)$/;
        }
        if (regex.test(value)) {
          updatedObjectiveData[`${objective}_target`] = parsedValue;
          updatedObjectiveData[`${objective}_lift`] = baselineValue
            ? parseFloat((parsedValue / baselineValue - 1).toFixed(2))
            : null;
        } else {
          let errorMsg = "Please enter a valid whole number for Target."
          if (objective == "gross_margin_percent") {
            errorMsg = "Please enter number less than 100."
          }
          setToast({
            isOpen: true,
            message: errorMsg,
            variant: "error",
          });
          return prev; // Prevent invalid updates
        }
      }

      return updatedObjectiveData;
    });
  };

  //Updates each objective's priority in objectiveData after reordering
  const updatePriorities = (updatedObjectives) => {
    const newObjectiveData = {
      ...objectiveData,
    };

    // Update priorities based on new order
    updatedObjectives.forEach((objective, index) => {
      newObjectiveData[
        `${objective.toLowerCase().replace(/\s+/g, "_")}_priority`
      ] = index + 1; // Priority starts from 1
    });

    setObjectiveData(newObjectiveData);
  };

  /*
        Validate user inputs before dispatching optimization.
        Checks discount type, scenario/slider correctness, objective selection, etc.
    */
  const validateOptimizationInputs = () => {
    // Must select a discount type
    if (!selectedOptions || !selectedOptions.value) {
      setToast({
        isOpen: true,
        message: "Please select a discount type.",
        variant: "error",
      });
      return false;
    }

    // If discount type is scenario-based, user must pick a scenario
    if (["bxgy", "bxgy_percent_off"].includes(selectedOptions.value)) {
      if (!selectedScenarios || selectedScenarios.length === 0) {
        setToast({
          isOpen: true,
          message: "Please select at least one scenario.",
          variant: "error",
        });
        return false;
      }
    }
    // If discount type is slider-based, verify min < max, etc.
    else if (
      ["extra_amount_off", "fixed_price", "percent_off"].includes(
        selectedOptions.value
      )
    ) {
      const { min = null, max = null } = sliderValue || {}; // Default to null if sliderValue is not set

      // Ensure both min and max are defined
      if (min === null || max === null) {
        setToast({
          isOpen: true,
          message: "Both Min and Max values must be set.",
          variant: "error",
        });
        return false;
      }

      // Ensure min is less than max
      if (min >= max) {
        setToast({
          isOpen: true,
          message: "Min value must be less than Max value.",
          variant: "error",
        });
        return false;
      }

      // Ensure min and max are within the slider range
      if (min < sliderRange?.min || max > sliderRange?.max) {
        setToast({
          isOpen: true,
          message: `Min and Max values must be within the range ${sliderRange?.min} to ${sliderRange?.max}.`,
          variant: "error",
        });
        return false;
      }
    }

    // Check if at least one objective is selected
    if (selectedObjectives.length === 0) {
      setToast({
        isOpen: true,
        message: "Please select at least one objective for optimization.",
        variant: "error",
      });
      return false;
    }

    if (!selectedMaxParameter) {
      setToast({
        isOpen: true,
        message: "Please select a maximization parameter.",
        variant: "error",
      });
      return false;
    }

    return true; // All validations passed
  };

  /*
        Dispatches the optimization with user-selected
        discount type, scenarios/slider, and objectives.
    */
  const handleOptimize = () => {
    if (!validateOptimizationInputs()) return; // Stop if validation fails

    if (!props.allowPromoEdit) {
      togglePanel();
      return
    };

    const guid = sessionStorage.getItem("UNIQ_SSE_KEY");

    const discoutTypeValues = [];
    if (selectedScenarios.length > 0) {
      discoutTypeValues.push(...selectedScenarios.map(scenario => scenario.value))
    }

    const optimizePromoPayload = {
      step_count: 3, // Optimization step
      promo_id: activePromoId, // Current promo ID
      promo_target: {
        reoptimise_flag: targetPromoDetails?.is_optimised ? 1 : 0, // Reoptimization flag
        rule_id: promoDetails?.discount_rules?.discount_type_id, // Discount rule ID
        opt_discount_type_id: selectedOptions?.offerTypeId, // Selected discount type ID
        opt_discount_type: selectedOptions?.value, // Selected discount type
        discount_type_values: [...discoutTypeValues],
        min_discount: [
          "extra_amount_off",
          "fixed_price",
          "percent_off",
          "upto_x_percent_off"
        ].includes(selectedOptions?.value)
          ? sliderValue?.min
          : null, // Minimum discount for applicable types
        max_discount: [
          "extra_amount_off",
          "fixed_price",
          "percent_off",
          "upto_x_percent_off"
        ].includes(selectedOptions?.value)
          ? sliderValue?.max
          : null, // Maximum discount for applicable types
        // Objectives data
        revenue_target: objectiveData.revenue_target,
        revenue_lift: objectiveData.revenue_lift,
        revenue_priority: selectedObjectives.indexOf("revenue") + 1 || null,
        gross_margin_percent_target: objectiveData.gross_margin_percent_target,
        gross_margin_percent_lift: objectiveData.gross_margin_percent_lift,
        gross_margin_percent_priority:
          selectedObjectives.indexOf("gross_margin_percent") + 1 || null,
        gross_margin_target: objectiveData.gross_margin_target,
        gross_margin_lift: objectiveData.gross_margin_lift,
        gross_margin_priority:
          selectedObjectives.indexOf("gross_margin") + 1 || null,
        units_target: objectiveData.units_target,
        units_lift: objectiveData.units_lift,
        units_priority: selectedObjectives.indexOf("units") + 1 || null,
        maximization_parameter: selectedMaxParameter?.value || null,
      },
    };
    dispatch(getPromoGifData({
      promo_id: activePromoId,
      discounts_data: [],
      include_temporary_saved_changes: false,
      action: "optimise"
    }))

    if (promoDetails?.is_optimised) {
      // Dispatch the optimization payload to the API
      dispatch(editOptimizeSimulation(optimizePromoPayload))
        .then((response) => {
          if (response.status === 200 || response.status === true) {
            togglePanel();
          }
        })
        .catch((error) => {
          setToast({
            isOpen: true,
            message: setErrorMessage(error),
            variant: "error",
          });
        });
    } else {
      dispatch(optimizeSimulation(optimizePromoPayload))
        .then((response) => {
          if (response.status === 200 || response.status === true) {
            togglePanel();
          }
        })
        .catch((error) => {
          setToast({
            isOpen: true,
            message: setErrorMessage(error),
            variant: "error",
          });
        });
    }
  };

  const onSelectAllHandler = () => {
    if (isSelectAll) {
      // Deselect all options
      setSelectedScenarios([]);
    } else {
      // Select all options
      setSelectedScenarios(scenarioOptions);
    }
    setIsSelectAll(!isSelectAll);
  };

  const resetPanelState = () => {
    setDiscountConfigurations({});
    setIsOpen(false);
    setDropdownOptions([]);
    setSelectedOptions({});
    setIsScenarioOpen(false);
    setScenarioOptions([]);
    setSelectedScenarios([]);
    setSliderRange([]);
    setSliderValue([]);
    setMaxInput(100);
    setSelectedObjectives([]);
    setDraggedObjectiveIndex(null);
    setObjectiveData({
      gross_margin_target: null,
      gross_margin_lift: null,
      gross_margin_priority: null,
      revenue_target: null,
      revenue_lift: null,
      revenue_priority: null,
      units_target: null,
      units_lift: null,
      units_priority: null,
      gross_margin_percent_target: null,
      gross_margin_percent_lift: null,
      gross_margin_percent_priority: null,
    });
  };

  const validateMaxValueChange = useCallback(
    _.debounce((parsedValue) => {
      if (isNaN(parsedValue)) {
        parsedValue = sliderRange?.max; // Fallback if input is invalid
      }
      // Clamp the value between the min and the allowed max range
      parsedValue = Math.max(
        sliderValue?.min,
        Math.min(sliderRange?.max, parsedValue)
      );
      // Update both the slider value and the temporary input state
      setSliderValue((prevState) => ({
        ...prevState,
        min: sliderValue?.min,
        max: parsedValue,
      }));
      setMaxInput(parsedValue);
    }, 700),
    [sliderRange, sliderValue] // Add dependencies here
  );

  const handleMaxValueChange = (e) => {
    // Parse and validate the input once the user is done typing
    let parsedValue = parseFloat(e.target.value);
    setMaxInput(parsedValue);
    validateMaxValueChange(parsedValue);
  }

  return (
    <div className="offer-target-panel">
      <Panel
        anchor="right"
        open={isPanelOpen}
        onClose={togglePanel}
        primaryButtonLabel="Optimize"
        onPrimaryButtonClick={handleOptimize}
        size="large"
        title={
          <div className="target-panel-title-with-icon">
            <img
              src={LocationIcon}
              alt="Target Icon"
              className="target-panel-icon"
            />
            <span>{capitalizeFirstLetter(global_labels?.promo_alias)} Target</span>
          </div>
        }
        className="target-panel"
      >
        <div className="target-panel-content">
          <div className="target-panel-toast">
            <Toast
              autoHideDuration={3000}
              message={toast.message}
              onClose={(_e, _reason) => {
                setToast({
                  isOpen: false,
                  message: "",
                  variant: "",
                });
              }}
              position="top-right"
              variant={toast.variant}
              isOpen={toast.isOpen}
            />
          </div>
          <div className="target-panel-optimization-type">
            <div className="target-panel-scenarios optimization-discount-type-container">
              <Select
                currentOptions={dropdownOptions}
                initialOptions={dropdownOptions}
                label="Optimization Discount Type"
                labelOrientation="top"
                setSelectedOptions={handleDropdownChange}
                isRequired={true}
                isWithSearch={false}
                isMulti={false}
                selectedOptions={selectedOptions}
                isOpen={isOpen}
                setIsOpen={setIsOpen}
                isCloseWhenClickOutside={true}
                isDisabled={!props.allowPromoEdit}
              />
              {selectedOptions?.value === 'upto_x_percent_off' && (
                <div className="target-panel-scenarios-info">
                  <Tooltip
                    orientation="top"
                    title={`The "% of Products on Max Discount" is "${promoDetails?.upto_percent?.products_on_max_upto_percent}"`}
                    variant="tertiary"
                  >
                    <img src={Info} alt="Info" />
                  </Tooltip>
                </div>
              )}
            </div>

            {/* Conditionally render scenario dropdown for bxgy types */}
            {["bxgy", "bxgy_percent_off"].includes(selectedOptions.value) && (
              <div className="target-panel-scenarios">
                <Select
                  currentOptions={filteredOptions?.length ? filteredOptions : scenarioOptions}
                  initialOptions={scenarioOptions}
                  label="Available Scenarios"
                  labelOrientation="top"
                  setSelectedOptions={handleScenarioChange}
                  isWithSearch={true}
                  selectedOptions={selectedScenarios}
                  setCurrentOptions={setFilteredOptions}
                  isMulti={true}
                  isSelectAll={isSelectAll}
                  onSelectAll={onSelectAllHandler}
                  setIsSelectAll={setIsSelectAll}
                  isOpen={isScenarioOpen}
                  setIsOpen={setIsScenarioOpen}
                  isCloseWhenClickOutside={true}
                  toggleSelectAll={true}
                />
              </div>
            )}

            {/* Conditionally render slider for other discount types */}
            {["extra_amount_off", "fixed_price", "percent_off", "upto_x_percent_off"].includes(
              selectedOptions.value
            ) && (
                <div className="target-panel-scenarios">
                  <label className="label-12px-normal">Min Max Discount</label>
                  <div className="target-panel-slider-minmax-slider">
                    <Slider
                      getAriaLabel={() => "Discount range"}
                      value={[sliderValue?.min, sliderValue?.max]}
                      onChange={handleSliderChange}
                      valueLabelDisplay="auto"
                      getAriaValueText={(value) => `${value}%`}
                      min={selectedOptions.value === "upto_x_percent_off" ? 0 : sliderRange?.min} // Dynamically set the minimum value
                      max={selectedOptions.value === "upto_x_percent_off" ? 100 : sliderRange?.max} // Dynamically set the maximum value
                      step={0.1}
                      disabled={!props.allowPromoEdit}
                    />
                  </div>
                  <div className="panel-slider-minmax-input">
                    <div className="min-input">
                      <label className="label-12px-normal">Min</label>
                      <Input
                        type="number"
                        value={sliderValue?.min ? sliderValue?.min : sliderRange?.min}
                        onChange={(e) => {
                          const newMin = Math.min(
                            Math.max(0, parseInt(e.target.value)),
                            sliderValue?.max ? sliderValue?.max : sliderRange?.max
                          );
                          setSliderValue((prevState) => ({
                            ...prevState,
                            min: newMin,
                            max: sliderValue?.max ? sliderValue?.max : sliderRange?.max,
                          }));
                        }}
                        disabled={!props.allowPromoEdit}
                      />
                    </div>
                    <div className="max-input">
                      <label className="label-12px-normal">Max</label>
                      <Input
                        type="number"
                        value={maxInput}
                        onChange={(e) => {
                          // Update the temporary state without clamping immediately
                          // setMaxInput(e.target.value);
                          handleMaxValueChange(e);
                        }}
                        disabled={!props.allowPromoEdit}
                      />
                    </div>
                  </div>
                </div>
              )}
          </div>
          <div className="target-panel-line" />
          <div className="maximization-parameter">
            <div className="title">
              <h2 className="text-14-800">Maximization parameter</h2>
              <Tooltip
                orientation="top"
                title="After achieving all the specified objectives, IA will maximize the chosen parameter"
                variant="tertiary"
              >
                <img src={Info} alt="Info" />
              </Tooltip>
            </div>
            <Select
              currentOptions={optimizationObjectives}
              initialOptions={optimizationObjectives}
              label="Parameter"
              labelOrientation="top"
              setSelectedOptions={setSelectedMaxParameter}
              setCurrentOptions={() => { }}
              isRequired={true}
              isWithSearch={false}
              isMulti={false}
              selectedOptions={selectedMaxParameter}
              isOpen={isMaxParameterOpen}
              setIsOpen={setIsMaxParameterOpen}
              isCloseWhenClickOutside={true}
              isDisabled={!props.allowPromoEdit}
            />
          </div>
          <div className="target-panel-line" />

          <div className="target-panel-objectives-wrapper">
            <h2 className="text-14-600">Select Objectives</h2>
            <div className="target-panel-objectives">
              {optimizationObjectives.map(({ label, value }, index) => (
                <Badge
                  key={index}
                  label={label}
                  color={
                    selectedObjectives.includes(value) ? "info" : "default"
                  }
                  variant="stroke"
                  onClick={() => handleBadgeClick(value)}
                  isSelected={selectedObjectives.includes(value)}
                />
              ))}
            </div>
          </div>

          {/* If objectives selected, render objective cards */}
          {selectedObjectives.length > 0 && (
            <div className="target-details-wrapper">
              {selectedObjectives.length > 0 && (
                <div className="target-details-wrapper">
                  {selectedObjectives.map((objective, index) => {
                    const { label } = optimizationObjectives.find(
                      (obj) => obj.value === objective
                    ) || {
                      label: objective,
                    }; // Fallback to objective if not found

                    return (
                      <div
                        key={index}
                        className="target-details"
                        draggable
                        onDragStart={() => handleDragStart(index)}
                        onDragOver={handleDragOver}
                        onDrop={() => handleDrop(index)}
                      >
                        <div className="target-details-drag">
                          <img src={DragDropIcon} alt="Drag and drop" />
                        </div>
                        <div className="target-details-header">
                          <div className="objective-name text-14-600">
                            {label}
                          </div>
                          <div className="ly-value text-12-500">
                            LY: {getLYValue(objective)}
                          </div>
                        </div>
                        <div className="target-details-content">
                          <div className="target-details-baseline">
                            <div className="baseline-heading ">Baseline</div>
                            <div className="baseline-value text-12-500">
                              {getDefaultBaseline(objective)}
                            </div>
                          </div>
                          <span className="cross-icon">*</span>
                          <div className="target-details-lift">
                            <Input
                              label="Lift"
                              type="number"
                              value={
                                (objectiveData[`${objective}_lift`] || objectiveData[`${objective}_lift`] == 0) 
                                      ? objectiveData[`${objective}_lift`] 
                                      : ""
                              }
                              onChange={(e) =>
                                handleInputChange(
                                  objective,
                                  "lift",
                                  e.target.value
                                )
                              }
                              disabled={!props.allowPromoEdit}
                            />
                          </div>
                          <span className="equal-icon">=</span>
                          <div className="target-details-target">
                            <Input
                              label="Target"
                              type="number"
                              value={
                                (objectiveData[`${objective}_target`] || objectiveData[`${objective}_target`] == 0) 
                                      ? objectiveData[`${objective}_target`] 
                                      : ""
                              }
                              onChange={(e) =>
                                handleInputChange(
                                  objective,
                                  "target",
                                  e.target.value
                                )
                              }
                              disabled={!props.allowPromoEdit}
                            />
                          </div>
                        </div>

                        <div className="target-details-action">
                          <Badge
                            label={`Priority: ${index + 1}`}
                            variant="stroke"
                            color="success"
                          />
                          <div className="horizontal-line" />
                          <Button
                            iconPlacement="left"
                            icon={<img src={DeleteIcon} />}
                            onClick={() => deleteObjective(objective)}
                            size="medium"
                            variant="text"
                            disabled={!props.allowPromoEdit}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          {/* Message displayed when no objectives selected */}
          {selectedObjectives.length === 0 && (
            <div className="add-objective-message secondaryText-14-500">
              <p>Please select at least one objective</p>
            </div>
          )}
          {/* Message prompting user to add more objectives if less than 4 selected */}
          {selectedObjectives.length > 0 && selectedObjectives.length < 4 && (
            <div className="add-objective-message secondaryText-14-500">
              <p>To add more select another objective</p>
            </div>
          )}
          {/* Drag-and-drop info message if user has multiple objectives */}
          {selectedObjectives.length > 1 && (
            <div className="add-drag-message">
              <img src={ISymbol} alt="Note" />
              <div>
                <p className="secondaryText-14-500">
                  The order of selection of objectives will be the order of
                  priority. You can rearrange the sequence at any time by
                  dragging the cards.
                </p>
              </div>
            </div>
          )}
        </div>
      </Panel>
    </div>
  );
}

export default OfferTargetPanel;
