import {
  replaceSpec<PERSON><PERSON><PERSON><PERSON>
} from "../../../utils/helpers/utility_helpers";
import {
  decimalFormatter,
  toUnit,
  toPercentage,
  toCurrencyByCurrencyId
} from "../../../utils/helpers/formatter";
import EditDiscount from "./EditDiscount";
import viewIcon from "../../../assets/imageAssets/viewIcon.svg?url";
import editIcon from "../../../assets/imageAssets/tierEditIcon.svg?url";
import { handleSimulationTableUpdate, getOfferTypeOptions } from "./OfferHelper";
import { commonProductFilterConfig, wholeStoreFilterConfig } from "../../../constants/FilterConfigConstants";

export const initBasicDetails = {
  promo_id: null,
  promo_name: "",
  event_name: "",
  start_date: null,
  end_date: null,
  metrics: {
    revenue_target: "",
    inventory: "",
    discount: "",
    gross_margin_target: "",
  },
};

export const eventOptions = [{
  label: 'Event 1',
  value: 'id1',
  start_date: "23/01/2025",
  end_date: "05/02/2025"
},
{
  label: 'Event 2',
  value: 'id2',
  start_date: "23/01/2025",
  end_date: "05/02/2025"
},
{
  label: 'Event 3',
  value: 'id3',
  start_date: "23/01/2025",
  end_date: "05/02/2025"
},
{
  label: 'Event 4',
  value: 'id4',
  start_date: "23/01/2025",
  end_date: "05/02/2025"
},
{
  label: 'Event 5',
  value: 'id5',
  start_date: "23/01/2025",
  end_date: "05/02/2025"
},
{
  label: 'Event 6',
  value: 'id6',
  start_date: "23/01/2025",
  end_date: "05/02/2025"
}
]

export const productSelectionOptions = [
  {
    label: "Sitewide",
    value: "sitewide",
    disabled: false,
  },
  {
    label: "Whole category",
    value: "whole_category",
    disabled: false,
  },
  {
    label: "Specific products",
    value: "specific_products",
    disabled: false,
  },
  {
    label: "Product group",
    value: "product_group",
    disabled: false,
  }
];

export const storeSelectionOptions = [
  {
    label: "All stores",
    value: "all_stores",
    disabled: false,
  },
  {
    label: "E-com stores",
    value: "ecom_stores",
    disabled: false,
  },
  {
    label: "BnM stores",
    value: "bnm_stores",
    disabled: false,
  },
  {
    label: "Specific stores",
    value: "specific_stores",
    disabled: false,
  }
];

export const sprecificProdSelectionsOptions = [
  {
    label: "Product hierarchy",
    value: "hierarchy",
    disabled: false
  },
  {
    label: "Upload products",
    value: "upload",
    disabled: false
  },
  {
    label: "Copy and paste",
    value: "copy_paste",
    disabled: false
  },
]

export const specificStoreSelectionsOptions = [{
  label: "Store hierarchy",
  value: "hierarchy",
  disabled: false
},
{
  label: "Upload stores",
  value: "upload",
  disabled: false
},
{
  label: "Copy and paste",
  value: "copy_paste",
  disabled: false
},
{
  label: "Store Group",
  value: "store_group",
  disabled: false
},
]

export const specificProductFilterConfig = [
  ...commonProductFilterConfig.map(item => ({
    ...item,
    selectOnLoad: false,
    selection: null,
  })),
]

export const specificStoreFilterConfig = [
  ...(
    wholeStoreFilterConfig
      .filter(item => !["s3_ids", "s4_ids", "s5_ids"].includes(item.filterId))
      .map(item => ({
        ...item,
        selectOnLoad: false,
        selection: null,
      }))
  ),
]

export const productTableConfig = [
  {
    field: "",
    checkboxSelection: true,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    suppressMenu: true,
    filter: false,
    sortable: false,
    pinned: "left",
    maxWidth: 50,
  },
  {
    field: "client_product_id",
    headerName: "Product ID",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
  },
  {
    field: "product_name",
    headerName: "Description",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
  },
  {
    field: "msrp",
    headerName: "MSRP",
    valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
    type: "number",
  },
  {
    field: "current_price",
    headerName: "Current Price",
    valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
    type: "number",
  },
  {
    field: "oh_inventory",
    headerName: "On Hand Inventory",
    type: "number",
  },
  {
    field: "it_inventory",
    headerName: "In transit Inventory",
    type: "number",
  },
  {
    field: "oo_inventory",
    headerName: "On order Inventory",
    type: "number",
  },
  {
    field: "cost",
    headerName: "Cost",
    valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
    type: "number",
  },
  {
    field: "launch_price",
    headerName: "Launch Price",
    valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
    type: "number",
  },
]

export const storeTableConfig = [{
  field: "",
  checkboxSelection: true,
  headerCheckboxSelection: true,
  headerCheckboxSelectionFilteredOnly: true,
  suppressMenu: true,
  filter: false,
  sortable: false,
  pinned: "left",
  maxWidth: 50,
},
{
  field: "store_id",
  headerName: "Store ID",
  valueFormatter: (params) => replaceSpecialCharacter(params.value),
},
{
  field: "store_name",
  headerName: "Store Name",
  valueFormatter: (params) => replaceSpecialCharacter(params.value),
},
{
  field: "s0_name",
  headerName: "Country",
},
{
  field: "s1_name",
  headerName: "Channel",
},
{
  field: "s2_name",
  headerName: "Group",
},
{
  field: "s4_name",
  headerName: "State",
},
{
  field: "s3_name",
  headerName: "District",
},
{
  field: "s5_name",
  headerName: "City",
},
]

export const X_VALUE_OFFERS = [
  "fixed_price",
  "extra_amount_off",
  "percent_off",
  "bxgy",
  "bmsm",
  "bxgy_percent_off",
  "upto_x_percent_off",
];
export const Y_VALUE_OFFERS = ["bxgy", "bmsm", "bxgy_percent_off"];
export const Z_VALUE_OFFERS = ["bxgy_percent_off"];
export const X_TYPE_OFFERS = ["bxgy", "bmsm", "bxgy_percent_off"];
export const Y_TYPE_OFFERS = ["bxgy", "bmsm", "bxgy_percent_off"];
export const Z_TYPE_OFFERS = ["bxgy_percent_off"];

export const simulationTableConfig = [
  {
    field: "",
    checkboxSelection: true,
    headerCheckboxSelection: true,
    headerCheckboxSelectionFilteredOnly: true,
    suppressMenu: true,
    filter: false,
    sortable: false,
    pinned: "left",
    maxWidth: 60,
    colId: "checkbox",
    hide: false
  },
  {
    field: "row_id",
    headerName: "ID",
    hide: true,
  },
  {
    field: "name",
    colId: "name",
    headerName: "Name",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
  },
  {
    field: "l0_cuq",
    colId: "l0_name",
    headerName: "Division",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
  },
  {
    field: "l1_cuq",
    colId: "l1_name",
    headerName: "Group",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "l2_cuq",
    colId: "l2_name",
    headerName: "Department",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "l3_cuq",
    colId: "l3_name",
    headerName: "Class",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "l4_cuq",
    colId: "l4_name",
    headerName: "Sub Class",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "brand",
    colId: "brand",
    headerName: "Brand",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "product_group",
    colId: "product_group",
    headerName: "Product Group",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "s0_name",
    colId: "s0_name",
    headerName: "Country",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "s1_name",
    colId: "s1_name",
    headerName: "Channel",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "s2_name",
    colId: "s2_name",
    headerName: "Group",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "s3_name",
    colId: "s3_name",
    headerName: "District",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "s4_name",
    colId: "s4_name",
    headerName: "State",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "s5_name",
    colId: "s5_name",
    headerName: "City",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "store_name",
    colId: "store_name",
    headerName: "Store",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    width: 250,
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "product_name",
    colId: "product_name",
    headerName: "Product",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
    hide: true,
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agTextColumnFilter",
  },
  {
    field: "price",
    colId: "price",
    headerName: "Price",
    valueFormatter: decimalFormatter,
    hide: true,
    type: "number",
    advanceSearchEnabled: true,
    isSearchable: true,
    filter: "agNumberColumnFilter",
  },
  {
    field: "ia_recommended",
    colId: "ia_recommended",
    headerName: "IA Recommended",
    cellRenderer: EditDiscount,
    hide: false,
    advanceSearchEnabled: true,
    filter: 'agTextColumnFilter',
    isSearchable: true,
    thirdOptionAsDropdown: true,
    toShowDuplicateColumnInAdvanceSearch: true,
    thirdOptionDropdownOptions1: () =>getOfferTypeOptions(),
    columnFieldsInAdvanceSearch: ["IA Recommended offerType", "IA Recommended offerValue"],
    // width: 350,
    minWidth: 311,
  },
  {
    field: "scenario_1",
    colId: "scenario_1",
    headerName: "Scenario 1",
    cellRenderer: EditDiscount,
    cellRendererParams: {
      onUpdate: handleSimulationTableUpdate
    },
    hide: false,
    advanceSearchEnabled: true,
    filter: 'agTextColumnFilter',
    isSearchable: true,
    thirdOptionAsDropdown: true,
    toShowDuplicateColumnInAdvanceSearch: true,
    columnFieldsInAdvanceSearch: ["Scenario 1 offer type", "Scenario 1 offer value"],
    thirdOptionDropdownOptions1: () => getOfferTypeOptions(),
    // thirdOptionDropdownOptions2: [{label: "Model Y", value: "modelY"}, {label: "Model 3", value: "model3"}, {label: "Model X", value: "modelX"}]
    // width: 350,
    minWidth: 311,
  },
  {
    field: "scenario_2",
    colId: "scenario_2",
    headerName: "Scenario 2",
    cellRenderer: EditDiscount,
    cellRendererParams: {
      onUpdate: handleSimulationTableUpdate
    },
    advanceSearchEnabled: true,
    filter: 'agTextColumnFilter',
    isSearchable: true,
    thirdOptionAsDropdown: true,
    toShowDuplicateColumnInAdvanceSearch: true,
    columnFieldsInAdvanceSearch: ["Scenario 2 offer type", "Scenario 2 offer value"],
    thirdOptionDropdownOptions1: () => getOfferTypeOptions(),
    hide: true,
    // width: 350,
    minWidth: 311,
  },
]

export const simulationTableStaticColumnsIds = [
  "checkbox",
]

export const VALUE_LABELS = {
  dollar: "$",
  unit: "Units",
  percent_off: "% Off",
  dollar_off: "Amount Off",
  at_dollar: "At Amount",
};

export const DISCOUNT_TYPES = {
  bxgy: "BxGy",
  bxgy_percent_off: "BxGy % Off",
  tiered_offer: "Tier",
  percent_off: "% Off",
  extra_amount_off: "Amount Off",
  fixed_price: "PP",
  bmsm: "BMSM",
}

export const PROMO_SIMULATOR = {
  DETAILED_METRICS: ["original", "incremental", "baseline"],
  SIMULATION_RESULTS: {
    METRIC_DISPLAY_ORDER: [{
      key: "margin",
      title: "GM$",
      itemOrder: [{
        title: "GM $",
        valueKey: "margin"
      },
      {
        title: "Baseline",
        valueKey: "baseline_margin"
      },
      {
        title: "Incremental",
        valueKey: "incremental_margin"
      },
      {
        title: "Halo",
        valueKey: "affinity_margin"
      },
      {
        title: "Pull Forward",
        valueKey: "pull_forward_margin"
      },
      {
        title: "Cannibalization",
        valueKey: "cannibalization_margin",
      },
      {
        title: "AUM $",
        valueKey: "aum",
        formatter: "dollar"
      },
      ],
    },
    {
      key: "revenue",
      title: "Revenue",
      itemOrder: [{
        title: "Revenue",
        valueKey: "revenue"
      },
      {
        title: "Baseline",
        valueKey: "baseline_revenue"
      },
      {
        title: "Incremental",
        valueKey: "incremental_revenue"
      },
      {
        title: "Halo",
        valueKey: "affinity_revenue"
      },
      {
        title: "Pull Forward",
        valueKey: "pull_forward_revenue"
      },
      {
        title: "Cannibalization",
        valueKey: "cannibalization_revenue",
      },
      {
        title: "AUR $",
        valueKey: "aur",
        formatter: "dollar"
      },
      ],
    },
    ],
    MARGIN_DISPLAY_ORDER: [{
      title: "GM %",
      valueKey: "gm_percent",
      formatter: "percent"
    },
    {
      title: "CM %",
      valueKey: "contribution_margin_percent",
      formatter: "percent",
    },
    {
      title: "CM $",
      valueKey: "contribution_margin",
      formatter: "dollar",
    },
    ],
    ST_DISPLAY_ORDER: [{
      title: "ST %",
      valueKey: "finalized_st_percent",
      formatter: "percent",
    },
    {
      title: "Inventory",
      valueKey: "total_inventory",
    },
    ],
    SALES_UNITS_DISPLAY_ORDER: [{
      title: "Total Sales Units",
      valueKey: "total_sales_units"
    },
    {
      title: "Baseline",
      valueKey: "baseline_sales_units"
    },
    {
      title: "Incremental",
      valueKey: "incremental_sales_units"
    },
    ],
  },
  OVERIDDEN_RESULTS: {
    METRIC_DISPLAY_ORDER: ["overridden", "original"]
  }
};

export const scenario_details_table = [
  {
    headerName: "Name",
    field: "hierarchy_name",
    hide: false,
    isSearchable: true,
    filter: "agTextColumnFilter",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
  },
  {
    hide: false,
    headerName: "ST %",
    children: [
      {
        headerName: "IA Recommended",
        field: "finalized_st_percent_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_finalized_st_percent",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_finalized_st_percent",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_finalized_st_percent",
            hide: false,
            valueFormatter: toPercentage,
            isSearchable: true,
            filter: "agTextColumnFilter",
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_finalized_st_percent",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "finalized_st_percent_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_finalized_st_percent",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_finalized_st_percent",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_finalized_st_percent",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_finalized_st_percent",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "finalized_st_percent_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_finalized_st_percent",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_finalized_st_percent",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_finalized_st_percent",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_finalized_st_percent",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toPercentage,
            type: "number",
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Inventory",
    children: [
      {
        headerName: "IA Recommended",
        field: "total_inventory_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_total_inventory",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_total_inventory",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_total_inventory",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_total_inventory",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "total_inventory_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_total_inventory",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_total_inventory",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_total_inventory",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_total_inventory",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "total_inventory_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_total_inventory",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_total_inventory",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_total_inventory",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_total_inventory",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
            type: "number",
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Margin",
    children: [
      {
        headerName: "IA Recommended",
        field: "margin_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "margin_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "margin_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Revenue",
    children: [
      {
        headerName: "IA Recommended",
        field: "revenue_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "revenue_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "revenue_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
            type: "number",
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Sales Units",
    children: [
      {
        headerName: "IA Recommended",
        field: "sales_units_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "sales_units_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "sales_units_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Incremental Margin",
    children: [
      {
        headerName: "IA Recommended",
        field: "incremental_margin_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "incremental_margin_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "incremental_margin_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Incremental Revenue",
    children: [
      {
        headerName: "IA Recommended",
        field: "incremental_revenue_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "incremental_revenue_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "incremental_revenue_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      }
    ]
  },
  {
    hide: true,
    headerName: "Incremental Sales Units",
    children: [
      {
        headerName: "IA Recommended",
        field: "incremental_sales_units_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_optimize_0_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_optimize_0_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "incremental_sales_units_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_1_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_1_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "incremental_sales_units_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked",
            field: "stacked_resimulation_2_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Stacked #",
            field: "stacked_override_resimulation_2_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      }
    ]
  }
]

export const scenario_details_table_only_without_stacked = [
  {
    headerName: "Name",
    field: "hierarchy_name",
    hide: false,
    isSearchable: true,
    filter: "agTextColumnFilter",
    valueFormatter: (params) => replaceSpecialCharacter(params.value),
  },
  {
    hide: false,
    headerName: "Margin",
    children: [
      {
        headerName: "IA Recommended",
        field: "margin_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "margin_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "margin_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_margin",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_margin",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Revenue",
    children: [
      {
        headerName: "IA Recommended",
        field: "revenue_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "revenue_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "revenue_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_revenue",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_revenue",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: decimalFormatter,
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Sales Units",
    children: [
      {
        headerName: "IA Recommended",
        field: "sales_units_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "sales_units_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "sales_units_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_sales_units",
            hide: false,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_sales_units",
            hide: true,
            isSearchable: true,
            filter: "agTextColumnFilter",
            valueFormatter: toUnit,
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Incremental Margin",
    children: [
      {
        headerName: "IA Recommended",
        field: "incremental_margin_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "incremental_margin_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "incremental_margin_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_incremental_margin",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_incremental_margin",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      }
    ]
  },
  {
    hide: false,
    headerName: "Incremental Revenue",
    children: [
      {
        headerName: "IA Recommended",
        field: "incremental_revenue_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "incremental_revenue_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "incremental_revenue_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_incremental_revenue",
            hide: false,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_incremental_revenue",
            hide: true,
            valueFormatter: decimalFormatter,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      }
    ]
  },
  {
    hide: true,
    headerName: "Incremental Sales Units",
    children: [
      {
        headerName: "IA Recommended",
        field: "incremental_sales_units_ia_recommended",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "optimize_0_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_optimize_0_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 1",
        field: "incremental_sales_units_scenario_1_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_1_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_1_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      },
      {
        headerName: "Scenario 2",
        field: "incremental_sales_units_scenario_2_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        children: [
          {
            headerName: "Without Stacking",
            field: "resimulation_2_incremental_sales_units",
            hide: false,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          },
          {
            headerName: "Without Stacking #",
            minWidth: 180,
            field: "override_resimulation_2_incremental_sales_units",
            hide: true,
            valueFormatter: toUnit,
            isSearchable: true,
            filter: "agTextColumnFilter",
          }
        ]
      }
    ]
  }
]

export const productExclusionTypeIds = {
  1: "hierarchy",
  2: "copy_paste",
  3: "product_group",
  4: "upload",
}

export const offerModeOptions = [
  {
    label: "",
    value: "view",
    disabled: false,
    icon: <img src={viewIcon} alt="view" />,
  },
  {
    label: "",
    value: "edit",
    disabled: false,
    icon: <img src={editIcon} alt="edit" />,
  },
];

export const uptoXPerDiscountType = [
  {
    label: "Min Discount %",
    key: "min_upto_percent",
  },
  
  {
    label: "Max Discount %",
    key: "max_upto_percent",
  },
  {
    label: "% of Products on Max Discount",
    key: "products_on_max_upto_percent",
  },
];

export const advanceSearchColumnsKeyMapping = {
  "Scenario 1 offer type": "scenario#1#offer_type_id",
  "Scenario 1 offer value": "scenario#1#offer_value",
  "Scenario 2 offer type": "scenario#2#offer_type_id",
  "Scenario 2 offer value": "scenario#2#offer_value",
  "IA Recommended offerType": "ia_recommended#offer_type_id",
  "IA Recommended offerValue": "ia_recommended#offer_value",
}

export const headerSearchColumnsKeyMapping = {
  "scenario_1": "scenario#1#offer_value",
  "scenario_2": "scenario#2#offer_value",
  "ia_recommended": "ia_recommended#offer_value",
}


export const numberOperatorKeyMapping = {
  "equals": "equals",
  "notEqual": "not_equals",
  "lessThan": "less_than",
  "lessThanOrEqual": "less_than_or_equals",
  "greaterThan": "greater_than",
  "greaterThanOrEqual": "greater_than_or_equals",
}