import React, {
    useState,
    useCallback,
    useRef,
    useEffect,
} from "react";
import { useSelector, useDispatch } from "react-redux";

import _ from "lodash";
import {
    Button,
    FileUpload,
    TextArea,
    Tabs,
    ButtonGroup,
    Input,
    Prompt,
    Modal,
    Badge,
} from "impact-ui";
import { Table } from "impact-ui-v3";
import {
    productSelectionOptions,
    sprecificProdSelectionsOptions,
    specificProductFilterConfig,
    productTableConfig
} from "./OfferConstants";

import ComponentFilters from "../../ui/componentFilters/ComponentFilters";
import {
    excelTemplateDownload,
    toastError,
} from "../../../store/features/global/global";
import EmptyData from "../../ui/emptyData/EmptyData";
import {
    overwriteFilters,
    resetAllFiltersData,
    setSelectedFilters,
} from "../../../store/features/filters/filters";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";
import { getProductDataFromFile, getProductFromIds, getProductGroups, getProducts, setIsEditedFlag } from "../../../store/features/promoReducer/promoReducer";
import { invalidDataColumnConfig, productGroupTableConfiguation } from "../marketingCalendar/createEvent/createEventConstants";
import { fabricatePayloadHierarchy, mergeFiltersData } from "../../../utils/helpers/utility_helpers";

const ProductSelection = props => {

    const dispatch = useDispatch();

    const {
        promoDetails = {},
        productData = [],
        productGroupData = [],
        productDataFromUploadOrCopyPaste = {},
        promoEventDetails = {}
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const [uploadedExcel, setUploadedExcel] = useState([]);
    const [copyPasteData, setCopyPasteData] = useState("");
    const [lastFiltersData, setLastFiltersData] = useState({});
    const [productTableData, setProductTableData] = useState([]);
    const [productGroupTableData, setProductGroupTableData] = useState([]);
    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [activeTab, setActiveTab] = useState(null)
    const [modifiedFilterConfig, setModifiedFilterConfig] = useState([]);

    const [showDataOverridePrompt, setShowDataOverridePrompt] = useState(false);
    const [tempTableData, setTempTableData] = useState([]);
    const [showInvalidDataModal, setShowInvalidDataModal] = useState(false);
    const [invalidTableData, setInvalidTableData] = useState([]);
    const [showOutofScopeDataModal, setShowOutofScopeDataModal] = useState(false);

    const [productTableDef, setProductTableDef] = useState(productTableConfig);
    const [productGroupTableDef, setProductGroupTableDef] = useState(productGroupTableConfiguation);

    const [productSelectOptions, setProductSelectOptions] = useState(productSelectionOptions);
    const [tabOptions, setTabOptions] = useState(sprecificProdSelectionsOptions);

    const productTableRef = useRef();
    const productGroupTableRef = useRef();

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    useEffect(() => {
        if (props?.selectedSpecificProductType) {
            setActiveTab(props?.selectedSpecificProductType)
        }
        if (props?.allowPromoEdit) {
            setTabOptions(sprecificProdSelectionsOptions);
        } else {
            const array = sprecificProdSelectionsOptions.map(item => {
                if (item.value != props?.selectedSpecificProductType) {
                    return { ...item, disabled: true }
                }
                return item;
            });
            setTabOptions(array);
        }
    }, [props?.selectedSpecificProductType])

    useEffect(() => {
        // set product selection options based on product restriction level
        // and we are suppose to show only those options which are allowed based on product restriction level
        const obj = {
            sitewide: ["sitewide", "whole_category", "specific_products", "product_group"],
            whole_category: ["whole_category", "specific_products", "product_group"],
            specific_products: ["specific_products", "product_group"],
            product_group: ["product_group"]
        }
        const toBeShownOptions = [];
        const product_restriction_level = promoEventDetails?.product_restriction?.product_restriction_level || "sitewide";
        _.forEach(productSelectionOptions, (option) => {
            let opt = _.cloneDeep(option);
            if (props?.allowPromoEdit) {
                if (product_restriction_level) {
                    if (
                        product_restriction_level &&
                        obj?.[product_restriction_level].includes(option.value)
                    ) {
                        if (props?.isRestricted && option.value !== product_restriction_level)
                            opt.disabled = true;
                    } else {
                        opt.disabled = true
                    }
                }
                if (props?.disabledOptions && props?.disabledOptions?.length > 0) {
                    if (props?.disabledOptions.includes(option.value)) {
                        opt.disabled = true;
                    }
                }
            } else if (props?.selectedProductType !== option.value) {
                opt.disabled = true;
            }
            toBeShownOptions.push(opt);
        });
        setProductSelectOptions(toBeShownOptions);
        if (props?.isRestricted && promoEventDetails?.product_restriction?.product_restriction_level === "whole_category") {
            const product_hierarchy = promoEventDetails?.product_restriction?.product_hierarchy || {}
            Object.keys(
                product_hierarchy || {}
            ).forEach((key) => {
                dispatch(
                    setSelectedFilters({
                        data: product_hierarchy?.[key] || [],
                        filterId: key,
                        from: "CREATE_OFFER_WHOLE_CATEGORY_COMPONENT",
                    })
                );
            });
        }
    }, [
        promoEventDetails,
        props?.isRestricted,
        props?.selectedProductType,
        props?.selectedSpecificProductType,
        props?.allowPromoEdit
    ]);

    useEffect(() => {
        const modifiedFilterConfig = specificProductFilterConfig.map((item) => {
            return {
                ...item,
                extraParams: {
                    event_id: promoDetails?.event_id || null,
                },
                isMandatory: props.selectedProductType === "whole_category" ? false : item.isMandatory
            };
        });
        setModifiedFilterConfig(modifiedFilterConfig);
    }, [promoDetails, props.selectedProductType]);

    useEffect(() => {
        if (!_.isEmpty(productDataFromUploadOrCopyPaste)) {
            if (!_.isEmpty(productDataFromUploadOrCopyPaste?.out_of_scope)) {
                setShowOutofScopeDataModal(true);
            }
            if (
                _.isEmpty(productDataFromUploadOrCopyPaste?.inactive) &&
                _.isEmpty(productDataFromUploadOrCopyPaste?.invalid) &&
                !_.isEmpty(productDataFromUploadOrCopyPaste?.valid)
            )
                handleProductTableData(productDataFromUploadOrCopyPaste.valid);
            else if (
                !_.isEmpty(productDataFromUploadOrCopyPaste?.inactive) ||
                !_.isEmpty(productDataFromUploadOrCopyPaste?.invalid)
            ) {
                setInvalidTableData([
                    ...productDataFromUploadOrCopyPaste.inactive,
                    ...productDataFromUploadOrCopyPaste.invalid,
                ]);
                setShowInvalidDataModal(true);
            }
        }
    }, [productDataFromUploadOrCopyPaste]);

    useEffect(() => {
        if (productData.length) {
            handleProductTableData(productData);
        }
    }, [productData]);

    useEffect(() => {
        if (productGroupData.length) {
            handleProductGroupTableData(productGroupData);
        }
    }, [productGroupData]);

    useEffect(() => {
        if (filtersData["CREATE_OFFER_WHOLE_CATEGORY_COMPONENT"]) {
            dispatch(setIsEditedFlag(true));
        }
    }, [filtersData["CREATE_OFFER_WHOLE_CATEGORY_COMPONENT"]])

    useEffect(() => {
        if (props?.isRestricted || !props?.allowPromoEdit) {
            let tableDef = _.cloneDeep(productTableDef);
            tableDef = _.filter(tableDef, (item) => item.field !== "" && !item.checkboxSelection);
            setProductTableDef(tableDef);

            tableDef = _.cloneDeep(productGroupTableDef);
            tableDef = _.filter(tableDef, (item) => item.field !== "" && !item.checkboxSelection);
            setProductGroupTableDef(tableDef);
        } else {
            setProductTableDef(productTableConfig);
            setProductGroupTableDef(productGroupTableConfiguation);
        }
    }, [props?.isRestricted, props?.allowPromoEdit]);

    const handleProductGroupTableData = (data) => {

        if (productGroupTableData.length > 0) {
            setTempTableData(data);
            setShowDataOverridePrompt(true);
        } else
            setProductGroupTableData(data);
    }

    const handleProductTableData = (data) => {
        if (productTableData.length > 0) {
            setTempTableData(data);
            setShowDataOverridePrompt(true);
        } else
            setProductTableData(data);
    }

    const onActiveProductProceed = () => {
        // if invalid data is present, show modal
        const validData = productDataFromUploadOrCopyPaste?.valid || [];
        handleProductTableData(validData);
        setShowInvalidDataModal(false);
        setInvalidTableData([]);
    }

    const onActiveInactiveProductProceed = () => {
        // if invalid data is present, show modal
        const validData = productDataFromUploadOrCopyPaste?.valid || [];
        const inactiveData = productDataFromUploadOrCopyPaste?.inactive || [];
        handleProductTableData([...validData, ...inactiveData]);
        setInvalidTableData([]);
        setShowInvalidDataModal(false);
    }

    const onRetainClick = () => {
        // if retain is clicked, add temp data to table data
        if (props.selectedProductType === "product_group") {
            // if product group is selected, add data to product group table
            let currentRows = _.cloneDeep(productGroupTableData);
            currentRows = [
                ...currentRows,
                ...tempTableData,
            ];
            currentRows = _.uniqBy(currentRows, "product_group_id");
            setProductGroupTableData(currentRows);
            setShowDataOverridePrompt(false);
            setTempTableData([]);
            const updatedFiltersData = mergeFiltersData(
                _.cloneDeep(lastFiltersData),
                _.cloneDeep(filtersData["CREATE_OFFER_PRODUCT_GROUP_COMPONENT"])
            );
            setLastFiltersData(_.cloneDeep(updatedFiltersData));
            dispatch(
                overwriteFilters({
                    filtersData: _.cloneDeep(updatedFiltersData),
                    activeScreen: "CREATE_OFFER_PRODUCT_GROUP_COMPONENT",
                })
            );
        } else {
            // if product is selected, add data to product table
            let currentRows = _.cloneDeep(productTableData);
            const tempRows = _.cloneDeep(tempTableData || []).map(item => ({
                ...item,
                // convert product_id to number as on upload it is string, and _.uniqBy is not working with string
                product_id: Number(item.product_id)
            }));
            currentRows = [
                ...currentRows,
                ...tempRows,
            ];
            currentRows = _.uniqBy(currentRows, "product_id");
            setProductTableData(currentRows);
            setShowDataOverridePrompt(false);
            setTempTableData([]);

            // concat currennt data with previous data and set both last filters and current filters as same
            const updatedFiltersData = mergeFiltersData(
                _.cloneDeep(lastFiltersData),
                _.cloneDeep(filtersData["CREATE_OFFER_SPECIFIC_PRODUCT_COMPONENT"])
            );
            setLastFiltersData(_.cloneDeep(updatedFiltersData));
            dispatch(
                overwriteFilters({
                    filtersData: _.cloneDeep(updatedFiltersData),
                    activeScreen: "CREATE_OFFER_SPECIFIC_PRODUCT_COMPONENT",
                })
            );
        }
    };

    const onReplaceAndOverwriteClick = () => {
        // if replace and overwrite is clicked, set temp data to table data 
        if (props.selectedProductType === "product_group") {
            setProductGroupTableData(tempTableData);
            setShowDataOverridePrompt(false);
            setTempTableData([]);
        } else {
            setProductTableData(tempTableData);
            setShowDataOverridePrompt(false);
            setTempTableData([]);
        }
    }

    const onProductTypeSelectionChange = (e) => {
        // on product type selection change, set selected product type
        if (props?.isRestricted) return;
        const { value } = e.target;
        props.setSelectedProductType(value);
        setGlobalSearchText("");
        dispatch(setIsEditedFlag(true));
    }

    const tabChangeHandler = (event, val) => {
        // on tab change, set active tab
        setActiveTab(val);
        props.setSelectedSpecificProductType(val);
    }

    const onSpecificProdFiltersApply = () => {
        let callAPI = true;
        // check if all mandatory filters are selected
        const filtersDataSelected = _.cloneDeep(
            filtersData["CREATE_OFFER_SPECIFIC_PRODUCT_COMPONENT"]
        );
        _.forEach(modifiedFilterConfig, (config) => {
            const key = config.filterId;
            if (
                config.isMandatory &&
                !filtersDataSelected?.[key]?.selectedOptionsArray.length
            ) {
                callAPI = false;
            }
        });
        if (!callAPI) {
            dispatch(toastError("Please select all mandatory filters"));
            return;
        }
        // set last filters data for replace and retain prompt
        if (_.isEmpty(lastFiltersData))
            setLastFiltersData(_.cloneDeep(filtersDataSelected));
        // if all mandatory filters are selected, call API to get product data
        const payload = fabricatePayloadHierarchy(filtersDataSelected);

        payload.event_id = promoDetails?.event_id || null;

        dispatch(getProducts(payload));
        dispatch(setIsEditedFlag(true));
    };

    const onProdGroupFiltersApply = () => {
        let callAPI = true;
        // check if all mandatory filters are selected
        const filtersDataSelected = _.cloneDeep(
            filtersData["CREATE_OFFER_PRODUCT_GROUP_COMPONENT"]
        );
        _.forEach(modifiedFilterConfig, (config) => {
            const key = config.filterId;
            if (
                config.isMandatory &&
                !filtersDataSelected?.[key]?.selectedOptionsArray.length
            ) {
                callAPI = false;
            }
        });
        if (!callAPI) {
            dispatch(toastError("Please select all mandatory filters"));
            return;
        }
        // set last filters data for replace and retain prompt
        if (_.isEmpty(lastFiltersData))
            setLastFiltersData(_.cloneDeep(filtersDataSelected));
        // if all mandatory filters are selected, call API to get product data
        const payload = fabricatePayloadHierarchy(filtersDataSelected);
        payload.event_id = promoDetails?.event_id || null;
        dispatch(getProductGroups(payload));
        dispatch(setIsEditedFlag(true));
    };

    const onUploadExcelNextClick = async () => {
        // on next click of excel upload, call API to get product data
        const formData = new FormData();
        formData.append("file", uploadedExcel[0]?.file);
        const payload = {
            file: formData,
            event_id: promoDetails?.event_id || null,
        }
        const responseFlag = await dispatch(getProductDataFromFile(payload));
        // if product data is not fetched, reset uploaded excel data
        if (!responseFlag) {
            setUploadedExcel([]);
        }
        dispatch(setIsEditedFlag(true));
    };

    const onClearFilter = () => {
        // clear filters data for product configuration
        dispatch(
            resetAllFiltersData({
                from: "CREATE_OFFER_SPECIFIC_PRODUCT_COMPONENT",
            })
        );
    };

    const onWholeCatClearFilter = () => {
        // clear filters data for product configuration
        dispatch(
            resetAllFiltersData({
                from: "CREATE_OFFER_WHOLE_CATEGORY_COMPONENT",
            })
        );
    };

    const onProdGroupClearFilter = () => {
        // clear filters data for product configuration
        dispatch(
            resetAllFiltersData({
                from: "CREATE_OFFER_PRODUCT_GROUP_COMPONENT",
            })
        );
    };

    const excelDownloadHandler = () => {
        // download excel template for product selection
        const payload = {
            type: "promo_product_upload",
        };
        dispatch(
            excelTemplateDownload(
                {
                    params: payload,
                    responseType: "blob",
                },
                "product_selection_template"
            )
        );
    };

    const onCopyPasteChange = (e) => {
        // set copy paste data in state
        const data = e.target.value;
        let array = _.filter(data.split("\n"), (item) => item !== "");
        let val = array.join(",");
        setCopyPasteData(val);
    };

    const onCopyPasteSubmitClick = () => {
        // on submit click of copy paste, call API to get product data
        let productIds = copyPasteData.split(",");
        productIds = _.map(productIds, (value) => value.trim());
        productIds = _.filter(productIds, (item) => item !== "").map(item => [...item.split(" ")]);
        const payload = {
            products_data: productIds,
            event_id: promoDetails?.event_id || null,
        };

        dispatch(getProductFromIds(payload));
        dispatch(setIsEditedFlag(true));
        setCopyPasteData("");
    };

    const onRowSelection = useCallback((context) => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // set selected products data
        const selectedRows = productTableRef.current.api.getSelectedRows();
        props?.setSelectedProducts(_.cloneDeep(selectedRows));

        // Added this source check to avoid calling setIsEditedFlag on apiSelectAll, as that is called by us through AG Grid API on first load
        if (context.source !== "apiSelectAll") dispatch(setIsEditedFlag(true));
    });

    const rowDataChanged = () => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // whenever row data updates in AG Grid, select all rows
        if (productTableRef?.current?.api) productTableRef.current.api.selectAll();
    };

    const rowDataChangedProductGroup = () => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // whenever row data updates in AG Grid, select all rows
        if (productGroupTableRef?.current?.api) productGroupTableRef.current.api.selectAll();
    };

    const onRowSelectionProductGroup = useCallback((context) => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // set selected products data
        const selectedRows = productGroupTableRef.current.api.getSelectedRows();
        props?.setSelectedProductGroups(_.cloneDeep(selectedRows));

        // Added this source check to avoid calling setIsEditedFlag on apiSelectAll, as that is called by us through AG Grid API on first load
        if (context.source !== "apiSelectAll") dispatch(setIsEditedFlag(true));
    }, []);

    const onFilterTextBoxChanged = useCallback((text, forTable) => {
        // on filter text box change, set global search text and filter data
        setGlobalSearchText(text);
        if (forTable === "product_group") {
            productGroupTableRef.current.api.setGridOption("quickFilterText", text);
        } else
            productTableRef.current.api.setGridOption("quickFilterText", text);
    }, []);

    return (
        <div>
            <p className="text-12-500">Apply these product settings to</p>
            <div className="marginTop-20">
                <ButtonGroup
                    selectedOption={props.selectedProductType || "sitewide"}
                    onChange={onProductTypeSelectionChange}
                    options={productSelectOptions}
                />
            </div>
            {
                props.selectedProductType === "whole_category" &&
                <div className="offer-container-background marginTop-16 padding-24">
                    <div className="flexColumn flex24">
                        <span className="text-14-800">Select Filter</span>
                        <ComponentFilters
                            filterConfig={modifiedFilterConfig}
                            callAPIonLoad={false}
                            screen="CREATE_OFFER_WHOLE_CATEGORY_COMPONENT"
                            onSecondaryButtonClick={onWholeCatClearFilter}
                            secondaryButtonText={props?.allowPromoEdit ? "Clear Filters" : null}
                            isDisabled={props?.isRestricted || !props?.allowPromoEdit}
                        />
                    </div>
                </div>
            }
            {
                props.selectedProductType === "specific_products" &&
                <div className="offer-container-background marginTop-16">
                    {!props.isRestricted && (
                        <div className="padding-16">
                            <Tabs
                                value={activeTab}
                                onChange={(event, val) => tabChangeHandler(event, val)}
                                tabNames={tabOptions}
                                tabPanels={[
                                    <div key="filter" className="flexColumn flex24">
                                        <span className="text-14-800">Select Filter</span>
                                        <ComponentFilters
                                            filterConfig={modifiedFilterConfig}
                                            callAPIonLoad={false}
                                            screen="CREATE_OFFER_SPECIFIC_PRODUCT_COMPONENT"
                                            onPrimaryButtonClick={onSpecificProdFiltersApply}
                                            onSecondaryButtonClick={onClearFilter}
                                            primaryButtonText={props?.allowPromoEdit ? "Submit" : null}
                                            secondaryButtonText={props?.allowPromoEdit ? "Clear Filters" : null}
                                            isDisabled={props?.isRestricted || !props?.allowPromoEdit}
                                        />
                                    </div>,
                                    <FileUpload
                                        key="fileUpload"
                                        fileList={uploadedExcel}
                                        numberOfFiles={1}
                                        onFileListChange={setUploadedExcel}
                                        onPrimaryButtonClick={onUploadExcelNextClick}
                                        onSecondaryButtonClick={() => setUploadedExcel([])}
                                        primaryButtonLabel="Next"
                                        secondaryButtonLabel="Cancel"
                                        validFileTypes={[
                                            {
                                                fileType: "xlsx",
                                                templateDownloader: excelDownloadHandler,
                                                typeOverride: false,
                                            },
                                        ]}
                                        disabled={props?.isRestricted || !props?.allowPromoEdit}
                                    />,
                                    <div key="copy_paste">
                                        <TextArea
                                            onChange={onCopyPasteChange}
                                            placeholder="Paste IDs here..."
                                            value={copyPasteData}
                                            width={"80vw"}
                                        />
                                        <div className="buttons_container">
                                            <Button
                                                onClick={() => setCopyPasteData("")}
                                                size="large"
                                                variant="url"
                                            >
                                                Reset
                                            </Button>
                                            <Button
                                                onClick={onCopyPasteSubmitClick}
                                                size="large"
                                                variant="primary"
                                                disabled={!copyPasteData.length || props?.isRestricted || !props?.allowPromoEdit}
                                            >
                                                Submit
                                            </Button>
                                        </div>
                                    </div>
                                ]}
                            />
                        </div>
                    )}
                    <div className="padding-16">
                        {productTableData?.length > 0 ?
                            <Table
                                tableHeader={"Products"}
                                ref={productTableRef}
                                suppressMenuHide
                                rowData={productTableData}
                                columnDefs={productTableDef}
                                rowSelection="multiple"
                                onSelectionChanged={onRowSelection}
                                onRowDataUpdated={rowDataChanged}
                                suppressRowClickSelection={props?.isRestricted || !props?.allowPromoEdit}
                                topRightOptions={
                                    <div className="centerFlexWithGap12">
                                        <div className="positionRelative">
                                            {showGlobalSearch ? (
                                                <div className="tableGlobalSearchContainer">
                                                    <Input
                                                        onChange={(e) =>
                                                            onFilterTextBoxChanged(
                                                                e.target.value,
                                                                "product"
                                                            )
                                                        }
                                                        placeholder="Search"
                                                        rightIcon={
                                                            <img
                                                                src={SearchIcon}
                                                                alt="search"
                                                            />
                                                        }
                                                        type="text"
                                                        value={globalSearchText}
                                                    />
                                                </div>
                                            ) : null}
                                            <Button
                                                iconPlacement="left"
                                                icon={<img src={SearchIcon} alt="search" />}
                                                onClick={() =>
                                                    setShowGlobalSearch(
                                                        (prev) => !prev
                                                    )
                                                }
                                                size="large"
                                                variant="tertiary"
                                            />
                                        </div>
                                    </div>
                                }
                                topLeftOptions={
                                    <Badge
                                        color="default"
                                        label={`Selected Products: ${props?.selectedProducts?.length} / ${productTableData.length}`}
                                        size="default"
                                        variant="subtle"
                                    />
                                }
                            /> :
                            (
                                <EmptyData text="Please select filters to view Products" />
                            )
                        }
                    </div>
                </div>
            }
            {
                props.selectedProductType === "product_group" &&
                <div className="offer-container-background marginTop-16 padding-24">
                    <div className="flexColumn flex24">
                        {!props.isRestricted && (
                            <div>
                                <span className="text-14-800">Select Filter</span>
                                <ComponentFilters
                                    filterConfig={modifiedFilterConfig}
                                    callAPIonLoad={false}
                                    screen="CREATE_OFFER_PRODUCT_GROUP_COMPONENT"
                                    onSecondaryButtonClick={onProdGroupClearFilter}
                                    onPrimaryButtonClick={onProdGroupFiltersApply}
                                    primaryButtonText={props?.allowPromoEdit ? "Submit" : null}
                                    secondaryButtonText={props?.allowPromoEdit ? "Clear Filters" : null}
                                />
                            </div>
                        )}
                        <div className="padding-16">
                            {productGroupData.length ?
                                <Table
                                    tableHeader={"Product Groups"}
                                    ref={productGroupTableRef}
                                    suppressMenuHide
                                    rowData={productGroupData}
                                    columnDefs={productGroupTableDef}
                                    rowSelection="multiple"
                                    onSelectionChanged={onRowSelectionProductGroup}
                                    suppressRowClickSelection={props?.isRestricted || !props?.allowPromoEdit}
                                    onRowDataUpdated={rowDataChangedProductGroup}
                                    topRightOptions={
                                        <div className="centerFlexWithGap12">
                                            <div className="positionRelative">
                                                {showGlobalSearch ? (
                                                    <div className="tableGlobalSearchContainer">
                                                        <Input
                                                            onChange={(e) =>
                                                                onFilterTextBoxChanged(
                                                                    e.target.value,
                                                                    "product_group"
                                                                )
                                                            }
                                                            placeholder="Search"
                                                            rightIcon={
                                                                <img
                                                                    src={SearchIcon}
                                                                    alt="search"
                                                                />
                                                            }
                                                            type="text"
                                                            value={globalSearchText}
                                                        />
                                                    </div>
                                                ) : null}
                                                <Button
                                                    iconPlacement="left"
                                                    icon={<img src={SearchIcon} alt="search" />}
                                                    onClick={() =>
                                                        setShowGlobalSearch(
                                                            (prev) => !prev
                                                        )
                                                    }
                                                    size="large"
                                                    variant="tertiary"
                                                />
                                            </div>
                                        </div>
                                    }
                                    topLeftOptions={
                                        <Badge
                                            color="default"
                                            label={`Selected Product Groups: ${props?.selectedProductGroups?.length} / ${productGroupData.length}`}
                                            size="default"
                                            variant="subtle"
                                        />
                                    }
                                /> :
                                <EmptyData text="Please select filters to view products groups" />
                            }
                        </div>
                    </div>
                </div>
            }
            <Prompt
                handleClose={() => {
                    setShowDataOverridePrompt(false);
                }}
                onPrimaryButtonClick={onRetainClick}
                onSecondaryButtonClick={onReplaceAndOverwriteClick}
                primaryButtonLabel="Retain"
                secondaryButtonLabel="Replace & overwrite"
                title="Confirm Selection"
                variant="warning"
                isOpen={showDataOverridePrompt}
            >
                Do you want to Retain and Continue or Replace table with current
                selection
            </Prompt>

            <Modal
                onClose={() => {
                    setShowInvalidDataModal(false);
                }}
                onPrimaryButtonClick={onActiveInactiveProductProceed}
                onSecondaryButtonClick={onActiveProductProceed}
                primaryButtonLabel="Proceed with all Active and Inactive Products"
                secondaryButtonLabel="Proceed with only Active Products"
                size="medium"
                title="Products Detail"
                open={showInvalidDataModal}
            >
                <div className="invalidProductsGrpModalContainer">
                    <span className="secondaryText-14-500">
                        Based on the data feed Pricesmart received, the uploaded
                        list contains some inactive/invalid SKUs
                    </span>
                    <Table
                        tableHeader={"Products"}
                        suppressMenuHide
                        rowData={invalidTableData}
                        columnDefs={invalidDataColumnConfig}
                    />
                </div>
            </Modal>

            <Modal
                onClose={() => setShowOutofScopeDataModal(false)}
                size="medium"
                title="Products Detail"
                open={showOutofScopeDataModal}
            >
                <div className="invalidProductsGrpModalContainer">
                    <span className="secondaryText-14-500">
                        Based on the data feed Pricesmart received, the uploaded
                        list contains out of the scope SKUs
                    </span>
                    <Table
                        tableHeader={"Products"}
                        suppressMenuHide
                        rowData={productDataFromUploadOrCopyPaste?.out_of_scope}
                        columnDefs={invalidDataColumnConfig}
                    />
                </div>
            </Modal>

        </div>
    )
}

export default ProductSelection;