import React, {
    useState,
    useEffect,
} from "react";
import { useSelector, useDispatch } from "react-redux";

import _ from "lodash";
import {
    Select,
    Accordion,
    Panel,
    But<PERSON>,
    Tag,
    Badge
} from "impact-ui";

import ProductIcon from "../../../assets/imageAssets/box.png";
import CustomerIcon from "../../../assets/imageAssets/customer.png";
import StoreIcon from "../../../assets/imageAssets/store.png";
import ExcludeIcon from "../../../assets/imageAssets/exclude-icon.svg?.url";

import {
    getCustomerTypes,
    getChannelTypes,
    setProductData,
    setProducGrouptData,
    setStoreData,
    setStoreGroupData,
    setSelectedPromoProducts,
    setSelectedPromoProductGroups,
    setSelectedPromoStores,
    setSelectedPromoStoreGroups,
    savePromoStep1,
    getProductConfigs,
    getStoreConfigs,
    editPromoStep1,
    getExclsionData,
    setIsEditedFlag,
    getStep1Basics,
    getStep1Details,
    getPromoEventDetails,
    updatePromoDetails,
    setPromoEventDetails,
    setProductDataFromUploadOrCopyPaste,
    setStoreDataFromUploadOrCopyPaste
} from "../../../store/features/promoReducer/promoReducer";

import StoreSelection from "./StoreSelection";
import ProductSelection from "./ProductSelection";
import { productSelectionOptions, storeSelectionOptions, productExclusionTypeIds } from "./OfferConstants";
import SpecficProductSelection from "../marketingCalendar/createEvent/SpecficProductSelection";
import { resetAllFiltersData, setSelectedFilters } from "../../../store/features/filters/filters";
import { toastError } from "../../../store/features/global/global";
import { global_labels } from "../../../constants/Constants";
import { capitalizeFirstLetter } from "../../../utils/helpers/utility_helpers";

const ProductStore = props => {
    const dispatch = useDispatch();
    const { global_configs } = useSelector((store) => store?.pricesmartPromoReducer.global);

    const [selectedProductType, setSelectedProductType] = useState("whole_category");
    const [selectedStoreType, setSelectedStoreType] = useState("all_stores");
    const [channelType, setChannelType] = useState(null);
    const [customerType, setCustomerType] = useState(null);
    const [isCustomerOpen, setIsCustomerOpen] = useState(false)
    const [isChannelOpen, setIsChannelOpen] = useState(false)
    const [isPanelOpen, setIsPanelOpen] = useState(false);
    const [selectedSpecificProductType, setSelectedSpecificProductType] = useState("hierarchy");
    const [selectedExclusionSpecificProductType, setSelectedExclusionSpecificProductType] = useState(null);
    const [selectedProductGroups, setSelectedProductGroups] = useState([]);
    const [selectedProducts, setSelectedProducts] = useState([]);
    const [specificStoreSelection, setSpecificStoreSelection] = useState("hierarchy");
    const [selectedStores, setSelectedStores] = useState([]);
    const [selectedStoreGroups, setSelectedStoreGroups] = useState([]);
    const [selectedExclusionProducts, setSelectedExclusionProducts] = useState([]);
    const [selectedTempExclusionProducts, setSelectedTempExclusionProducts] = useState([]);
    const [selectedExclusionProductGroups, setSelectedExclusionProductGroups] = useState([]);
    const [selectedTempExclusionProductGroups, setSelectedTempExclusionProductGroups] = useState([]);

    const [productSelectionRestricted, setProductSelectionRestricted] = useState(false);
    const [storeSelectionRestricted, setStoreSelectionRestricted] = useState(false);
    const [disabledExclusionOptions, setDisabledExclusionOptions] = useState([]);
    const [productConfigs, setProductConfigs] = useState([]);
    const [storeConfigs, setStoreConfigs] = useState([]);
    const [isExclusionAdded, setIsExclusionAdded] = useState(false);
    const [disabledStoreOptions, setDisabledStoreOptions] = useState([]);
    const [exclusionHierarchy, setExclusionHierarchy] = useState({});
    const [validDataForExclusion, setValidDataForExclusion] = useState([]);
    const [expandedAccodionStates, setExpandedAccodionStates] = useState([
        "product-selection",
    ]);
    const [filteredOptions, setFilteredOptions] = useState({});


    const {
        customerOptions = [],
        channelOptions = [],
        promoDetails = {},
        promoEventDetails = {},
        isEdited,
        activePromoId,
        productData = [],
        productGroupData = [],
        storeData = [],
        storeGroupData = []
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const {
        filtersData = {}
    } = useSelector((store) => store?.pricesmartPromoReducer.filters);

    useEffect(() => {
        dispatch(getCustomerTypes());
        dispatch(getChannelTypes());
        loadProductAndStoreConfigs();
        return () => {
            clearAll();
        }
    }, []);

    useEffect(() => {
        if (props?.activeStep === props?.stepNumber) {
            if (promoDetails?.event_id && global_configs?.event?.use_event) {
                dispatch(getPromoEventDetails({
                    event_id: promoDetails?.event_id
                }));
            }
        }
    }, [promoDetails?.event_id, props?.activeStep, global_configs?.event?.use_event]);

    useEffect(() => {
        if (activePromoId && props?.activeStep === props?.stepNumber) {
            dispatch(getStep1Basics(activePromoId));
            dispatch(getStep1Details(activePromoId));
        }
    }, [activePromoId, props?.activeStep]);

    useEffect(() => {
        if (props.activeStep !== props.stepNumber) {
            clearAll();
        }
    }, [props?.activeStep])

    const clearAll = () => {
        dispatch(setSelectedPromoProducts([]));
        dispatch(setSelectedPromoProductGroups([]));
        dispatch(setSelectedPromoStores([]));
        dispatch(setSelectedPromoStoreGroups([]));
        dispatch(setProductData([]));
        dispatch(setProducGrouptData([]));
        dispatch(setStoreData([]));
        dispatch(setStoreGroupData([]));
        dispatch(setProductDataFromUploadOrCopyPaste([]));
        dispatch(setStoreDataFromUploadOrCopyPaste([]));
        dispatch(updatePromoDetails({
            product_details: [],
            store_details: [],
            product_group_details: [],
            store_group_details: [],
            product_selections: [],
            store_selections: [],
            customer_selections: [],
            product_exclusions: {}
        }));
        dispatch(setPromoEventDetails({}));
        dispatch(setIsEditedFlag(false));
        dispatch(resetAllFiltersData({ from: "CREATE_OFFER_WHOLE_CATEGORY_COMPONENT" }));
        dispatch(resetAllFiltersData({ from: "CREATE_EVENT_SPECIFIC_PRODUCT" }));
        dispatch(resetAllFiltersData({ from: "CREATE_OFFER_PRODUCT_GROUP_COMPONENT" }));
        dispatch(resetAllFiltersData({ from: "CREATE_OFFER_STORE_GROUP_COMPONENT" }));
        dispatch(resetAllFiltersData({ from: "CREATE_OFFER_SPECIFIC_STORES_COMPONENT" }));
        dispatch(resetAllFiltersData({ from: "CREATE_OFFER_SPECIFIC_PRODUCT_COMPONENT" }));
        setSelectedExclusionProductGroups([]);
        setSelectedExclusionProducts([]);
        setSelectedExclusionSpecificProductType(null);
        setExclusionHierarchy({});
        setValidDataForExclusion([]);
        setSelectedTempExclusionProducts([]);
        setSelectedTempExclusionProductGroups([]);
    }

    useEffect(() => {
        if (!_.isEmpty(promoEventDetails)) {
            handleProductRestriction(promoEventDetails.product_restriction);
            handleStoreRestriction(promoEventDetails.store_restriction);
        }
    }, [promoEventDetails]);

    // Handle product selections
    useEffect(() => {
        if (!_.isEmpty(promoDetails?.product_selections)) {
            handleProductSelection(promoDetails.product_selections);
        }
    }, [promoDetails?.product_selections]);

    // Handle store selections
    useEffect(() => {
        if (!_.isEmpty(promoDetails?.store_selections)) {
            handleStoreSelection(promoDetails.store_selections);
        }
    }, [promoDetails?.store_selections]);

    // Handle customer selections
    useEffect(() => {
        if (!_.isEmpty(promoDetails?.customer_selections)) {
            handleCustomerSelection(promoDetails.customer_selections);
        }
    }, [promoDetails?.customer_selections]);


    useEffect(() => {
        if (!_.isEmpty(promoDetails?.product_exclusions)) {
            const productExclusionType = productExclusionTypeIds[promoDetails?.exclusion_selection_type];
            setSelectedExclusionSpecificProductType(productExclusionType);

            if (productExclusionType === "product_group") {
                const exEventPgIds = promoEventDetails?.product_exclusion?.product_groups?.map(item => item?.product_group_id) || [];

                const commonExclusionPgIds = _.cloneDeep(promoDetails?.product_exclusions?.product_groups?.filter(item => exEventPgIds.includes(item?.product_group_id)) || []);
                const notCommonExclusionPgIds = _.cloneDeep(promoDetails?.product_exclusions?.product_groups?.filter(item => !exEventPgIds.includes(item?.product_group_id)) || []);

                commonExclusionPgIds.forEach(item => item["is_excluded_in_event"] = true);

                setSelectedExclusionProductGroups(commonExclusionPgIds.concat(notCommonExclusionPgIds));
            } else {
                props?.allowPromoEdit && setDisabledExclusionOptions([]);
            }

            if (productExclusionType === "upload") {
                setValidDataForExclusion(promoDetails?.product_exclusions?.upload_details);
            } else if (productExclusionType === "hierarchy") {
                setExclusionHierarchy(promoDetails?.product_exclusions?.product_hierarchy?.[0]);
            } else if (productExclusionType === "copy_paste") {
                setSelectedExclusionProducts(promoDetails?.product_exclusions?.product_ids);
            }
        }
    }, [promoDetails?.product_exclusions])

    useEffect(() => {
        if (props?.allowPromoEdit) {
            if (!_.isEmpty(promoDetails?.product_exclusions)) {
                if (promoEventDetails?.product_exclusion === "product_group") {
                    setDisabledExclusionOptions(["hierarchy", "upload", "copy_paste"]);
                } else {
                    setDisabledExclusionOptions([]);
                }
            } else {
                // if allowPromoEdit is true, and product exclusions are not present, then we need to show all the options
                setDisabledExclusionOptions([]);
            }
        } else {
            const availableOptions = ["hierarchy", "upload", "copy_paste", "product_group"];
            setDisabledExclusionOptions(availableOptions.filter(item => item !== promoDetails?.product_exclusions?.product_exclusion_level));
        }
    }, [props?.allowPromoEdit])
    

    useEffect(() => {
        dispatch(setSelectedPromoProducts(selectedProducts));
    }, [selectedProducts])

    useEffect(() => {
        dispatch(setSelectedPromoProductGroups(selectedProductGroups));
    }, [selectedProductGroups]);

    useEffect(() => {
        dispatch(setSelectedPromoStores(selectedStores));
    }, [selectedStores]);

    useEffect(() => {
        dispatch(setSelectedPromoStoreGroups(selectedStoreGroups));
    }, [selectedProductGroups]);

    useEffect(() => {
        setIsExclusionAdded(
            selectedExclusionProducts?.length > 0 ||
            selectedExclusionProductGroups?.length > 0 ||
            !_.isEmpty(exclusionHierarchy) ||
            !_.isEmpty(validDataForExclusion)
        );
    }, [selectedExclusionProducts, selectedExclusionProductGroups, exclusionHierarchy, validDataForExclusion]);

    useEffect(() => {
        if (customerOptions?.length) {
            const defaultCustomerType = customerOptions?.find(item => item?.value === 1) || {};
            setCustomerType(defaultCustomerType);
        }
    }, [customerOptions]);

    useEffect(() => {
        if (channelOptions?.length) {
            const defaultChannelType = channelOptions?.find(item => item?.value === 0) || {};
            setChannelType(defaultChannelType);
        }
    }, [channelOptions]);

    useEffect(() => {
        if (!_.isEmpty(exclusionHierarchy) && isPanelOpen) {
            Object.keys(exclusionHierarchy || {}).forEach((key) => {
                dispatch(setSelectedFilters({
                    data: exclusionHierarchy[key],
                    filterId: key,
                    from: "CREATE_EVENT_SPECIFIC_PRODUCT",
                }));
            });
        }
    }, [isPanelOpen]);

    const loadProductAndStoreConfigs = async () => {
        const productConfigs = await dispatch(getProductConfigs());
        const storeConfigs = await dispatch(getStoreConfigs());
        setProductConfigs(productConfigs);
        setStoreConfigs(storeConfigs);
    }

    const handleProductRestriction = (product_restriction) => {
        // to handle product restriction
        if (!_.isEmpty(product_restriction)) {
            if (product_restriction?.lock) setProductSelectionRestricted(true);
            setSelectedProductType(product_restriction?.product_restriction_level || "sitewide");
            setSelectedSpecificProductType(product_restriction?.specific_product_type || "hierarchy");
            if (product_restriction?.product_restriction_level == "whole_category") {
                Object.keys(product_restriction?.hierarchy || {}).forEach((key) => {
                    dispatch(
                        setSelectedFilters({
                            data: product_restriction?.hierarchy?.[key] || [],
                            filterId: key,
                            from: "CREATE_OFFER_WHOLE_CATEGORY_COMPONENT",
                        })
                    );
                });
            }

            if (product_restriction?.products && product_restriction?.product_restriction_level == "specific_products") {
                if (product_restriction?.lock) {
                    setSelectedProducts(product_restriction?.products);
                    _.isEmpty(productData) && dispatch(setProductData(product_restriction?.products));
                }
            }

            if (product_restriction?.product_groups && product_restriction?.product_restriction_level == "product_group") {
                if (product_restriction?.lock) {
                    setSelectedProductGroups(product_restriction?.product_groups);
                    _.isEmpty(productGroupData) && dispatch(setProducGrouptData(product_restriction?.product_groups));
                }
            }
        }
    };

    const handleStoreRestriction = (store_restriction) => {
        // to handle store restriction
        if (!_.isEmpty(store_restriction)) {
            if (store_restriction?.lock) setStoreSelectionRestricted(true);
            let storeRestrictionLevel = store_restriction?.store_restriction_level || "all_stores";
            if (store_restriction?.specific_store_type === "store_group") {
                storeRestrictionLevel = "specific_stores";
            }
            if (storeRestrictionLevel === "store_group") {
                setSelectedStoreType("specific_stores");
                setSpecificStoreSelection("store_group");
            } else {
                setSelectedStoreType(storeRestrictionLevel);
                setSpecificStoreSelection(store_restriction?.specific_store_type || "hierarchy");
            }
            if (store_restriction?.stores) {
                if (store_restriction?.lock) {
                    setSelectedStores(store_restriction?.stores);
                    _.isEmpty(storeData) && dispatch(setStoreData(store_restriction?.stores));
                }
            }
            if (store_restriction?.store_groups) {
                if (store_restriction?.lock) {
                    setSelectedStoreGroups(store_restriction?.store_groups);
                    _.isEmpty(storeGroupData) && dispatch(setStoreGroupData(store_restriction?.store_groups));
                }
            }
        }
    };

    const channelTypeChangeHandler = (selectedOptions) => {
        // to handle channel type change
        // for bnm_stores, specific_stores, store_group we need to check if the event has store_restriction
        // if it has then App Only Promotion is not allowed
        const arr = ["bnm_stores", "specific_stores", "store_group"];
        if (
            selectedOptions?.value === 1 &&
            arr.includes(promoEventDetails?.store_restriction?.store_restriction_level)
        ) {
            dispatch(toastError(`This distribution channel is not allowed by the ${global_labels.event_primary}`)); 
            return;
        }
        setChannelType(selectedOptions);
        if (!promoEventDetails.store_restriction?.lock) {
            if (selectedOptions?.value === 1) {
                setDisabledStoreOptions(["all_stores", "bnm_stores", "specific_stores"]);
                setSelectedStoreType("ecom_stores");
            } else {
                setDisabledStoreOptions([]);
                setSelectedStoreType(
                    promoDetails?.store_selections?.store_selection_type ||
                    promoEventDetails?.store_restriction?.store_restriction_level ||
                    "all_stores"
                );
            }
        }
        dispatch(setIsEditedFlag(true));
    }

    const customerTypeChangeHandler = (selectedOptions) => {
        // to handle customer type change
        setCustomerType(selectedOptions);
        dispatch(setIsEditedFlag(true));
    }

    const handleApply = () => {
        // to handle apply button click
        const existing_ex_pids = !_.isEmpty(promoDetails?.product_exclusions?.product_ids)
            ? promoDetails?.product_exclusions?.product_ids?.map(item => item.product_id)
            : [];
        const existing_ex_pgids = !_.isEmpty(promoDetails?.product_exclusions?.product_groups)
            ? promoDetails?.product_exclusions?.product_groups?.map(item => item.product_group_id)
            : [];
        const ex_pids = selectedTempExclusionProducts?.map(item => item.product_id) || [];
        const ex_pgids = selectedTempExclusionProductGroups?.map(item => item.product_group_id) || [];

        const hierarchy = {};

        if (selectedExclusionSpecificProductType !== productExclusionTypeIds[promoDetails?.exclusion_selection_type]) {
            dispatch(setIsEditedFlag(true));
        }
        if (selectedExclusionSpecificProductType === "hierarchy") {
            Object.keys(filtersData?.["CREATE_EVENT_SPECIFIC_PRODUCT"] || {}).forEach((key) => {
                hierarchy[key] = filtersData?.["CREATE_EVENT_SPECIFIC_PRODUCT"]?.[key]?.selectedOptions || [];
            });
        }

        const isHierarchyEmpty = Object.values(hierarchy).every(i => _.isEmpty(i));

        if (!isHierarchyEmpty) {
            setIsExclusionAdded(true);
            setExclusionHierarchy(hierarchy);
            dispatch(setIsEditedFlag(true));
            if (
                (_.isEmpty(promoDetails?.product_exclusions?.product_hierarchy)) ||
                (!_.isEqual(promoDetails?.product_exclusions?.product_hierarchy, hierarchy))
            ) {
                dispatch(setIsEditedFlag(true));
            }
        } else {
            if (!_.isEmpty(promoDetails?.product_exclusions?.product_hierarchy)) {
                dispatch(setIsEditedFlag(true));
            }
            setExclusionHierarchy({});
        }

        if (
            (selectedExclusionSpecificProductType !== "product_group" && !_.isEqual(existing_ex_pids, ex_pids)) ||
            (selectedExclusionSpecificProductType === "product_group" && !_.isEqual(existing_ex_pgids, ex_pgids))
        ) {
            selectedExclusionSpecificProductType !== "product_group" && setSelectedExclusionProducts(selectedTempExclusionProducts);
            selectedExclusionSpecificProductType === "product_group" && productGroupExclusionSelectionHandler(selectedTempExclusionProductGroups);
            dispatch(setIsEditedFlag(true));
        }

        if (
            (selectedExclusionSpecificProductType != "hierarchy" && _.isEmpty(ex_pids) && _.isEmpty(ex_pgids)) ||
            (selectedExclusionSpecificProductType === "hierarchy" && isHierarchyEmpty)
         ) {
            setSelectedExclusionSpecificProductType(null);
            setExclusionHierarchy({});
            setSelectedExclusionProducts([]);
            setSelectedExclusionProductGroups([]);
            setIsExclusionAdded(false);
        }

        setIsPanelOpen(false);
    }

    const handleCancel = () => {
        // to handle reset button click
        setIsPanelOpen(false);
        if (props?.allowPromoEdit) {
            setSelectedExclusionProductGroups([]);
            setSelectedExclusionProducts([]);
            setSelectedExclusionSpecificProductType(null);
            setExclusionHierarchy({});
            setValidDataForExclusion(null);
            setSelectedTempExclusionProducts([]);
            setSelectedTempExclusionProductGroups([]);
            setIsExclusionAdded(false);
            dispatch(setIsEditedFlag(true));
        }
    }

    const handleProductSelection = (product_selections) => {
        if (!_.isEmpty(product_selections)) {
            setSelectedProductType(
                product_selections?.product_selection_type
                || promoEventDetails?.product_restriction?.product_restriction_level
                || "sitewide"
            );
            setSelectedSpecificProductType(product_selections?.product_selection_sub_type || "hierarchy");
            if (product_selections?.product_selection_type === "specific_products") {
                if (!_.isEmpty(promoDetails?.product_details) && !promoEventDetails?.product_restriction?.lock) {
                    setSelectedProducts(promoDetails?.product_details || []);
                    dispatch(setProductData(promoDetails?.product_details || []));
                }
            } else if (product_selections?.product_selection_type === "product_group") {
                if (!_.isEmpty(promoDetails?.product_group_details) && !promoEventDetails?.product_restriction?.lock) {
                    setSelectedProductGroups(promoDetails?.product_group_details || []);
                    dispatch(setProducGrouptData(promoDetails?.product_group_details || []));
                }
            }
            setIsExclusionAdded(product_selections?.is_exclusion_added);

            if (product_selections?.is_exclusion_added) {
                dispatch(getExclsionData({
                    promo_id: promoDetails?.promo_id
                }));
            }

            if (product_selections?.product_selection_type === "whole_category") {
                Object.keys(product_selections?.product_hierarchy || {}).forEach((key) => {
                    dispatch(
                        setSelectedFilters({
                            data: product_selections?.product_hierarchy?.[key] || [],
                            filterId: key,
                            from: "CREATE_OFFER_WHOLE_CATEGORY_COMPONENT",
                        })
                    );
                });
            }
        }
    }

    const handleStoreSelection = (store_selections) => {
        if (!_.isEmpty(store_selections)) {
            let storeRestrictionLevel = store_selections?.store_selection_type || promoEventDetails?.store_restriction?.store_restriction_level || "all_stores";
            let specificStoreSelection = store_selections?.store_selection_sub_type || "hierarchy";
            if (storeRestrictionLevel === "store_group") {
                storeRestrictionLevel = "specific_stores";
                specificStoreSelection = "store_group";
            }
            setSelectedStoreType(storeRestrictionLevel);
            setSpecificStoreSelection(specificStoreSelection);
            if (specificStoreSelection !== "store_group") {
                if (!_.isEmpty(promoDetails?.store_details) && !promoEventDetails?.store_restriction?.lock) {
                    setSelectedStores(promoDetails?.store_details || []);
                    dispatch(setStoreData(promoDetails?.store_details || []));
                }
            } else {
                if (!_.isEmpty(promoDetails?.store_group_details) && !promoEventDetails?.store_restriction?.lock) {
                    setSelectedStoreGroups(promoDetails?.store_group_details || []);
                    dispatch(setStoreGroupData(promoDetails?.store_group_details || []));
                }
            }
        }
    }

    const handleCustomerSelection = (customer_selections) => {
        if (!_.isEmpty(customer_selections)) {
            customer_selections?.customer_type?.value && setCustomerType(customer_selections?.customer_type);
            customer_selections?.offer_distribution_channel?.value && setChannelType(customer_selections?.offer_distribution_channel);
        }
    }

    if (props.activeStep !== props.stepNumber) {
        return "";
    }

    const productGroupExclusionSelectionHandler = (selectedProductGroups) => {
        const arr = [];
        const selectedExclusionProductGroupsArr = selectedExclusionProductGroups?.filter(item => !item?.is_excluded_in_event);
        if (_.isEqual(selectedProductGroups, selectedExclusionProductGroupsArr)) return;
        // the promoEventDetails.product_exclusion?.product_groups is the product groups that are excluded in the event
        // we need to add the event excluded product groups to the selected exclusion product groups to show in the UI that they are excluded by event
        if (!_.isEmpty(promoEventDetails.product_exclusion?.product_groups)) {
            const event_exclusion_product_groups = _.cloneDeep(promoEventDetails.product_exclusion.product_groups);
            event_exclusion_product_groups.forEach((item) => {
                item["is_excluded_in_event"] = true;
            })
            arr.push(...event_exclusion_product_groups);
        }
        arr.push(
            ...selectedProductGroups
        );
        // to handle product group selection
        setSelectedExclusionProductGroups(_.uniqBy(arr, "product_group_id"));
    }

    const handleNext = async () => {
        // to handle next button click
        if (!props?.allowPromoEdit && promoDetails?.step_count === 1) {
            dispatch(toastError(`For this ${global_labels?.promo_primary} data is available till product and store selection`));
            return;
        }
        if ((!isEdited || !props?.allowPromoEdit) && promoDetails?.step_count > 0) {
            props.setActiveStep(props.stepNumber + 1);
            return;
        }

        const payload = {
            promo_id: promoDetails?.promo_id,
            event_id: promoDetails?.event_id,
            product_selections: [],
            store_selections: [],
            product_exclusions: [],
            customer_selections: []
        };

        // product_selections
        {
            let productTypeId = productConfigs.find((config) => {
                if (
                    (selectedProductType == "product_group" && config.product_selection_type == "product_group") ||
                    (selectedProductType == "sitewide" && config.product_selection_type == "sitewide") ||
                    (selectedProductType == "whole_category" && config.product_selection_type == "whole_category")
                )
                    return true;
                return config.product_selection_type === selectedProductType && (config.product_selection_sub_type === (selectedSpecificProductType || "hierarchy"))
            })?.id;

            if (!productTypeId) {
                dispatch(toastError("Please select product type"));
                return;
            }

            const productHierarchy = {};
            if (selectedProductType === "whole_category") {
                const filterData = filtersData?.["CREATE_OFFER_WHOLE_CATEGORY_COMPONENT"] || {};
                Object.keys(filterData)?.forEach((filter) => {
                    productHierarchy[filter] = filterData[filter]?.selectedOptionsArray;
                });
                if (_.isEmpty(productHierarchy)) {
                    dispatch(toastError("Please select product hierarchy"));
                    return;
                }
            } else if (selectedProductType === "specific_products") {
                const filterData = filtersData?.["CREATE_OFFER_SPECIFIC_PRODUCT_COMPONENT"] || {};
                Object.keys(filterData)?.forEach((filter) => {
                    productHierarchy[filter] = filterData[filter]?.selectedOptionsArray;
                });
            } else if (selectedProductType === "product_group") {
                const filterData = filtersData?.["CREATE_OFFER_PRODUCT_GROUP_COMPONENT"] || {};
                Object.keys(filterData)?.forEach((filter) => {
                    productHierarchy[filter] = filterData[filter]?.selectedOptionsArray;
                });
            }

            const productIds = [];
            if (selectedProductType === "specific_products") {
                selectedProducts.map((product) => productIds.push(product.product_id));
                if (_.isEmpty(productIds)) {
                    dispatch(toastError("Please select some products"));
                    return;
                }
            }

            const productGroupIds = [];
            if (selectedProductType === "product_group") {
                selectedProductGroups.map((productGroup) => productGroupIds.push(productGroup.product_group_id));
                if (_.isEmpty(productGroupIds)) {
                    dispatch(toastError("Please select some product groups"));
                    return;
                }
            }

            payload.product_selections = {
                product_selection_type: productTypeId,
                product_hierarchy: productHierarchy,
                product_ids: productIds,
                product_group_ids: productGroupIds
            }
        }
        // store_selections
        {
            let storeTypeId = storeConfigs.find((config) => {
                if (
                    (selectedStoreType == "all_stores" && config.store_selection_type == "all_stores") ||
                    (selectedStoreType == "bnm_stores" && config.store_selection_type == "bnm_stores") ||
                    (selectedStoreType == "ecom_stores" && config.store_selection_type == "ecom_stores")
                )
                    return true;
                return config.store_selection_type === selectedStoreType && (config.store_selection_sub_type === (specificStoreSelection || "hierarchy"))
            })?.id;

            if (!storeTypeId) {
                dispatch(toastError("Please select store type"));
                return;
            }

            const storeHierarchy = {};
            if (selectedStoreType === "specific_stores") {
                const filterData = filtersData?.["CREATE_OFFER_SPECIFIC_STORES_COMPONENT"] || {};
                Object.keys(filterData)?.forEach((filter) => {
                    storeHierarchy[filter] = filterData[filter]?.selectedOptionsArray;
                });
            } else if (selectedStoreType === "store_groups") {
                const filterData = filtersData?.["CREATE_OFFER_STORE_GROUP_COMPONENT"] || {};
                Object.keys(filterData)?.forEach((filter) => {
                    storeHierarchy[filter] = filterData[filter]?.selectedOptionsArray;
                });
            }

            const storeIds = [];
            const storeGroupIds = [];

            if (!["bnm_stores", "ecom_stores", "all_stores"].includes(selectedStoreType)) {
                if (selectedStoreType === "specific_stores" && specificStoreSelection !== "store_group") {
                    selectedStores.map((store) => storeIds.push(store.store_id));
                    if (_.isEmpty(storeIds)) {
                        dispatch(toastError("Please select some stores"));
                        return;
                    }
                } else if (specificStoreSelection === "store_group") {
                    selectedStoreGroups.map((storeGroup) => storeGroupIds.push(storeGroup.store_group_id));
                    if (_.isEmpty(storeGroupIds)) {
                        dispatch(toastError("Please select some store groups"));
                        return;
                    }
                }
            }


            payload.store_selections = {
                store_selection_type: storeTypeId,
                store_hierarchy: storeHierarchy,
                store_ids: storeIds,
                store_group_ids: storeGroupIds
            }
        }
        // product_exclusions
        {
            const productExclusionTypeNumberIds = {
                hierarchy: 1,
                copy_paste: 2,
                product_group: 3,
                upload: 4
            }

            const productIds = [];
            const productGroupIds = [];
            const hierarchy = {};
            let is_all_empty = false;

            if (productExclusionTypeNumberIds?.[selectedExclusionSpecificProductType]) {
                if (selectedExclusionSpecificProductType !== "product_group" && selectedExclusionSpecificProductType !== "hierarchy" && selectedExclusionSpecificProductType !== "upload") {
                    const arr = selectedExclusionProducts?.length > 0 ? selectedExclusionProducts : promoEventDetails.product_exclusion?.products;
                    arr?.forEach(item => productIds.push(item.product_id));
                    if (_.isEmpty(productIds)) {
                        dispatch(toastError("Please select some products for Exclusion"));
                        return;
                    }
                }

                if (selectedExclusionSpecificProductType === "product_group") {
                    const arr = selectedExclusionProductGroups?.length > 0 ? selectedExclusionProductGroups : promoEventDetails.product_exclusion?.product_groups;
                    arr?.forEach(item => productGroupIds.push(item.product_group_id));
                    if (_.isEmpty(productGroupIds)) {
                        is_all_empty = true;
                        // dispatch(toastError("Please select some product groups for Exclusion"));
                        // return;
                    }
                }

                if (selectedExclusionSpecificProductType == "hierarchy") {
                    const filterData = exclusionHierarchy || {};
                    Object.keys(filterData)?.forEach((filter) => {
                        if (filterData[filter]?.length > 0) {
                            hierarchy[filter] = filterData[filter]?.map(item => item.value);
                        }
                    });
                    if (_.isEmpty(hierarchy)) {
                        is_all_empty = true;
                        // dispatch(toastError("Please select some hierarchy for Exclusion"));
                        // return;
                    }
                }

                if (selectedExclusionSpecificProductType == "upload") {
                    if (_.isEmpty(validDataForExclusion)) {
                        is_all_empty = true;
                        // dispatch(toastError("Please upload some products for Exclusion"));
                        // return;
                    }
                }
            }

            payload.product_exclusions = {
                product_exclusion_type: is_all_empty ? null : productExclusionTypeNumberIds[selectedExclusionSpecificProductType] || null,
                upload_details: selectedExclusionSpecificProductType == "upload" ? validDataForExclusion : [],
                product_ids: productIds,
                product_group_ids: productGroupIds,
                product_hierarchy: hierarchy
            }
        }

        // customer_selections
        if (_.isEmpty(customerType)) {
            dispatch(toastError("Please select customer type"));
            return;
        }

        if (_.isEmpty(channelType)) {
            dispatch(toastError(`Please select ${global_labels?.promo_alias} distribution channel`));
            return;
        }

        payload.customer_selections = {
            customer_type: customerType?.value,
            offer_distribution_channel: channelType?.value,
        }

        let res = false;
        if (promoDetails?.step_count <= 0) {
            res = await dispatch(savePromoStep1(payload));
        } else {
            res = await dispatch(editPromoStep1(payload));
        }
        if (res) {
            props.setActiveStep(props.stepNumber + 1);
        }
    }

    const handleAccordionChange = (e) => {
        if (expandedAccodionStates.includes(e)) {
            setExpandedAccodionStates(expandedAccodionStates.filter(item => item !== e));
        } else {
            setExpandedAccodionStates([...expandedAccodionStates, e]);
        }
    }

    const handleFilterOptions = (filteredOptions,key) => {
        setFilteredOptions(prevState => ({
            ...prevState,
            [key]: filteredOptions
        }));
    }


    return (
        <div >
            <div className="step1-accordian-container margin-20">
                <Accordion
                    isSingleItem={false}
                    expanded={expandedAccodionStates}
                    isMultiExpanded={true}
                    onChange={(e) => {
                        handleAccordionChange(e);
                    }}
                    data={[
                        {
                            content: (
                                <div>
                                    <ProductSelection
                                        selectedProductType={selectedProductType}
                                        setSelectedProductType={setSelectedProductType}
                                        setSelectedProducts={setSelectedProducts}
                                        setSelectedProductGroups={setSelectedProductGroups}
                                        isRestricted={productSelectionRestricted}
                                        selectedSpecificProductType={selectedSpecificProductType}
                                        setSelectedSpecificProductType={setSelectedSpecificProductType}
                                        allowPromoEdit={props?.allowPromoEdit}
                                        selectedProducts={selectedProducts}
                                        selectedProductGroups={selectedProductGroups}
                                    />
                                    <div className="offer-container-background marginTop-16 padding-8 flexContentAround">
                                        <div className="display_flex">
                                            <span className="text-14-500">You have selected {productSelectionOptions.find(item => item.value === selectedProductType)?.label || "-"}  for promotion, you can still exclude if you want</span>
                                            <Button
                                                onClick={() => setIsPanelOpen(true)}
                                                size="small"
                                                variant="url"
                                                className="marginLeft-8"
                                                disabled={!props?.allowPromoEdit && !isExclusionAdded}
                                            >
                                                {isExclusionAdded ? "View excluded" : "Exclude products"}
                                            </Button>
                                        </div>
                                        {isExclusionAdded ? (
                                            <div className="display_flex">
                                                {/* <Tag
                                                label={"20 products excluded"}
                                                onClick={() => { }}
                                                onDelete={() => { }}
                                                size="large"
                                                variant="filled"
                                                className="tag-margin"
                                            /> */}
                                                <Button
                                                    onClick={() => {
                                                        handleCancel();
                                                        dispatch(setIsEditedFlag(true));
                                                    }}
                                                    size="small"
                                                    variant="url"
                                                    className="marginLeft-8"
                                                    disabled={!props?.allowPromoEdit}
                                                >
                                                    Remove excluded products
                                                </Button>
                                            </div>
                                        ) : null}
                                    </div>
                                </div>
                            ),
                            header: (
                                <div className="flexWithGap12">
                                    <img className="label-icon" src={ProductIcon} alt="product" />
                                    <label className="text-16-500" htmlFor="product-selection">
                                        Product settings
                                    </label>
                                    {!expandedAccodionStates.includes("product-selection") ? (
                                        <div className="flexWithGap12">
                                            <Badge
                                                color="info"
                                                label={productSelectionOptions.find(item => item.value === selectedProductType)?.label || "-"}
                                                onClick={() => { }}
                                                variant="stroke"
                                            />
                                            <Badge
                                                color="default"
                                                label={`${promoDetails?.product_selections?.products_count} / ${promoDetails?.product_selections?.total_products_count}`}
                                                onClick={() => { }}
                                                variant="stroke"
                                            />

                                        </div>
                                    ) : null}
                                </div>
                            ),
                            value: "product-selection",
                        },
                        {
                            content: (
                                <div className="customer-selection-container">
                                    <Select
                                        currentOptions={filteredOptions?.["customer"] || customerOptions}
                                        initialOptions={customerOptions}
                                        label="Customer type"
                                        labelOrientation="top"
                                        setSelectedOptions={customerTypeChangeHandler}
                                        setCurrentOptions={(data) => handleFilterOptions(data,"customer")}
                                        placeholder="Select"
                                        isRequired={true}
                                        isWithSearch={true}
                                        isMulti={false}
                                        selectedOptions={customerType}
                                        isOpen={isCustomerOpen}
                                        setIsOpen={setIsCustomerOpen}
                                        isCloseWhenClickOutside={true}
                                        isDisabled={!props?.allowPromoEdit}
                                    />
                                    <Select
                                        currentOptions={filteredOptions?.["channel"] || channelOptions}
                                        initialOptions={channelOptions}
                                        label={`${capitalizeFirstLetter(global_labels?.promo_alias)} distribution channel`}
                                        labelOrientation="top"
                                        setSelectedOptions={channelTypeChangeHandler}
                                        setCurrentOptions={(data) => handleFilterOptions(data,"channel")}
                                        placeholder="Select"
                                        isRequired={true}
                                        isWithSearch={true}
                                        isMulti={false}
                                        selectedOptions={channelType}
                                        isOpen={isChannelOpen}
                                        setIsOpen={setIsChannelOpen}
                                        isCloseWhenClickOutside={true}
                                        isDisabled={!props?.allowPromoEdit}
                                    />
                                </div>
                            ),
                            header: (
                                <div className="flexWithGap12">
                                    <img className="label-icon" src={CustomerIcon} alt="customer" />
                                    <label className="text-16-500" htmlFor="customer-selection">
                                        Customer settings
                                    </label>
                                    {!expandedAccodionStates.includes("customer-selection") ? (
                                        <div className="flexWithGap12">
                                            <Badge
                                                color="info"
                                                label={customerType?.label || "-"}
                                                onClick={() => { }}
                                                variant="stroke"
                                            />
                                            <Badge
                                                color="default"
                                                label={channelType?.label || "-"}
                                                onClick={() => { }}
                                                variant="stroke"
                                            />
                                        </div>
                                    ) : null}
                                </div>
                            ),
                            value: "customer-selection",
                        },
                        {
                            content: (
                                <StoreSelection
                                    selectedStoreType={selectedStoreType}
                                    setSelectedStoreType={setSelectedStoreType}
                                    setSelectedStoreGroups={setSelectedStoreGroups}
                                    setSelectedStores={setSelectedStores}
                                    setSpecificStoreSelection={setSpecificStoreSelection}
                                    isRestricted={storeSelectionRestricted}
                                    specificStoreSelection={specificStoreSelection}
                                    disabledOptions={disabledStoreOptions}
                                    allowPromoEdit={props?.allowPromoEdit}
                                    selectedStores={selectedStores}
                                    selectedStoreGroups={selectedStoreGroups}
                                />
                            ),
                            header: (
                                <div className="flexWithGap12">
                                    <img className="label-icon" src={StoreIcon} alt="store" />
                                    <label className="text-16-500" htmlFor="store-selection">
                                        Store settings
                                    </label>
                                    {!expandedAccodionStates.includes("store-selection") ? (
                                        <div className="flexWithGap12">
                                            <Badge
                                                color="info"
                                                label={storeSelectionOptions.find(item => item.value === selectedStoreType)?.label || "-"}
                                                onClick={() => { }}
                                                variant="stroke"
                                            />
                                            <Badge
                                                color="default"
                                                label={`${promoDetails?.store_selections?.stores_count} / ${promoDetails?.store_selections?.total_stores_count}`}
                                                onClick={() => { }}
                                                variant="stroke"
                                            />
                                        </div>
                                    ) : null}
                                </div>
                            ),
                            value: "store-selection",
                        }
                    ]}
                />
            </div>
            <Panel
                anchor="right"
                open={isPanelOpen}
                onClose={() => {
                    setIsPanelOpen(false);
                }}
                onPrimaryButtonClick={handleApply}
                onSecondaryButtonClick={handleCancel}
                primaryButtonLabel={props?.allowPromoEdit ? "Apply" : null}
                secondaryButtonLabel={props?.allowPromoEdit ? "Clear" : "Close"}
                size="large"
                className="offer-exlusion-panel"
                title={
                    <div className="panel-title-with-icon">
                        <img
                            src={ExcludeIcon}
                            alt="Level Filters"
                            className="panel-icon"
                        />
                        <span>Exclude Products</span>
                    </div>
                }
            >
                <SpecficProductSelection
                    eventId={promoDetails?.event_id}
                    includeProductGroup={true}
                    setSelectedProductGroups={setSelectedTempExclusionProductGroups}
                    setSelectedProducts={setSelectedTempExclusionProducts}
                    setSelectedSpecificProductType={setSelectedExclusionSpecificProductType}
                    selectedProducts={selectedExclusionProducts}
                    selectedSpecificProductType={selectedExclusionSpecificProductType}
                    isFromOfferExclusion={true}
                    selectedProductGroups={selectedExclusionProductGroups}
                    setUploadedExcel={setValidDataForExclusion}
                    disabledOptions={disabledExclusionOptions}
                    viewMode={!props?.allowPromoEdit}
                    validData={validDataForExclusion}
                />
            </Panel>
            <div className="footer_section step1-footer">
                <Button
                    onClick={() => { handleNext() }}
                    size="large"
                    variant="primary"
                >
                    Next
                </Button>
            </div>
        </div>
    )
}

export default ProductStore;