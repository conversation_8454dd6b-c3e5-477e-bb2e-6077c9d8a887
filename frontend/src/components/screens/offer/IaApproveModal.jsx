import React from "react";
import { Modal, RadioButtonGroup } from "impact-ui";

export const IaApproveModal = (props) => {


    const {
        showModal,
        closeModal,
        handleIaApprove,
        scenarioData,
        promoDetails
    } = props;

    const excludeIaRecommended = React.useMemo(() => {
        
        let data = scenarioData?.filter((scenario) => scenario?.scenario_name !== "IA Recommended");

        // If no scenarios exist which means no simulation was performed, default to Scenario 1 from promoDetails as fallback
        if (!data?.length) {
            const fallbackScenario = promoDetails?.simulation?.[0]?.scenario_data?.find((scenario) => +scenario?.scenario_order_id === 1);
            data = [{
                scenario_name : fallbackScenario?.scenario_name,
                scenario_id : fallbackScenario?.scenario_id
            }];
        }
        return data;
    }, [scenarioData]);

    const [selectedScenario, setSelectedScenario] = React.useState(excludeIaRecommended[0]?.scenario_id || null);


    return (
        <Modal
            onClose={closeModal}
            onPrimaryButtonClick={() => handleIaApprove(selectedScenario)}
            onSecondaryButtonClick={closeModal}
            primaryButtonLabel="Submit & approve"
            secondaryButtonLabel="Cancel"
            size="small"
            title="Copy and approve"
            open={showModal}
        >
            <div className="ia-approve-modal-content">
                <p className="text-16-800">
                    Select Target Scenario
                </p>
                <p className="text-14-600">
                    Please choose the scenario where you would like to copy the approved <br />
                    IA-recommended values
                </p>
                <RadioButtonGroup
                    name="ia-test-radio-group"
                    onChange={(e) => {
                        setSelectedScenario(e.target.value);
                    }}
                    options={excludeIaRecommended?.map((scenario) => ({
                        label: scenario?.scenario_name,
                        value: scenario?.scenario_id
                    }))}
                    orientation="row"
                    selectedOption={selectedScenario}
                />
            </div>
        </Modal>
    );
};
