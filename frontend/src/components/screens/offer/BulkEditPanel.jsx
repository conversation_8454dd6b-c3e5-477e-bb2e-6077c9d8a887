import React, { useState, useMemo, useEffect } from 'react';
import { Panel, Button, Select, Input, Toast } from 'impact-ui';
import BlueEditIcon from "../../../assets/imageAssets/blueEditIcon.svg?.url";
import { VALUE_LABELS } from './OfferConstants';
import { useSelector, useDispatch } from 'react-redux';
import { getSpecialOfferTypeDetails } from '../../../store/features/promoReducer/promoReducer';
import LoaderComponent from '../../ui/loaderComponent/LoaderComponent';
import { global_labels } from '../../../constants/Constants';

const BulkEditPanel = (props) => {

    const { isPanelOpen, setIsPanelOpen, selectedRows, scenarioData = {}, applyBulkEdit } = props;
    const {
        promoDetails = {},
        validOffers,
        specialOfferTypeData,
        tiersData = [],
        bmsmOfferData = []
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const dispatch = useDispatch();

    const selectvalue = useMemo(() => {
        if (!isPanelOpen || !scenarioData) return [];
        const arr = [];
        const scenarioKeys = Object.keys(scenarioData).filter(key => key.includes("scenario_"));

        if (scenarioKeys) {
            scenarioKeys.forEach((key) => {
                if (scenarioData?.[key]?.id) {
                    arr.push({
                        ...scenarioData?.[key],
                        label: scenarioData?.[key]?.name || ("Scenario " + scenarioData?.[key]?.order_id),
                        value: scenarioData?.[key]?.order_id,
                    })
                }
            })
        }
        return arr;
    }, [scenarioData, isPanelOpen])

    const [selectedScenario, setSelectedScenario] = useState(null);
    const [isOpen, setIsOpen] = useState(false);
    const [tierDropdownOpen, setTierDropdownOpen] = useState(false);
    const [offerTypeDropdownOpen, setOfferTypeDropdownOpen] = useState(false);
    const [offerTypes, setOfferTypes] = useState([]);
    const [selectedOfferType, setSelectedOfferType] = useState(null);
    const [tiers, setTiers] = useState([]);
    const [bmsmXTypeOptions, setBmsmXTypeOptions] = useState([]);
    const [bmsmYTypeOptions, setBmsmYTypeOptions] = useState({});
    const [bmsmXTypeDropdownOpen, setBmsmXTypeDropdownOpen] = useState(false);
    const [bmsmYTypeDropdownOpen, setBmsmYTypeDropdownOpen] = useState(false);
    const [specialOfferTypeOptions, setSpecialOfferTypeOptions] = useState([]);
    const [specialOfferTypeDropdownOpen, setSpecialOfferTypeDropdownOpen] = useState(false);
    const [selectedSpecialOfferType, setSelectedSpecialOfferType] = useState(null);
    const [toast, setToast] = useState({
        isOpen: false,
        message: "",
        variant: "",
    });
    const [selectedOfferValue, setSelectedOfferValue] = useState({
        x_value: null,
        offer_x_type: null,
        y_value: null,
        offer_y_type: null,
        z_value: null,
        offer_z_type: null,
        tier_id: null
    });

    useEffect(() => {
        if (promoDetails && isPanelOpen) {
            some();
        }
    }, [promoDetails, isPanelOpen]);

    useEffect(() => {
        let tiers = tiersData || [];

        if (tiers.length == 0) {
            tiers = [{
                tier_id: null,
                tier_name: "No tiers found",
                label: "No tiers found",
                value: null
            }]
        }

        setTiers(tiers.map(tier => ({
            ...tier,
            label: tier.tier_name,
            value: tier.tier_id
        })));
    }, [tiersData]);

    const some = () => {
        const { promo_id } = promoDetails;
        const offerTypeOptions = [];
        validOffers?.[promo_id]?.forEach(item => {
            const option = {
                ...item,
                value: item.offer_type,
                label: item.offer_display_name,
            }
            offerTypeOptions.push(option);
        });
        setOfferTypes(offerTypeOptions);
    }

    const handleOfferTypeChange = async (selectedOption) => {
        console.log(selectedOption);
        setSelectedOfferType(selectedOption);
        setSelectedOfferValue({
            x_value: null,
            offer_x_type: null,
            y_value: null,
            offer_y_type: null,
            z_value: null,
            offer_z_type: null,
            tier_id: null
        });
         if (selectedOption?.value === "special_offer_type") {
            const specialOfferTypes = specialOfferTypeData;
            setSpecialOfferTypeOptions(specialOfferTypes);
        } else if (selectedOption?.value === "bmsm") {
            const bmsmOfferTypes = bmsmOfferData;
            let xTypeOptions = [];
            let yOptions = {};
            bmsmOfferTypes.forEach((data) => {
                let xOption = {
                    value: data.x_value_types,
                    label: VALUE_LABELS[data.x_value_types],
                };
                let yTypeOptions = [];
                data?.y_value_types?.forEach((yvalue) => {
                    let yOption = {
                        value: yvalue,
                        label: VALUE_LABELS[yvalue],
                    };
                    yTypeOptions.push(yOption);
                });
                xTypeOptions.push(xOption);
                yOptions[data.x_value_types] = yTypeOptions;
            });

            setBmsmXTypeOptions(xTypeOptions);
            setBmsmYTypeOptions(yOptions);
        }
    }

    const handleOfferValueChange = (val, offerType, max = Infinity) => {
        console.log(val, selectedOfferType);
        setSelectedOfferValue((prev) => ({
            ...prev,
            [offerType]: parseFloat(val) > max ? max : parseFloat(val)
        }));
        if (selectedOfferType?.value === "bmsm") {
            return;
        }
        validateOfferValue(parseFloat(val) > max ? max : parseFloat(val));
    }

    const validateOfferValue = (val) => {
        // vaidation on each value
        if (val === "" || val === null || val === undefined) {
            let message = `Please fill all the fields`;
            if (selectedOfferType?.value === "tiered_offer") {
                message = `Please select a valid tier`;
            }
            setToast({
                isOpen: true,
                message: message,
                variant: "error",
            });
            return false;
        }
        let regex = /^([0-9]+(?:[.][0-9]{0,2})?|.[0-9]+)$/;
        if (
            (val && !regex.test(val)) ||
            (
                selectedOfferType?.min_price !== null && selectedOfferType?.max_price !== null &&
                val && regex.test(val) && (val < selectedOfferType?.min_price || val > selectedOfferType?.max_price)
            ) ||
            (selectedOfferType?.value === "tiered_offer" && !selectedOfferValue?.tier_id)
        ) {
            setToast({
                isOpen: true,
                message: `Please enter a valid number between ${selectedOfferType?.min_price} and ${selectedOfferType?.max_price}`,
                variant: "error",
            });
            return false;
        }
        return true;
    }

    const handleApply = async () => {
        const isValid = [];

        if (!selectedScenario?.value) {
            setToast({
                isOpen: true,
                message: `Please select a scenario`,
                variant: "error",
            });
            return;
        }

        if (selectedOfferType?.value === "tiered_offer") {
            isValid.push(validateOfferValue(selectedOfferValue?.tier_id));
        } else if (selectedOfferType?.value === "special_offer_type") {
            if (!selectedOfferValue?.special_offer_data) {
                setToast({
                    isOpen: true,
                    message: `Please select a special ${global_labels?.promo_alias} type`,
                    variant: "error",
                });
                isValid.push(false);
            } else {
                isValid.push(true);
            }
        } else {
            isValid.push(validateOfferValue(selectedOfferValue?.x_value));
            if (["bxgy", "bxgy_percent_off", "bmsm"].includes(selectedOfferType?.value)) {
                isValid.push(validateOfferValue(selectedOfferValue?.y_value));
            }
            if (["bxgy_percent_off"].includes(selectedOfferType?.value)) {
                isValid.push(validateOfferValue(selectedOfferValue?.z_value));
            }
            if (selectedOfferType?.value === "tiered_offer") {
                isValid.push(validateOfferValue(selectedOfferValue?.tier_id));
            }
        }

        if (isValid.includes(false)) {
            return;
        }
        // upate multiple rows via using aggrid  transaction
        const itemsToUpdate = _.cloneDeep(selectedRows);

        itemsToUpdate.forEach(row => {
            let scenario_data = row?.[`scenario_${selectedScenario?.order_id}`];
            scenario_data = {
                ...scenario_data,
                offer_type: selectedOfferType?.value,
                offer_type_id: selectedOfferType?.offer_type_id,
                offer_x_value: selectedOfferValue?.x_value || null,
                offer_x_type: selectedOfferValue?.offer_x_type || null,
                offer_y_value: selectedOfferValue?.y_value || null,
                offer_y_type: selectedOfferValue?.offer_y_type || null,
                offer_z_value: selectedOfferValue?.z_value || null,
                offer_z_type: selectedOfferValue?.offer_z_type || null,
                tier_id: selectedOfferValue?.tier_id || null,
                special_offer_data: selectedOfferValue?.special_offer_data || null,
            }
            row[`scenario_${selectedScenario?.value}`] = scenario_data;
        });

        const scenarioObjectList = [];
        let scenarioObject = {
            scenario_id: selectedScenario?.id,
            scenario_order_id: selectedScenario?.order_id,
            scenario_name: selectedScenario?.scenario_name,
            offer_type: selectedOfferType?.value,
            offer_type_id: selectedOfferType?.offer_type_id,
            offer_x_value: selectedOfferValue?.x_value || null,
            offer_x_type: selectedOfferValue?.offer_x_type || null,
            offer_y_value: selectedOfferValue?.y_value || null,
            offer_y_type: selectedOfferValue?.offer_y_type || null,
            offer_z_value: selectedOfferValue?.z_value || null,
            offer_z_type: selectedOfferValue?.offer_z_type || null,
            tier_id: selectedOfferValue?.tier_id || null,
            special_offer_data: selectedOfferValue?.special_offer_data || null,
        };
        scenarioObjectList.push(scenarioObject);

        applyBulkEdit(itemsToUpdate, scenarioObjectList);
    }

    const getSpecialOfferTypeDetailsAPI = async (specialOfferIdentifier) => {
        const response = await dispatch(getSpecialOfferTypeDetails(specialOfferIdentifier));
        if (response) {
            const data = {
                ...response,
                customer_reach: response?.customer_reach,
                customer_redemption_rate: response?.customer_redemption_rate,
                discount_type: response?.discount_type,
                discount_value: response?.discount_value,
                name: response?.name,
                special_offer_type_id: response?.id
            }
            setSelectedOfferValue({
                special_offer_data: data
            });
            return data;
        }
    }

    return (
        <Panel
            anchor="right"
            open={isPanelOpen}
            className="bulk-edit-panel"
            onClose={() => setIsPanelOpen(false)}
            onPrimaryButtonClick={handleApply}
            onSecondaryButtonClick={() => {
                setIsPanelOpen(false);
            }}
            primaryButtonLabel={`Apply`}
            secondaryButtonLabel={`Cancel`}
            size="large"
            title={
                <div className="flexWithGap8">
                    <Button
                        icon={<img className="blue-filter" src={BlueEditIcon} />}
                        iconPlacement="left"
                        disabled
                        size="large"
                        variant="secondary"
                    />
                    <p>Bulk Edit</p>
                </div>
            }
        >
            <LoaderComponent>
                <div className="target-panel-toast">
                    <Toast
                        autoHideDuration={3000}
                        message={toast.message}
                        onClose={(_e, _reason) => {
                            setToast({
                                isOpen: false,
                                message: "",
                                variant: "",
                            });
                        }}
                        position="top-right"
                        variant={toast.variant}
                        isOpen={toast.isOpen}
                    />
                </div>
                <div className='bulk-edit-panel-container'>
                    <div className='bulk-edit-panel-header'>
                        <p>Attribute</p>
                        <Button
                            size="large"
                            variant="text"
                            onClick={() => {
                                setSelectedScenario(null);
                                setSelectedOfferType(null);
                                setSelectedOfferValue({});
                            }}
                        >
                            Clear All
                        </Button>
                    </div>
                    <div className='bulk-edit-panel-body'>
                        <div>
                            <Select
                                currentOptions={selectvalue}
                                initialOptions={selectvalue}
                                label="Select Scenario"
                                labelOrientation="top"
                                setSelectedOptions={setSelectedScenario}
                                setCurrentOptions={() => { }}
                                placeholder="Select.."
                                isRequired={false}
                                isWithSearch={false}
                                isMulti={false}
                                isSelectAll={false}
                                onSelectAll={() => { }}
                                setIsSelectAll={() => { }}
                                selectedOptions={selectedScenario}
                                isOpen={isOpen}
                                setIsOpen={setIsOpen}
                                isCloseWhenClickOutside={true}
                                toggleSelectAll
                            />
                        </div>
                        <div className="horizontal-divider-line" />
                        <div className="marginTop-16 bulk-edit-value-container finalize-offer-value-container">
                            <Select
                                currentOptions={
                                    offerTypes
                                }
                                initialOptions={
                                    offerTypes
                                }
                                label={`Select ${global_labels?.promo_alias} type`}
                                labelOrientation="top"
                                setSelectedOptions={handleOfferTypeChange}
                                setCurrentOptions={() => { }}
                                placeholder="Select..."
                                isRequired={true}
                                isWithSearch={false}
                                isMulti={false}
                                selectedOptions={selectedOfferType}
                                isOpen={offerTypeDropdownOpen}
                                setIsOpen={setOfferTypeDropdownOpen}
                                isCloseWhenClickOutside={true}
                            />
                            {["percent_off", "fixed_price", "extra_amount_off", "upto_x_percent_off"].includes(selectedOfferType?.value) && (
                                <Input
                                    value={selectedOfferValue?.x_value || ""}
                                    onChange={(e) => handleOfferValueChange(e.target.value, "x_value")}
                                    onBlur={() => { }}
                                    disabled={false}
                                    type="number"
                                />
                            )}
                            {["bxgy", "bxgy_percent_off"].includes(selectedOfferType?.value) && (
                                <div className="flexWithGap8">
                                    <p className="field-label">
                                        BUY
                                    </p>
                                    <Input
                                        value={selectedOfferValue?.x_value || ""}
                                        onChange={(e) => handleOfferValueChange(e.target.value, "x_value", 20)}
                                        onBlur={() => { }}
                                        disabled={false}
                                        type="number"
                                    />
                                    <p className="field-label">
                                        GET
                                    </p>
                                    <Input
                                        value={selectedOfferValue?.y_value || ""}
                                        onChange={(e) => handleOfferValueChange(e.target.value, "y_value", 20)}
                                        onBlur={() => { }}
                                        disabled={false}
                                        type="number"
                                    />
                                    {
                                        selectedOfferType?.value === "bxgy_percent_off" && (
                                            <>
                                                <p className="field-label">
                                                    AT
                                                </p>
                                                <Input
                                                    value={selectedOfferValue?.z_value || ""}
                                                    onChange={(e) => handleOfferValueChange(e.target.value, "z_value", 100)}
                                                    onBlur={() => { }}
                                                    disabled={false}
                                                    type="number"
                                                />
                                            </>
                                        )
                                    }
                                </div>
                            )}
                            {selectedOfferType?.value === "tiered_offer" && (
                                <Select
                                    currentOptions={tiers}
                                    initialOptions={tiers}
                                    label="Select tier"
                                    labelOrientation="top"
                                    setSelectedOptions={(selectedOption) => {
                                        setSelectedOfferValue({
                                            tier_id: selectedOption.value
                                        });
                                    }}
                                    setCurrentOptions={() => { }}
                                    placeholder="Select..."
                                    isRequired={true}
                                    isWithSearch={false}
                                    isMulti={false}
                                    selectedOptions={
                                        tiers?.find(tier => tier.tier_id === selectedOfferValue?.tier_id) || []
                                    }
                                    isOpen={tierDropdownOpen}
                                    setIsOpen={setTierDropdownOpen}
                                    isCloseWhenClickOutside={true}
                                />
                            )}
                            {selectedOfferType?.value === "special_offer_type" && (
                                <Select
                                    currentOptions={specialOfferTypeOptions}
                                    initialOptions={specialOfferTypeOptions}
                                    label={`Select special ${global_labels?.promo_alias} type`}
                                    labelOrientation="top"
                                    setSelectedOptions={(selectedOption) => {
                                        setSelectedSpecialOfferType(selectedOption);
                                        selectedSpecialOfferType?.value !== selectedOption.value && getSpecialOfferTypeDetailsAPI(selectedOption.value);
                                    }}
                                    setCurrentOptions={() => { }}
                                    placeholder="Select..."
                                    isRequired={true}
                                    isWithSearch={false}
                                    isMulti={false}
                                    selectedOptions={selectedSpecialOfferType}
                                    isOpen={specialOfferTypeDropdownOpen}
                                    setIsOpen={setSpecialOfferTypeDropdownOpen}
                                    isCloseWhenClickOutside={true}
                                />
                            )}
                        </div>
                        {selectedOfferType?.value === "bmsm" && (
                            <div className="flexWithGap8 bmsm-offer-container">
                                <p className="field-label">
                                    BUY
                                </p>
                                <Input
                                    value={selectedOfferValue?.x_value || ""}
                                    onChange={(e) => handleOfferValueChange(
                                        e.target.value,
                                        "x_value",
                                        bmsmXTypeOptions?.find(option => option.value === selectedOfferValue?.offer_x_type).value != "unit" ? Infinity : 20
                                    )}
                                    onBlur={() => { }}
                                    disabled={!selectedOfferValue?.offer_x_type}
                                    type="number"
                                />
                                <Select
                                    currentOptions={bmsmXTypeOptions || []}
                                    initialOptions={bmsmXTypeOptions || []}
                                    label=""
                                    labelOrientation="top"
                                    setCurrentOptions={() => { }}
                                    placeholder="Select..."
                                    isRequired={true}
                                    isWithSearch={false}
                                    isMulti={false}
                                    setSelectedOptions={(selectedOption) => {
                                        setSelectedOfferValue((prev) => ({
                                            ...prev,
                                            offer_x_type: selectedOption.value,
                                            x_value: ""
                                        }));
                                    }}
                                    selectedOptions={bmsmXTypeOptions?.find(option => option.value === selectedOfferValue?.offer_x_type)}
                                    isOpen={bmsmXTypeDropdownOpen}
                                    setIsOpen={setBmsmXTypeDropdownOpen}
                                    isCloseWhenClickOutside={true}
                                />
                                <p className="field-label">
                                    GET
                                </p>
                                <Input
                                    value={selectedOfferValue?.y_value || ""}
                                    onChange={(e) => handleOfferValueChange(
                                        e.target.value,
                                        "y_value",
                                        bmsmYTypeOptions?.[selectedOfferValue?.offer_x_type]?.find(option => option.value === selectedOfferValue?.offer_y_type)?.value != "percent_off" ? Infinity : 100
                                    )}
                                    onBlur={() => { }}
                                    disabled={!selectedOfferValue?.offer_y_type}
                                    type="number"
                                />
                                <Select
                                    currentOptions={bmsmYTypeOptions?.[selectedOfferValue?.offer_x_type] || []}
                                    initialOptions={bmsmYTypeOptions?.[selectedOfferValue?.offer_x_type] || []}
                                    selectedOptions={
                                        bmsmYTypeOptions?.[selectedOfferValue?.offer_x_type]?.find(option => option.value === selectedOfferValue?.offer_y_type)
                                    }
                                    label=""
                                    labelOrientation="top"
                                    setSelectedOptions={(selectedOption) => {
                                        setSelectedOfferValue((prev) => ({
                                            ...prev,
                                            offer_y_type: selectedOption.value,
                                            y_value: ""
                                        }));
                                    }}
                                    setCurrentOptions={() => { }}
                                    placeholder="Select..."
                                    isRequired={true}
                                    isWithSearch={false}
                                    isMulti={false}
                                    isOpen={bmsmYTypeDropdownOpen}
                                    setIsOpen={setBmsmYTypeDropdownOpen}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </LoaderComponent>
        </Panel>
    )
}

export default BulkEditPanel    