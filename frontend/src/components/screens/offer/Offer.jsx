import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom-v5-compat";
import { useSelector, useDispatch } from "react-redux";
import moment from "moment";
import { But<PERSON> } from "impact-ui";

import { breadcrumbRoutes } from "../../../constants/RouteConstants";
import { DEFAULT_DATE_FORMAT } from "../../../constants/Constants";
import ScreenBreadcrumb from "../../common/breadCrumb/ScreenBreadcrumb";
import viewIconBlue from "../../../assets/imageAssets/viewIcon_blue.svg?url";
import editIconBlue from "../../../assets/imageAssets/editIcon_blue.svg?url";
import ReturnIcon from "../../../assets/imageAssets/returnIcon.svg?.url";
import viewIcon from "../../../assets/imageAssets/viewIcon.svg?url";
import editIcon from "../../../assets/imageAssets/tierEditIcon.svg?url";
import {
    setActivePromoId,
    setIsEditedFlag,
    setPromoDetails,
    setPromoEventDetails,
    setSelectedPromoProducts,
    setSelectedPromoProductGroups,
    setSelectedPromoStores,
    setProductData,
    setProducGrouptData,
    setStoreData,
    setStoreGroupData,
    setSelectedPromoStoreGroups,
    setMaxStepCount,
    setOfferModeState,
} from "../../../store/features/promoReducer/promoReducer";

import OfferBasicDetails from "./OfferBasicDetails";
import OfferDetails from "./OfferDetails";
import "./Offer.scss";
import {
    toastError,
    validateOperation,
} from "../../../store/features/global/global";
import { offerModeOptions } from "./OfferConstants";

const Offer = (props) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();

    const { promoDetails = {}, maxStepCount, offerModeState } = useSelector(
        (store) => store?.pricesmartPromoReducer.promo
    );

    const { roleActions } = useSelector(
        (store) => store?.pricesmartPromoReducer.auth
    );

    const [isBasicDetails, setIsBasicDetails] = useState(true);
    const [allowPromoEdit, setAllowPromoEdit] = useState(true);
    const [isEditBasicInfo, setIsEditBasicInfo] = useState(false);
    const [isModeChangeAllowed, setIsModeChangeAllowed] = useState(true);
    const [activeStep, setActiveStep] = useState(-1);
    const [offerMode, setOfferMode] = useState("edit");
    const [modeOptions, setModeOptions] = useState([...offerModeOptions]);

    useEffect(() => {
        const query = new URLSearchParams(window.location.search);
        const promoId = query.get("promo") ? query.get("promo") : null;
        dispatch(setIsEditedFlag(false));
        const viewMode = query.get("view")
            ? query.get("view") === "true"
            : false;
        if (_.isNull(promoId)) {
            setOfferMode("edit");
            dispatch(setOfferModeState("edit"));
        } else if (promoId && !viewMode) {
            validateOperationHandler(promoId);
        } else if (promoId && viewMode) {
            viewModeAction(promoId);
        }

        return () => {
            dispatch(setActivePromoId(null));
            dispatch(setPromoDetails(null));
            dispatch(setPromoEventDetails({}));
            dispatch(setMaxStepCount(null));
            dispatch(setSelectedPromoProducts([]));
            dispatch(setSelectedPromoProductGroups([]));
            dispatch(setSelectedPromoStores([]));
            dispatch(setSelectedPromoStoreGroups([]));
            dispatch(setProductData([]));
            dispatch(setProducGrouptData([]));
            dispatch(setStoreData([]));
            dispatch(setStoreGroupData([]));
            dispatch(setIsEditedFlag(false));
        };
    }, []);

    const viewModeAction = async (promoId) => {
        const isOpAllowed = await checkValidOperation(promoId);
        if (!isOpAllowed.is_valid) {
            dispatch(toastError(isOpAllowed.message));
            setIsModeChangeAllowed(false);
        } else {
            setIsModeChangeAllowed(true);
        }
        dispatch(setActivePromoId(parseInt(promoId)));
        setOfferMode("view");
        dispatch(setOfferModeState("view"));
        setAllowPromoEdit(false);
    };

    const checkValidOperation = async (promoId) => {
        const isOpAllowed = await dispatch(
            validateOperation({
                promo_id: [promoId],
            })
        );
        return isOpAllowed;
    };

    const validateOperationHandler = async (promoId) => {
        const isOpAllowed = await checkValidOperation(promoId);

        if (!isOpAllowed.is_valid) {
            dispatch(toastError(isOpAllowed.message));
            navigate("/pricesmart-promo/workbench");
            return;
        }

        if (promoId) {
            dispatch(setActivePromoId(parseInt(promoId)));
        }
        if (maxStepCount > 0) {
            setIsBasicDetails(false);
            if (maxStepCount == 4) {
                handleStep(maxStepCount - 2);
            } else {
                handleStep(maxStepCount - 1);
            }
        }
        setIsModeChangeAllowed(true);
        setOfferMode("edit");
        dispatch(setOfferModeState("edit"));
    };

    useEffect(() => {
        if (maxStepCount > 0) {
            setIsBasicDetails(false);
            if (maxStepCount == 4) {
                handleStep(maxStepCount - 2);
            } else {
                handleStep(maxStepCount - 1);
            }
        } else if (maxStepCount == 0 || !maxStepCount) {
            setIsBasicDetails(true);
            handleStep(-1);
        }
    }, [maxStepCount]);

    useEffect(() => {
        // Check if Promo is editable or not
        // Check user permission, promo status (ongoing/ completed/ archived), event status (locked)
        // Check if its opened in View Mode
        // status id 8 - Execution Approved, 6 - Archived, 4 - Finalized
        const viewModeStatuses = [8, 6];
        if (!_.isEmpty(promoDetails)) {
            const currDt = moment().format(DEFAULT_DATE_FORMAT);
            if (
                moment(currDt).isSameOrAfter(
                    moment(promoDetails?.start_date)
                ) ||
                // promoDetails?.status === 6 ||
                // _.isEmpty(roleActions?.["Workbench Offer"]?.edit) ||
                // (viewMode && offerMode == 'view') ||
                viewModeStatuses.includes(promoDetails?.status_id)
            ) {
                setAllowPromoEdit(false);
                setOfferMode("view");
                dispatch(setOfferModeState("view"));
            } else if (isModeChangeAllowed) {
                offerModeState == "view"
                    ? setAllowPromoEdit(false)
                    : setAllowPromoEdit(true);
            }
            if (
                promoDetails.step_count > 0 &&
                isBasicDetails &&
                !isEditBasicInfo
            ) {
                setIsBasicDetails(false);
                handleStep(0);
            }
        }
    }, [promoDetails, offerModeState]);

    useEffect(() => {
        let updatedOptions = [...offerModeOptions];
        updatedOptions.forEach((option) => {
            if (option.value == "edit" && !isModeChangeAllowed) {
                option["disabled"] = true;
            } else {
                option["disabled"] = false;
            }
        });
        setModeOptions(updatedOptions);
    }, [isModeChangeAllowed]);

    useEffect(() => {
        let updatedOptions = [...offerModeOptions];
        updatedOptions.forEach((option) => {
            if (option.value == offerMode) {
                option["icon"] =
                    offerMode == "edit" ? (
                        <img src={editIconBlue} alt="edit" />
                    ) : (
                        <img src={viewIconBlue} alt="view" />
                    );
            } else {
                option["icon"] =
                    option.value == "edit" ? (
                        <img src={editIcon} alt="edit" />
                    ) : (
                        <img src={viewIcon} alt="view" />
                    );
            }
        });
        setModeOptions(updatedOptions);
    }, [offerMode]);

    const handleStep = (stepNumber) => {
        setActiveStep(stepNumber);
    };

    const returnToWorkbenchHandler = () => {
        dispatch(setActivePromoId(null));
        dispatch(setPromoDetails({}));
        navigate("/pricesmart-promo/workbench");
    };

    const handleBasicInfo = () => {
        setIsBasicDetails(true);
        setIsEditBasicInfo(true);
        handleStep(-1);
    };

    const handleOfferModeChange = (e, value) => {
        if (value == "view") {
            setAllowPromoEdit(false);
        } else {
            setAllowPromoEdit(true);
        }
        setOfferMode(value);
        dispatch(setOfferModeState(value));
    };

    return (
        <div className="screen_container">
            <ScreenBreadcrumb breadcrumbList={breadcrumbRoutes()?.["offer"]}>
                <Button
                    icon={<img src={ReturnIcon} alt="back" />}
                    iconPlacement="left"
                    onClick={() => returnToWorkbenchHandler()}
                    size="large"
                    variant="url"
                    className="offer-grey-background"
                >
                    Return to Workbench
                </Button>
            </ScreenBreadcrumb>
            <div className="paddingTop-12">
                {isBasicDetails ? (
                    <OfferBasicDetails
                        setIsBasicDetails={setIsBasicDetails}
                        allowPromoEdit={allowPromoEdit}
                        handleStep={handleStep}
                        activeStep={activeStep}
                        isModeChangeAllowed={isModeChangeAllowed}
                        offerMode={offerMode}
                        setOfferMode={setOfferMode}
                        modeOptions={modeOptions}
                        handleOfferModeChange={handleOfferModeChange}
                    />
                ) : (
                    <OfferDetails
                        setIsBasicDetails={handleBasicInfo}
                        allowPromoEdit={allowPromoEdit}
                        handleStep={handleStep}
                        activeStep={activeStep}
                        isModeChangeAllowed={isModeChangeAllowed}
                        offerMode={offerMode}
                        setOfferMode={setOfferMode}
                        modeOptions={modeOptions}
                        handleOfferModeChange={handleOfferModeChange}
                    />
                )}
            </div>
        </div>
    );
};

export default Offer;
