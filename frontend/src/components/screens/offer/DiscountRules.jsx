import React, {
    useState,
    useEffect,
    useCallback,
} from "react";
import { useSelector, useDispatch } from "react-redux";

import _ from "lodash";
import {
    Button,
    Input,
    Select,
    RadioButtonGroup,
    Modal
} from "impact-ui";

import {
    setIsEditedFlag,
    getValidDiscountLevels,
    getValidOfferTypes,
    getPriorityNumberList,
    getStep2Details,
    editPromoStep2,
    getVendorFundingDetails,
    getDiscountRules
} from "../../../store/features/promoReducer/promoReducer";
import {
    DISCOUNT_TYPES,
    uptoXPerDiscountType
} from "./OfferConstants";
import { toastError } from "../../../store/features/global/global";
import { global_labels } from "../../../constants/Constants";

const DiscountRules = props => {

    const dispatch = useDispatch();

    const [discountType, setDiscountType] = useState(null);
    const [discountLevel, setDiscountLevel] = useState(null);
    const [priorityNumber, setPriorityNumber] = useState(null);
    const [isDiscountTypeOpen, setIsDiscountTypeOpen] = useState(null);
    const [isDiscountLevelOpen, setIsDiscountLevelOpen] = useState(null);
    const [isPriorityNumberOpen, setIsPriorityNumberOpen] = useState(null);
    const [discountTypeOptions, setDiscountTypeOptions] = useState([]);
    const [discountLevelOptions, setDiscountLevelOptions] = useState([]);
    const [priorityNumberOptions, setPriorityNumberOptions] = useState([]);
    const [isVendorFunding, setIsVendorFunding] = useState(0);
    const [vendorFundingType, setVendorFundingType] = useState(null);
    const [adsAmount, setAdsAmount] = useState(null);
    const [perUnit, setPerUnit] = useState(null);
    const [showWarnigModal, setShowWarnigModal] = useState(false);
    const [productDiscountDropdownOpen, setProductDiscountDropdownOpen] = useState(false);
    const [storeDiscountDropdownOpen, setStoreDiscountDropdownOpen] = useState(false);
    const [productDiscountLevel, setProductDiscountLevel] = useState([]);
    const [storeDiscountLevel, setStoreDiscountLevel] = useState([]);
    const [productDiscountLevelOptions, setProductDiscountLevelOptions] = useState([]);
    const [storeDiscountLevelOptions, setStoreDiscountLevelOptions] = useState([]);
    const [isSelectAllProductDiscountLevel, setIsSelectAllProductDiscountLevel] = useState(false);
    const [isSelectAllStoreDiscountLevel, setIsSelectAllStoreDiscountLevel] = useState(false);
    const [uptoXPerDiscountFields, setUpToXPerDiscountFields] = useState({
        ...uptoXPerDiscountType.reduce((acc, item) => {
            acc[item.key] = 0;
            return acc;
        }, {})
    });
    const [filteredOptions, setFilteredOptions] = useState({});

    const {
        activePromoId,
        validDiscountLevel,
        validOfferTypes,
        priorityNumberList,
        promoDetails,
        isEdited,
        vendorFundingDetails
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);
    const {
        global_configs
    } = useSelector((store) => store?.pricesmartPromoReducer?.global);

    useEffect(() => {
        if (props.activeStep === props.stepNumber) {
            dispatch(setIsEditedFlag(false));
            getProductDiscountLevelApi();
        }
    }, [props.activeStep]);

    useEffect(() => {
        onChangeUseEffect();
    }, [props.activeStep, activePromoId]);

    const onChangeUseEffect = async () => {
        // If landing here in Edit Flow, call the get promo API to get details and populate

        if (
            props.activeStep === props.stepNumber
            && activePromoId
        ) {

            dispatch(getStep2Details(activePromoId));

            const payload = {
                promo_id: activePromoId,
            };

            dispatch(getValidDiscountLevels(payload));
            dispatch(getVendorFundingDetails());
        }
    };

    // useEffect(() => {

    //     const updatedValidDiscountLevel = validDiscountLevel?.map((item) => {
    //         return {
    //             ...item,
    //             label: item?.["discount_level_value"],
    //             value: item?.["discount_level_id"],
    //         };
    //     });
    //     setDiscountLevelOptions(updatedValidDiscountLevel);
    //     if (validDiscountLevel && validDiscountLevel.length === 1) {
    //         const selectedOption = validDiscountLevel?.map((item) => {
    //             return {
    //                 ...item,
    //                 label: item?.["discount_level_value"],
    //                 value: item?.["discount_level_id"],
    //             };
    //         })
    //         setProductDiscountLevel(selectedOption);
    //         setStoreDiscountLevel(selectedOption);
    //     }

    //     if (promoDetails?.discount_rules?.discount_level || promoDetails?.discount_rules?.discount_level == 0) {
    //         let seletedOption = updatedValidDiscountLevel.find(item => item.discount_level_id == promoDetails?.discount_rules?.discount_level);
    //         setDiscountLevel(seletedOption);
    //     }
    // }, [validDiscountLevel]);

    useEffect(() => {
        const updatedValidOfferTypes = validOfferTypes?.map((item) => {
            return {
                ...item,
                label: item?.["display_name"],
                value: item?.["offer_type"],
            };
        })
        setDiscountTypeOptions(updatedValidOfferTypes);

        if (promoDetails?.discount_rules?.discount_type) {
            let seletedOption = updatedValidOfferTypes.find(item => item.offer_type == promoDetails?.discount_rules?.discount_type);
            setDiscountType(seletedOption);
        } else {
            setDiscountType({});
        }

    }, [validOfferTypes]);

    useEffect(() => {
        setPriorityNumberOptions(priorityNumberList);

        if (promoDetails?.discount_rules?.priority_number?.value) {
            let seletedOption = priorityNumberList.find(item => item.value == promoDetails?.discount_rules?.priority_number?.value);
            setPriorityNumber(seletedOption);
        } else {
            setPriorityNumber({});
        }
    }, [priorityNumberList]);

    useEffect(() => {
        if (
            !_.isEmpty(promoDetails) &&
            props.activeStep === props.stepNumber
        ) {
            if (promoDetails?.discount_rules) {
                const {
                    discount_level,
                    discount_type,
                    priority_number,
                    discount_display_name,
                    discount_type_id
                } = promoDetails?.discount_rules;
                if (priority_number) {
                    let seletedOption = promoDetails?.discount_rules?.priority_number;
                    setPriorityNumber(seletedOption);
                } else {
                    setPriorityNumber({});
                }

                if (discount_type) {
                    setDiscountType({
                        label: discount_display_name || DISCOUNT_TYPES?.[discount_type],
                        value: discount_type,
                        offer_type_id: discount_type_id
                    });
                } else {
                    setDiscountType({});
                }
            }
        }
        if (promoDetails?.vendor_funding) {
            setIsVendorFunding(promoDetails?.vendor_funding?.vf_type ? 1 : 0);
            setVendorFundingType(promoDetails?.vendor_funding?.vf_type);
            setAdsAmount(promoDetails?.vendor_funding?.vf_fixed_amount);
            setPerUnit(promoDetails?.vendor_funding?.vf_per_unit);
        }
        if (promoDetails?.upto_percent) {
            setUpToXPerDiscountFields({
                ...promoDetails?.upto_percent
            });
        }
    }, [promoDetails]);

    useEffect(() => {
        if (promoDetails?.product_discount_level && !_.isEmpty(productDiscountLevelOptions)) {
            const productDiscountLevel = productDiscountLevelOptions?.filter((item) => {
                return promoDetails?.product_discount_level?.includes(item.value);
            });
            setProductDiscountLevel(productDiscountLevel);
        } else {
            setProductDiscountLevel([]);
        }
        if (promoDetails?.store_discount_level && !_.isEmpty(storeDiscountLevelOptions)) {
            const storeDiscountLevel = storeDiscountLevelOptions?.filter((item) => {
                return promoDetails?.store_discount_level?.includes(item.value);
            });
            setStoreDiscountLevel(storeDiscountLevel);
        } else {
            setStoreDiscountLevel([]);
        }
    }, [promoDetails, productDiscountLevelOptions, storeDiscountLevelOptions]);

    const cascadeClear = (field) => {
        switch(field) {
            case "all":
                setProductDiscountLevel(null);
                setProductDiscountLevelOptions([]);
                setStoreDiscountLevel(null);
                setStoreDiscountLevelOptions([]);
            case "productDiscountLevel":
            case "storeDiscountLevel":
                setPriorityNumber(null);
                setPriorityNumberOptions([]);
            case "priorityNumber":
                setDiscountType(null)
                setDiscountTypeOptions([])
        }
    }

    const toggleChangeFlag = () => {
        dispatch(setIsEditedFlag(true));
    };

    const updateDiscountLevel = useCallback((data, from) => {
        if (from == "product") {
            // -200 is overall and 7 is product group
            if (data.find((item) => item?.value == -200 || item?.value == 7)) {
                setProductDiscountLevel(prev => {
                    const newItems = data.filter(item =>
                        !prev.some(existing => existing.value === item.value)
                    );
                    return newItems;
                })
            } else {
                setProductDiscountLevel(data);
            }
            //cascade clear for priority number and discount type
            cascadeClear("productDiscountLevel");
        } else if (from == "store") {
            // -200 is all stores and 7 is store group
            if (data.find((item) => item?.value == -200 || item?.value == 7)) {
                setStoreDiscountLevel(prev => {
                    const newItems = data.filter(item =>
                        !prev.some(existing => existing.value === item.value)
                    );
                    return newItems;
                })
            } else {
                setStoreDiscountLevel(data);
            }
            //cascade clear for priority number and discount type
            cascadeClear("storeDiscountLevel");
        }
        toggleChangeFlag();
    });

    const updatePriorityNumber = useCallback((data) => {
        setPriorityNumber(data);
        toggleChangeFlag();
        //cascade clear for discount type
        cascadeClear("priorityNumber");
    });

    const updateDiscountType = useCallback((data) => {
        setDiscountType(data);
        toggleChangeFlag();
    });

    const handleIsVendorFunding = (e) => {
        dispatch(setIsEditedFlag(true));
        setIsVendorFunding(e.target.value);
    }

    const handleFundingType = (e) => {
        setAdsAmount(null);
        setPerUnit(null);
        dispatch(setIsEditedFlag(true));
        setVendorFundingType(e.target.value);
    }

    const getProductDiscountLevelApi = async () => {
        const res = await dispatch(getDiscountRules(activePromoId));
        if (res) {
            if (res?.product) {
                setProductDiscountLevelOptions(res?.product);
            }
            if (res?.store) {
                setStoreDiscountLevelOptions(res?.store);
            }
        }
    }

    if (props.activeStep !== props.stepNumber) {
        return "";
    }

    const handleBack = () => {
        props.setActiveStep(props.stepNumber - 1);
    }

    const submitHandler = async () => {

        if (_.isEmpty(productDiscountLevel) || _.isEmpty(storeDiscountLevel)) {
            dispatch(toastError("Select a Discounting Level"));
            return;
        }
        if (_.isEmpty(discountType)) {
            dispatch(toastError("Select a discount type"));
            return;
        } // priority No. made mandatory
        if (_.isEmpty(priorityNumber)) {
            dispatch(toastError("Select a priority number"));
            return;
        }

        if (+isVendorFunding === 1) {
            if (_.isEmpty(vendorFundingType)) {
                dispatch(toastError("Select a funding type"));
                return;
            }
            const hasMultipleInputs = vendorFundingTypeInput[vendorFundingType]?.length > 1;
            const isAdsAmountType = vendorFundingTypeInput[vendorFundingType]?.includes("Ads.amount");
            const isAmountInvalid = hasMultipleInputs ? (!adsAmount || !perUnit) : isAdsAmountType ? !adsAmount : !perUnit;

            if (isAmountInvalid) {
                dispatch(toastError("Enter a valid amount"));
                return;
            }
        }

        const payload = {
            promo_id: activePromoId,
            product_discount_level: productDiscountLevel?.map((item) => item.value),
            store_discount_level: storeDiscountLevel?.map((item) => item.value),
            discount_type: discountType?.value,
            discount_type_id: discountType?.offer_type_id,
            customer_discount_level: [],
            priority_number: priorityNumber?.value,
            ...(+isVendorFunding === 1 && {
                vendor_funding: {
                    vf_type: vendorFundingType,
                    vf_per_unit: perUnit,
                    vf_fixed_amount: adsAmount
                }
            }),
            ...(discountType?.value === "upto_x_percent_off" && {
                "upto_percent": {
                    ...uptoXPerDiscountFields
                }
            })
        }

        let res = null;
        res = await dispatch(editPromoStep2(payload));

        setShowWarnigModal(false);

        if (res) {
            props.setActiveStep(props.stepNumber + 1);
        }

    }

    const handleNext = async () => {
        // if promo has simulation data then show warning popup

        if (discountType?.value === "upto_x_percent_off") {
            let isError = false;
            Object.entries(uptoXPerDiscountFields).forEach(([key, value]) => {
                if (+value < 0 || +value > 100) {
                    const label = uptoXPerDiscountType.find(item => item.key === key)?.label;
                    dispatch(toastError(`"${label}" must be between 0 and 100`));
                    isError = true;
                }
            });
            if (isError) return;
            if (uptoXPerDiscountFields.min_upto_percent > uptoXPerDiscountFields.max_upto_percent) {
                dispatch(toastError("Minimum discount must be less than maximum discount"));
                return;
            }
        }

        if (!props?.allowPromoEdit && promoDetails?.step_count === 2) {
            dispatch(toastError(`For this ${global_labels?.promo_primary} data is available till discount rules`));
            return;
        }
        if ((!props.allowPromoEdit || !isEdited) && promoDetails?.step_count > 1) {
            props.setActiveStep(props.stepNumber + 1);
            return;
        }
        if (promoDetails?.step_count > 2 && isEdited) {
            setShowWarnigModal(true);
        } else {
            // Proceed with submission if no simulation data is present
            submitHandler();
        }
    }


    const closeModal = () => {
        setShowWarnigModal(false)
    }

    const handleVendorFundingInput = (e) => {
        dispatch(setIsEditedFlag(true));
        if (e.target.name == "Ads.amount") {
            setAdsAmount(+e.target.value);
        } else {
            setPerUnit(+e.target.value);
        }
    }

    const handleChangeDiscountTypeFields = (e) => {
        dispatch(setIsEditedFlag(true));
        setUpToXPerDiscountFields({ ...uptoXPerDiscountFields, [e.target.name]: +e.target.value });
    }

    const vendorFundingTypeInput = {
        "Fixed amount": ["Ads.amount"],
        "Per unit $": ["Per unit $"],
        "Ads.amount & Per unit $": ["Ads.amount", "Per unit $"]
    }

    const handleFilterOptions = (filteredOptions,key) => {
        setFilteredOptions({
            ...filteredOptions,
            [key]: filteredOptions
        });
    }

    const getValidOfferTypesApi = async ({
        product_level_ids,
        store_level_ids = [-200],
        priority_number
    }) => {
        const res = await dispatch(getValidOfferTypes({
            promo_id: activePromoId,
            product_level_ids,
            store_level_ids,
            priority_number
        }));
        return res || [];
    }

    const getPriorityNumberListApi = async ({ product_level_ids, store_level_ids = [-200] }) => {
        const res = await dispatch(getPriorityNumberList({
            promo_id: activePromoId,
            product_level_ids,
            store_level_ids,
        }));
        return res || [];
    }

    const onDropdownOpen = (field) => {
        if (field === "priorityNumber" && _.isEmpty(priorityNumberOptions) && !_.isEmpty(productDiscountLevel)) {
            getPriorityNumberListApi({
                product_level_ids: productDiscountLevel?.map(item => item.value),
                store_level_ids: storeDiscountLevel?.map(item => item.value),
            });
        } else if (field == "discountType" && _.isEmpty(discountTypeOptions) && !_.isEmpty(priorityNumber)) {
            getValidOfferTypesApi({
                product_level_ids: productDiscountLevel?.map(item => item.value),
                store_level_ids: storeDiscountLevel?.map(item => item.value),
                priority_number: priorityNumber?.value
            });
        }
    }


    return (
        <div>
            <Modal
                onClose={() => { closeModal(); }}
                onPrimaryButtonClick={() => { submitHandler() }}
                onSecondaryButtonClick={() => { closeModal() }}
                primaryButtonLabel="Confirm"
                secondaryButtonLabel="Cancel"
                size="small"
                title="Confirm to Proceed"
                open={showWarnigModal}
            >
                <p className="text-14-500">Previously calculated simulation data will be changed</p>
            </Modal>
            <div className="content_container margin-20">
                <div>
                    <p className="text-14-800">Select discount rules</p>
                    <div className="offer-flex-container discount-rules-container">
                        {/* product discount level */}
                        <Select
                            currentOptions={
                                filteredOptions?.["product-discount-level"] || productDiscountLevelOptions
                            }
                            initialOptions={
                                productDiscountLevelOptions
                            }
                            label="Product discount level"
                            labelOrientation="top"
                            setSelectedOptions={(data) => updateDiscountLevel(data, "product")}
                            setCurrentOptions={(data) => handleFilterOptions(data,"product-discount-level")}
                            placeholder="Product discount level"
                            isRequired={true}
                            isWithSearch={true}
                            isMulti={true}
                            selectedOptions={productDiscountLevel}
                            isOpen={productDiscountDropdownOpen}
                            setIsOpen={setProductDiscountDropdownOpen}
                            isCloseWhenClickOutside={true}

                            isDisabled={!props.allowPromoEdit}
                            isSelectAll={isSelectAllProductDiscountLevel}
                            setIsSelectAll={setIsSelectAllProductDiscountLevel}
                        />
                        {/* store discount level */}
                        <Select
                            currentOptions={
                                filteredOptions?.["store-discount-level"] || storeDiscountLevelOptions
                            }
                            initialOptions={
                                storeDiscountLevelOptions
                            }
                            label="Store discount level"
                            labelOrientation="top"
                            setSelectedOptions={(data) => updateDiscountLevel(data, "store")}
                            setCurrentOptions={(data) => handleFilterOptions(data,"store-discount-level")}
                            placeholder="Store discount level"
                            isRequired={true}
                            isWithSearch={true}
                            isMulti={true}
                            selectedOptions={storeDiscountLevel}
                            isOpen={storeDiscountDropdownOpen}
                            setIsOpen={setStoreDiscountDropdownOpen}
                            isCloseWhenClickOutside={true}
                            isDisabled={!props.allowPromoEdit}
                            isSelectAll={isSelectAllStoreDiscountLevel}
                            setIsSelectAll={setIsSelectAllStoreDiscountLevel}
                        />
                        {/* priority number */}
                        <Select
                            currentOptions={
                                filteredOptions?.["priority-number"] || priorityNumberOptions
                            }
                            initialOptions={
                                priorityNumberOptions
                            }
                            label="Priority number"
                            labelOrientation="top"
                            setSelectedOptions={updatePriorityNumber}
                            setCurrentOptions={(data) => handleFilterOptions(data,"priority-number")}
                            placeholder="Priority number"
                            onDropdownOpen={() => onDropdownOpen("priorityNumber")} 
                            isRequired={true}
                            isWithSearch={true}
                            isMulti={false}
                            selectedOptions={priorityNumber}
                            isOpen={isPriorityNumberOpen}
                            setIsOpen={setIsPriorityNumberOpen}
                            isCloseWhenClickOutside={true}
                            isDisabled={!props.allowPromoEdit}
                        />
                        {/* discount type */}
                        <Select
                            currentOptions={
                                filteredOptions?.["discount-type"] || discountTypeOptions
                            }
                            initialOptions={discountTypeOptions}
                            label="Default discount type"
                            labelOrientation="top"
                            setSelectedOptions={updateDiscountType}
                            setCurrentOptions={(data) => handleFilterOptions(data,"discount-type")}
                            placeholder="Default discount type"
                            onDropdownOpen={() => onDropdownOpen("discountType")} 
                            isRequired={true}
                            isWithSearch={true}
                            isMulti={false}
                            selectedOptions={discountType}
                            isOpen={isDiscountTypeOpen}
                            setIsOpen={setIsDiscountTypeOpen}
                            isCloseWhenClickOutside={true}
                            isDisabled={!props.allowPromoEdit}
                        />
                        {discountType?.value === "upto_x_percent_off" && (
                            uptoXPerDiscountType.map(({ label, key }) => (
                                <Input
                                    key={key}
                                    id="offer_name"
                                    inputProps={{}}
                                    label={label}
                                    name={key}
                                    onChange={handleChangeDiscountTypeFields}
                                    placeholder="Enter..."
                                    type="number"
                                    min={0}
                                    max={100}
                                    isRequired={true}
                                    value={uptoXPerDiscountFields[key] || ""}
                                    disabled={!props.allowPromoEdit}
                                />
                            ))
                        )}
                    </div>
                    {/* <div className=" offer-container-background padding-24 width-500">
                        <hr className="dotted-divider" />
                    </div>
                    <div className=" offer-container-background padding-24 width-500">
                        <p className="text-14-800">Default discount type</p>
                        <hr className="dotted-divider" />
                    </div>
                    <div className=" offer-container-background padding-24 width-500">
                        <p className="text-14-800">Priority number</p>
                        <hr className="dotted-divider" />
                    </div> */}
                </div>
                {/* commented below code for demo purpose */}
                {global_configs?.promo?.use_vendor_funding && (
                    <div className="vendor-funding-details">
                        <p className="text-14-800">Vendor funding</p>
                        <hr className="dotted-divider" />
                        <div className="offer-flex-container">
                            <div>
                                <p className="text-14-600 marginBottom-8">Do you have vendor funding</p>
                                <RadioButtonGroup
                                    isDisabled={!props.allowPromoEdit}
                                    name="vendor-funding-radio-group"
                                    onChange={handleIsVendorFunding}
                                    options={[
                                        {
                                            label: "Yes",
                                            value: 1,
                                        },
                                        {
                                            label: "No",
                                            value: 0,
                                        },
                                    ]}
                                    orientation="row"
                                    selectedOption={isVendorFunding}
                                />
                            </div>
                            {isVendorFunding == 1 && !_.isEmpty(vendorFundingDetails?.vendor_funding_types) &&
                                <>
                                    <div className="vertical-dotted-divider"></div>
                                    <div className="">
                                        <p className="text-14-600 marginBottom-8">Funding type</p>
                                        <RadioButtonGroup
                                            isDisabled={!props.allowPromoEdit}
                                            name="vendor-funding-radio-group"
                                            onChange={handleFundingType}
                                            options={vendorFundingDetails?.vendor_funding_types?.map((item) => ({
                                                label: item,
                                                value: item,
                                            }))}
                                            orientation="row"
                                            selectedOption={vendorFundingType}
                                        />
                                    </div>
                                </>}
                        </div>
                        {isVendorFunding == 1 && <div className="marginTop-24 offer-flex-container">
                            {vendorFundingTypeInput[vendorFundingType]?.map((item) => (
                                <Input
                                    key={item}
                                    id="offer_name"
                                    inputProps={{}}
                                    label={item}
                                    name={item}
                                    onChange={handleVendorFundingInput}
                                    placeholder="Enter..."
                                    type="number"
                                    isRequired={true}
                                    value={item == "Ads.amount" ? adsAmount : perUnit}
                                    disabled={!props.allowPromoEdit}
                                />
                            ))}
                        </div>}
                    </div>
                )}
            </div>
            <div className="footer_section">
                <Button
                    onClick={() => { handleBack() }}
                    size="large"
                    variant="secondary"
                >
                    Back
                </Button>
                <Button
                    onClick={() => { handleNext() }}
                    size="large"
                    variant="primary"
                // disabled={getSubmitButtonDisabledState()}
                >
                    Next
                </Button>
            </div>
        </div>
    )
}

export default DiscountRules;