import React, {
    useState,
    useEffect,
} from "react";
import { useSelector } from "react-redux";
import whiteCheckIcon from "../../../assets/imageAssets/whiteCheckIcon.svg?url";
import {
    <PERSON><PERSON>,
    Tooltip,
    Switch,
    Badge
} from "impact-ui";
import { PROMO_SIMULATOR } from "./OfferConstants";
import {
    toCurrencyByCurrencyIdWithDecimalFormatted,
    toPercentage,
    toUnit,
} from "../../../utils/helpers/formatter";
import ScenarioName from "./SimulationScenarioName";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?url";
import OverrideIcon from "../../../assets/imageAssets/override.svg?url";
import DefaultIcon from "../../../assets/imageAssets/default.svg?url";
import ApproveIcon from "../../../assets/imageAssets/thumbup.svg?url";
import { label<PERSON>urrency<PERSON>and<PERSON> } from "../../../utils/helpers/utility_helpers";

const SimulationResultItem = (props) => {
    const {
        promoDetails
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const {
        currency_details = {}
    } = props;

    const [defaultSelected, setDefaultSelected] = useState("original"); // data?.default_selected || 'original';
    const [scenarioData, setScenarioData] = useState({});
    const [isApproved, setIsApproved] = useState(false);
    const [isIARecommended, setIsIARecommended] = useState(false);
    const [datakey, setDataKey] = useState("original");

    useEffect(() => {
        if (!_.isEqual(props.data, scenarioData)) {
            setScenarioData(props.data);
        }
        props?.data?.scenario_type === "optimise"
            ? setIsIARecommended(true)
            : setIsIARecommended(false);
    }, [props.data]);

    useEffect(() => {
        setDefaultSelected(scenarioData.default_selected);
        if (props.showWithStacking) {
            scenarioData.default_selected == "overridden"
                ? setDataKey("stacked_overridden")
                : setDataKey("stacked_original");
        } else {
            scenarioData.default_selected == "overridden"
                ? setDataKey("overridden")
                : setDataKey("original");
        }
    }, [scenarioData]);

    useEffect(() => {
        props.lastApprovedScenario === scenarioData?.scenario_id
            ? setIsApproved(true)
            : setIsApproved(false);
    }, [props.lastApprovedScenario, props.data]);

    useEffect(() => {
        if (props.showWithStacking) {
            if (defaultSelected == "original") {
                setDataKey("stacked_original");
            } else {
                setDataKey("stacked_overridden");
            }
        } else {
            setDataKey(defaultSelected);
        }
    }, [props.showWithStacking]);

    const handleScenarioNameChange = (scenarioName) => {
        props.updateScenarioName(scenarioData, scenarioName);
    };

    const handleApproveClick = () => {
        props.onClickApprove(scenarioData);
    };

    const handleOverrideForecast = () => {
        props.onClickOverrideForecast(scenarioData);
    };

    const handleShowData = () => {
        if (props.showWithStacking) {
            defaultSelected !== "overridden"
                ? setDataKey("stacked_overridden")
                : setDataKey("stacked_original");
        } else {
            defaultSelected !== "overridden"
                ? setDataKey("overridden")
                : setDataKey("original");
        }

        defaultSelected !== "overridden"
            ? setDefaultSelected("overridden")
            : setDefaultSelected("original");
    };

    const kpiItemValue = (formatter, value) => {
        if (typeof value === "number") {
            if (formatter === "dollar") {
                return toCurrencyByCurrencyIdWithDecimalFormatted({
                    value,
                    ...(currency_details || {}),
                });
            }
            if (formatter === "percent") {
                return toPercentage({ value });
            }
            return value;
        }
        return "-";
    };

    const handleMarkAsDefault = () => {
        const reqObj = {
            promo_id: promoDetails?.promo_id,
            scenario_id: scenarioData?.scenario_id,
            default: defaultSelected,
        };
        props.onClickMarkAsDefault(reqObj);
    };

    return (
        <div
            className={`simulation-results-scenario-item-container ${isIARecommended ? "ia-recommended-scenario" : scenarioData?.scenario_order_id == 1 ? "scenario_1" : "scenario_2"}`}
        >
            <div className="scenario-heading-container">
                <div className="scenario-left-heading">
                    <ScenarioName
                        name={scenarioData?.scenario_name || "Scenario " + (scenarioData?.scenario_order_id)}
                        handleNameChange={handleScenarioNameChange}
                        isIARecommended={isIARecommended}
                        allowPromoEdit={props.allowPromoEdit}
                    />
                    {isApproved && (
                        <>
                            <div className="horizontal-line" />
                            <div className="approved_badge">
                                <Badge
                                    size="small"
                                    label="Approved"
                                    color="success"
                                    isIcon
                                    variant="filled"
                                    icon={<img src={whiteCheckIcon} />}
                                />
                            </div>
                        </>
                    )}
                    {scenarioData?.is_overridden &&
                        scenarioData?.default_selected &&
                        scenarioData.default_selected === defaultSelected && (
                            <>
                                <div className="horizontal-line" />
                                <Tooltip
                                    title={"Default # will be shown as the finalized forecast on other screens for an approved scenario."}
                                    variant={"tertiary"}
                                    orientation={"right"}
                                >
                                    <div className="inactive-badge-shape-div">
                                        <p className="secondaryText-14-500">Default # </p>
                                    </div>
                                </Tooltip>
                            </>
                        )}
                </div>
                <div className="flexWithGap16">
                    {scenarioData?.is_overridden && (
                        <Button
                            variant="url"
                            onClick={handleShowData}
                        >
                            {`${defaultSelected === "overridden" ? "Show original forecast" : "Show overridden forecast"}`}
                        </Button>
                        // <Switch
                        //     iaFallback="v3"
                        //     leftLabel={`${defaultSelected === "overridden" ? "Overridden" : "Original"} #`}
                        //     onChange={handleShowData}
                        //     value={defaultSelected === "overridden"}
                        // />
                    )}
                    {isIARecommended && (
                        <Tooltip orientation="top" title={"Edit"} variant="tertiary">
                            <div>
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={EditIcon} />}
                                    onClick={props.openPromoTarget}
                                    size="large"
                                    variant="tertiary"
                                />
                            </div>
                        </Tooltip>
                    )}
                    <Tooltip orientation="top" title={`Override forecast`} variant="tertiary">
                        <div>
                            <Button
                                iconPlacement="left"
                                icon={<img src={OverrideIcon} />}
                                onClick={handleOverrideForecast}
                                disabled={
                                    !promoDetails?.show_override ||
                                    !props.allowPromoEdit ||
                                    (props.showWithStacking &&
                                        !promoDetails?.show_stacked_override) ||
                                    false
                                }
                                size="large"
                                variant="tertiary"
                            />
                        </div>
                    </Tooltip>
                    {scenarioData?.is_overridden &&
                        scenarioData?.default_selected &&
                        scenarioData.default_selected !== defaultSelected && (
                            <Tooltip orientation="top" title={`Make as Default`} variant="tertiary">
                                <div>
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={DefaultIcon} />}
                                        onClick={handleMarkAsDefault}
                                        disabled={!props.allowPromoEdit || false}
                                        size="large"
                                        variant="tertiary"
                                    />
                                </div>
                            </Tooltip>
                        )}

                    {((scenarioData?.is_overridden &&
                        scenarioData?.default_selected &&
                        scenarioData.default_selected === defaultSelected) ||
                        !scenarioData?.is_overridden) ?

                        <Tooltip orientation="top" title={`Approve`} variant="tertiary">
                            <div>
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={ApproveIcon} />}
                                    onClick={handleApproveClick}
                                    disabled={!props.allowPromoEdit || isApproved}
                                    size="large"
                                    variant="tertiary"
                                />
                            </div>
                        </Tooltip>
                        : null
                    }
                </div>
            </div>

            {/* itereate  */}
            <div className="scenario-result-container">
                <div className="scenario-result-scrollable-container">
                    <div className="scenario-metric-row-container">
                        {PROMO_SIMULATOR.SIMULATION_RESULTS.METRIC_DISPLAY_ORDER.map(
                            (item) => (
                                <div key={item.key} className="flexWithCenterAlign">
                                    <div className="scenario-metric-chip">
                                        <p className="primaryText-12-500 scenario-name-text">{labelCurrencyHandler(item?.title, currency_details?.symbol || "$")}</p>
                                    </div>
                                    <div className="scenario-metric-chip-separator" />
                                    <div className="centerFlexWithGap12">
                                        {item.itemOrder.map((metricItem) => (
                                            <div
                                                key={`item_${metricItem.valueKey}`}
                                                className="scenario-metric-item"
                                            >
                                                <p className="text-12-500">
                                                    {labelCurrencyHandler(metricItem?.title, currency_details?.symbol || "$")}
                                                </p>
                                                <p className="text-12-800">
                                                    {typeof scenarioData?.[datakey]?.[metricItem?.valueKey] ===
                                                        "number"
                                                        ? toCurrencyByCurrencyIdWithDecimalFormatted({
                                                            value: scenarioData?.[datakey]?.[metricItem?.valueKey],
                                                            ...(currency_details || {}),
                                                        })
                                                        : "-"}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )
                        )}
                    </div>
                    <div className="scenario-data-kpi-container">
                        {PROMO_SIMULATOR.SIMULATION_RESULTS.MARGIN_DISPLAY_ORDER.map(
                            (kpi) => (
                                <div
                                    key={`kpiItem_${kpi.valueKey}`}
                                    className="scenario-kpi-item"
                                >
                                    <p className="text-12-500">{labelCurrencyHandler(kpi.title, currency_details?.symbol || "$")}</p>
                                    <p className="text-12-800">
                                        {kpiItemValue(kpi.formatter, scenarioData?.[datakey]?.[kpi?.valueKey])}
                                    </p>
                                </div>
                            )
                        )}
                    </div>
                    <div className="scenario-data-kpi-container">
                        {PROMO_SIMULATOR.SIMULATION_RESULTS.ST_DISPLAY_ORDER.map(
                            (kpi) => (
                                <div
                                    key={`kpiItem_${kpi.valueKey}`}
                                    className="scenario-kpi-item"
                                >
                                    <p className="text-12-500">{labelCurrencyHandler(kpi.title, currency_details?.symbol || "$")}</p>
                                    <p className="text-12-800">
                                        {kpiItemValue(kpi.formatter, scenarioData?.[datakey]?.[kpi?.valueKey])}
                                    </p>
                                </div>
                            )
                        )}
                    </div>
                </div>

                <div className="scenario-sales-units-container">
                    <p className="text-12-800">Units</p>
                    <div className="horizontal-divider-line marginTop-8 marginBottom-16" />
                    <div className="scenario-sales-units-item-container">
                        {PROMO_SIMULATOR.SIMULATION_RESULTS.SALES_UNITS_DISPLAY_ORDER.map(
                            (salesUnitItem, index) => (
                                <div
                                    key={`salesUnitItem_${salesUnitItem.valueKey}`}
                                    className="scenario-sales-units-item"
                                >
                                    <p className="text-12-500">
                                        {labelCurrencyHandler(salesUnitItem.title, currency_details?.symbol || "$")}
                                    </p>
                                    <p className="text-12-800">
                                        {typeof scenarioData?.[datakey]?.[salesUnitItem.valueKey] ===
                                            "number"
                                            ? toUnit({
                                                value: scenarioData?.[datakey]?.[salesUnitItem?.valueKey],
                                            })
                                            : "-"}
                                    </p>
                                </div>
                            )
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SimulationResultItem;
