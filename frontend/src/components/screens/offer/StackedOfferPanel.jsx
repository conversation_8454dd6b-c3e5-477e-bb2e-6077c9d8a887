import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import _ from "lodash";
import StackedIcon from "../../../assets/imageAssets/stackedIcon.svg?.url";
import CouponIcon from "../../../assets/imageAssets/couponIcon.png";
import { Panel, Button, Tooltip, } from "impact-ui";
import verticalLineIcon from "../../../assets/imageAssets/verticalLineIcon.svg?.url";
import { global_labels } from "../../../constants/Constants";

const StackedOfferPanel = (props) => {
    const [stackedOffers, setStackedOffers] = useState([]);
    const [nonStackedOffers, setNonStackedOffers] = useState([]);

    const {
        promoDetails,
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    useEffect(() => {
        const stackableOffers = promoDetails?.stackable || [];
        const nonStackableOffers = promoDetails?.non_stackable || [];

        setStackedOffers([...stackableOffers]);
        setNonStackedOffers([...nonStackableOffers]);
    }, [promoDetails]);

    return (
        <Panel
            anchor="right"
            open={props.showStackedOfferModal}
            onClose={() => props.setShowStackedOfferModal(false)}
            className="promo-stack-dialog-container"
            size="large"
            title={
                <div className="offer-flex-container">
                    <Button
                        iconPlacement="left"
                        icon={<img src={StackedIcon} />}
                        size="large"
                        variant="secondary"
                    />
                    <p>Stack {global_labels?.promo_alias_plural}</p>
                </div>
            }
        >
            {
                stackedOffers.length > 0 ?
                    <div>
                        <p className="text-14-800">Stackable</p>
                        {stackedOffers.map((offer) => {
                            return <div className="marginTop-12 marginBottom-12 stacked-offer-details-container">
                                <div className="stacked-icon-container">
                                    <div />
                                    <div>
                                        <img alt={"offer"} src={CouponIcon} />
                                    </div>
                                </div>
                                <div className="stacked-offer-details paddingLeft-24">
                                    <Tooltip
                                        title={offer?.promo_name || "-"}
                                        orientation="bottom"
                                        variant={"tertiary"}
                                    >
                                        <p className="text-16-800 stacked-offer-name">{offer.promo_name}</p>
                                    </Tooltip>
                                    <p className="secondaryText-12-500">{offer.offer_type_combined_display_name}</p>
                                    <div className="offer-duration-container">
                                        <p className="text-14-500">{offer.start_date} - {offer.end_date} : </p>
                                        <div className="stacked-offer-duration-details">
                                            <p className="secondaryText-12-500"> {offer.duration} days</p>
                                            <p className="secondaryText-12-500"> : {offer.overlap_duration} overlapping</p>
                                        </div>
                                    </div>
                                    <div className="stacked-offer-product-details">
                                        <div
                                            className="stacked-badge-shape-div"
                                        >
                                            <p className="primaryText-14-500">{`Priority ${offer.priority_number}`}</p>
                                        </div>
                                        <img src={verticalLineIcon} alt="verticalLine" />
                                        <p className="secondaryText-14-500">{offer.products_count} products</p>
                                    </div>
                                </div>
                                <div className="coupon-type-container">
                                    <span className="coupon-type-details">Offer</span>
                                </div>
                            </div>
                        })}
                    </div>
                    : null
            }
            {
                nonStackedOffers.length > 0 ?
                    <div>
                        <p className="text-14-800">Non stackable</p>
                        {nonStackedOffers.map((offer) => {
                            return <div className="marginTop-12 marginBottom-12 stacked-offer-details-container">
                                <div className="stacked-icon-container">
                                    <div />
                                    <div>
                                        <img alt={"offer"} src={CouponIcon} />
                                    </div>
                                </div>
                                <div className="stacked-offer-details paddingLeft-24">
                                    <Tooltip
                                        title={
                                            <div>
                                                <div>{offer?.promo_name || "-"}</div>
                                            </div>
                                        }
                                        orientation="bottom"
                                        variant={"tertiary"}
                                    >
                                        <p className="text-16-800 stacked-offer-name">{offer.promo_name}</p>
                                    </Tooltip>
                                    <p className="secondaryText-12-500">{offer.offer_type_combined_display_name}</p>
                                    <div className="offer-duration-container">
                                        <p className="text-14-500">{offer.start_date} - {offer.end_date}</p>
                                        <div className="stacked-offer-duration-details">
                                            <p className="secondaryText-12-500"> {offer.duration} days</p>
                                            <p className="secondaryText-12-500"> : {offer.overlap_duration} overlapping</p>
                                        </div>
                                    </div>
                                    <div className="stacked-offer-product-details">
                                        <div
                                            className="stacked-badge-shape-div"
                                        >
                                            <p className="primaryText-14-500">{`Priority ${offer.priority_number}`}</p>
                                        </div>
                                        <img src={verticalLineIcon} alt="verticalLine" />
                                        <p className="secondaryText-14-500">{offer.products_count} products</p>
                                    </div>
                                </div>
                                <div className="coupon-type-container">
                                    <span className="coupon-type-details">Offer</span>
                                </div>
                            </div>
                        })}
                    </div>
                    : null
            }
        </Panel>
    )
}

export default StackedOfferPanel;