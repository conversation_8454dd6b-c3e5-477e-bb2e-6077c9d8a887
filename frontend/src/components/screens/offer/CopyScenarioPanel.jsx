import React, { useState, useMemo } from 'react';
import { Panel, RadioButtonGroup, Button } from "impact-ui";
import GreyCheckIcon from "../../../assets/imageAssets/GreyCheckIcon.svg?.url";
import _ from "lodash";
import LoaderComponent from '../../ui/loaderComponent/LoaderComponent';
function CopyScenarioPanel({
    isPanelOpen,
    setIsPanelOpen,
    handleApply,
    scenarioData = {},
    props
}) {
    const [selectedScenarioToCopy, setSelectedScenarioToCopy] = useState(0);
    const [selectedDestinationScenario, setSelectedDestinationScenario] = useState(null);

    const selectvalue = useMemo(() => {
        if (!isPanelOpen || !scenarioData) return [];
        const arr = [];
        const scenarioKeys = ["ia_recommended"].concat(Object.keys(scenarioData).filter(key => key.includes("scenario_")));

        if (scenarioKeys && !_.isEmpty(scenarioData)) {
            scenarioKeys.forEach((key) => {
                if (key === "ia_recommended") {
                    arr.push({
                        ...scenarioData?.[key],
                        label: "IA Recommended",
                        value: "ia_recommended",
                    })
                }
                else if (scenarioData?.[key]?.scenario_id) {
                    arr.push({
                        ...scenarioData?.[key],
                        label: scenarioData?.[key]?.name || ("Scenario " + key.slice(-1)),
                        value: key,
                    })
                }
            })
        }
        return arr;
    }, [scenarioData, isPanelOpen])

    const copyDestinationOptions = useMemo(() => {
        if (!isPanelOpen || !scenarioData) return [];
        let arr = [];
        if (selectvalue?.length) {
            arr = selectvalue?.filter(item => {
                return (item?.value != selectedScenarioToCopy) && (item?.value != "ia_recommended");
            });
        }
        return arr;
    }, [selectvalue, selectedScenarioToCopy])

    const handleCopyScenario = () => {
        if (selectedScenarioToCopy != null && selectedDestinationScenario != null) {
            const source = {
                scenario_order_id: selectvalue?.find(item => item?.value == selectedScenarioToCopy)?.scenario_order_id,
                scenario_id: selectvalue?.find(item => item?.value == selectedScenarioToCopy)?.scenario_id
            }
            const target = {
                scenario_order_id: selectvalue?.find(item => item?.value == selectedDestinationScenario)?.scenario_order_id,
                scenario_id: selectvalue?.find(item => item?.value == selectedDestinationScenario)?.scenario_id
            }
            handleApply(source, target);
        }
    }

    return (
        <Panel
            anchor="right"
            open={isPanelOpen}
            className="bulk-edit-panel"
            onClose={() => setIsPanelOpen(false)}
            onPrimaryButtonClick={handleCopyScenario}
            onSecondaryButtonClick={() => {
                setIsPanelOpen(false);
            }}
            primaryButtonLabel={`Apply`}
            secondaryButtonLabel={`Cancel`}
            size="large"
            title={`Copy Scenario`}
        >
            <LoaderComponent>
                <p className="marginBottom-12 label-14px-normal">Select the scenario from which you want to copy</p>
                <div className="padding-8">
                    <RadioButtonGroup
                        options={selectvalue}
                        selectedOption={selectedScenarioToCopy}
                        isDisabled={false}
                        onChange={(_e, val) => {
                            setSelectedScenarioToCopy(val);
                            setSelectedDestinationScenario(null);
                        }}
                        orientation="row"
                        className="metrics_radio_button_group"
                    />
                </div>
                <hr className="copy-scenario-horizontal-line" />
                <p className="marginBottom-12 label-14px-normal">Select the scenario to which you want to copy</p>
                <div className="paddingTop-8 flexWithGap16">
                    {copyDestinationOptions.map((item, index) => {
                        return (
                            <Button
                                className=""
                                icon={<img src={GreyCheckIcon} alt="grey-check-icon" className={selectedDestinationScenario == item?.value ? "svg-blue-icon" : ""} />}
                                iconPlacement="left"
                                size="large"
                                type="default"
                                variant={selectedDestinationScenario == item?.value ? "secondary" : "tertiary"}
                                onClick={() => {
                                    setSelectedDestinationScenario(item?.value);
                                }}
                                key={`${item?.value}-${index}`}
                            >
                                {item?.label}
                            </Button>
                        )
                    })}
                </div>
            </LoaderComponent>
        </Panel>
    )
}

export default CopyScenarioPanel
