import React, { useEffect, useState, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";
import _ from "lodash";
import { Button, Table, Select, Badge, Panel, Tooltip } from "impact-ui";
import EmptyData from "../../ui/emptyData/EmptyData";
import {
    setNewSseConnection,
    setSseEventTriggeredStatus,
    toggleLengthyOpLoader,
    toastError
} from "../../../store/features/global/global";

import {
    getStep2Details,
    getStep3SimulationResults,
    getStep3Basics,
    getStackedOffers,
    getViewByOptions,
    setPromoDetails,
    getValidOffersDetails,
    generateSimulationData,
    setFromStackingView,
    setIsOptimise,
    approveScenario,
    updateScenarioName,
    saveAsDefault,
    getDetailedSimulationResults,
    setSimulatedPromoId,
    setDetailedSimulationResults,
    updatePromoDetails,
    promoDownloads,
    getTableMetadata,
    getPromoDiscounts,
    getNewScenarioID,
    setScenarioUpdatedData,
    bulkEditScenarioData,
    getPromoGifData,
    setPromoGifData,
    downloadPriceFile,
    copyScenarioData,
    getSpecialOfferTypes,
    getTiers,
    getBmsmOfferTypes,
    getValidTierOfferTypes,
    getCurrencyOptions
} from "../../../store/features/promoReducer/promoReducer";
import {
    X_VALUE_OFFERS,
    Y_VALUE_OFFERS,
    Z_VALUE_OFFERS,
    X_TYPE_OFFERS,
    Y_TYPE_OFFERS,
    simulationTableConfig,
    scenario_details_table,
    simulationTableStaticColumnsIds,
    advanceSearchColumnsKeyMapping,
    headerSearchColumnsKeyMapping
} from "./OfferConstants";
import SimulationResultsScenarios from "./SimulationResultScenarios";
import unhappyImage from "../../../assets/imageAssets/unhappyImage.svg?.url";
import warningIcon from "../../../assets/imageAssets/warningIcon.svg?.url";
import verticalLineIcon from "../../../assets/imageAssets/verticalLineIcon.svg?.url";
import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import levelFilterIconInPanel from "../../../assets/imageAssets/levelFilterIconInPanel.svg?.url";
import CopyIcon from "../../../assets/imageAssets/copyIcon.svg?.url";
import GraphBar from "../../../assets/imageAssets/graphBar.svg?.url";
import GIFOverlay from "../../common/gifOverlay/GIFOverlay";
import StackedOfferPanel from "./StackedOfferPanel";
import OfferTargetPanel from "./OfferTargetPanel";
import { capitalizeFirstLetter, replaceSpecialCharToCharCode } from "../../../utils/helpers/utility_helpers";
import SimulationResultComparisonView from "./SimulationResultComparisonView";
import BulkEditPanel from "./BulkEditPanel";
import CopyScenarioPanel from "./CopyScenarioPanel";
import GraphView from "./GraphView";
import { DEFAULT_CURRENCY, global_labels } from "../../../constants/Constants";
import { productSelection, storeSelection, timeSelection } from "../../../constants/FilterConfigConstants";

const OfferSimulator = (props) => {
    const simulationTableRef = useRef();
    const detailedSimulationTableRef = useRef();
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const {
        activePromoId,
        viewByOptions,
        promoDetails,
        validOffers,
        simulatedPromoId,
        simulationInvalid,
        updatedScenarioName,
        detailedSimulationResults,
        scenarioUpdatedData,
        promoGifData,
        isOptimise
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const { sseRecievedEvents, lengthyOpLoader, global_configs } = useSelector(
        (store) => store?.pricesmartPromoReducer.global
    );

    const [simulationTableData, setSimulationTableData] = useState([]);
    const [discountLevelOptions, setDiscountLevelOptions] = useState(null);
    const [updatedSimulationData, setUpdatedSimulationData] = useState([]);
    const [offerComment, setOfferComment] = useState("");
    const [scenarioNames, setScenarioNames] = useState({});
    const [validOffersDispatched, setValidOffersDispatched] = useState(false);
    const [scenarioCount, setScenarioCount] = useState(1);
    const [discountLevel, setDiscountLevel] = useState(-200);
    const [updatedScenarios, setUpdatedScenarios] = useState([]);
    const [lastUpdatedScenario, setLastUpdatedScenario] = useState("");
    const [showWithStacking, setShowWithStacking] = useState(false);
    const [showDetailedResults, setShowDetailedResults] = useState(false);
    const [showStackedOfferModal, setShowStackedOfferModal] = useState(false);
    const [isPromoTargetPanelOpen, setIsPromoTargetPanelOpen] = useState(false);
    const [nameUpdatedScenario, setNameUpdatedScenario] = useState({});
    const [isDiscountLevelOpen, setIsDiscountLevelOpen] = useState(null);
    const [showComparison, setShowComparison] = useState(false);
    const [newScenarioDetails, setNewScenarioDetails] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [unselectedRows, setUnselectedRows] = useState([]);
    const [isBulkEditPanelOpen, setIsBulkEditPanelOpen] = useState(false);
    const [isPanelOpen, setIsPanelOpen] = useState(false);
    const [selectedProductHierarchy, setSelectedProductHierarchy] = useState([]);
    const [selectedStoreHierarchy, setSelectedStoreHierarchy] = useState([]);
    const [selectedTimeHierarchy, setSelectedTimeHierarchy] = useState([]);
    const [simulationTableConfigdata, setSimulationTableConfigdata] = useState([]);
    const [isSpecialOfferTypePanelOpen, setIsSpecialOfferTypePanelOpen] = useState(true);
    const [step3uuid, setStep3uuid] = useState(null);
    const [isBulkEdited, setIsBulkEdited] = useState(false);
    const [isSelectAllRows, setIsSelectAllRows] = useState(false);
    const [gifTimerData, setGIFTimerData] = useState({});
    const [gifData, setGIFData] = useState({});
    const [isGraphViewPanelOpen, setIsGraphViewPanelOpen] = useState(false);
    const [isCopyScenarioPanelOpen, setIsCopyScenarioPanelOpen] = useState(false);
    const [isScenarioCopied, setIsScenarioCopied] = useState(false);
    const [advanceSearchFilters, setAdvanceSearchFilters] = useState([]);
    const [currencyOptions, setCurrencyOptions] = useState([]);
    const [step3Basics, setStep3Basics] = useState(null);
    const [selectedCurrency, setSelectedCurrency] = useState({
        value: promoDetails?.currency_id || DEFAULT_CURRENCY?.value,
        symbol: promoDetails?.currency_symbol || DEFAULT_CURRENCY?.symbol,
        label: promoDetails?.currency_name || DEFAULT_CURRENCY?.label,
    });
    const [currency, setCurrency] = useState({
        currency_id: DEFAULT_CURRENCY?.value,
        currency_symbol: DEFAULT_CURRENCY?.symbol,
        currency_name: DEFAULT_CURRENCY?.label,
    });
    const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);

    // Configuration for hierarchy filters used in detailed results panel
    const hierarchyConfig = [
        {
            id: "productHierarchy", 
            title: "Product Hierarchy",
            labels: productSelection,
            selected: selectedProductHierarchy,
            setter: setSelectedProductHierarchy,
        },
        {
            id: "storeHierarchy",
            title: "Store Hierarchy",
            labels: storeSelection,
            selected: selectedStoreHierarchy,
            setter: setSelectedStoreHierarchy,
        },
        {
            id: "timeHierarchy",
            title: "Time Hierarchy",
            labels: timeSelection,
            selected: selectedTimeHierarchy,
            setter: setSelectedTimeHierarchy,
        },
    ];

    // Cleanup effect when component unmounts
    useEffect(() => {
        return () => {
            setNewScenarioDetails([]);
            dispatch(setScenarioUpdatedData({}));
            setSelectedRows([]);
        }
    }, []);

    useEffect(() => {
        if (props.activeStep === props.stepNumber) {
            // Called once as the data is same for all the rows if they are selected for special offer type
            dispatch(getSpecialOfferTypes({}));
            dispatch(getTiers({ promo_id: activePromoId }));
            dispatch(getBmsmOfferTypes());
            dispatch(getValidTierOfferTypes());
            callCurrencyOptions();
            setAdvanceSearchFilters([]);
        }
    }, [props.activeStep]);

    useEffect(() => {
        if (props.activeStep === props.stepNumber) {
            const getStep3BasicsData = async () => {
                const step3BasicsData = await dispatch(getStep3Basics(activePromoId));
                setSelectedCurrency({
                    value: step3BasicsData?.currency_id,
                    symbol: step3BasicsData?.currency_symbol,
                    label: step3BasicsData?.currency_name,
                });
                setCurrency({
                    currency_id: step3BasicsData?.currency_id,
                    currency_symbol: step3BasicsData?.currency_symbol,
                    currency_name: step3BasicsData?.currency_name,
                });
                setStep3Basics(step3BasicsData);
            }
            getStep3BasicsData();
        }
    }, [props.activeStep, activePromoId]);

    // Initialize component data when step or promo ID changes
    useEffect(() => {
        if (!_.isEmpty(step3Basics)) {
            onChangeCall();
        }
    }, [step3Basics]);

    // Main initialization function for loading promo and simulation data
    const onChangeCall = async () => {
        if (props.activeStep === props.stepNumber && activePromoId) {
            // Check if a guid is present. If not, create a new connection
            const guid = sessionStorage.getItem("UNIQ_SSE_KEY");
            if (!guid) {
                dispatch(setNewSseConnection(true));
            }

            const step3uuid = crypto.randomUUID();
            setStep3uuid(step3uuid);

            setDiscountLevel({
                label: "Overall",
                value: -200,
            });
            setValidOffersDispatched(false);
            dispatch(setScenarioUpdatedData({}));
            setNewScenarioDetails([]);
            setIsBulkEdited(false);
            setAdvanceSearchFilters([]);

            await dispatch(getStep2Details(activePromoId));
            const tableMetadata = await dispatch(
                getTableMetadata(activePromoId)
            );
            let columnsToDisplay = {};

            Object.keys(tableMetadata).forEach((key) => {
                if (
                    tableMetadata[key]?.columns &&
                    tableMetadata[key]?.columns?.length > 0
                ) {
                    tableMetadata[key].columns.forEach((column) => {
                        columnsToDisplay[column.value_key] = {
                            field: column.value_key,
                            headerName: column.column_name,
                            filter: column.value_key?.includes("price") ? "agNumberColumnFilter" : "agTextColumnFilter",
                        }
                    });
                }
            });

            const columnsToDisplayKeys = Object.keys(columnsToDisplay);
            const tempConfig = simulationTableConfig.map((column) => {
                if (columnsToDisplayKeys.includes(column.field)) {
                    column.hide = false;
                    column.headerName = columnsToDisplay[column.field]?.headerName
                } else if (
                    simulationTableStaticColumnsIds.includes(column.colId)
                ) {
                    column.hide = false;
                } else {
                    column.hide = true;
                }
                return column;
            });
            setSimulationTableConfigdata(tempConfig);
            refreshGridData({
                new_scenario_details: newScenarioDetails,
            })
            const simulationResults = await dispatch(getStep3SimulationResults({
                promo_id: activePromoId,
                target_currency_id: selectedCurrency?.value,
            }));

            if (
                simulationResults?.scenario_data?.length > 0 &&
                _.isNull(simulationResults?.scenario_data?.[0])
            ) {
                const temoScenariData =
                    step3Basics?.simulation?.[0]?.scenario_data || [];
                let updatedScenarioData = [];
                if (temoScenariData?.length > 0) {
                    temoScenariData.forEach((scenario) => {
                        if (scenario?.scenario_type === "resimulation") {
                            updatedScenarioData.push(
                                `scenario_${scenario?.scenario_order_id}`
                            );
                        }
                    });
                }

                setUpdatedScenarios(updatedScenarioData);
            }
            dispatch(getStackedOffers({ promo_id: activePromoId }));

            const payload = {
                tableType: "simulation_results_details",
                screenType: "simulation",
            };

            dispatch(getViewByOptions(payload));
        } else {
            dispatch(
                updatePromoDetails({
                    simulation: [],
                    scenario_data: [],
                    targets: {},
                })
            );
            setUpdatedSimulationData([]);
        }
    };

    useEffect(() => {
        setDiscountLevelOptions(_.cloneDeep(viewByOptions));
    }, [viewByOptions]);

    useEffect(() => {
        if (updatedScenarioName) {
            const currentPromoDetails = _.cloneDeep(promoDetails);

            // Update scenario name in the scenario results (aggregate)
            if (!_.isEmpty(currentPromoDetails?.scenario_data)) {
                let updatedIndex = currentPromoDetails?.scenario_data?.findIndex(
                    (d) => d.scenario_id === nameUpdatedScenario.scenario_id
                );
                currentPromoDetails.scenario_data[
                    updatedIndex
                ].scenario_name = updatedScenarioName;
            }

            // Reset the column name in the discounts table
            if (
                !_.isEmpty(currentPromoDetails?.simulation?.[0]?.scenario_data)
            ) {
                const updatedIndex = currentPromoDetails.simulation[0].scenario_data?.findIndex(
                    (d) => d.scenario_id === nameUpdatedScenario.scenario_id
                );
                if (updatedIndex !== -1) {
                    currentPromoDetails.simulation[0].scenario_data[
                        updatedIndex
                    ].scenario_name = updatedScenarioName;
                }
            }

            dispatch(
                setPromoDetails({
                    ...props.promoDetails,
                    ...currentPromoDetails,
                })
            );

            // Reset column name in the detailed simulation results
            setScenarioNames({
                ...scenarioNames,
                [`scenario_${nameUpdatedScenario.scenario_order_id}`]: updatedScenarioName,
            });
            setNameUpdatedScenario({});
        }
    }, [updatedScenarioName]);

    useEffect(() => {
        if (!_.isEmpty(promoDetails) && props.activeStep === props.stepNumber) {
            // Each row has every value of the selected discount level
            if (
                !_.isEmpty(promoDetails?.discount_rules) &&
                promoDetails.simulation
            ) {
                const { offer_comment } = promoDetails;
                setOfferComment(offer_comment);

                // Call valid offers
                if (!validOffersDispatched) {
                    getValidOffersRequest();
                }

                // If valid offers already exists
                if (!_.isEmpty(validOffers?.[activePromoId])) {
                    setDiscountLevel({
                        label: "Overall",
                        value: -200,
                    });
                    // prepareSimulationTableData(discountLevel);
                }
            }
        }
    }, [promoDetails]);

    useEffect(() => {
        if (
            props.activeStep === props.stepNumber &&
            !_.isEmpty(promoDetails?.discount_rules) &&
            promoDetails.simulation
        ) {
            setValidOffersDispatched(false);
            // set the discount level for step 2 discount level
            setDiscountLevel({
                label: "Overall",
                value: -200,
            });
            //prepare the simulation data for the selected discount level
            // prepareSimulationTableData(discountLevel);
            // prepareSimulationTableData();
        }
    }, [validOffers]);

    useEffect(async () => {
        const { override = {}, approve_scenario = {} } =
            sseRecievedEvents || {};

        if (
            (override?.eventData?.status === 200 ||
                override?.eventData?.status === true ||
                approve_scenario?.eventData?.status === 200 ||
                approve_scenario?.eventData?.status === true) &&
            (override?.triggeredStatus === false ||
                approve_scenario?.triggeredStatus === false)
        ) {
            //Refresh scenario table and simulation results after receiving sseResponse
            await dispatch(getStep3Basics(activePromoId));

            refreshGridData({
                new_scenario_details: newScenarioDetails,
            })
            console.log('sse recieved events');
            await dispatch(getStep3SimulationResults({
                promo_id: activePromoId,
                target_currency_id: selectedCurrency?.value,
            }));
            override?.triggeredStatus === false &&
                dispatch(
                    setSseEventTriggeredStatus({
                        eventId: "override",
                        status: true,
                    })
                );
            approve_scenario?.triggeredStatus === false &&
                dispatch(
                    setSseEventTriggeredStatus({
                        eventId: "approve_scenario",
                        status: true,
                    })
                );
        }
    }, [sseRecievedEvents]);

    useEffect(async () => {
        if (
            !lengthyOpLoader &&
            simulatedPromoId === activePromoId &&
            props.activeStep === props.stepNumber
        ) {
            setUpdatedSimulationData([]);
            // Refresh Simulation table
            dispatch(getStep2Details(activePromoId));
            await dispatch(getStep3Basics(activePromoId));
            refreshGridData({
                new_scenario_details: newScenarioDetails,
            })
            console.log('use effect length op[ loader');
            await dispatch(getStep3SimulationResults({
                promo_id: activePromoId,
                target_currency_id: selectedCurrency?.value,
            }));
        }

        if (!lengthyOpLoader) {
            setGIFTimerData({});
            setGIFData({});
        }
    }, [lengthyOpLoader, simulatedPromoId]);

    useEffect(() => {
        if (lastUpdatedScenario) {
            setUpdatedScenarios(
                _.uniq([...updatedScenarios, lastUpdatedScenario])
            );
        }
    }, [lastUpdatedScenario]);

    useEffect(() => {
        //on time estimate api call, we save data to promoGifData redux state
        //if promoGifData is not empty, we set the gif modal data
        if (!_.isEmpty(promoGifData)) {
            //send the type in payload of time estimate, so that we can set the gif modal data accordingly
            const timerData = {
                time_in_mins: promoGifData.time_in_mins,
            };

            let tempGifData = {
                product_count: promoGifData.product_count,
                store_count: promoGifData.store_count,
                data_points: promoGifData.data_points,
                promo_duration: promoGifData.promo_duration,
                scenario_count: promoGifData.scenario_count,
                discount_points: promoGifData.discount_points,
            };
            if (promoGifData.type === "optimize") {
                tempGifData.discount = promoGifData.discount;
            }

            setGIFTimerData(timerData);
            setGIFData(tempGifData);
        }
    }, [promoGifData]);

    const adjustColumns = () => {
        if (!simulationTableRef?.current || !simulationTableRef?.current?.api) {
            return;
        }
        const gridApi = simulationTableRef?.current?.api;
        const allColumnIds = [];
        gridApi.getColumns().forEach((column) => {
            if (!column?.colDef?.hide) {
                allColumnIds.push(column.getId());
            }
        });

        // First, auto-size the columns to fit their content
        gridApi?.autoSizeColumns(allColumnIds);

        // Get the width of the grid and the total width of the columns
        const gridElement = document.querySelector('.ag-root-wrapper');
        const gridWidth = gridElement?.clientWidth || 0;
    
        const totalColumnWidth = allColumnIds.reduce((totalWidth, colId) => {
            const column = gridApi
                ?.getColumnState()
                ?.find((col) => col?.colId === colId && col?.hide === false);
            return totalWidth + (column ? column?.width : 0);
        }, 0);

        // If the total column width is greater than the grid width, call autoSizeColumns
        if (totalColumnWidth > gridWidth) {
            gridApi?.autoSizeColumns(allColumnIds);
        } else {
            gridApi?.sizeColumnsToFit(allColumnIds); // Fit columns to grid width
        }
    };

    const callCurrencyOptions = async () => {
        const response = await dispatch(getCurrencyOptions({
            promo_id: activePromoId,
        }));
        if (!_.isEmpty(response)) {
            setCurrencyOptions(response);
        } else {
            setCurrencyOptions([]);
        }
    }

    const updateCurrency = (selectedOptions) => {
        setSelectedCurrency(selectedOptions);
        setCurrency({
            currency_id: selectedOptions?.value,
            currency_symbol: selectedOptions?.symbol || "$",
            currency_name: selectedOptions?.label,
        });
        if (selectedOptions.value !== currency?.value) {
            dispatch(getStep3SimulationResults({
                promo_id: activePromoId,
                target_currency_id: selectedOptions?.value,
            }));
        }
    }

    const createServerSideDatasource = (payload = {}) => {
        return {
            getRows: async (params) => {
                const { sortModel, filterModel = {} } = params.request;
                const filters = [];
                if (!_.isEmpty(filterModel)) {
                    Object.keys(filterModel).forEach(key => {
                        filters.push({
                            column_name: headerSearchColumnsKeyMapping[key] || key,
                            operator: filterModel[key]?.type,
                            value: filterModel[key]?.filter || null,
                            type: filterModel[key]?.filterType === "text" ? "string" : "numeric",
                        })
                    })
                }
                let tablePayload = {
                    promo_id: activePromoId,
                    limit: 100,
                    page: params.request.endRow / 100,
                    sort_key: sortModel?.[0]?.colId || "id",
                    sort_order: sortModel?.[0]?.sort || "asc",
                    filters: [...advanceSearchFilters, ...filters],
                    unsaved_scenario_data: [],
                    new_scenario_details: newScenarioDetails,
                    include_temporary_saved_changes: false,
                    ...payload
                };




                const tableResponse = await dispatch(getPromoDiscounts(tablePayload));

                if (!_.isEmpty(tableResponse)) {
                    // Since the backend does not provide a total row count,
                    // we calculate it dynamically based on the current response:
                    // - If response length equals page size (100), assume more data exists
                    // - Otherwise, calculate total using current page data
                    params.success({
                        rowData: _.cloneDeep(prepareSimulationTableData(tableResponse)),
                        rowCount: tableResponse?.length === 100 ? params.request.endRow + 100 : (params.request.endRow - 100) + tableResponse?.length,
                    });
                } else {
                    params.success({ rowData: [], rowCount: 0 });
                }
            },
        };
    };

    const onGridReady = useCallback((params) => {
        // select all effected plans on grid ready for modal table
        adjustColumns();

        // let datasource = createServerSideDatasource();
        // params.api.setGridOption("serverSideDatasource", datasource);

    });


    const refreshGridData = (payload = {}) => {
        if (simulationTableRef?.current?.api) {
            const datasource = createServerSideDatasource(payload);
            simulationTableRef?.current?.api.setGridOption('serverSideDatasource', datasource);
        }
    };

    const getValidOffersRequest = async () => {
        // Check that valid offers doesn't already exist for this promoID
        if (!validOffers?.[activePromoId]) {
            const payload = {
                actionType: "resimulate",
                promo_id: activePromoId,
            };
            dispatch(getValidOffersDetails(payload));
            setValidOffersDispatched(true);
        }
    };

    const prepareSimulationTableData = (simulationData) => {
        const simulationTableData = [];
        const simulation = simulationData


        const unhideColumnsTemp = [];

        if (!_.isEmpty(simulation)) {
            simulation.forEach((record) => {
                // Extract all properties except scenario_data
                const { scenario_data, ...rowData } = record;

                // Find IA recommendation and scenarios more efficiently
                const iaReco =
                    scenario_data?.find(
                        (s) => s.scenario_type === "ia_recommended"
                    ) || {};
                const scenarios = _.sortBy(
                    scenario_data?.filter(
                        (s) => s.scenario_type !== "ia_recommended"
                    ),
                    "scenario_order_id"
                );

                // Set IA recommended data
                rowData.ia_recommended = {
                    ...iaReco,
                    offer_type: iaReco.offer_type || null,
                    offer_type_id: iaReco.offer_type_id || null,
                    offer_x_value: iaReco.offer_x_value,
                    offer_y_value: iaReco.offer_y_value,
                    offer_z_value: iaReco.offer_z_value,
                    offer_x_type: iaReco.offer_x_type,
                    offer_y_type: iaReco.offer_y_type,
                    tier_id: iaReco.tier_id,
                    scenario_type: iaReco.scenario_type,
                    order_id: iaReco.scenario_order_id || 0,
                };

                // Process scenarios more efficiently
                scenarios.forEach((sc) => {
                    const scenarioKey = `scenario_${sc.scenario_order_id}`;
                    unhideColumnsTemp.push(scenarioKey);

                    rowData[scenarioKey] = {
                        ...sc,
                        offer_type: sc.offer_type,
                        offer_type_id: sc.offer_type_id,
                        id: sc.scenario_id,
                        name: sc.scenario_name || `Scenario ${sc.scenario_order_id}`,
                        scenario_type: sc.scenario_type,
                        order_id: sc.scenario_order_id,
                        offer_x_value: sc.offer_x_value,
                        offer_y_value: sc.offer_y_value,
                        offer_z_value: sc.offer_z_value,
                        offer_x_type: sc.offer_x_type,
                        offer_y_type: sc.offer_y_type,
                        tier_id: sc.tier_id,
                    };
                });

                simulationTableData.push(rowData);
            });

            setSimulationTableData(simulationTableData);
            setUpdatedSimulationData(simulationTableData);

            // Calculate scenario count more efficiently
            setScenarioCount(
                new Set(
                    unhideColumnsTemp.filter((col) =>
                        col.startsWith("scenario")
                    )
                ).size
            );

            // Update table column visibility and refresh
            const gridApi = simulationTableRef?.current?.api;
            if (gridApi) {
                const savedState = gridApi.getColumnState();
                if (savedState) {
                    const unhideColumnsSet = new Set(unhideColumnsTemp);
                    savedState.forEach((config) => {
                        if (unhideColumnsSet.has(config.colId)) {
                            config.hide = false;
                        }
                    });

                    // Apply column state
                    gridApi.applyColumnState({
                        state: _.cloneDeep(savedState),
                        applyOrder: true,
                    });

                    // Update row data and refresh the grid
                    // const res = await gridApi?.applyTransaction({ update: simulationTableData });
                    // redraw only updated rows
                    // gridApi?.redrawRows({ rowNodes: res.update });

                    // Adjust columns after data update
                    setTimeout(() => {
                        adjustColumns();
                    }, 0);
                }
            }
        }
        return simulationTableData;
    }

    const addScenarioHandler = async () => {
        if (scenarioCount >= 2) {
            dispatch(toastError("You can only add 2 scenarios"));
            return;
        }

        setScenarioCount(scenarioCount + 1);
        const newScenarioId = await dispatch(getNewScenarioID());
        console.log("newScenarioId", newScenarioId);
        console.log("simulationTableData", simulationTableData);

        let addedScenarioDetails = newScenarioDetails;

        if (newScenarioId?.new_scenario_id) {
            addedScenarioDetails.push({
                scenario_id: newScenarioId?.new_scenario_id,
                scenario_name: `Scenario ${scenarioCount + 1}`,
                scenario_order_id: scenarioCount + 1,
            });

            // Get current data and modify it
            const gridApi = simulationTableRef.current.api;
            const updatedRows = [];

            gridApi.forEachNode(node => {
                const updatedRow = { ...node.data };
                updatedRow["scenario_2"] = {
                    ...updatedRow["ia_recommended"],
                    id: newScenarioId?.new_scenario_id,
                    scenario_id: newScenarioId?.new_scenario_id,
                    name: `Scenario ${scenarioCount + 1}`,
                    scenario_name: `Scenario ${scenarioCount + 1}`,
                    order_id: scenarioCount + 1,
                    scenario_order_id: scenarioCount + 1,
                    scenario_type: "resimulation",
                };
                updatedRows.push(updatedRow);
            });

            setSimulationTableData([...updatedRows]);

            // Update column visibility
            const savedState = gridApi.getColumnState();
            _.forEach(savedState, (config) => {
                if (config?.colId == "scenario_2") {
                    config.hide = false;
                }
            });

            //save the updated column state to ag grid ref
            gridApi.applyColumnState({
                state: _.cloneDeep(savedState),
                applyOrder: true,
            });

            // Apply transaction to update the rows
            gridApi.applyServerSideTransactionAsync({
                update: updatedRows
            });

            adjustColumns();
            setNewScenarioDetails([...addedScenarioDetails]);
        }
    };

    const getUpdatedScenarioData = () => {
        let discountsData = [];
        Object.values(scenarioUpdatedData).forEach((data) => {
            let scenarioRowData = {
                row_id: data.row_id,
            };
            let scenarioDetails = [];
            
            const scenarioKeys = Object.keys(data).filter(key => key.startsWith('scenario'));
            scenarioKeys.forEach(key => {
                if (data[key].id) {
                    scenarioDetails.push({
                        scenario_id: data[key].id,
                        scenario_name: data[key].name,
                        scenario_type: data[key].scenario_type,
                        offer_type: data[key].offer_type,
                        scenario_order_id: data[key].order_id,
                        offer_type_id: data[key].offer_type_id,
                        offer_x_value: (data[key].offer_x_value || data[key].offer_x_value == 0) && X_VALUE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_type === "bundle_offer"
                                ? parseInt(data[key]?.offer_x_value)
                                : parseFloat(data[key]?.offer_x_value)
                            : null,
                        offer_x_type: data[key].offer_x_type && X_TYPE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_x_type
                            : null,
                        offer_y_value: (data[key]?.offer_y_value || data[key]?.offer_y_value == 0) && Y_VALUE_OFFERS.includes(data[key]?.offer_type)
                            ? parseFloat(data[key]?.offer_y_value)
                            : null,
                        offer_y_type: data[key].offer_y_type && Y_TYPE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_y_type
                            : null,
                        offer_z_value: (data[key]?.offer_z_value || data[key]?.offer_z_value == 0) && Z_VALUE_OFFERS.includes(data[key]?.offer_type)
                            ? parseFloat(data[key]?.offer_z_value)
                            : null,
                        offer_z_type: data[key].offer_z_type && Z_TYPE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_z_type
                            : null,
                        tier_id: data[key].tier_id && data[key].offer_type == "tiered_offer"
                            ? data[key].tier_id
                            : null,
                        special_offer_data: data[key].offer_type == "special_offer_type"
                            ? data[key].special_offer_data
                            : null,
                        updated: data[key].updated,
                    })
                }
            })
            scenarioRowData['scenario_data'] = scenarioDetails;
            discountsData.push(scenarioRowData);
        });
        return discountsData;
    };

    const handleSimulate = async () => {
        if (!_.isEmpty(updatedSimulationData)) {
            if (!promoDetails?.promo_id) {
                dispatch(toastError(`${capitalizeFirstLetter(global_labels?.promo_alias)} Id not found`));
                return;
            }

            let simulatePayload = {
                promo_id: promoDetails?.promo_id,
                new_scenario_details: newScenarioDetails,
                include_temporary_saved_changes: isBulkEdited || isScenarioCopied,
            }

            let discountsData = getUpdatedScenarioData();

            simulatePayload.discounts_data = discountsData;

            dispatch(getPromoGifData({
                promo_id: activePromoId,
                discounts_data: discountsData,
                include_temporary_saved_changes: isBulkEdited || isScenarioCopied,
                action: "simulation"
            }))
            const isSimulation = await dispatch(generateSimulationData(simulatePayload));
            if (isSimulation) {
                setUpdatedScenarios([]);
                setLastUpdatedScenario("");
                dispatch(setScenarioUpdatedData({}));
                setIsBulkEdited(false);
                setNewScenarioDetails([]);
                setIsScenarioCopied(false);
            }
            setSimulatedPromoId(null);
            dispatch(setDetailedSimulationResults({}));
        }
    };

    const updateSimulationData = (event) => {
        // Find columnId that was updated, and send only that scenario in the update request
        const data = [];
        simulationTableRef.current.api.forEachNode((column) => {
            data.push(column.data);
        });
        const scenario = event.column.colId;
        setUpdatedSimulationData(data);
        setLastUpdatedScenario(scenario);
        adjustColumns();
    };

    const handleShowWithStacking = () => {
        dispatch(setFromStackingView(!showWithStacking));
        setShowWithStacking(!showWithStacking);
    };

    const toggleDetailedSimulationResults = () => {
        if (!showDetailedResults) {
            const simulationMetricPayload = {
                promo_id: activePromoId,
                aggregation: discountLevel?.value,
            };
            dispatch(getDetailedSimulationResults(simulationMetricPayload));
            setShowDetailedResults(!showDetailedResults);
            // Scroll to Detailed Simulation Results
            if (!showDetailedResults) {
                document
                    .getElementById("detailed-simulation-results")
                    .scrollIntoView({
                        behavior: "smooth",
                        block: "end",
                    });
            }
        } else {
            setShowDetailedResults(!showDetailedResults);
        }
    };

    const handleStackedOfferDisplay = () => {
        setShowStackedOfferModal(true);
    };

    const returnWorkbenchHandler = () => {
        setGIFData({});
        dispatch(setPromoGifData({}));
        dispatch(setIsOptimise(false));
        dispatch(toggleLengthyOpLoader(false));
        navigate("/pricesmart-promo/workbench");
    };

    const createNewPromoHanlder = () => {
        dispatch(setIsOptimise(false));
        dispatch(toggleLengthyOpLoader(false));
        setGIFData({});
        dispatch(setPromoGifData({}));
        const query = new URLSearchParams(window.location.search);
        const promoId = query.get("promo") ? query.get("promo") : null;
        if (promoId) {
            window.location = window.location.href.split("?")[0];
        } else {
            window.location.reload(false);
        }
    };

    const closeGif = () => {
        dispatch(toggleLengthyOpLoader(false));
    };

    const handleApprove = async (scenario, comment) => {
        // Handle approve
        const approveScenarioPayload = {
            promo_id: activePromoId,
            source: scenario.scenario_type,
            scenario_id: scenario.scenario_id,
            offer_comment: replaceSpecialCharToCharCode(comment),
            status_id: promoDetails.status_id,
        };
        const isApproved = await dispatch(
            approveScenario({
                payload: approveScenarioPayload,
            })
        );

        setOfferComment("");
        if (
            isApproved &&
            promoDetails.status_id !== 4 &&
            promoDetails.status_id !== 8
        ) {
            navigate(`/pricesmart-promo/workbench?approved=true`, {
                replace: false,
            });
        }
    };

    const handleScenarioNameChange = async (scenario, changedScenarioName) => {
        const newScenarioName = changedScenarioName.trim();
        if (!newScenarioName) {
            dispatch(toastError("Please enter valid scenario name"));
        } else if (newScenarioName !== scenario.scenario_name) {
            setNameUpdatedScenario(_.cloneDeep(scenario));
            dispatch(
                updateScenarioName({
                    scenario_id: scenario.scenario_id,
                    scenario_name: newScenarioName,
                })
            );
        }
    };

    const handleMarkAsDefault = async (scenarioData) => {
        const reqObj = {
            payload: {
                ...scenarioData,
                from_stacking_view: props.fromStackingView,
            },
            isRefreshSimulationResults: true,
            promoId: activePromoId,
            currency_detail: currency,
        };
        dispatch(saveAsDefault(reqObj));
    };

    const openPromoTarget = () => {
        setIsPromoTargetPanelOpen(true);
    };

    const handleBack = () => {
        props.setActiveStep(props.stepNumber - 1);
    };

    const handleNext = () => {
        if (!props?.allowPromoEdit && promoDetails?.step_count === 3) {
            dispatch(
                toastError(`For this ${global_labels?.promo_primary} data is available till simulations`)
            );
            return;
        }
        props.setActiveStep(props.stepNumber + 1);
    };

    const updateDiscountLevel = (data) => {
        setDiscountLevel(data);

        const simulationMetricPayload = {
            promo_id: activePromoId,
            aggregation: data?.value,
        };

        dispatch(getDetailedSimulationResults(simulationMetricPayload));
    };

    const handleShowComparison = () => {
        setShowComparison(!showComparison);
    };

    const handleDownloadDetailedSimulationResults = () => {
        const payload = {
            action: "get",
            report_name: `${global_labels?.promo_primary}_simulator_report_extensive_data`,
            report_file_name: `${global_labels?.promo_primary}_simulator_report_extensive_data_${promoDetails?.promo_name}_${(discountLevel?.value == -200 ? "overall" : discountLevel?.value)}`,
            report_type: "excel",
            promo_name: promoDetails?.promo_name,
            promo_id: activePromoId,
            aggregation: {
                product_hierarchy_levels: selectedProductHierarchy,
                store_hierarchy_levels: selectedStoreHierarchy,
                timeline: selectedTimeHierarchy?.[0], // just this time time hierarchy is single value, if they come back for multiple values, we need to change this
            },
            aggregation_level: discountLevel?.value,
            target_currency_id: selectedCurrency?.value,
        };

        dispatch(promoDownloads(payload));
    };

    // Function to handle chip selection in panel
    // This function updates the selected state for hierarchy chips in the panel
    const handleChipSelect = (id, key, selected, setter) => {
        setter((prev) => {
            if (id === "timeHierarchy" && key !== -200) {
                return [key];
            } else if (key === -200) {
                return prev.includes(-200) ? [] : [-200];
            } else {
                return prev.includes(key)
                    ? prev.filter((v) => v !== key)
                    : [...prev.filter((v) => v !== -200), key];
            }
        });
    };

    const bulkEditHandler = () => {
        setIsBulkEditPanelOpen(true);
    };

    const onRowSelection = (event) => {
        const selectedRows = simulationTableRef.current.api.getSelectedRows();
        const serverSideSelectionState = simulationTableRef.current.api.getServerSideSelectionState();
        setIsSelectAllRows(serverSideSelectionState?.selectAll);
        if (!serverSideSelectionState?.selectAll) {
            setSelectedRows(selectedRows);
        }
        if (serverSideSelectionState?.toggledNodes?.length) {
            setUnselectedRows(serverSideSelectionState?.toggledNodes);
        } else {
            setUnselectedRows([]);
        }
    }

    const applyBulkEdit = async (itemsToUpdate, scenarioObjectList) => {
        let discountsData = [];
        Object.values(scenarioUpdatedData).forEach((data) => {
            let scenarioRowData = {
                row_id: data.row_id,
            };
            let scenarioDetails = [];
            const scenarioKeys = Object.keys(data).filter(key => key.startsWith('scenario'));
            scenarioKeys.forEach(key => {
                if (data[key]?.id) {
                    scenarioDetails.push({
                        scenario_id: data[key].id,
                        scenario_name: data[key].name,
                        scenario_type: data[key].scenario_type,
                        offer_type: data[key].offer_type,
                        scenario_order_id: data[key].order_id,
                        offer_type_id: data[key].offer_type_id,
                        offer_x_value: (data[key].offer_x_value || data[key].offer_x_value == 0) && X_VALUE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_type === "bundle_offer"
                                ? parseInt(data[key]?.offer_x_value)
                                : parseFloat(data[key]?.offer_x_value)
                            : null,
                        offer_x_type: data[key].offer_x_type && X_TYPE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_x_type
                            : null,
                        offer_y_value: (data[key]?.offer_y_value || data[key]?.offer_y_value == 0) && Y_VALUE_OFFERS.includes(data[key]?.offer_type)
                            ? parseFloat(data[key]?.offer_y_value)
                            : null,
                        offer_y_type: data[key].offer_y_type && Y_TYPE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_y_type
                            : null,
                        offer_z_value: (data[key]?.offer_z_value || data[key]?.offer_z_value == 0) && Z_VALUE_OFFERS.includes(data[key]?.offer_type)
                            ? parseFloat(data[key]?.offer_z_value)
                            : null,
                        offer_z_type: data[key].offer_z_type && Z_TYPE_OFFERS.includes(data[key].offer_type)
                            ? data[key].offer_z_type
                            : null,
                        tier_id: data[key].tier_id && data[key].offer_type == "tiered_offer"
                            ? data[key].tier_id
                            : null,
                        special_offer_data: data[key].offer_type == "special_offer_type"
                            ? data[key].special_offer_data
                            : null,
                        updated: data[key].updated,
                    })
                }
            })
            scenarioRowData['scenario_data'] = scenarioDetails;
            discountsData.push(scenarioRowData);
        });
        const updatedRows = itemsToUpdate.map((item) => item.row_id);
        const payload = {
            promo_id: activePromoId,
            session_id: step3uuid,
            discounts_data: [...discountsData],
            bulk_edit_data: [...scenarioObjectList],
            selected_rows: isSelectAllRows ? [] : [...updatedRows],
            unselected_rows: isSelectAllRows ? [...unselectedRows] : [],
            filters: []
        }
        const data = await dispatch(bulkEditScenarioData(payload));
        if (data) {
            setIsBulkEditPanelOpen(false);
            setIsBulkEdited(true);
            dispatch(setScenarioUpdatedData({}));

            refreshGridData({
                include_temporary_saved_changes: true,
                new_scenario_details: newScenarioDetails,
            })
            console.log('apply bulk edit');
        }
    };

    const handleDownloadPriceFile = () => {
        const payload = {
            promo_id: activePromoId,
        };
        dispatch(downloadPriceFile(payload));
    }

    const copyScenarioHandler = async (from, to) => {
        const selectedRowIds = simulationTableRef.current.api.getSelectedRows().map(row => row.row_id);
        const payload = {
            promo_id: activePromoId,
            source: +from?.scenario_order_id,
            target: +to?.scenario_order_id,
            source_scenario_id: +from?.scenario_id,
            target_scenario_id: +to?.scenario_id,
            selected_rows: isSelectAllRows ? [] : [...selectedRowIds],
            unselected_rows: isSelectAllRows ? [...unselectedRows] : [],
            filters: [...advanceSearchFilters],
            session_id: step3uuid,
        }
        const data = await dispatch(copyScenarioData(payload));
        if (data) {
            setIsCopyScenarioPanelOpen(false);
            setIsScenarioCopied(true);
            refreshGridData({
                new_scenario_details: newScenarioDetails,
                include_temporary_saved_changes: true
            });
        }
    }

    const handleAdvanceSearch = async (params) => {
        let filters = [];
        params.forEach(param => {
            const tempValue = `${typeof param.value === 'object' ? param.value?.value : param.value}`;
            const isNumericVal = param?.column?.type === "number";
            if (tempValue) {
                filters.push({
                    column_name: advanceSearchColumnsKeyMapping[param?.column?.value] || param?.column?.value,
                    operator: (isNumericVal ? numberOperatorKeyMapping[param?.operation?.value] : param?.operation?.value) || 'contains',
                    ...{ [isNumericVal ? 'value1' : 'value']: isNumericVal ? +tempValue : tempValue },
                    type: isNumericVal ? "numeric" : "string"
                })
            }
        });

        simulationTableRef.current.api.applyColumnState({
            defaultState: {
                sort: null,
            }
        });

        simulationTableRef.current.api.setFilterModel({});
        setAdvanceSearchFilters(filters);
        refreshGridData({
            filters: [...filters],
            unsaved_scenario_data: [...getUpdatedScenarioData()],
            new_scenario_details: newScenarioDetails,
        })
    }

    const createEditScenarioHandler = () => {
        dispatch(setIsOptimise(false));
        dispatch(toggleLengthyOpLoader(false));
        setGIFData({});
        dispatch(setPromoGifData({}));
    }

    if (props.activeStep !== props.stepNumber) {
        return "";
    }

    return (
        <div>
            <div className="margin-20">
                <div>
                    <div
                        className="content_container"
                        id="table_add_scenario_simulator"
                    >
                        <Table
                            tableHeader={`${capitalizeFirstLetter(global_labels?.promo_alias)} scenario simulator`}
                            ref={simulationTableRef}
                            onGridReady={onGridReady}
                            suppressMenuHide
                            rowModelType="serverSide"
                            serverSideStoreType="partial"
                            cacheBlockSize={100}
                            columnDefs={simulationTableConfigdata}
                            onCellValueChanged={updateSimulationData}
                            onSelectionChanged={onRowSelection}
                            rowSelection="multiple"
                            getRowId={(params) => {
                                return params.data?.row_id;
                            }}
                            onSearchApplyClick={(params) => {
                                handleAdvanceSearch(params);
                            }
                            }
                            topRightOptions={
                                <div className="centerFlexWithGap12">
                                    <div className="positionRelative">
                                        <Button
                                            onClick={() =>
                                                addScenarioHandler()
                                            }
                                            size="medium"
                                            variant="secondary"
                                            disabled={
                                                !props.allowPromoEdit ||
                                                scenarioCount >= 2
                                            }
                                        >
                                            Add scenario
                                        </Button>
                                    </div>
                                    <Tooltip
                                        title="Download Price File"
                                        variant="tertiary"
                                        placement="top"
                                    >
                                        <Button
                                            onClick={() =>
                                                handleDownloadPriceFile()
                                            }
                                            icon={
                                                <img src={DownloadIcon} />
                                            }
                                            size="medium"
                                            variant="tertiary"
                                            disabled={
                                                !props.allowPromoEdit
                                            }
                                        />
                                    </Tooltip>
                                    {(selectedRows.length > 0 || isSelectAllRows) && (
                                        <>
                                            {!_.isEmpty(promoDetails?.scenario_data?.[0]) &&
                                                <Tooltip
                                                    title="Copy scenario"
                                                    variant="tertiary"
                                                    placement="top"
                                                >
                                                    <Button
                                                        onClick={() => setIsCopyScenarioPanelOpen(true)}
                                                        icon={<img src={CopyIcon} alt="copy" />}
                                                        size="medium"
                                                        variant="tertiary"
                                                    />
                                                </Tooltip>
                                            }
                                            <Tooltip
                                                title="Bulk edit"
                                                variant="tertiary"
                                                placement="top"
                                            >
                                                <Button
                                                    onClick={() =>
                                                        bulkEditHandler()
                                                    }
                                                    icon={<img src={EditIcon} alt="edit" />}
                                                    size="medium"
                                                    variant="tertiary"
                                                    disabled={!props.allowPromoEdit}
                                                />
                                            </Tooltip>
                                        </>
                                    )}
                                </div>
                            }
                            topLeftOptions={
                                promoDetails?.no_of_stacked_promos &&
                                    promoDetails?.no_of_stacked_promos > 0 ? (
                                    <div className="scenarion-table-header">
                                        <img
                                            src={verticalLineIcon}
                                            alt="verticalLine"
                                        />
                                        <img
                                            src={warningIcon}
                                            alt="warningIcon"
                                        />
                                        <p
                                            className="primaryText-12-500"
                                            onClick={
                                                handleStackedOfferDisplay
                                            }
                                        >
                                            {
                                                promoDetails?.no_of_stacked_promos
                                            }{" "}
                                            Finalized {global_labels?.promo_alias_plural}
                                        </p>
                                    </div>
                                ) : null
                            }
                        />
                        <div className="simulation-table-cta">
                            <Button
                                variant="primary"
                                id="copy-btn"
                                size="large"
                                onClick={handleSimulate}
                                disabled={
                                    (
                                        (!promoDetails?.enable_simulation && (_.isEmpty(scenarioUpdatedData) && (!isBulkEdited && !isScenarioCopied)) ||
                                            simulationInvalid ||
                                            false) || !props.allowPromoEdit)
                                }
                            >
                                Simulate
                            </Button>
                        </div>
                    </div>
                </div>
                <div className="marginTop-24">
                    {promoDetails?.scenario_data?.length > 0 &&
                        promoDetails?.scenario_data?.[0] !== null ? (
                        <div className="content_container">
                            <div className="text-14-800 marginBottom-12 simulation-results-title">
                                <p>Simulation Results</p>
                                <div className="cta-flex-wrapper" >
                                    {global_labels?.promo?.showCurrencyDropdownStep3 && (
                                        <Select
                                            currentOptions={currencyOptions}
                                            initialOptions={currencyOptions}
                                            label="Currency"
                                            labelOrientation="left"
                                            setSelectedOptions={updateCurrency}
                                            isWithSearch={false}
                                            isMulti={false}
                                            selectedOptions={selectedCurrency}
                                            isOpen={isCurrencyOpen}
                                            setIsOpen={setIsCurrencyOpen}
                                            isCloseWhenClickOutside={true}
                                            placeholder="Select currency"
                                        />
                                    )}
                                    <Button
                                        variant="tertiary"
                                        id="comparison-btn"
                                        onClick={() => {
                                            setIsGraphViewPanelOpen(true);
                                        }}
                                        icon={<img className="svg-blue-icon-hover" src={GraphBar} alt="graphBar" />}
                                    />
                                    {promoDetails?.enable_stacking_view ? (
                                        <Button
                                            variant="tertiary"
                                            id="copy-btn"
                                            onClick={handleShowWithStacking}
                                        >
                                            {showWithStacking
                                                ? "Show without stacking"
                                                : "Show with stacking"}
                                        </Button>
                                    ) : null}
                                    <Button
                                        variant="secondary"
                                        id="comparison-btn"
                                        onClick={handleShowComparison}
                                    >
                                        {showComparison ? "Hide" : "Show"}{" "}
                                        Comparison
                                    </Button>
                                </div>
                            </div>
                            {!showComparison ? (
                                <SimulationResultsScenarios
                                    data={promoDetails?.scenario_data}
                                    handleScenarioNameChange={
                                        handleScenarioNameChange
                                    }
                                    offerComment={offerComment}
                                    handleApprove={handleApprove}
                                    handleMarkAsDefault={handleMarkAsDefault}
                                    lastApprovedScenario={
                                        promoDetails?.last_approved_scenario_id
                                    }
                                    openPromoTarget={openPromoTarget}
                                    promoDetails={promoDetails}
                                    allowPromoEdit={props.allowPromoEdit}
                                    showWithStacking={showWithStacking}
                                    currency_detail={currency}
                                />
                            ) : (
                                <SimulationResultComparisonView
                                    data={_.cloneDeep(
                                        promoDetails?.scenario_data || []
                                    ).sort(
                                        (a, b) =>
                                            a.scenario_order_id -
                                            b.scenario_order_id
                                    )}
                                    currency_details={currency}
                                />
                            )}
                            <div className="simulation-view-detailed-results-cta-container">
                                <Button
                                    variant="tertiary"
                                    id="detailed-simulation-btn"
                                    onClick={toggleDetailedSimulationResults}
                                >
                                    {`${showDetailedResults ? "Hide" : "View"
                                        } detailed simulation results`}
                                </Button>
                            </div>
                        </div>
                    ) : null}
                </div>
                <div id="detailed-simulation-results">
                    {showDetailedResults ? (
                        <Table
                            tableHeader={"Detailed simulation results"}
                            ref={detailedSimulationTableRef}
                            suppressMenuHide
                            rowData={detailedSimulationResults?.[discountLevel?.value] || []}
                            columnDefs={scenario_details_table}
                            topRightOptions={
                                <div className="centerFlexWithGap12">
                                    <Select
                                        currentOptions={discountLevelOptions}
                                        initialOptions={discountLevelOptions}
                                        label="View by"
                                        labelOrientation="left"
                                        setSelectedOptions={updateDiscountLevel}
                                        setCurrentOptions={() => { }}
                                        placeholder="View by"
                                        isRequired={true}
                                        isWithSearch={false}
                                        isMulti={false}
                                        selectedOptions={discountLevel}
                                        isOpen={isDiscountLevelOpen}
                                        setIsOpen={setIsDiscountLevelOpen}
                                        isCloseWhenClickOutside={true}
                                    />
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={DownloadIcon}
                                                alt="download"
                                            />
                                        }
                                        onClick={() => setIsPanelOpen(true)}
                                        size="large"
                                        variant="secondary"
                                    />
                                </div>
                            }
                        />
                    ) : null}
                </div>
                {!promoDetails?.is_optimised && (
                    <div className="simulation-target-request">
                        <div>
                            <img src={unhappyImage} alt="unhappy" />
                        </div>
                        <div className="simulation-msg-container">
                            <p className="text-14-800 marginTop-24 ">
                                Not happy with Simulation Results?
                            </p>
                            <p className="text-14-500 marginTop-12">
                                Click below to enter the {global_labels?.promo_primary} targets and get
                                IA recommendation
                            </p>
                            <div className="marginTop-12">
                                <Button
                                    variant="secondary"
                                    size="medium"
                                    onClick={() => setIsPromoTargetPanelOpen(true) }
                                    disabled={!props.allowPromoEdit}
                                >
                                    Generate
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
                {lengthyOpLoader ? (
                    <GIFOverlay
                        headerText="The operation is in process"
                        primaryButtonLabel={`Create new ${global_labels?.promo_primary}`}
                        primaryButtonAction={() => {
                            createNewPromoHanlder();
                        }}
                        secondaryButtonLabel="Return to workbench"
                        secondaryButtonAction={() => {
                            returnWorkbenchHandler();
                        }}
                        footerText="Don't worry. You'll get notification once operation is completed."
                        tertiaryButtonLabel={isOptimise ? "Create/Edit Scenarios" : null}
                        tertiaryButtonAction={() => {
                            createEditScenarioHandler();
                        }}
                        processStatus={null}
                        closeGIFModal={() => closeGif()}
                        timerData={gifTimerData}
                        data={gifData}
                    />
                ) : null}
                <StackedOfferPanel
                    showStackedOfferModal={showStackedOfferModal}
                    setShowStackedOfferModal={setShowStackedOfferModal}
                    promoDetails={props.promoDetails}
                    allowPromoEdit={props.allowPromoEdit}
                />
                <OfferTargetPanel
                    isPanelOpen={isPromoTargetPanelOpen}
                    setIsPanelOpen={setIsPromoTargetPanelOpen}
                    allowPromoEdit={props.allowPromoEdit}
                />
                <BulkEditPanel
                    isPanelOpen={isBulkEditPanelOpen}
                    setIsPanelOpen={setIsBulkEditPanelOpen}
                    selectedRows={selectedRows}
                    scenarioData={simulationTableData?.[0] || {}}
                    aggridProps={simulationTableRef?.current?.api}
                    applyBulkEdit={applyBulkEdit}
                />
                <CopyScenarioPanel
                    isPanelOpen={isCopyScenarioPanelOpen}
                    setIsPanelOpen={setIsCopyScenarioPanelOpen}
                    handleApply={copyScenarioHandler}
                    scenarioData={simulationTableData?.[0] || {}}
                    props={props}
                />
                <GraphView
                    isPanelOpen={isGraphViewPanelOpen}
                    setIsPanelOpen={setIsGraphViewPanelOpen}
                    currency={currency}
                />
            </div>
            <div className="footer_section">
                <Button
                    onClick={() => handleBack()}
                    size="large"
                    variant="secondary"
                >
                    Back
                </Button>
                <Button
                    onClick={() => handleNext()}
                    size="large"
                    variant="primary"
                >
                    Next
                </Button>
            </div>
            {isPanelOpen && (
                <Panel
                    anchor="right"
                    open={isPanelOpen}
                    onClose={() => setIsPanelOpen(false)}
                    onPrimaryButtonClick={handleDownloadDetailedSimulationResults}
                    onSecondaryButtonClick={() => setIsPanelOpen(false)}
                    primaryButtonLabel="Download"
                    secondaryButtonLabel="Cancel"
                    size="large"
                    title={
                        <div className="panel-title-with-icon">
                            <img
                                src={levelFilterIconInPanel}
                                alt="Level Filters"
                                className="panel-icon"
                            />
                            <span>Level Filters</span>
                        </div>
                    }
                >
                    <div className="aggregation-panel-content">
                        {hierarchyConfig.map(
                            ({ id, title, labels, selected, setter }) => (
                                <div key={id} className="hierarchy-chip-group">
                                    <h4>{title}</h4>
                                    <div className="chip-wrapper">
                                        {labels.map(({ label, key }) => (
                                            <Badge
                                                key={label}
                                                label={label}
                                                variant="stroke"
                                                color={
                                                    selected.includes(key)
                                                        ? "info"
                                                        : "default"
                                                }
                                                onClick={() =>
                                                    handleChipSelect(
                                                        id,
                                                        key,
                                                        selected,
                                                        setter
                                                    )
                                                }
                                            />
                                        ))}
                                    </div>
                                </div>
                            )
                        )}
                    </div>
                </Panel>
            )}
        </div>
    );
};

export default OfferSimulator;
