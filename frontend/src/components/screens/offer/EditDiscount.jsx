import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import moment from "moment";
import {
    Select,
} from "impact-ui";
import {
    VALUE_LABELS
} from "./OfferConstants";
import { DEFAULT_DATE_FORMAT, global_labels } from "../../../constants/Constants";
import {
    setSimulationInvalid
} from "../../../store/features/promoReducer/promoReducer";

import Percentage from "./OfferTypes/PercentageOffer";
import Bxgy from "./OfferTypes/BxgyOffer";
import BxgyPercOff from "./OfferTypes/BxgyPercOffer";
import ExtraAmountOff from "./OfferTypes/ExtraAmountOffer";
import FixedPrice from "./OfferTypes/FixedPriceOffer";
import BmsmOffers from "./OfferTypes/BmsmOffer";
import TieredOffer from "./OfferTypes/TieredOffer";
import SpecialOfferType from "./SpecialOfferType";
import UptoXOffer from "./OfferTypes/UptoXOffer";
import { capitalizeFirstLetter } from "../../../utils/helpers/utility_helpers";

const EditDiscount = props => {
    const dispatch = useDispatch();

    const {
        promoDetails = {},
        validOffers,
        bmsmOfferData,
        offerModeState
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const [offerTypeOptions, setOfferTypeOptions] = useState([]);
    const [selectedOfferType, setSelectedOfferType] = useState("");
    const [selectedOfferTypeOption, setSelectedOfferTypeOption] = useState(null);
    const [selectedOfferValue, setSelectedOfferValue] = useState("");
    const [offerValueOptions, setOfferValueOptions] = useState({});
    const [offerTypeMinPrice, setOfferTypeMinPrice] = useState({});
    const [offerTypeMaxPrice, setOfferTypeMaxPrice] = useState({});
    const [disableFlag, setDisableFlag] = useState(false);
    const [bmsmXTypeOptions, setBmsmXTypeOptions] = useState([]);
    const [bmsmYTypeOptions, setBmsmYTypeOptions] = useState({});
    const [isOfferTypeOpen, setIsOfferTypeOpen] = useState(false);

    useEffect(() => {
        screenMount();
    }, []);

    const screenMount = async () => {
        const viewMode = offerModeState === 'view';
        const currDt = moment().format(DEFAULT_DATE_FORMAT);
        // status id 8 - Execution Approved, 6 - Archived, 4 - Finalized
        const viewModeStatuses = [8, 6];
        const isView =
            viewMode ||
            viewModeStatuses.includes(promoDetails?.status_id);
        if (
            !_.isEmpty(promoDetails) &&
            (props.column?.colId !== "ia_recommended" ||
                (props.column?.colId === "ia_recommended" &&
                    !_.isEmpty(props.data.ia_recommended)))
        ) {
            setDiscountTypesAndValues();
        }

        if (
            props.column?.colId === "ia_recommended" ||
            isView ||
            moment(currDt).isSameOrAfter(
                moment(promoDetails?.start_date)
            ) ||
            promoDetails?.status === 6
        ) {
            setDisableFlag(true);
        } else {
            setDisableFlag(false);
        }
    };

    useEffect(() => {
        screenMount();
    }, [offerModeState]);

    useEffect(() => {
        formatBmsmTypeOptions(bmsmOfferData);
    }, [bmsmOfferData]);

    const setDiscountTypesAndValues = async () => {
        // Call Valid offers API based on discount level
        const { promo_id, discount_rules } = promoDetails;
        const {
            discount_level,
            discount_type_id,
            discount_type,
        } = discount_rules;

        const offerTypeOptions = [];
        validOffers?.[promo_id]?.forEach(
            (ot) => {
                // If min discount value is set to > 0, exclude Reg and Reg CV
                const minDiscount = getMinDisount(discount_type) || 0;
                if (
                    (minDiscount > 0 &&
                        ot.offer_type !== "reg_price" &&
                        ot.offer_type !== "reg_price_cv") ||
                    minDiscount === 0
                ) {
                    const option = {
                        value: ot.offer_type,
                        label: ot.offer_display_name
                    }
                    // offerTypeOptions[ot.offer_type] = ot.offer_display_name;
                    offerTypeOptions.push(option);
                }
            }
        );
        setOfferTypeOptions(offerTypeOptions);
        const offerValueOptions = {};
        const offerTypeMinPrice = {};
        const offerTypeMaxPrice = {};
        validOffers?.[promo_id]?.map(
            (ot) => {
                offerValueOptions[ot.offer_type] = ot.offer_values;
                offerTypeMinPrice[ot.offer_type] = ot.min_price;
                offerTypeMaxPrice[ot.offer_type] = ot.max_price;
            }
        );
        setOfferValueOptions(offerValueOptions);
        setOfferTypeMinPrice(offerTypeMinPrice);
        setOfferTypeMaxPrice(offerTypeMaxPrice);

        if ((!selectedOfferValue || !selectedOfferType) && !props.value) {
            const selectedOffer = validOffers?.[promo_id]?.find(
                (offer) =>
                    offer.offer_type_id === parseInt(discount_type_id)
            );
            const defaultVal = getDefaultMinValueForOfferType(
                selectedOffer?.offer_type,
                offerValueOptions,
                offerTypeMinPrice,
                offerTypeMaxPrice
            );
            setSelectedOfferValue(defaultVal);
            setSelectedOfferType(selectedOffer?.offer_type);
            const selectedOption = offerTypeOptions.find((element) => element.value == selectedOffer?.offer_type);
            setSelectedOfferTypeOption(selectedOption);
            let newCellData = {};
            if (
                selectedOffer?.offer_type === "bxgy" ||
                selectedOffer?.offer_type === "bmsm" ||
                selectedOffer?.offer_type === "bxgy_percent_off"
            ) {
                newCellData = {
                    ...defaultVal,
                    offer_type: selectedOffer?.offer_type,
                    offer_type_id: selectedOffer?.offer_type_id,
                };
            } else if (selectedOffer?.offer_type === "tiered_offer") {
                newCellData = {
                    tier_id: null,
                    offer_type: selectedOffer?.offer_type,
                    offer_type_id: selectedOffer?.offer_type_id,
                };
            } else {
                newCellData = {
                    offer_x_value: defaultVal,
                    offer_type: selectedOffer?.offer_type,
                    offer_type_id: selectedOffer?.offer_type_id,
                };
            }

            // if (props?.data && !props?.data?.[props?.column?.colId]) {
            //     newCellData["name"] =
            //         props?.column?.colId === "scenario_1"
            //             ? "Scenario 1"
            //             : props?.column?.colId === "scenario_2"
            //                 ? "Scenario 2"
            //                 : "Scenario 3";
            //     newCellData["order_id"] =
            //         props?.column?.colId === "scenario_1" ? 1 : 2;
            // }

            props.setValue(newCellData);
            // if (!props.node.selected) {
            //     props.node.setSelected(true);
            // }
        } else if (props.value || props.value === 0) {
            const { value } = props;
            const newOfferValue = value.offer_x_value;

            setSelectedOfferType(value.offer_type);
            const selectedOption = offerTypeOptions.find((element) => element.value == value?.offer_type);
            setSelectedOfferTypeOption(selectedOption);

            if (value.offer_type === "reg_price_cv") {
                const regPriceCompValue =
                    offerValueOptions["reg_price_cv"]?.[0]?.offer_x_value;
                setSelectedOfferValue(regPriceCompValue);
            } else if (
                value.offer_type === "bxgy" ||
                value.offer_type === "bmsm" ||
                value?.offer_type === "bxgy_percent_off"
            ) {
                setSelectedOfferValue(value);
            } else if (value.offer_type === "tiered_offer") {
                setSelectedOfferValue(value.tier_id);
            } else if (value.offer_type === "special_offer_type") {
                setSelectedOfferValue(value.special_offer_data);
            } else {
                setSelectedOfferValue(newOfferValue);
            }
        }
    };

    const formatBmsmTypeOptions = (offerData) => {
        let xTypeOptions = [];
        let yOptions = {};
        offerData.forEach((data) => {
            let xOption = {
                value: data.x_value_types,
                label: VALUE_LABELS[data.x_value_types],
            };
            let yTypeOptions = [];
            data?.y_value_types.forEach((yvalue) => {
                let yOption = {
                    value: yvalue,
                    label: VALUE_LABELS[yvalue],
                };
                yTypeOptions.push(yOption);
            });
            xTypeOptions.push(xOption);
            yOptions[data.x_value_types] = yTypeOptions;
        });

        setBmsmXTypeOptions(xTypeOptions);
        setBmsmYTypeOptions(yOptions);
    };

    const getMinDisount = (discountType) => {
        const { promo_id, discount_rules } = promoDetails;
        // const { discount_level } = !_.isEmpty(discount_rules)
        //     ? discount_rules
        //     : {};

        // if (discount_level || discount_level === 0) {
        const offerTypeDetails = validOffers?.[promo_id]?.find((ot) => ot.offer_type === discountType);

        return offerTypeDetails?.min_price
            ? offerTypeDetails?.min_price
            : 0;
        // }
        // return 0;
    };

    const getDefaultMinValueForOfferType = (
        offerType,
        offerValueOptions,
        offerTypeMinPriceParam = null,
        offerTypeMaxPriceParam = null
    ) => {
        // For %off
        // No validation problem
        // Default set to minEffDisc and validation message accordingly

        // For PP
        // Default set to minEffDisc
        // If default > maxAllowed, highlight in red and throw validation error

        // For $off
        // Default set to minEffDisc
        // If default > maxAllowed, highlight in red and throw validation error
        let offerTypeMinPriceTemp = offerTypeMinPriceParam
            ? { ...offerTypeMinPriceParam }
            : null;
        if (!offerTypeMinPriceTemp) {
            offerTypeMinPriceTemp = { ...offerTypeMinPrice };
        }

        let offerTypeMaxPriceTemp = offerTypeMaxPriceParam
            ? { ...offerTypeMaxPriceParam }
            : null;
        if (!offerTypeMaxPriceTemp) {
            offerTypeMaxPriceTemp = { ...offerTypeMaxPrice };
        }

        let minEffectiveDiscount = getMinDisount(offerType);
        const promoId = promoDetails?.promo_id;
        const defaultDiscountType =
            promoDetails?.discount_rules?.discount_type;

        let minVal = null;
        if (offerType === "bxgy") {
            minVal = {
                offer_x_value: 1,
                offer_y_value: 1,
            };
        } else if (offerType === "percent_off") {
            minVal = minEffectiveDiscount;
        } else if (offerType === "extra_amount_off") {
            minVal = parseFloat(minEffectiveDiscount);
        } else if (offerType === "fixed_price") {
            // Price of min priced item minus 25% off of min priced item
            minVal = parseFloat(minEffectiveDiscount);
        } else if (offerType === "bmsm") {
            minVal = {
                offer_x_value: 1,
                offer_y_value: 1,
                offer_x_type: "dollar",
                offer_y_type: "dollar_off",
            };
        } else if (offerType === "bxgy_percent_off") {
            minVal = {
                offer_x_value: 1,
                offer_y_value: 1,
                offer_z_value: 1,
            };
        } else if (offerType === "upto_x_percent_off") {
            minVal = parseFloat(minEffectiveDiscount);
        }
        return minVal;
    };

    const offerTypeChangeHandler = async (selectedOption) => {
        let val = selectedOption.value;

        const { promo_id, discount_rules } = promoDetails;
        // const { discount_level } = discount_rules;
        let selectedOffer = {};
        selectedOffer = validOffers?.[promo_id]?.find((offer) => offer.offer_type === val);

        dispatch(setSimulationInvalid(false));

        let newCellData = {
            ...props.value,
            offer_type: val,
            offer_type_id: selectedOffer?.offer_type_id,
        };

        const defaultVal = getDefaultMinValueForOfferType(
            val,
            offerValueOptions
        );
        setSelectedOfferValue(defaultVal);

        if (
            val === "bundle_offer" ||
            val === "bmsm" ||
            val === "bxgy" ||
            val === "bxgy_percent_off"
        ) {
            newCellData = {
                ...newCellData,
                ...defaultVal,
            };
        } else if (val === "tiered_offer") {
            newCellData = {
                ...newCellData,
                tier_id: null,
            };
        } else {
            newCellData = {
                ...newCellData,
                offer_x_value: defaultVal,
            };
        }

        props.setValue(newCellData);

        setSelectedOfferType(val);
        setSelectedOfferTypeOption(selectedOption);
        props.onUpdate(props.data);
    };

    const handleOfferValueChange = (val) => {
        if (
            selectedOfferType === "bundle_offer" ||
            selectedOfferType === "bmsm" ||
            selectedOfferType === "bxgy" ||
            selectedOfferType === "bxgy_percent_off"
        ) {
            props.setValue({
                ...props.value,
                ...val,
                updated: true
            });
            setSelectedOfferValue(val);
        } else if (selectedOfferType === "tiered_offer") {
            props.setValue({
                ...props.value,
                tier_id: val,
                updated: true
            });
            setSelectedOfferValue(val);
        } else if (selectedOfferType === "special_offer_type") {
            props.setValue({
                ...props.value,
                special_offer_data: val,
                updated: true
            });
            setSelectedOfferValue(val);
        } else {
            props.setValue({
                ...props.value,
                offer_x_value: val,
                updated: true
            });
            setSelectedOfferValue(val);
        }

        // if (!props.node.selected) {
        //     props.node.setSelected(true);
        // }
        props.onUpdate(props.data);
    };


    return (
        <div className="finalize-cell-edit-container">
            <div className="finalize-offer-type-container">
                <Select
                    currentOptions={
                        offerTypeOptions
                    }
                    initialOptions={
                        offerTypeOptions
                    }
                    setSelectedOptions={offerTypeChangeHandler}
                    setCurrentOptions={() => { }}
                    placeholder={`${capitalizeFirstLetter(global_labels?.promo_alias)} type`}
                    isRequired={true}
                    isWithSearch={false}
                    isMulti={false}
                    selectedOptions={selectedOfferTypeOption}
                    isOpen={isOfferTypeOpen}
                    setIsOpen={setIsOfferTypeOpen}
                    isCloseWhenClickOutside={true}
                    withPortal={true}
                    isDisabled={disableFlag}
                />
            </div>
            <div className="finalize-offer-value-container">
                {
                    selectedOfferType === "percent_off" && (
                        <Percentage
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            rowData={props.data}
                            columnData={props.column}
                            minDiscount={getMinDisount("percent_off")}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "fixed_price" && (
                        <FixedPrice
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            offerTypeMinPrice={
                                offerTypeMinPrice[selectedOfferType]
                            }
                            offerTypeMaxPrice={
                                offerTypeMaxPrice[selectedOfferType]
                            }
                            rowData={props.data}
                            columnData={props.column}
                            minDiscount={getMinDisount(selectedOfferType)}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "upto_x_percent_off" && (
                        <UptoXOffer
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            offerTypeMinPrice={
                                offerTypeMinPrice[selectedOfferType]
                            }
                            offerTypeMaxPrice={
                                offerTypeMaxPrice[selectedOfferType]
                            }
                            rowData={props.data}
                            columnData={props.column}
                            minDiscount={getMinDisount(selectedOfferType)}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "extra_amount_off" && (
                        <ExtraAmountOff
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            offerTypeMinPrice={
                                offerTypeMinPrice[selectedOfferType]
                            }
                            offerTypeMaxPrice={
                                offerTypeMaxPrice[selectedOfferType]
                            }
                            rowData={props.data}
                            columnData={props.column}
                            minDiscount={getMinDisount(
                                "extra_amount_off"
                            )}
                            defaultDiscountType={
                                promoDetails?.discount_rules?.[0]
                                    ?.discount_type
                            }
                            promoId={promoDetails?.promo_id}
                            validOffers={validOffers}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "bxgy" && (
                        <Bxgy
                            offerValueOptions={offerValueOptions}
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            rowData={props.data}
                            columnData={props.column}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "bmsm" && (
                        <BmsmOffers
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            offerValueOptions={offerValueOptions}
                            xTypeOptions={bmsmXTypeOptions}
                            yTypeOptions={bmsmYTypeOptions}
                            // getOfferTypes={props.getBmsmOfferTypes}
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            rowData={props.data}
                            columnData={props.column}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "tiered_offer" && (
                        <TieredOffer
                            offerValueOptions={offerValueOptions}
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            disableFlag={disableFlag}
                            selectedOfferValue={selectedOfferValue}
                            rowData={props.data}
                            columnData={props.column}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "bxgy_percent_off" && (
                        <BxgyPercOff
                            offerValueOptions={offerValueOptions}
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            rowData={props.data}
                            columnData={props.column}
                            columnName={props.column.colId}
                        />
                    )
                }
                {
                    selectedOfferType === "special_offer_type" && (
                        <SpecialOfferType
                            handleOfferValueChange={
                                handleOfferValueChange
                            }
                            selectedOfferValue={selectedOfferValue}
                            disableFlag={disableFlag}
                            rowData={props.data}
                            columnData={props.column}
                            columnName={props.column.colId}
                        />
                    )
                }
            </div>

        </div>
    )
}

export default EditDiscount;