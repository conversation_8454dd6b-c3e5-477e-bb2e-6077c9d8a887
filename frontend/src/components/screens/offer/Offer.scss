.offer_details_container {
    height: calc(100vh - 216px);
    overflow: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;

    .impact_accordion_main_container .Mui-disabled {
        background: #f8f9fb !important;
    }
}

.basic-details {
    width: calc(100% - 400px);
    display: flex;
    justify-content: space-between;
}

.basic-details-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-container {
    width: 500px;
    display: flex;
    gap: 15px;
}

.step-0-container {
    display: flex;
    gap: 25px;
    margin: 20px;
}

.offer-image-container {
    width: 212px;
    height: 187px;
}

.info-container-details {
    width: 245px;
}

.basic-details-button-container {
    display: flex;
    gap: 15px;
}

.basic-details-content {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.offer-basic-details {
    display: flex;
    height: 46px;
    width: 90%;
    padding: 7px 16px;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    border-radius: 8px;
    background: var(--Colors-Greys-0, #FFF);
}

.badge-shape-div {
    display: flex;
    padding: 6px 12px;
    align-items: center;
    gap: 4px;
    border-radius: var(--Radius-R5, 16px);
    border: 1px solid #B3BDF8;
}

.offer-stepper-container {
    margin: 25px auto;
    width: 760px;
}

.offer-container-background {
    border-radius: 8px;
    background: var(--Colors-Greys-100, #F8F9FB);
}

.customer-selection-container {
    display: flex;
    gap: 20px;
}

.offer-flex-container {
    display: flex;
    gap: 24px;
    align-items: center;
    margin-top: 24px;
}

.vertical-dotted-divider {
    border-left: 1px solid #D9DDE7;
    // transform: rotate(90deg);
    height: 35px;
}

.vendor-funding-details {
    margin-top: 24px;
    // width: max-content;
}

.finalize-offer-type-container {
    .ia-select-styled-dropdown-main-button {
        min-width: 150px !important;

        .ia-select-button-text {
            width: 100px;
        }
    }
}

.finalize-offer-value-container .impact_inputbox_container .MuiInputBase-root input.MuiInputBase-input {
    min-width: 70px;
    max-width: 90px;
}

.offer-grey-background {
    background: #F5F6FA;
}

.bmsm-offer-value-container {
    display: flex;
    gap: 8px;
}

.bmsm-offer-value-container .ia-select-styled-dropdown-main-button {
    min-width: 100px !important;
    max-width: 100px !important;
}

.simulation-table-cta {
    text-align: right;
}

.tiered-offer-container {
    display: flex;
    gap: 5px
}

.inactive-badge-shape-div {
    display: flex;
    padding: 6px 12px;
    align-items: center;
    gap: 4px;
    border-radius: var(--Radius-R5, 16px);
    border: 1px solid var(--Colors-Neutrals-Border-Default, #C3C8D4);
    background: var(--Colors-Neutrals-Surface-Lighter, #F5F6FA);
}

.active-badge-shape-div {
    display: flex;
    padding: 6px 12px;
    align-items: center;
    gap: 4px;
    border-radius: var(--Radius-R5, 16px);
    border: 1px solid var(--Colors-Primary-Border-Default, #4259EE);
    background: var(--Colors-Primary-Surface-Subtle, #ECEEFD);
}

.tier-header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.badge-shape {
    max-width: 200px;
}

.tier-badge-container {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    margin: 16px 0;
}

.subtier-content {
    display: flex;
    gap: 16px;

    .ia-select-styled-dropdown-main-button,
    .ia-select-container-v3-styled-menu {
        min-width: 60px;
        max-width: 100px;
    }

    .impact_inputbox_container .MuiInputBase-root input.MuiInputBase-input {
        min-width: 50px;
        max-width: 65px;
    }
}

.subtier-input-field {
    display: flex;
    gap: 5px;
    align-items: center;
}

.tier-summary-container {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #D9DDE7;
    background: #FFF;
}

.tier-summary-title {
    text-align: left;
    letter-spacing: 0px;
    color: #1F2B4D;
}

.tier-summary-content {
    margin-top: 10px;
}

.subtier-details {
    display: flex;
    flex-direction: column;
}

.simulation-target-request {
    border-radius: 8px;
    border: 1px solid #4259EE;
    background: #ffffff 0% 0% no-repeat padding-box;
    text-align: left;
    padding: 16px 16px;
    margin: 20px 0;
    display: flex;
    gap: 20px;
}

.simulation-results-title {
    display: flex;
    justify-content: space-between;
}

.simulation-view-detailed-results-cta-container {
    display: flex;
    justify-content: flex-end;
}

.scenarion-table-header {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    // align-items: center;
}

.target-panel {
    width: 700px;
}


/* Panel Title and Icon */
.target-panel-title-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Divider line */
.target-panel-line {
    border-bottom: 1px solid #D9DDE7;
    margin: 16px 0;
}

/* Objectives section */
.target-panel-objectives {
    margin: 12px 0;
    display: flex;
    gap: 12px;
}

/* Target Details Section */
.target-details-wrapper {
    margin-top: 20px;
}


.target-details {
    display: flex;
    border-radius: 8px;
    border: 1px solid #D9DDE7;
    background: #FFF;
    margin: 16px 0;
    padding: 12px;
    align-items: center;
    justify-content: space-between;
}


.target-details-content {
    display: flex;
    gap: 20px;
    align-items: end;
    border-left: 1px solid #D9DDE7;
    padding-left: 16px;
}

.target-details-header {
    display: flex;
    gap: 5px;
    flex-direction: column;
    min-width: 100px;
}

.target-details-baseline {
    display: flex;
    gap: 8px;
    flex-direction: column;
}

.target-details-lift {
    .impact_inputbox_container .MuiInputBase-root input.MuiInputBase-input {
        width: 60px;
        min-width: unset;
    }
}

.target-details-target {
    .impact_inputbox_container .MuiInputBase-root input.MuiInputBase-input {
        width: 100px;
        min-width: unset;
    }
}

.target-details-action {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-top: 24px;
}

.stacked-offer-details-container {
    display: flex;
    gap: 5px;
    border-radius: 10px;
    border: 1px solid #31935F;
    background: #EBF7F1;
    height: 140px;
}

.stacked-icon-container {
    width: 20%;
    display: flex;
    align-items: center;
    gap: 30px;
}

.stacked-offer-details {
    border-left: 1px dashed #687AF1;
    margin: 12px 0;
    width: 70%;
}

.stacked-offer-product-details {
    display: flex;
    gap: 8px;
    align-items: center;
}

.coupon-type-container {
    background: #4259EE;
    border-radius: 0 10px 10px 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 10%;
}

.coupon-type-details {
    color: white;
    font-weight: 600;
    position: absolute;
    transform: rotate(90deg);
}


.stacked-badge-shape-div {
    display: flex;
    padding: 2px 8px;
    align-items: center;
    gap: 4px;
    border-radius: var(--Radius-R5, 16px);
    background: #ECEEFD;
    color: #4259EE;
    height: 25px;
}

.add-objective-message {
    display: flex;
    border-radius: 8px;
    border: 1px solid #D9DDE7;
    background: #FFF;
    padding: 24px;
    margin: 16px 0;
    justify-content: space-around;
}

.add-drag-message {
    display: flex;
    border-radius: 8px;
    border: 1px solid #7BCFFF;
    background: #E2F4FF;
    padding: 12px;
    margin: 16px 0;
    gap: 12px;
    align-items: center;
}

.target-panel-scenarios {
    flex: 1;
}

.optimization-discount-type-container {
    display: flex;
    gap: 12px;
    align-items: center;

    .target-panel-scenarios-info {
        margin-top: 24px;
    }
}

.target-panel-optimization-type {
    display: flex;
    gap: 12px;
}

.panel-slider-minmax-input {
    display: flex;
    gap: 12px;
    justify-content: space-between;
}

.panel-slider-minmax-input {
    .impact_inputbox_container .MuiInputBase-root input.MuiInputBase-input {
        width: 80px;
        min-width: unset;
    }
}

.min-input,
.max-input {
    display: flex;
    gap: 8px;
    align-items: center;
}

.simulation-results-scenario-item-container {
    display: flex;
    flex-direction: column;
    position: relative;
    flex: 1;
    border: 1px solid #636CE7;
    margin-bottom: 15px;
    border-radius: 4px;
    padding: 15px 15px 15px 20px;
}

.ia-recommended-scenario {
    border-radius: 8px;
    border: 1px solid #4259EE;

    .scenario-metric-chip {
        border: 1px solid #4259EE;
        background-color: #ECEEFD;
    }

    .scenario-metric-chip-title {
        color: #4259EE;
    }

    .scenario-metric-chip-separator {
        background-color: #4259EE;
    }

    .scenario-metric-item {
        background-color: #ECEEFD;
    }

    .scenario-data-kpi-container {
        background-color: #ECEEFD;
    }

    .scenario-sales-units-container {
        border: 1px solid #4259EE;
    }

    .scenario-name-text {
        color: #4259EE;
    }
}

.scenario_1 {
    border-radius: 8px;
    border: 1px solid #AE57EA;

    .scenario-metric-chip {
        border: 1px solid #AE57EA;
        background-color: #F6EDFD;
    }

    .scenario-metric-chip-title {
        color: #931CE3;
    }

    .scenario-metric-chip-separator {
        background-color: #AE57EA;
    }

    .scenario-metric-item {
        background-color: #F6EDFD;
    }

    .scenario-data-kpi-container {
        background-color: #F6EDFD;
    }

    .scenario-sales-units-container {
        border: 1px solid #AE57EA;
    }

    .scenario-name-text {
        color: #931CE3;
    }

    .horizontal-divider-line {
        color: #D09BF3;
        ;
    }
}

.scenario_2 {
    border-radius: 8px;
    border: 1px solid #99BF3E;

    .scenario-metric-chip {
        border: 1px solid #99BF3E;
        background-color: #F5F9EC;
    }

    .scenario-metric-chip-title {
        color: #7D9A32;
    }

    .scenario-metric-chip-separator {
        background-color: #99BF3E;
    }

    .scenario-metric-item {
        background-color: #F5F9EC;
    }

    .scenario-data-kpi-container {
        background-color: #F5F9EC;
    }

    .scenario-sales-units-container {
        border: 1px solid #99BF3E;
    }

    .scenario-name-text {
        color: #7D9A32;
    }

    .horizontal-divider-line {
        color: #C3DA8B;
    }
}

.scenario-heading-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.scenario-left-heading {
    display: flex;
    gap: 16px;
    align-items: center;
}

.scenario-heading-btn-container {
    display: flex;
    gap: 16px;
}

.scenario-result-scrollable-container {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    scrollbar-width: none;
}

.scenario-metric-chip {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 21px;
    width: 62px;
    border: 1px solid #636CE7;
    border-radius: 12px;
    background-color: #EFF0FF;
}

.scenario-metric-chip-separator {
    height: 1px;
    width: 30px;
    background-color: #636CE7;
}

.scenario-metric-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-evenly;
    padding: 8px 12px;
    background-color: #EFF0FF;
    border-radius: 4px;
    height: 60px;
    width: 150px;
}

.scenario-data-kpi-container {
    display: flex;
    flex-direction: column;
    background-color: #EFF0FF;
    border-radius: 4px;
    padding: 12px;
    justify-content: space-evenly;
    width: 156px;
    height: 132px;
}

.scenario-kpi-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 132px;
    justify-content: space-between;
}

.scenario-sales-units-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    border: 1px solid #636CE7;
    border-radius: 2px;
    padding: 16px;
    justify-content: space-evenly;
    height: 132px;
}

.scenario-sales-units-item {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.scenario-metric-row-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 12px;
}

.comment-modal-content {
    justify-content: center;
}

.scenario-left-heading .inactive-badge-shape-div {
    height: 25px;
    color: #7A8294;
}

.info-description {
    gap: 4px;
}

.override-data-container {
    display: flex;
    flex-wrap: wrap;
}

.override-results-content {
    flex: 1 0 40%;
    margin: 10px;
    background: #ffffff 0% 0% no-repeat padding-box;
    border: 1px solid #EFF2FA;
    border-radius: 3px;
    text-align: left;
    padding: 16px 16px;
}

.override-metrics-row {
    display: flex;
    flex-wrap: wrap;
}

.override-metrics-item {
    flex: 1 0 30%;
    margin: 4px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.promo-override-dialog-container {
    width: 1000px !important;
}

.override-horizontal-divider-line {
    border-top: 1px solid #EFF2FA;
    margin: 12px 0;
}

.override-data-field-container {
    display: flex;
    gap: 24px;
}

.scenario-sales-units-item-container {
    display: flex;
    width: 365px;
    justify-content: space-between;
}

.panel-title-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
}

.offer-exlusion-panel {
    width: 620px;
}

.label-icon {
    width: 20px;
    height: 20px;
    aspect-ratio: 1;
}

.tag-margin {
    background-color: red;
}

.execution-metadata-form-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, 300px);
    gap: 16px;
}


.integerField-style .impact_inputbox_container .MuiInputBase-root input.MuiInputBase-input {
    min-width: 100px;
}

.integerField-style {
    display: flex;
    justify-content: space-between;
    gap: 28px;
}

.scenario-result-container {
    display: flex;
    gap: 12px;
}

.step1-footer {
    justify-content: flex-end;
}

.step1-accordian-container .impact_accordion_main_container {
    gap: 12px;
}

.finalize-cell-edit-container {
    display: flex;
    gap: 12px;
    padding-top: 5px;
}

.bxgy-offer-value-container {
    display: flex;
    gap: 8px;
}

.sfcc-label {
    margin-bottom: 6px;
}

.baseline-heading {
    color: #60697d;
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 6px;
    font-weight: 500;
}

.baseline-value {
    margin-bottom: 8px;
}

.approved_badge .MuiChip-root.success-badge-filled {
    display: flex;
    gap: 12px;
}

.offer-duration-container {
    margin: 12px 0;
    display: flex;
    gap: 8px;
}

.stacked-offer-duration-details {
    display: flex;
    gap: 4px;
    align-items: center;
}

.stacked-offer-details-container {
    &::before {
        content: "";
        position: absolute;
        height: 24px;
        width: 50px;
        background: #ffffff;
        border-radius: 100px 100px 0 0;
        top: 105px;
        left: 2px;
        border: inherit;
        border-bottom-color: white;
        transform: rotate(90deg);
        border-bottom-width: 1.5px;
    }
}

.cross-icon {
    line-height: 0;
    margin-bottom: 12px;
}

.equal-icon {
    line-height: 0;
    margin-bottom: 15px;
}

.tier-content-input {
    margin-top: 12px;
}

.tier-content-subtier-container {
    .ia-select-styled-dropdown-main-button {
        min-width: unset !important;
        max-width: unset !important;
        width: 80px;
    }
}

.stacked-offer-name {
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// Simulation Result Comparison View

.chip-label {
    color: #7A8294;
    background-color: #F5F6FA;
    border: 1px solid #C3C8D4;
}

.scenario-0 {
    border-color: #4259EE !important;

    h3 {
        color: #4259EE;
    }

    .expand-btn {
        border-color: #4259EE;
    }

    .metric-line-1,
    .metric-line {
        background-color: #4259EE !important;
    }

    .comparison-accordion-header,
    .expand-btn {
        background-color: #ECEEFD;
    }

    .comparison-accordion-header.active {
        border: 1px solid #4259EE;
    }
}

.scenario-1 {
    border-color: #AE57EA !important;

    h3 {
        color: #AE57EA;
    }

    .expand-btn {
        border-color: #AE57EA;
    }

    .metric-line-1,
    .metric-line {
        background-color: #AE57EA !important;
    }

    .comparison-accordion-header,
    .expand-btn {
        background-color: #F6EDFD;
    }

    .comparison-accordion-header.active {
        border: 1px solid #AE57EA;
    }
}

.scenario-2 {
    border-color: #99BF3E !important;

    .expand-btn {
        border-color: #99BF3E;
    }

    .metric-line-1,
    .metric-line {
        background-color: #99BF3E !important;
    }

    h3 {
        color: #99BF3E;
    }

    .comparison-accordion-header,
    .expand-btn {
        background-color: #F5F9EC;
    }

    .comparison-accordion-header.active {
        border: 1px solid #99BF3E;
    }
}

.scenario-comaprison {
    padding: 20px;
    border: 1px solid;
    border-radius: 8px;
    width: 420px;

    h3 {
        text-align: center;
        font-size: 16px;
        font-weight: 800;
    }

    p {
        text-align: center;
        font-size: 12px;
    }
}

.scenario-comparison-accordion {

    .accordion-header,
    .MuiAccordionSummary-content {
        display: block;
    }

    .accordion-header {
        border: none !important;
    }

    .Mui-expanded.accordion-header {
        padding: 0;
    }

    .accordion-content {
        &::before {
            // this is to remove the blue line from the accordion content
            content: none;
        }
        position: relative;
        border: none !important;
    }

    margin-bottom: 8px !important;
}

.grid-4-1fr {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    align-items: center;
}

.value-container {
    border: 1px solid #D9DDE7;
    border-radius: 4px;
    margin-bottom: 5px;
    padding: 10px 0;
}

.comparison-accordion-header {
    border-radius: 8px;

    p {
        padding: 10px 0;
    }
}

.expand-btn {
    border: 1px solid;
    border-radius: 12px;
    font-size: 12px;
    padding: 4px 6px;
    text-align: center;
    z-index: 2;
    display: flex;
    align-items: center;
}

.cursor-pointer {
    cursor: pointer;
}

.metric-line-1 {
    position: absolute;
    width: 69px;
    height: 2px;
    // background-color: red;
    left: -90px;
    z-index: 0;
}

.metric-line {
    // background-color: red;
    position: absolute;
    width: 50px;
    height: 2px;
    left: -70px;
}

.chip-selected {
    border: 1px solid #4259EE;
    color: #4259EE;
    background: #eceefd;

    &:hover {
        background-color: #fff !important;
    }
}

.vert-line {
    border-left: 2px solid #c8ced7;
    height: 100%;
}

.position-absolute-left-130 {
    position: absolute;
    left: -130px;
}

.chip-container {
    gap: 10px;
    border: 1px solid #D9DDE7;
    border-radius: 8px;
    padding: 16px;

    .flexWithCenterAlign {
        gap: 10px;
    }
}

.comparison-container {
    gap: 50px;
    justify-content: center;
    display: flex;
    margin-top: 20px;
    padding-left: 112px;
}

.scenario-comparison-accordion:before {
    content: unset;
}

.tiered-offer-panel {
    min-width: 600px;
}

.offer-mode-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 12px;
}

.event-change-model {
    width: 440px;
    font-size: 12px;
    text-align: center;
    padding: 4px 0;
}

// for enchanced download panel at step 3
.aggregation-panel-content {
    padding: 10px;
    background-color: #ffffff;
}

.hierarchy-chip-group {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px dashed #e6e6e6;
}

.hierarchy-chip-group h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #4a4a4a;
}

.hierarchy-chip-group .badge {
    transition: all 0.2s ease-in-out;
}

.chip-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.hierarchy-chip-group .badge:hover {
    background-color: #e6e6e6;
}

.panel-title-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-icon {
    width: 20px;
    height: 20px;
    margin: 5px;
    border-radius: 4px;
}

// Offer Target Panel
.target-panel-slider-minmax-slider {
    padding: 0 10px;
}

.target-panel-toast {
    position: relative;

    .MuiSnackbar-root {
        right: 25%;
        position: absolute;
    }
}

.bulk-edit-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: #60697D;
}

.bulk-edit-panel-body {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.bulk-edit-value-container {
    display: flex;
    align-items: end;
    gap: 16px;
}

.bmsm-offer-container {
    .ia-select-styled-dropdown-main-button {
        min-width: unset !important;
        max-width: unset !important;
        width: 100px;
    }
}

.bulk-edit-panel {
    width: 600px;
}

.maximization-parameter {
    .title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        img {
            width: 16px;
            height: 16px;
        }
    }
}

.cta-flex-wrapper {
    display: flex;
    gap: 16px;
    align-items: center;
}

.objective-name {
    position: relative;
    top: -8px;
}

.special-offer-type-panel-container {
    padding: 16px;
}

.special-offer-type-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.error-message {
    color: red;
    font-size: 12px;
    margin: 16px 0;
}

.ia-approve-modal-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.discount-rules-container {
    flex-wrap: wrap;
}

.copy-scenario-horizontal-line {
    border-top: 1px solid #d9dde7;
    margin: 16px 0;
}

.graph-view-container {
    height: 56px;
}

.graph-view-header {
    display: flex;
    align-items: end;
    gap: 24px;
    height: inherit;

    .vertical-line {
        height: 32px;
        border-left: 1px solid #D9DDE7;
    }

    .graph-view-header-info {
        height: inherit;
    }

    .offer-name-label {
        margin: 4px 0 8px 0;
    }
}

.graph-view-graph-container {
    padding: 12px;
    border: 1px solid #C3C8D4;
    border-radius: 8px;
    margin-top: 12px;
}

.graph-view-panel {
    width: 925px;
}

.graph-view-card-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: center;
    margin-top: 16px;
    gap: 12px;
}

.graph-view-card {
    display: flex;
    gap: 12px;
    width: 288px;
    height: 71px;
    border: 1px solid #D9DDE7;
    align-items: center;
    border-radius: 8px;
    padding: 0px 20px;
}

.vertical-dotted-line {
    width: 1px;
    border-left: 1px dashed #EFF2FA;
    height: inherit;
}