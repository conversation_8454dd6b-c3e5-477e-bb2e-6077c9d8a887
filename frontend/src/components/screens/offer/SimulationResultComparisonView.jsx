import React, { useEffect } from 'react'
import { toCurrencyByCurrencyIdWithDecimalFormatted, toPercentage, toUnit } from '../../../utils/helpers/formatter';
import CustomAccordion from '../../ui/calendar/customAccordion/CustomAccordion';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import { Chip } from '@mui/material';
import _ from 'lodash';
import { replaceSpecialCharacter } from '../../../utils/helpers/utility_helpers';

const SimulationResultComparisonView = (props) => {
  const { currency_details = {} } = props;
  const [expandGm, setExpandGm] = React.useState(true);
  const [expandRevenue, setExpandRevenue] = React.useState(false);
  const [expandSales, setExpandSales] = React.useState(false);
  const [selectedCol, setSelectedCol] = React.useState(["normal", "stacked"]);
  const [selectedScenario, setSelectedScenario] = React.useState([0, 1, 2]);
  const [data, setData] = React.useState(_.cloneDeep(props.data) || []);
  useEffect(() => {
    console.log(props);
  }, [])
  const metricOrder = {
      margin: [
          { title: "Baseline", valueKey: "baseline_margin" },
          { title: "Incremental", valueKey: "incremental_margin" },
          { title: "Halo", valueKey: "affinity_margin" },
          { title: "Pull Forward", valueKey: "pull_forward_margin" },
          { title: "Cannibalization", valueKey: "cannibalization_margin" },
          { title: "AUM $", valueKey: "aum", formatter: "dollar" },
      ],
      revenue: [
          { title: "Baseline", valueKey: "baseline_revenue" },
          { title: "Incremental", valueKey: "incremental_revenue" },
          { title: "Halo", valueKey: "affinity_revenue" },
          { title: "Pull Forward", valueKey: "pull_forward_revenue" },
          { title: "Cannibalization", valueKey: "cannibalization_revenue", },
          { title: "AUR $", valueKey: "aur", formatter: "dollar" },
      ],
      gm_percent: [
          { title: "GM %", valueKey: "gm_percent", formatter: "percent" },
          { title: "CM %", valueKey: "contribution_margin_percent", formatter: "percent", },
          { title: "CM $", valueKey: "contribution_margin", formatter: "dollar", }
      ],
      st_percent: [
          { title: "ST %", valueKey: "st_percent", formatter: "percent" },
          { title: "Inventory", valueKey: "inventory", formatter: "dollar", }
      ],
      sales: [
          // { title: "Total Sales Units", valueKey: "total_sales_units" },
          { title: "Baseline", valueKey: "baseline_sales_units" },
          { title: "Incremental", valueKey: "incremental_sales_units" },
      ],
  }
  const cols = [
    {
      label: "Normal",
      key: "original",
      value: "normal",
      disabled: true
    },
    {
      label: "Normal #",
      key: "overridden",
      value: "normal#"
    },
    {
      label: "Stacked",
      key: "stacked_original",
      value: "stacked",
      disabled: true
    },
    {
      label: "Stacked #",
      key: "stacked_overridden",
      value: "Stacked#"
    },
  ]
  const kpiItemValue = (formatter, value) => {
    if (typeof value === "number") {
      if (formatter === "dollar") {
        return toCurrencyByCurrencyIdWithDecimalFormatted({
          value,
          ...(currency_details || {}),
        });
      }
      if (formatter === "percent") {
        return toPercentage({ value });
      }
      return value;
    }
    return "-";
  };
  const handleClick = (value) => {
    if (selectedCol.includes(value))
      setSelectedCol(selectedCol.filter((col) => col !== value));
    else
      setSelectedCol([...selectedCol, value]);
  }
  const handleScenarioClick = (value) => {
    let temp_arr = [];
    if (selectedScenario.includes(value)) {
      temp_arr = selectedScenario.filter((scenario) => scenario !== value)
    }
    else {
      temp_arr = [...selectedScenario, value]
    }
    setSelectedScenario(temp_arr);
    setData(props.data.filter(item =>
      temp_arr.includes(item.scenario_order_id)
    )); 
  }
  return (
    <div className="marginBottom-12">
        <div className="flexWithCenterAlign chip-container">
          <div className="flexWithCenterAlign">
            <p className="text-14-600">Select Scenario Type: &nbsp;</p>
            {props.data.map((item, index) =>
              <Chip
                label={(replaceSpecialCharacter(item.scenario_name || "Scenario " + item.scenario_order_id))}
                className={`${selectedScenario.includes(item.scenario_order_id) ? "chip-selected" : "chip-label"} text-14-500`}
                // scenario_order_id 0 is for IA Recommended
                variant={!item.scenario_order_id == 0 ? "outlined" : ""}
                onClick={() => handleScenarioClick(item.scenario_order_id)}
                key={"scenario-chip-"+index}
              />
            )}
          </div>
          <div className="vert-line" style={{height: "25px"}} />
          <div className="flexWithCenterAlign">
            <p className="text-14-600">Select View Type: &nbsp;</p>
            {cols.map((col, index) =>
              <Chip
                key={"view-type-chip-"+index}
                label={replaceSpecialCharacter(col.label)}
                className={`${selectedCol.includes(col.value) && !col.disabled ? "chip-selected" : ""} text-14-500 ${!col.disabled ? "chip-label" : ""}`}
                variant={!col.disabled ? "outlined" : ""}
                onClick={() => !col.disabled && handleClick(col.value)}
              />
            )}
          </div>
        </div>
      <div className="comparison-container" >
        {data.map((item, data_index) => {
          return (
            selectedScenario.includes(item.scenario_order_id) &&
            <div className={`scenario-${item.scenario_order_id} scenario-comaprison`}>
              <h3>{replaceSpecialCharacter(item.scenario_name || "Scenario " + item.scenario_order_id)}</h3>
              <div>
                <div className="grid-4-1fr" style={{ margin: "20px 0" }}>
                  {cols.map((col, index) =>
                    selectedCol.includes(col.value)
                      ? <p className="text-12-600" key={"col-label-"+index}>{replaceSpecialCharacter(col.label)}</p>
                      : <p></p>
                  )}
                </div>
                <div className="text-14-500">
                  {/* Margin Accordion */}
                  <CustomAccordion
                    id="gm-accordion"
                    label={null}
                    isCustomLabel={true}
                    customClassName={"scenario-comparison-accordion"}
                    hideBlueLine={true}
                    expanded={expandGm}
                    hideExpandIcon={true}
                    cusotomLabelComponent={
                      <div className={`grid-4-1fr comparison-accordion-header ${expandGm ? "active" : ""}`}>
                        {data_index === 0 ?
                          <>
                            <span
                              className={`expand-btn ${expandGm ? "active" : ""} cursor-pointer position-absolute-left-130`}
                              onClick={() => setExpandGm(!expandGm)}
                              key={"gm-expand-btn"}
                            >
                              GM $
                              {expandGm ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                            </span>
                            <div className="metric-line-1"></div>
                          </>
                          : <div className="metric-line"></div>
                        }
                        {cols.map((col, index) =>
                          selectedCol.includes(col.value)
                            ? <p key={"gm-value-"+index}>{toCurrencyByCurrencyIdWithDecimalFormatted({
                                value: item[col.key]?.["margin"],
                                ...(currency_details || {}),
                              })}</p>
                            : <p></p>
                        )}
                      </div>
                    }
                    handleAccordion={(params) => { }}
                  >
                    {metricOrder.margin.map((metric, index) => {
                      return (
                        <div key={"gm-value-container-"+index} className="grid-4-1fr value-container">
                          {data_index === 0 && <p key={"gm-value-title-"+index} className="position-absolute-left-130">{metric.title}</p>}
                          {cols.map((col, index) =>
                            selectedCol.includes(col.value)
                              ? <p key={"gm-value-"+index}>{
                                  toCurrencyByCurrencyIdWithDecimalFormatted({
                                    value: item[col.key]?.[metric.valueKey],
                                    ...(currency_details || {}),
                                  })}</p>
                              : <p></p>
                          )}
                        </div>
                      )
                    })}
                  </CustomAccordion>
                  {/* Revenue */}
                  <CustomAccordion
                    id="revenue-accordion"
                    label={null}
                    isCustomLabel={true}
                    customClassName={"scenario-comparison-accordion"}
                    hideBlueLine={true}
                    expanded={expandRevenue}
                    hideExpandIcon={true}
                    cusotomLabelComponent={
                      <div className={`grid-4-1fr comparison-accordion-header ${expandRevenue ? "active" : ""}`}>
                        {data_index === 0 ? <>
                          <span
                            className={`expand-btn ${expandRevenue ? "active" : ""} cursor-pointer position-absolute-left-130`}
                            onClick={() => setExpandRevenue(!expandRevenue)}
                          >
                            Revenue
                            {expandRevenue ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                          </span>
                          <div className="metric-line-1"></div>
                        </>
                          : <div className="metric-line"></div>
                        }
                        {cols.map((col, index) =>
                          selectedCol.includes(col.value)
                            ? <p key={index}>{toCurrencyByCurrencyIdWithDecimalFormatted({
                                value: item[col.key]?.["revenue"],
                                ...(currency_details || {}),
                              })}</p>
                            : <p></p>
                        )}
                      </div>
                    }
                    handleAccordion={(params) => { }}
                  >
                    {metricOrder.revenue.map((metric, index) => {
                      return (
                        <div key={"revenue-value-container-"+index} className="grid-4-1fr value-container">
                          {data_index === 0 && <p key={"revenue-value-title-"+index} className="position-absolute-left-130">{metric.title}</p>}
                          {cols.map((col, index) =>
                            selectedCol.includes(col.value)
                              ? <p key={"revenue-value-"+index}>{toCurrencyByCurrencyIdWithDecimalFormatted({
                                value: item[col.key]?.[metric.valueKey],
                                ...(currency_details || {}),
                              })}</p>
                              : <p></p>
                          )}
                        </div>
                      )
                    })}
                  </CustomAccordion>
                  {/* Sales */}
                  <CustomAccordion
                    id="sales-accordion"
                    label={null}
                    isCustomLabel={true}
                    customClassName={"scenario-comparison-accordion"}
                    hideBlueLine={true}
                    expanded={expandSales}
                    hideExpandIcon={true}
                    cusotomLabelComponent={
                      <div className={`grid-4-1fr comparison-accordion-header ${expandSales ? "active" : ""}`}>
                        {data_index === 0 ?
                          <>
                            <span
                              className={`expand-btn ${expandSales ? "active" : ""} cursor-pointer position-absolute-left-130`}
                              onClick={() => setExpandSales(!expandSales)}
                            >
                              Sales
                              {expandSales ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                            </span>
                            <div className="metric-line-1"></div>
                          </>
                          : <div className="metric-line"></div>
                        }
                        {cols.map((col, index) =>
                          selectedCol.includes(col.value)
                            ? <p key={"sales-value-"+index}>{toUnit({ value: item[col.key]?.["total_sales_units"] })}</p>
                            : <p></p>
                        )}
                      </div>
                    }
                    handleAccordion={(params) => { }}
                  >
                    {metricOrder.sales.map((metric, index) => {
                      return (
                        <div key={"sales-value-container-"+index} className="grid-4-1fr value-container">
                          {data_index === 0 && <p key={"sales-value-title-"+index} className="position-absolute-left-130">{metric.title}</p>}
                          {cols.map((col, index) =>
                            selectedCol.includes(col.value)
                              ? <p key={"sales-value-"+index}>{toUnit({ value: item[col.key]?.[metric.valueKey] })}</p>
                              : <p></p>
                          )}
                        </div>
                      )
                    })}
                  </CustomAccordion>
                  <div style={{ position: "relative" }}>
                    {data_index === 0 && <div
                      className={`expand-btn position-absolute-left-130`}
                      style={{ top: "10px", flexDirection: "column", gap: "15px" }}
                    >
                      {metricOrder.gm_percent.map((metric, index) =>
                        <p key={"gm-percent-title-"+index}>{metric.title}</p>
                      )}
                    </div>}
                    {data_index === 0 ?
                      <div className="metric-line-1" style={{ top: "50%" }} />
                      : <div className="metric-line" style={{ top: "50%" }} />
                    }
                    <div className="comparison-accordion-header">
                      {metricOrder.gm_percent.map((metric, index) => {
                        return (
                          <div key={"gm-percent-header-"+index} className="grid-4-1fr">
                            {cols.map((col, index) =>
                              selectedCol.includes(col.value)
                                ? <p key={"gm-percent-value-"+index}>{kpiItemValue(metric.formatter, item[col.key]?.[metric.valueKey])}</p>
                                : <p></p>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
export default SimulationResultComparisonView