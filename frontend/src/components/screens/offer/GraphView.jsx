import React, { useEffect, useState } from 'react';
import { Panel, Select } from "impact-ui";
import { useSelector } from "react-redux";
import { capitalizeFirstLetter, labelCurrencyHandler, replace<PERSON><PERSON>ialCharacter } from '../../../utils/helpers/utility_helpers';
import ActualSalesU from "../../../assets/imageAssets/actualSalesU.png";
import FinSalesUnits from "../../../assets/imageAssets/finSalesUnits.png";
import AffinityIcon from "../../../assets/imageAssets/affinityIcon.png";
import CannabalizationIcon from "../../../assets/imageAssets/cannabalizationIcon.png";
import PullForwardIcon from "../../../assets/imageAssets/pullForwardIcon.png";
import TotalIcon from "../../../assets/imageAssets/lift.png";

import Charts from '../../ui/charts/Charts';
import { global_labels } from '../../../constants/Constants';
import { toCurrencyByCurrencyIdWithDecimalFormatted, toUnit } from '../../../utils/helpers/formatter';


const graphViewIcons = {
    "Baseline": ActualSalesU,
    "Incremental": FinSalesUnits,
    "Affinity": AffinityIcon,
    "Cannibalization": CannabalizationIcon,
    "PullForward": PullForwardIcon,
    "Total": TotalIcon,
}

const GraphView = (props) => {
    const { isPanelOpen, setIsPanelOpen, currency } = props;

    const metricOrder = {
        margin: {
            // Margin: "margin",
            "Baseline": "baseline_margin",
            "Incremental": "incremental_margin",
            "Affinity": "affinity_margin",
            // "Halo": "affinity_margin",
            "Cannibalization": "cannibalization_margin",
            "PullForward": "pull_forward_margin",
            // "AUM$": "aum",
            "Total": "total_margin",
        },
        revenue: {
            "Baseline": "baseline_revenue",
            "Incremental": "incremental_revenue",
            "Affinity": "affinity_revenue",
            // "Halo": "affinity_revenue",
            "PullForward": "pull_forward_revenue",
            "Cannibalization": "cannibalization_revenue",
            // "AUR$": "aur",
            "Total": "total_revenue",
        },
        // gm_percent: {
        //     GM$: "gm_percent", formatter: "percent",
        //     CM$: "contribution_margin_percent", formatter: "percent",
        //     CM$: "contribution_margin", formatter: "dollar",
        // },
        units: {
            // { title: "Total Sales Units", valueKey: "total_sales_units" },
            "Baseline": "baseline_sales_units",
            "Incremental": "incremental_sales_units",
            "Total": "total_sales_units",
        },
    }

    const scenario_type = [
        {
            label: "Normal",
            value: "original",
        },
        {
            label: "Normal #",
            value: "overridden",
        },
        {
            label: "Stacked",
            value: "stacked_original",
        },
        {
            label: "Stacked #",
            value: "stacked_overridden",
        },
    ]

    const {
        scenario_data = [],
        promo_name,
        start_date,
        end_date,
    } = useSelector(store => {
        return store.pricesmartPromoReducer?.promo?.promoDetails || {}
    });

    const [selectedScenario, setSelectedScenario] = useState(null);
    const [isOpen, setIsOpen] = useState(false);
    const [scenarioOptions, setScenarioOptions] = useState([]);
    const [chartData, setChartData] = useState([]);
    const [selectedMetric, setSelectedMetric] = useState({ label: global_labels.margin, value: "margin" });
    const [metricDropDownOpen, setMetricDropDownOpen] = useState(false);
    const [metricDropdownOptions, setMetricDropdownOptions] = useState([]);
    const [scenarioTypeDropdownOpen, setScenarioTypeDropdownOpen] = useState(false);
    const [selectedScenarioType, setSelectedScenarioType] = useState({ label: "Normal", value: "original" });
    const [cardData, setCardData] = useState([]);

    useEffect(() => {
        const options = Object.keys(metricOrder).map(metric => ({
            label: labelCurrencyHandler(global_labels[metric], currency?.symbol || "$"),
            value: metric,
        }));
        setMetricDropdownOptions(options);
        setSelectedMetric({ label: labelCurrencyHandler(selectedMetric?.label, currency?.symbol || "$"), value: selectedMetric?.value });
    }, [isPanelOpen]);

    useEffect(() => {
        const arr = [];

        if (scenario_data.length > 0) {
            const sorted_scenario_data = _.cloneDeep(scenario_data).sort((a, b) => a?.scenario_order_id - b?.scenario_order_id);
            sorted_scenario_data.forEach(scenario => {
                if (scenario?.scenario_id || scenario?.scenario_id === 0) {
                    arr.push({
                        ...scenario,
                        label: scenario?.scenario_name || ("Scenario " + scenario?.scenario_order_id),
                        value: scenario?.scenario_id,
                    })
                }
            })
        }
        if (_.isEmpty(selectedScenario)) {
            setSelectedScenario(arr?.[0]);
        }
        setScenarioOptions(arr);
    }, [scenario_data, isPanelOpen]);

    useEffect(() => {
        if (selectedScenario && scenario_data.length > 0 && selectedMetric && isPanelOpen) {
            const data = [
                {
                    data: [],
                    colors: [],
                    colorByPoint: true,
                },
            ];
            const card_data = [];
            const metric = metricOrder[selectedMetric?.value];
            const scenario = scenario_data.find(scenario => scenario.scenario_id === selectedScenario?.value);

            Object.keys(metric).forEach(key => {
                if (key === "Total" || key === "Baseline") {
                    data[0].colors.push("#658EC4");
                } else {
                    data[0].colors.push(
                        scenario?.[selectedScenarioType?.value]?.[metric[key]] >= 0
                            ? "#6BBEC2"
                            : "#D5647B"
                    );
                }
                card_data.push({
                    label: key,
                    value: scenario?.[selectedScenarioType?.value]?.[metric[key]],
                });
                data[0].data.push([
                    key,
                    selectedMetric.value === "units"
                        ? Math.round(scenario?.[selectedScenarioType?.value]?.[metric[key]])
                        : scenario?.[selectedScenarioType?.value]?.[metric[key]]
                    ,
                ]);
            });
            setChartData({
                seriesData: data,
            });
            // Added this timeout as a fix for a rendering issue
            // where the chart was showing incorrectt labels
            const time_out = setTimeout(() => {
                setCardData(card_data);
            }, 10);
            return () => clearTimeout(time_out);
        }
    }, [selectedScenario, scenario_data, selectedMetric, isPanelOpen, selectedScenarioType]);

    const getFormatterValue = (metric, value) => {
        if (metric === "units") {
            return toUnit({ value: value });
        }

        return toCurrencyByCurrencyIdWithDecimalFormatted({ value: value, ...(currency || {}) });
    }

    return (
        <Panel
            anchor="right"
            open={isPanelOpen}
            className="graph-view-panel"
            onClose={() => setIsPanelOpen(false)}
            size="large"
            title={"Graph View"}
        >
            <div className="graph-view-container">
                <div className="graph-view-header">
                    <Select
                        currentOptions={scenarioOptions}
                        initialOptions={scenarioOptions}
                        label="Select Scenario"
                        labelOrientation="top"
                        setSelectedOptions={setSelectedScenario}
                        setCurrentOptions={() => { }}
                        placeholder="Select.."
                        isRequired={false}
                        isWithSearch={false}
                        isMulti={false}
                        isSelectAll={false}
                        onSelectAll={() => { }}
                        setIsSelectAll={() => { }}
                        selectedOptions={selectedScenario}
                        isOpen={isOpen}
                        setIsOpen={setIsOpen}
                        isCloseWhenClickOutside={true}
                        toggleSelectAll
                    />
                    <div className="vertical-line" />
                    <div className="graph-view-header-info">
                        <p className="secondaryText-14-700 offer-name-label">{capitalizeFirstLetter(global_labels?.promo_alias)} name :</p>
                        <p className="text-14-800">{replaceSpecialCharacter(promo_name)}</p>
                    </div>
                    <div className="graph-view-header-info">
                        <p className="secondaryText-14-700 offer-name-label">Date :</p>
                        <p className="text-14-800">{start_date} - {end_date}</p>
                    </div>
                </div>
                <div className="graph-view-graph-container">
                    <div className="flexContentAround">
                        <p className="secondaryText-14-700 offer-name-label">{selectedScenario?.label}</p>
                        <div className="flexWithGap16">
                            <Select
                                currentOptions={scenario_type}
                                initialOptions={scenario_type}
                                label="Scenario Type"
                                labelOrientation="right"
                                setSelectedOptions={setSelectedScenarioType}
                                setCurrentOptions={() => { }}
                                placeholder="Select.."
                                isRequired={false}
                                isWithSearch={false}
                                isMulti={false}
                                isSelectAll={false}
                                onSelectAll={() => { }}
                                setIsSelectAll={() => { }}
                                selectedOptions={selectedScenarioType}
                                isOpen={scenarioTypeDropdownOpen}
                                setIsOpen={setScenarioTypeDropdownOpen}
                            />
                            <div className="vertical-line" />
                            <Select
                                currentOptions={metricDropdownOptions}
                                initialOptions={metricDropdownOptions}
                                label="Metrics"
                                labelOrientation="right"
                                setSelectedOptions={setSelectedMetric}
                                setCurrentOptions={() => { }}
                                placeholder="Select.."
                                isRequired={false}
                                isWithSearch={false}
                                isMulti={false}
                                isSelectAll={false}
                                onSelectAll={() => { }}
                                setIsSelectAll={() => { }}
                                selectedOptions={selectedMetric}
                                isOpen={metricDropDownOpen}
                                setIsOpen={setMetricDropDownOpen}
                            />
                        </div>
                    </div>
                    <Charts
                        data={_.cloneDeep(chartData)}
                        enableLegend={false}
                        tooltipFormatter={(e) => {
                            return `
                                <div style="display: flex; flex-direction: column; gap: 8px;">
                                    <div>${e.key}</div>
                                    <div>${getFormatterValue(selectedMetric.value, e.y)}</div>
                                </div>
                            `;
                        }}
                    />
                </div>
                <div className="graph-view-card-container">
                    {cardData?.map((data, index) => {
                        return (
                            <div className="graph-view-card" key={`${index}-${data?.label}`}>
                                <img src={graphViewIcons[data?.label]} alt={data?.label} width={"32px"} height={"32px"} />
                                <div className="vertical-dotted-line" />
                                <div>
                                    <p className="secondaryText-14-700 offer-name-label">{data?.label}</p>
                                    <p className="text-14-800">{getFormatterValue(selectedMetric.value, data?.value)}</p>
                                </div>
                            </div>
                        )
                    })}
                </div>
            </div>
        </Panel>
    )
}

export default GraphView