import React, {
    useState,
    useCallback,
    useRef,
    useEffect,
} from "react";
import { useDispatch, useSelector } from "react-redux";

import _ from "lodash";
import {
    Button,
    Input,
    Tabs,
    FileUpload,
    TextArea,
    ButtonGroup,
    Modal,
    Prompt,
    Badge
} from "impact-ui";
import { Table } from "impact-ui-v3";
import {
    storeSelectionOptions,
    specificStoreSelectionsOptions,
    specificStoreFilterConfig,
} from "./OfferConstants";

import ComponentFilters from "../../ui/componentFilters/ComponentFilters";
import {
    excelTemplateDownload,
    toastError,
} from "../../../store/features/global/global";
import EmptyData from "../../ui/emptyData/EmptyData";
import {
    overwriteFilters,
    resetAllFiltersData,
} from "../../../store/features/filters/filters";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";
import ISymbol from "../../../assets/imageAssets/ISymbol.svg?.url";
import { createStoreTableConfig, invalidDataColumnConfigStore, storeGroupTableConfiguation } from "../marketingCalendar/createEvent/createEventConstants";
import { getStoreGroups, getStores, getStoresFromFile, getStoresFromIds, setIsEditedFlag } from "../../../store/features/promoReducer/promoReducer";
import { fabricatePayloadHierarchy, mergeFiltersData } from "../../../utils/helpers/utility_helpers";

const StoreSelection = props => {
    const dispatch = useDispatch();
    const storeTableRef = useRef();
    const storeGroupTableRef = useRef();

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const {
        promoDetails = {},
        storeData = [],
        storeGroupData = [],
        storeDataFromUploadOrCopyPaste = {},
        promoEventDetails = {},
    } = useSelector((store) => store?.pricesmartPromoReducer.promo);

    const [uploadedExcel, setUploadedExcel] = useState([]);
    const [copyPasteData, setCopyPasteData] = useState("");
    const [lastFiltersData, setLastFiltersData] = useState({});
    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [activeTab, setActiveTab] = useState(null)
    const [modifiedFilterConfig, setModifiedFilterConfig] = useState([]);
    const [storeTableData, setStoreTableData] = useState([]);
    const [tempTableData, setTempTableData] = useState([]);

    const [showDataOverridePrompt, setShowDataOverridePrompt] = useState(false);
    const [showInvalidDataModal, setShowInvalidDataModal] = useState(false);
    const [invalidTableData, setInvalidTableData] = useState([]);
    const [showOutofScopeDataModal, setShowOutofScopeDataModal] = useState(false);

    const [storeTableDef, setStoreTableDef] = useState(createStoreTableConfig);
    const [storeGroupTableDef, setStoreGroupTableDef] = useState(storeGroupTableConfiguation);
    const [storeSelectOptions, setStoreSelectOptions] = useState([]);
    const [tabOptions, setTabOptions] = useState(specificStoreSelectionsOptions);

    useEffect(() => {
        const obj = {
            all_stores: ["all_stores", "specific_stores", "ecom_stores", "bnm_stores", "store_group"],
            ecom_stores: ["ecom_stores", "specific_stores", "store_group"],
            bnm_stores: ["bnm_stores", "specific_stores", "store_group"],
            specific_stores: ["specific_stores", "store_group"],
            store_group: ["specific_stores", "store_group"]
        }
        const toBeShownOptions = [];
        _.forEach(storeSelectionOptions, (option) => {
            let opt = _.cloneDeep(option);
            if (props?.allowPromoEdit) {
                if (promoEventDetails?.store_restriction?.store_restriction_level) {
                    if (
                        obj?.[promoEventDetails?.store_restriction?.store_restriction_level].includes(option.value)
                    ) {
                        if (props?.isRestricted && option.value !== promoEventDetails?.store_restriction?.store_restriction_level)
                            opt.disabled = true;
                    } else {
                        opt.disabled = true
                    }
                } else if (props?.disabledOptions && props?.disabledOptions?.length > 0) {
                    if (props?.disabledOptions.includes(option.value)) {
                        opt.disabled = true;
                    }
                }
            } else if (props?.selectedStoreType !== option.value) {
                opt.disabled = true;
            }
            toBeShownOptions.push(opt);
        });
        setStoreSelectOptions(toBeShownOptions);

        if (props?.disabledOptions && props?.disabledOptions?.length > 0) {
            let storeOptions = _.cloneDeep(storeSelectionOptions);
            _.forEach(storeOptions, (option) => {
                if (props?.disabledOptions.includes(option.value) || !props?.allowPromoEdit) {
                    option.disabled = true;
                }
            });
            setStoreSelectOptions(storeOptions);
        }
    }, [
        promoEventDetails,
        props?.isRestricted,
        props?.selectedStoreType,
        props?.disabledOptions,
        props?.allowPromoEdit
    ]);


    useEffect(() => {
        if (props?.specificStoreSelection) {
            setActiveTab(props?.specificStoreSelection)
        }
        if (props?.allowPromoEdit) {
            setTabOptions(specificStoreSelectionsOptions);
        } else {
            const array = specificStoreSelectionsOptions.map(item => {
                if (item.value != props?.specificStoreSelection) {
                    return { ...item, disabled: true }
                }
                return item;
            });
            setTabOptions(array);
        }
    }, [props?.specificStoreSelection, props?.allowPromoEdit])

    useEffect(() => {
        const modifiedFilterConfig = specificStoreFilterConfig.map((item) => {
            return {
                ...item,
                extraParams: {
                    event_id: promoDetails?.event_id || null,
                }
            };
        });
        setModifiedFilterConfig(modifiedFilterConfig);
    }, [promoDetails]);

    useEffect(() => {
        if (storeData.length) {
            handleStoreTableData(storeData);
        }
    }, [storeData]);

    useEffect(() => {
        if (props?.isRestricted || !props?.allowPromoEdit) {
            let tableDef = _.cloneDeep(createStoreTableConfig);
            tableDef = _.filter(tableDef, (item) => item.field !== "" && !item.checkboxSelection);
            setStoreTableDef(tableDef);

            tableDef = _.cloneDeep(storeGroupTableConfiguation);
            tableDef = _.filter(tableDef, (item) => item.field !== "" && !item.checkboxSelection);
            setStoreGroupTableDef(tableDef);
        } else {
            setStoreTableDef(createStoreTableConfig);
            setStoreGroupTableDef(storeGroupTableConfiguation);
        }
    }, [props.isRestricted, props?.allowPromoEdit]);

    useEffect(() => {
        if (!_.isEmpty(storeDataFromUploadOrCopyPaste)) {
            if (!_.isEmpty(storeDataFromUploadOrCopyPaste?.out_of_scope)) {
                setShowOutofScopeDataModal(true);
            }
            if (
                _.isEmpty(storeDataFromUploadOrCopyPaste?.inactive) &&
                _.isEmpty(storeDataFromUploadOrCopyPaste?.invalid) &&
                !_.isEmpty(storeDataFromUploadOrCopyPaste?.valid)
            )
                handleStoreTableData(storeDataFromUploadOrCopyPaste.valid);
            else if (
                !_.isEmpty(storeDataFromUploadOrCopyPaste?.inactive) ||
                !_.isEmpty(storeDataFromUploadOrCopyPaste?.invalid)
            ) {
                setInvalidTableData([
                    ...storeDataFromUploadOrCopyPaste.inactive,
                    ...storeDataFromUploadOrCopyPaste.invalid,
                ]);
                setShowInvalidDataModal(true);
            }
        }
    }, [storeDataFromUploadOrCopyPaste]);

    const handleStoreTableData = (data) => {
        if (storeTableData.length > 0) {
            setTempTableData(data);
            setShowDataOverridePrompt(true);
        } else
            setStoreTableData(data);
    }

    const onStoreTypeSelectionChange = (e) => {
        // set selected store type
        if (props?.isRestricted) return;
        const { value } = e.target;
        props.setSelectedStoreType(value);
        setGlobalSearchText("");
        dispatch(setIsEditedFlag(true));
    }

    const onSpecificStoresFiltersApply = () => {
        let callAPI = true;
        // check if all mandatory filters are selected
        const filtersDataSelected = _.cloneDeep(
            filtersData["CREATE_OFFER_SPECIFIC_STORES_COMPONENT"]
        );
        _.forEach(modifiedFilterConfig, (config) => {
            const key = config.filterId;
            if (
                config.isMandatory &&
                !filtersDataSelected?.[key]?.selectedOptionsArray.length
            ) {
                callAPI = false;
            }
        });
        if (!callAPI) {
            dispatch(toastError("Please select all mandatory filters"));
            return;
        }
        // set last filters data for replace and retain prompt
        if (_.isEmpty(lastFiltersData))
            setLastFiltersData(_.cloneDeep(filtersDataSelected));
        // if all mandatory filters are selected, call API to get store data
        const payload = fabricatePayloadHierarchy(filtersDataSelected);

        payload.event_id = promoDetails?.event_id || null;

        dispatch(getStores(payload));
        dispatch(setIsEditedFlag(true));
    };

    const onStoreGroupFiltersApply = () => {
        let callAPI = true;
        // check if all mandatory filters are selected
        const filtersDataSelected = _.cloneDeep(
            filtersData["CREATE_OFFER_STORE_GROUP_COMPONENT"]
        );
        _.forEach(modifiedFilterConfig, (config) => {
            const key = config.filterId;
            if (
                config.isMandatory &&
                !filtersDataSelected?.[key]?.selectedOptionsArray.length
            ) {
                callAPI = false;
            }
        });
        if (!callAPI) {
            dispatch(toastError("Please select all mandatory filters"));
            return;
        }
        // set last filters data for replace and retain prompt
        if (_.isEmpty(lastFiltersData))
            setLastFiltersData(_.cloneDeep(filtersDataSelected));
        // if all mandatory filters are selected, call API to get store data
        const payload = fabricatePayloadHierarchy(filtersDataSelected);
        payload.event_id = promoDetails?.event_id || null;
        dispatch(getStoreGroups(payload));
        dispatch(setIsEditedFlag(true));
    };

    const onUploadExcelNextClick = async () => {
        // on next click of excel upload, call API to get store data
        const formData = new FormData();
        formData.append("file", uploadedExcel[0]?.file);
        const payload = {
            file: formData,
            event_id: promoDetails?.event_id || null
        }
        const responseFlag = false
        await dispatch(
            getStoresFromFile(payload)
        );
        // if store data is not fetched, reset uploaded excel data
        if (!responseFlag) {
            setUploadedExcel([]);
        }
        dispatch(setIsEditedFlag(true));
    };

    const onSpecificStoreClearFilter = () => {
        // clear filters data for store configuration
        dispatch(
            resetAllFiltersData({
                from: "CREATE_OFFER_SPECIFIC_STORES_COMPONENT",
            })
        );
    };

    const onStoreGroupClearFilter = () => {
        // clear filters data for store configuration
        dispatch(
            resetAllFiltersData({
                from: "CREATE_OFFER_STORE_GROUP_COMPONENT",
            })
        );
    };

    const tabChangeHandler = (event, val) => {
        // set active tab
        setActiveTab(val)
        props.setSpecificStoreSelection(val);
    }

    const excelDownloadHandler = () => {
        // download excel template for store selection
        const payload = {
            type: "store_group_upload",
        };
        dispatch(
            excelTemplateDownload(
                {
                    params: payload,
                    responseType: "blob",
                },
                "store_selection_template"
            )
        );
    };

    const onCopyPasteChange = (e) => {
        // set copy paste data in state
        const data = e.target.value;
        let array = _.filter(data.split("\n"), (item) => item !== "");
        let val = array.join(",");
        setCopyPasteData(val);
    };

    const onCopyPasteSubmitClick = () => {
        // on submit click of copy paste, call API to get store data
        let storeIds = copyPasteData.split(",");
        storeIds = _.map(storeIds, (value) => value.trim());
        storeIds = _.filter(storeIds, (item) => item !== "").map(item => [...item.split(" ")]);
        const payload = {
            stores_data: storeIds,
            event_id: promoDetails?.event_id || null
        };
        dispatch(getStoresFromIds(payload));
        dispatch(setIsEditedFlag(true));
        setCopyPasteData("");
    };

    const onStoreRowSelection = useCallback((context) => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // set selected stores data
        const selectedRows = storeTableRef.current.api.getSelectedRows();
        props?.setSelectedStores(_.cloneDeep(selectedRows));

        // Added this source check to avoid calling setIsEditedFlag on apiSelectAll, as that is called by us through AG Grid API on first load
        if (context.source !== "apiSelectAll") dispatch(setIsEditedFlag(true));
    });

    const storeRowDataChanged = () => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // whenever row data updates in AG Grid, select all rows
        if (storeTableRef?.current?.api) storeTableRef.current.api.selectAll();
    };

    const onStoreGroupRowSelection = useCallback((context) => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // set selected stores data
        const selectedRows = storeGroupTableRef.current.api.getSelectedRows();
        props?.setSelectedStoreGroups(_.cloneDeep(selectedRows));

        // Added this source check to avoid calling setIsEditedFlag on apiSelectAll, as that is called by us through AG Grid API on first load
        if (context.source !== "apiSelectAll") dispatch(setIsEditedFlag(true));
    }, []);

    const storeGroupRowDataChanged = () => {
        if (props?.isRestricted || !props?.allowPromoEdit) return;
        // whenever row data updates in AG Grid, select all rows
        if (storeGroupTableRef?.current?.api) storeGroupTableRef.current.api.selectAll();
    };

    const onFilterTextBoxChanged = useCallback((text, forTable) => {
        // set global search text in state
        setGlobalSearchText(text);
        if (forTable === "store")
            storeTableRef.current.api.setGridOption("quickFilterText", text);
        else if (forTable === "store_group")
            storeGroupTableRef.current.api.setGridOption("quickFilterText", text);
    }, []);

    const onReplaceAndOverwriteClick = () => {
        // on replace and overwrite click, replace table data with current selection
        setStoreTableData(tempTableData);
        setShowDataOverridePrompt(false);
        setTempTableData([]);
    }

    const onRetainClick = () => {
        // on retain click, retain current selection and concat with previous selection
        let currentRows = _.cloneDeep(storeTableData);
        const tempRows = _.cloneDeep(tempTableData || []).map(item => ({
            ...item,
            // convert store_id to number as on upload it is string, and _.uniqBy is not working with string
            store_id: Number(item.store_id)
        }));
        currentRows = [
            ...currentRows,
            ...tempRows,
        ];
        currentRows = _.uniqBy(currentRows, "store_id");
        setStoreTableData(currentRows);
        setShowDataOverridePrompt(false);
        setTempTableData([]);

        // concat currennt data with previous data and set both last filters and current filters as same
        const updatedFiltersData = mergeFiltersData(
            _.cloneDeep(lastFiltersData),
            _.cloneDeep(filtersData["CREATE_OFFER_SPECIFIC_STORES_COMPONENT"])
        );
        setLastFiltersData(_.cloneDeep(updatedFiltersData));
        dispatch(
            overwriteFilters({
                filtersData: _.cloneDeep(updatedFiltersData),
                activeScreen: "CREATE_OFFER_SPECIFIC_STORES_COMPONENT",
            })
        );
    };

    const onActiveInactiveStoreProceed = () => {
        // on proceed click, set valid and inactive stores in state
        const validData = storeDataFromUploadOrCopyPaste?.valid || [];
        const inactiveData = storeDataFromUploadOrCopyPaste?.inactive || [];
        handleStoreTableData([...validData, ...inactiveData]);
        setInvalidTableData([]);
        setShowInvalidDataModal(false);
    }

    const onActiveStoreProceed = () => {
        // on proceed click, set valid stores in state
        const validData = storeDataFromUploadOrCopyPaste?.valid || [];
        handleStoreTableData(validData);
        setShowInvalidDataModal(false);
        setInvalidTableData([]);
    }

    return (
        <div>
            <ButtonGroup
                selectedOption={props.selectedStoreType}
                onChange={onStoreTypeSelectionChange}
                options={storeSelectOptions}
            // isDisabled={props?.isRestricted}
            />
            <span className="text-12-500 marginLeft-8">(you can choose only one of these options)</span>
            {
                props?.selectedStoreType === "all_stores" && (
                    <div className="add-drag-message">
                        <img src={ISymbol} alt="Note" />
                        <div>
                            <p className="secondaryText-14-500">
                                You have selected all stores.
                            </p>
                        </div>
                    </div>
                )
            }
            {
                props?.selectedStoreType === "ecom_stores" && (
                    <div className="add-drag-message">
                        <img src={ISymbol} alt="Note" />
                        <div>
                            <p className="secondaryText-14-500">
                                You have selected all E-com stores.
                            </p>
                        </div>
                    </div>
                )
            }
            {
                props?.selectedStoreType === "bnm_stores" && (
                    <div className="add-drag-message">
                        <img src={ISymbol} alt="Note" />
                        <div>
                            <p className="secondaryText-14-500">
                                You have selected all BnM stores.
                            </p>
                        </div>
                    </div>
                )
            }
            {props?.selectedStoreType === "specific_stores" &&
                <div className="offer-container-background marginTop-16">
                    {!props?.isRestricted && (
                        <div className="padding-16">
                            <Tabs
                                value={activeTab}
                                onChange={(event, val) => tabChangeHandler(event, val)}
                                tabNames={tabOptions}
                                tabPanels={[
                                    <div key="specific-stores-filter" className="flexColumn flex24">
                                        <span className="text-14-800">Select Filter</span>
                                        <ComponentFilters
                                            filterConfig={modifiedFilterConfig}
                                            callAPIonLoad={false}
                                            screen="CREATE_OFFER_SPECIFIC_STORES_COMPONENT"
                                            onPrimaryButtonClick={onSpecificStoresFiltersApply}
                                            onSecondaryButtonClick={onSpecificStoreClearFilter}
                                            primaryButtonText="Submit"
                                            secondaryButtonText="Clear Filters"
                                        />
                                    </div>,
                                    <FileUpload
                                        key="file-upload"
                                        fileList={uploadedExcel}
                                        numberOfFiles={1}
                                        onFileListChange={setUploadedExcel}
                                        onPrimaryButtonClick={onUploadExcelNextClick}
                                        onSecondaryButtonClick={() => setUploadedExcel([])}
                                        primaryButtonLabel="Next"
                                        secondaryButtonLabel="Cancel"
                                        validFileTypes={[
                                            {
                                                fileType: "xlsx",
                                                templateDownloader: excelDownloadHandler,
                                                typeOverride: false,
                                            },
                                        ]}
                                    />,
                                    <div key="copy-paste">
                                        <TextArea
                                            onChange={onCopyPasteChange}
                                            placeholder="Paste IDs here..."
                                            value={copyPasteData}
                                            width={"80vw"}
                                        />
                                        <div className="buttons_container">
                                            <Button
                                                onClick={() => setCopyPasteData("")}
                                                size="large"
                                                variant="url"
                                            >
                                                Reset
                                            </Button>
                                            <Button
                                                onClick={onCopyPasteSubmitClick}
                                                size="large"
                                                variant="primary"
                                                disabled={!copyPasteData.length}
                                            >
                                                Submit
                                            </Button>
                                        </div>
                                    </div>,
                                    <div key="store-group-filter" className="flexColumn flex24">
                                        <span className="text-14-800">Select Filter</span>
                                        <ComponentFilters
                                            filterConfig={modifiedFilterConfig}
                                            callAPIonLoad={false}
                                            screen="CREATE_OFFER_STORE_GROUP_COMPONENT"
                                            onPrimaryButtonClick={onStoreGroupFiltersApply}
                                            onSecondaryButtonClick={onStoreGroupClearFilter}
                                            primaryButtonText="Submit"
                                            secondaryButtonText="Clear Filters"
                                        />
                                    </div>,
                                ]}
                            />
                        </div>
                    )}
                    {activeTab !== "store_group" && (
                        <div className="padding-16">
                            {storeTableData?.length > 0 ?
                                <Table
                                    tableHeader={"Stores"}
                                    ref={storeTableRef}
                                    suppressMenuHide
                                    rowData={storeTableData}
                                    columnDefs={storeTableDef}
                                    rowSelection="multiple"
                                    onSelectionChanged={onStoreRowSelection}
                                    suppressRowClickSelection={props?.isRestricted || !props?.allowPromoEdit}
                                    onRowDataUpdated={storeRowDataChanged}
                                    topRightOptions={
                                        <div className="centerFlexWithGap12">
                                            <div className="positionRelative">
                                                {showGlobalSearch ? (
                                                    <div className="tableGlobalSearchContainer">
                                                        <Input
                                                            onChange={(e) =>
                                                                onFilterTextBoxChanged(
                                                                    e.target.value,
                                                                    "store"
                                                                )
                                                            }
                                                            placeholder="Search"
                                                            rightIcon={
                                                                <img
                                                                    src={SearchIcon}
                                                                    alt="Search Icon"
                                                                />
                                                            }
                                                            type="text"
                                                            value={globalSearchText}
                                                        />
                                                    </div>
                                                ) : null}
                                                <Button
                                                    icon={<img src={SearchIcon} alt="Search Icon" />}
                                                    onClick={() =>
                                                        setShowGlobalSearch(
                                                            (prev) => !prev
                                                        )
                                                    }
                                                    size="large"
                                                    variant="tertiary"
                                                />
                                            </div>
                                        </div>
                                    }
                                    topLeftOptions={
                                        <Badge
                                            color="default"
                                            label={`Selected Stores: ${props?.selectedStores?.length} / ${storeTableData?.length}`}
                                            size="default"
                                            variant="subtle"
                                        />
                                    }
                                />
                                : <EmptyData text="Please Select Filters to view stores" />
                            }
                        </div>
                    )}
                    {activeTab === "store_group" && (
                        <div className="padding-16">
                            {storeGroupData?.length > 0 ?
                                <Table
                                    tableHeader={"Stores Group"}
                                    ref={storeGroupTableRef}
                                    suppressMenuHide
                                    rowData={storeGroupData}
                                    columnDefs={storeGroupTableDef}
                                    rowSelection="multiple"
                                    onSelectionChanged={onStoreGroupRowSelection}
                                    suppressRowClickSelection={props?.isRestricted || !props?.allowPromoEdit}
                                    onRowDataUpdated={storeGroupRowDataChanged}
                                    topRightOptions={
                                        <div className="centerFlexWithGap12">
                                            <div className="positionRelative">
                                                {showGlobalSearch ? (
                                                    <div className="tableGlobalSearchContainer">
                                                        <Input
                                                            onChange={(e) =>
                                                                onFilterTextBoxChanged(
                                                                    e.target.value,
                                                                    "store_group"
                                                                )
                                                            }
                                                            placeholder="Search"
                                                            rightIcon={
                                                                <img
                                                                    src={SearchIcon}
                                                                    alt="Search Icon"
                                                                />
                                                            }
                                                            type="text"
                                                            value={globalSearchText}
                                                        />
                                                    </div>
                                                ) : null}
                                                <Button
                                                    iconPlacement="left"
                                                    icon={<img
                                                        src={SearchIcon}
                                                        alt="Search Icon"
                                                    />}
                                                    onClick={() =>
                                                        setShowGlobalSearch(
                                                            (prev) => !prev
                                                        )
                                                    }
                                                    size="large"
                                                    variant="tertiary"
                                                />
                                            </div>
                                        </div>
                                    }
                                    topLeftOptions={
                                        <Badge
                                            color="default"
                                            label={`Selected Stores Group: ${props?.selectedStoreGroups?.length} / ${storeGroupData?.length}`}
                                            size="default"
                                            variant="subtle"
                                        />
                                    }
                                />
                                : <EmptyData text="Please Select Filters to view stores groups" />
                            }
                        </div>
                    )}
                </div>
            }
            <Prompt
                handleClose={() => {
                    setShowDataOverridePrompt(false);
                }}
                onPrimaryButtonClick={onRetainClick}
                onSecondaryButtonClick={onReplaceAndOverwriteClick}
                primaryButtonLabel="Retain"
                secondaryButtonLabel="Replace & overwrite"
                title="Confirm Selection"
                variant="warning"
                isOpen={showDataOverridePrompt}
            >
                Do you want to Retain and Continue or Replace table with current
                selection
            </Prompt>

            <Modal
                onClose={() => {
                    setShowInvalidDataModal(false);
                }}
                onPrimaryButtonClick={onActiveInactiveStoreProceed}
                onSecondaryButtonClick={onActiveStoreProceed}
                primaryButtonLabel="Proceed with all Active and Inactive Stores"
                secondaryButtonLabel="Proceed with only Active Stores"
                size="medium"
                title="Stores Detail"
                open={showInvalidDataModal}
            >
                <div className="invalidProductsGrpModalContainer">
                    <span className="secondaryText-14-500">
                        Based on the data feed Pricesmart received, the uploaded
                        list contains some inactive/invalid Stores
                    </span>
                    <Table
                        tableHeader={"Stores"}
                        suppressMenuHide
                        rowData={invalidTableData}
                        columnDefs={invalidDataColumnConfigStore}
                    />
                </div>
            </Modal>

            <Modal
                onClose={() => setShowOutofScopeDataModal(false)}
                size="medium"
                title="Store Detail"
                open={showOutofScopeDataModal}
            >
                <div className="invalidProductsGrpModalContainer">
                    <span className="secondaryText-14-500">
                        Based on the data feed Pricesmart received, the uploaded
                        list contains out of the scope stores
                    </span>
                    <Table
                        tableHeader={"Stores"}
                        suppressMenuHide
                        rowData={storeDataFromUploadOrCopyPaste?.out_of_scope}
                        columnDefs={invalidDataColumnConfigStore}
                    />
                </div>
            </Modal>
        </div>
    )
}

export default StoreSelection;