import React, { useEffect, useState } from "react";
import { TextArea, Modal } from "impact-ui";
import {
	toastError
} from "../../../store/features/global/global";
import { useDispatch } from "react-redux";

export const PromoComment = (props) => {
	const dispatch = useDispatch();
	const [comment, setComment] = useState("");

	const closeModal = () => {
		setComment("");
		props.closeModal();
	};

	const offerCommentHandler = (e) => {
		if (e.target.value?.length <= 140) {
			setComment(e.target.value);
		} else {
			dispatch(toastError("Maximum of 140 characters allowed"));
		}
	};

	useEffect(() => {
		if (props.offerComment) {
			setComment(props.offerComment);
		}
	}, [props.offerComment]);

	return (
		<Modal
			onClose={() => { closeModal(); }}
			onPrimaryButtonClick={() => { props.handleApproveScenario(comment) }}
			onSecondaryButtonClick={() => { closeModal() }}
			primaryButtonLabel="Approve"
			secondaryButtonLabel="Abort"
			size="small"
			title="Approve?"
			open={props.showModal}
		>
			<div className="comment-modal-content">
				<p className="text-16-500 marginBottom-12">
					Would you like to enter any comment?
				</p>
				<TextArea
					onChange={offerCommentHandler}
					placeholder="Comment..."
					value={comment}
					width={"450px"}
					height={"80px"}
				/>
			</div>
		</Modal>
	);
};
