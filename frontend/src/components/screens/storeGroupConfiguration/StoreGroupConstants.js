import { dateFormatter } from "../../../utils/helpers/formatter";
import { capitalizeFirstLetter, replaceSpecialCharacter } from "../../../utils/helpers/utility_helpers";
import { CellsWithLinkandIcon } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { storeGroupLinkClick } from "../../../store/features/storeConfigurationReducer/storeConfigurationReducer";

import { containerStore } from "../../../store";
import { global_labels } from "../../../constants/Constants";

export const storeTableConfiguation = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "store_group_name",
        headerName: "Group Name",
        pinned: "left",
        isSearchable: true,
        filter: "agTextColumnFilter",
        cellRenderer: CellsWithLinkandIcon,
        cellRendererParams: {
            onClick: (data) => containerStore.dispatch(storeGroupLinkClick(data)),
            icons: {
                processing: {
                    key: "group_under_process",
                    trueValue: 1,
                }
            }
        },
        width: 250,
    },
    {
        field: "store_group_description",
        headerName: "Description",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "stores_count",
        headerName: "Stores",
        isSearchable: true,
        filter: "agTextColumnFilter",
        minWidth: 150,
        type: "number",
    },
    {
        field: "s1_name",
        headerName: "Channel",
        isSearchable: true,
        filter: "agTextColumnFilter",
        maxWidth: 152,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s0_name",
        headerName: "Country",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 152,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s2_name",
        headerName: "Store Group",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_by_user",
        headerName: "Created By",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 172,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_at",
        headerName: "Created On",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 172,
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "modified_by_user",
        headerName: "Modified By",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "modified_at",
        headerName: "Modified On",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "promos_count",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 150,
        type: "number",
    },
]

export const storeTableConfig = (hierarchyGlobalKeys) => ([
	{
		field: "store_id",
		headerName: "Store ID",
        valueFormatter: (params) => parseInt(params.value),
	},
	{
		field: "store_name",
		headerName: "Store Name",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s0_name",
		headerName: hierarchyGlobalKeys?.s0_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s1_name",
		headerName: hierarchyGlobalKeys?.s1_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s2_name",
		headerName: hierarchyGlobalKeys?.s2_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s3_name",
		headerName: hierarchyGlobalKeys?.s3_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s4_name",
		headerName: hierarchyGlobalKeys?.s4_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
])