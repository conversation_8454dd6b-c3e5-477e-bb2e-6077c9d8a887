import React, { useState, useCallback, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";
import _ from "lodash";
import { Button, ProgressBar, Input, Prompt, Modal } from "impact-ui";
import { Table } from "impact-ui-v3";
import ScreenBreadcrumb from "../../common/breadCrumb/ScreenBreadcrumb";
import FilterWrapper from "../../common/filters/FilterWrapper";
import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";

import { breadcrumbRoutes } from "../../../constants/RouteConstants";
import {
    storeGroupConfigurationFilterConfig as filterConfig,
    storeGroupConfigurationRequiredFiltersOnLoad as requiredFiltersOnLoad,
} from "../../../constants/FilterConfigConstants";

import {
    storeTableConfig,
    storeTableConfiguation,
} from "./StoreGroupConstants";
import {
    callStoreConfigTableAPI,
    callStoreConfigUngroupedDetailsAPI,
    setSelectedStoreGroupConfigData,
    getStoreGroupProcessStatus,
    storeGroupDownload,
    deleteStoreGroup,
    getStoreDetailsForStoreGroup,
} from "../../../store/features/storeConfigurationReducer/storeConfigurationReducer";

import EmptyData from "../../ui/emptyData/EmptyData";
import {
    requestComplete,
    requestStart,
    toastError,
} from "../../../store/features/global/global";
import { promoBasicTableConfiguation } from "../productGroupConfiguration/ProductGroupConstants";
import { CellOnClickButton } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { API } from "../../../utils/axios";
import {
    capitalizeFirstLetter,
    fabricatePayloadHierarchy,
    replaceSpecialCharacter,
    replaceSpecialCharToCharCode,
} from "../../../utils/helpers/utility_helpers";
import { global_labels } from "../../../constants/Constants";

function StoreGroupConfiguration() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const tableRef = useRef(null);
    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const {
        storeConfigurationTableData,
        ungroupedStoreDetails,
        selectedStoreGroupConfigData = [],
        editStoreGroupFlag = false,
    } = useSelector(
        (store) => store?.pricesmartPromoReducer.storeConfiguration
    );

    const { hierachy_keys } = useSelector(
        (store) => store?.pricesmartPromoReducer.global
    );

    const [showFiltersSection, setShowFiltersSection] = useState(true);
    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [showDeletePrompt, setShowDeletePrompt] = useState(false);

    const [showModal, setShowModal] = useState(false);
    const [dialogTableData, setDialogTableData] = useState([]);
    const [modifiedColumn, setModifiedColumn] = useState([]);
    const [storeDetailModalOpen, setStoreDetailModalOpen] = useState(false);
    const [storeDetailTableData, setStoreDetailTableData] = useState([]);
    const storeDetailTableRef = useRef(null);

    useEffect(() => {
        if (storeTableConfiguation.length) {
            const coldef = _.cloneDeep(storeTableConfiguation);
            const col = coldef.find((ele) => ele.field == "promos_count");
            if (col) {
                col["cellRenderer"] = (props) => (
                    <CellOnClickButton {...props} clickHandler={clickHandler} />
                );
            }
            const col_stores = coldef.find(
                (ele) => ele.field == "stores_count"
            );
            if (col_stores) {
                col_stores["cellRenderer"] = (props) => (
                    <CellOnClickButton
                        {...props}
                        clickHandler={getStoreDetails}
                    />
                );
            }
            setModifiedColumn(coldef);
        }
    }, [storeTableConfiguation]);

    useEffect(async () => {
        if (editStoreGroupFlag) {
            await onEditStoreGroupClick();
        }
    }, [editStoreGroupFlag]);

    const getStoreDetails = async (data) => {
        const storeDetails = await dispatch(
            getStoreDetailsForStoreGroup({
                store_group_id: data.store_group_id,
            })
        );
        setStoreDetailTableData(storeDetails);
        setStoreDetailModalOpen(true);
    };

    const onFilterApply = () => {
        //extract filters data for store configuration from store
        const storeConfigFilters = _.cloneDeep(filtersData.STORE_CONFIGURATION);
        //create payload for table data API
        let payload = fabricatePayloadHierarchy(storeConfigFilters);

        dispatch(callStoreConfigTableAPI(payload));
        //add only_meta_data flag for ungrouped store details payload to get only meta data
        payload.only_meta_data = true;
        dispatch(callStoreConfigUngroupedDetailsAPI(payload));
    };

    const onCreateNewGroupClick = () => {
        //navigate to create store group screen
        navigate("/pricesmart-promo/store-group-config/create-store-group");
    };

    const onEditStoreGroupClick = async () => {
        //fetch selected store group id and call API to get process status
        const selectedStoreGroup = selectedStoreGroupConfigData[0];
        const payload = {
            store_group_id: selectedStoreGroup.store_group_id,
        };
        const response = await dispatch(getStoreGroupProcessStatus(payload));
        // if process not ongoing then navigate to edit store group screen
        if (response)
            navigate("/pricesmart-promo/store-group-config/create-store-group");
    };

    const onRowSelection = useCallback(() => {
        //get selected store groups and set in store
        const selectedRows = tableRef.current.api.getSelectedRows();
        dispatch(setSelectedStoreGroupConfigData(_.cloneDeep(selectedRows)));
    });

    const onDownloadStoreGroupClick = () => {
        //download store group details
        //extract filters data for store configuration from store
        const storeConfigFilters = _.cloneDeep(filtersData.STORE_CONFIGURATION);

        //create payload for table data API
        let payload = {
            ...fabricatePayloadHierarchy(storeConfigFilters),
        };


        dispatch(storeGroupDownload(payload));
    };

    const onFilterTextBoxChanged = useCallback((text) => {
        const encodedText = replaceSpecialCharToCharCode(text);
        setGlobalSearchText(encodedText);
        tableRef.current.api.setGridOption("quickFilterText", encodedText);
    }, []);

    const handleDelete = async () => {
        setShowDeletePrompt(false);

        //build payload for delete, and cell the api, once delete is done, refresh screen data
        const payload = {
            store_group_id: selectedStoreGroupConfigData.map(
                (ele) => ele.store_group_id
            ),
        };
        const isDeleted = await dispatch(deleteStoreGroup(payload));

        if (isDeleted) {
            onFilterApply();
            dispatch(setSelectedStoreGroupConfigData([]));
        }
    };

    const clickHandler = async (data) => {
        if (data?.["store_group_id"]) {
            dispatch(requestStart());
            await API.get(`store-groups/${data?.["store_group_id"]}/promos`)
                .then((res) => {
                    dispatch(requestComplete());
                    setShowModal(true);
                    const { data } = res;
                    setDialogTableData(data.data);
                })
                .catch((err) => {
                    dispatch(requestComplete());
                    dispatch(toastError("Somthing went wrong"));
                });
        }
    };

    React.useEffect(() => {
        dispatch(setSelectedStoreGroupConfigData([]));
    }, []);

    const onDownloadStoreButtonClick = () => {
        // download stores data as excel
        const params = {
            fileName: "stores.xlsx",
        };
        storeDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

    return (
        <div className="screen_container">
            <ScreenBreadcrumb
                breadcrumbList={breadcrumbRoutes()?.["storeConfiguration"]}
            >
                <Button
                    iconPlacement="left"
                    onClick={() => {
                        setShowFiltersSection((prev) => !prev);
                    }}
                    size="large"
                    variant="secondary"
                >
                    {showFiltersSection ? "Hide" : "Show"} Filters
                </Button>
            </ScreenBreadcrumb>
            <FilterWrapper
                defaultOpen="store_hierarchy"
                screen="STORE_CONFIGURATION"
                callAPIonLoad={true}
                filterConfig={filterConfig}
                requiredFiltersOnLoad={requiredFiltersOnLoad}
                onFilterApply={onFilterApply}
                showFiltersSection={showFiltersSection}
            />
            <div className="screen_data_container flex1 paddingTop-20">
                {storeConfigurationTableData.length ? (
                    <div>
                        <Table
                            ref={tableRef}
                            tableHeader={"Store Groups"}
                            suppressMenuHide
                            rowData={storeConfigurationTableData}
                            columnDefs={modifiedColumn}
                            rowSelection="multiple"
                            onSelectionChanged={onRowSelection}
                            suppressRowClickSelection={true}
                            topRightOptions={
                                <div className="centerFlexWithGap12">
                                    {!selectedStoreGroupConfigData?.length && (
                                        <>
                                            <ProgressBar
                                                customLabel={`${ungroupedStoreDetails?.ungrouped_count}(${ungroupedStoreDetails?.ungrouped_percentage}%) stores are not in any group`}
                                                value={
                                                    ungroupedStoreDetails?.ungrouped_percentage ||
                                                    0
                                                }
                                                showTime={false}
                                            />
                                            <div className="horizontal-line" />
                                            <Button
                                                iconPlacement="left"
                                                icon={
                                                    <img src={DownloadIcon} />
                                                }
                                                onClick={
                                                    onDownloadStoreGroupClick
                                                }
                                                size="large"
                                                variant="tertiary"
                                            />
                                        </>
                                    )}
                                    {selectedStoreGroupConfigData.length ===
                                        1 ? (
                                        <Button
                                            iconPlacement="left"
                                            icon={<img src={EditIcon} />}
                                            onClick={onEditStoreGroupClick}
                                            size="large"
                                            variant="tertiary"
                                        />
                                    ) : null}
                                    {selectedStoreGroupConfigData.length ? (
                                        <Button
                                            iconPlacement="left"
                                            icon={<img src={DeleteIcon} />}
                                            onClick={() =>
                                                setShowDeletePrompt(true)
                                            }
                                            size="large"
                                            variant="secondary"
                                            type="destructive"
                                            className="delete-button"
                                        />
                                    ) : null}
                                    {!selectedStoreGroupConfigData.length ? (
                                        <div className="centerFlexWithGap12">
                                            <div className="positionRelative">
                                                {showGlobalSearch ? (
                                                    <div className="tableGlobalSearchContainer">
                                                        <Input
                                                            onChange={(e) =>
                                                                onFilterTextBoxChanged(
                                                                    e.target
                                                                        .value
                                                                )
                                                            }
                                                            placeholder="Search"
                                                            rightIcon={
                                                                <img
                                                                    src={
                                                                        SearchIcon
                                                                    }
                                                                />
                                                            }
                                                            type="text"
                                                            value={replaceSpecialCharacter(
                                                                globalSearchText
                                                            )}
                                                        />
                                                    </div>
                                                ) : null}
                                                <Button
                                                    iconPlacement="left"
                                                    icon={
                                                        <img src={SearchIcon} />
                                                    }
                                                    onClick={() =>
                                                        setShowGlobalSearch(
                                                            (prev) => !prev
                                                        )
                                                    }
                                                    size="large"
                                                    variant="tertiary"
                                                />
                                            </div>
                                            <div className="horizontal-line" />
                                            <Button
                                                onClick={onCreateNewGroupClick}
                                                size="large"
                                                variant="primary"
                                            >
                                                Create New Group
                                            </Button>
                                        </div>
                                    ) : null}

                                    {/* <div className="horizontal-line" /> */}
                                </div>
                            }
                        />
                    </div>
                ) : (
                    <div>
                        <div className="display-flex-end marginBottom-20">
                            <Button
                                onClick={onCreateNewGroupClick}
                                size="large"
                                variant="primary"
                            >
                                Create New Group
                            </Button>
                        </div>
                        <EmptyData text="Please Select Filters to view Store Groups" />
                    </div>
                )}
            </div>
            <Prompt
                handleClose={() => setShowDeletePrompt(false)}
                onPrimaryButtonClick={handleDelete}
                onSecondaryButtonClick={() => setShowDeletePrompt(false)}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title="Confirm deletion of store groups"
                variant="warning"
                isOpen={showDeletePrompt}
            >
                Are you sure you want to delete these store groups?
            </Prompt>

            <Modal
                onClose={() => {
                    setStoreDetailModalOpen(false);
                }}
                onPrimaryButtonClick={() => {
                    setStoreDetailModalOpen(false);
                }}
                primaryButtonLabel="Ok"
                title="Store Details"
                open={storeDetailModalOpen}
                className="product-store-detail-modal"
            >
                <Table
                    ref={storeDetailTableRef}
                    suppressMenuHide
                    rowData={storeDetailTableData}
                    columnDefs={storeTableConfig(hierarchyGlobalKeys)}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <Button
                            iconPlacement="left"
                            icon={<img src={DownloadIcon} />}
                            onClick={onDownloadStoreButtonClick}
                            size="large"
                            variant="tertiary"
                        />
                    }
                />
            </Modal>
            <Modal
                onClose={() => {
                    setShowModal(false);
                }}
                title={`${capitalizeFirstLetter(global_labels?.promo_alias_plural)} that are part of this group`}
                open={showModal}
            >
                <Table
                    rowData={dialogTableData}
                    columnDefs={promoBasicTableConfiguation}
                />
            </Modal>
        </div>
    );
}

export default StoreGroupConfiguration;
