import React, { useState, useEffect, useRef, useCallback } from "react";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import {
	ButtonGroup,
	FileUpload,
	TextArea,
	Prompt,
	Modal,
	Button,
	Input,
	Badge,
} from "impact-ui";
import { Table } from "impact-ui-v3";
import ComponentFilters from "../../../ui/componentFilters/ComponentFilters";
import EmptyData from "../../../ui/emptyData/EmptyData";
import {
	resetAllFiltersData,
	overwriteFilters,
} from "../../../../store/features/filters/filters";
import {
	filterTypeButton,
	createStoreFilterConfig,
	createStoreTableConfig,
	invalidDataColumnConfig,
} from "./CreateStoreGroupConstants";
import {
	callCreateStoreConfigFilterTableAPI,
	setSelectedCreateStoreConfigTableData,
	getCreateStoreGrpDataFromExcel,
	getCreateStoreGrpDataFromCopyPaste,
} from "../../../../store/features/storeConfigurationReducer/storeConfigurationReducer";

import {
	mergeFiltersData,
	downloadCSV,
	fabricatePayloadHierarchy,
} from "../../../../utils/helpers/utility_helpers";
import DownloadIcon from "../../../../assets/imageAssets/downloadIcon.svg?.url";
import SearchIcon from "../../../../assets/imageAssets/searchIcon.svg?.url";
import "./CreateStoreConfiguration.scss";

import {
	excelTemplateDownload,
	requestStart,
	requestComplete,
	toastError,
} from "../../../../store/features/global/global";
import { global_labels } from "../../../../constants/Constants";

function CreateStoreConfigSelection(props) {
	const dispatch = useDispatch();
	const tableRef = useRef();

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);
	const {
		createStoreConfigTableData = [],
		selectedCreateStoreConfigTableData = [],
		tableDataFromUploadorCopyPaste = {},
	} = useSelector(
		(store) => store?.pricesmartPromoReducer.storeConfiguration
	);

	const { hierachy_keys } = useSelector(
		(store) => store?.pricesmartPromoReducer.global
	);

	const [selectedFilterType, setSelectedFilterType] = useState(
		"selectFilters"
	);

	const [showGlobalSearch, setShowGlobalSearch] = useState(false);
	const [globalSearchText, setGlobalSearchText] = useState("");
	const [storeTableData, setStoreTableData] = useState([]);
	const [uploadedExcel, setUploadedExcel] = useState([]);
	const [lastFiltersData, setLastFiltersData] = useState({});
	const [showDataOverridePrompt, setTableDataOverridePrompt] = useState(
		false
	);
	const [showInvalidDataModal, setShowInvalidDataModal] = useState(false);
	const [invalidTableData, setInvalidTableData] = useState([]);
	const [copyPasteData, setCopyPasteData] = useState("");
	const [tempTableData, setTempTableData] = useState([]);
	const [flagToStopRepeatedSetData, setFlagToStopRepeatedSetData] = useState(false);

	useEffect(() => {
		// if store data is fetched from API by selecting the filters, set it in table
		// if (
		// 	createStoreConfigTableData &&
		// 	selectedFilterType === "selectFilters"
		// ) {
		// 	// if there is no previous data in table, set the fetched data
		// 	if (!storeTableData.length) {
		// 		setStoreTableData(createStoreConfigTableData);
		// 	} else {
		// 		// if there is previous data in table, show prompt to retain or replace
		// 		setTableDataOverridePrompt(true);
		// 	}
		// }

		if (!_.isEmpty(createStoreConfigTableData)) {
			// if there is no previous data in table, set the fetched data
			handleSetStoreTableData(createStoreConfigTableData);
		}
	}, [createStoreConfigTableData]);

	useEffect(() => {
		// if store data is fetched from API by uploading excel or copy paste, set it in table
		if (
			(selectedFilterType === "uploadExcel" ||
				selectedFilterType === "copyPaste") &&
			!_.isEmpty(tableDataFromUploadorCopyPaste)
		) {
			// if there are invalid or inactive stores, show modal with invalid and inactive stores
			if (
				tableDataFromUploadorCopyPaste?.invalid.length ||
				tableDataFromUploadorCopyPaste?.inactive.length
			) {
				const newTableData = [
					...tableDataFromUploadorCopyPaste?.invalid,
					...tableDataFromUploadorCopyPaste?.inactive,
				];
				setInvalidTableData(_.cloneDeep(newTableData));
				setShowInvalidDataModal(true);
			} else {
				// if there are only valid stores, set it in table
				handleSetStoreTableData(tableDataFromUploadorCopyPaste.valid);
			}
		}
	}, [tableDataFromUploadorCopyPaste]);

	const handleSetStoreTableData = (data) => {
		if (flagToStopRepeatedSetData) {
			return;
		}
		if (storeTableData?.length) {
			setTableDataOverridePrompt(true);
			setTempTableData(data);
		} else if (
			(selectedFilterType === "upload" || selectedFilterType === "copy_paste")
		) {
			setStoreTableData(data);
		} else
			setStoreTableData(data);
		setFlagToStopRepeatedSetData(true);
	};

	const onComponentFiltersApply = () => {
		let callAPI = true;
		// check if all mandatory filters are selected
		const filtersDataSelected = _.cloneDeep(
			filtersData["CREATE_STORE_CONFIG_COMPONENT"]
		);
		_.forEach(createStoreFilterConfig, (config) => {
			const key = config.filterId;
			if (
				config.isMandatory &&
				!filtersDataSelected?.[key]?.selectedOptionsArray.length
			) {
				callAPI = false;
			}
		});
		if (!callAPI) {
			dispatch(toastError("Please select all mandatory filters"));
			return;
		}
		// set last filters data for replace and retain prompt
		if (_.isEmpty(lastFiltersData))
			setLastFiltersData(_.cloneDeep(filtersDataSelected));
		// if all mandatory filters are selected, call API to get store data
		const payload = fabricatePayloadHierarchy(filtersDataSelected);
		setFlagToStopRepeatedSetData(false);
		dispatch(callCreateStoreConfigFilterTableAPI(payload));
	};

	const onClearFilter = () => {
		// clear filters data for store configuration
		dispatch(
			resetAllFiltersData({
				from: "CREATE_STORE_CONFIG_COMPONENT",
			})
		);
	};

	const onRowSelection = useCallback(() => {
		// set selected stores data
		const selectedRows = tableRef.current.api.getSelectedRows();
		dispatch(
			setSelectedCreateStoreConfigTableData(_.cloneDeep(selectedRows))
		);
	});

	const rowDataChanged = () => {
		// whenever row data updates in AG Grid, select all rows
		if (tableRef?.current?.api) tableRef.current.api.selectAll();
	};

	const onReplaceAndOverwriteClick = () => {
		// replace table data with current selection and set last filters data same as current selection
		setStoreTableData(tempTableData);
		setTableDataOverridePrompt(false);
		// adding this as the dispatch will trigger the useEffect then the handleSetStoreTableData will be called
		// and it will again set the same data in table, so we need to stop that
		setFlagToStopRepeatedSetData(true);
		setTempTableData([]);
		setLastFiltersData(filtersData["CREATE_STORE_CONFIG_COMPONENT"]);
	};

	const onRetainClick = () => {
		//concat current data with previous data, and set it in table
		let currentRows = _.cloneDeep(storeTableData);
		const tempRows = _.cloneDeep(tempTableData || []).map(item => ({
			...item,
			// convert store_id to number as on upload it is string, and _.uniqBy is not working with string
			store_id: Number(item.store_id)
		}));
		currentRows = [...currentRows, ...tempRows];
		currentRows = _.uniqBy(currentRows, "store_id");
		setStoreTableData(_.cloneDeep(currentRows));
		setTableDataOverridePrompt(false);
		setTempTableData([]);
		// concat currennt data with previous data and set both last filters and current filters as same
		setFlagToStopRepeatedSetData(true);
		const updatedFiltersData = mergeFiltersData(
			_.cloneDeep(lastFiltersData),
			_.cloneDeep(filtersData["CREATE_STORE_CONFIG_COMPONENT"])
		);
		setLastFiltersData(_.cloneDeep(updatedFiltersData));
		dispatch(
			overwriteFilters({
				filtersData: _.cloneDeep(updatedFiltersData),
				activeScreen: "CREATE_STORE_CONFIG_COMPONENT",
			})
		);
	};

	const onFilterTypeTabChange = (e) => {
		// set selected filter type and reset all filters data if filter type is not select filters
		setSelectedFilterType(e.target.value);
		if (e.target.value !== "selectFilters") {
			dispatch(
				resetAllFiltersData({
					from: "CREATE_STORE_CONFIG_COMPONENT",
				})
			);
		}
		setUploadedExcel([]);
		setCopyPasteData("");
	};

	const onUploadExcelNextClick = async () => {
		// on next click of excel upload, call API to get store data
		const formData = new FormData();
		formData.append("file", uploadedExcel[0]?.file);
		setFlagToStopRepeatedSetData(false);
		const responseFlag = await dispatch(
			getCreateStoreGrpDataFromExcel(formData)
		);
		// if store data is not fetched, reset uploaded excel data
		if (!responseFlag) {
			setUploadedExcel([]);
		}
	};

	const onActiveInactiveStoreProceed = () => {
		// on proceed click of invalid stores modal, set valid and inactive stores in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		const newTableData = [
			...tableDataFromUploadorCopyPaste?.valid,
			...tableDataFromUploadorCopyPaste?.inactive,
		];
		handleSetStoreTableData(newTableData);
	};

	const onActiveStoreProceed = () => {
		// on proceed click of invalid stores modal, set only valid stores in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		handleSetStoreTableData(tableDataFromUploadorCopyPaste.valid || []);
	};

	const onCopyPasteChange = (e) => {
		// set copy paste data in state
		const data = e.target.value;
		let array = _.filter(data.split("\n"), (item) => item !== "");
		let val = array.join(",");
		setCopyPasteData(val);
	};

	const onCopyPasteSubmitClick = () => {
		// on submit click of copy paste, call API to get store data
		let storeIds = copyPasteData.split(",");
		storeIds = _.map(storeIds, (value) => value.trim());
		storeIds = _.filter(storeIds, (item) => item !== "").map(item => [...item.split(" ")]);
		const payload = { stores_data: storeIds };
		setFlagToStopRepeatedSetData(false);
		setCopyPasteData("");
		dispatch(getCreateStoreGrpDataFromCopyPaste(payload));
	};

	const excelDownloadHandler = () => {
		// download excel template for store selection
		const payload = {
			type: "store_group_upload",
		};
		dispatch(
			excelTemplateDownload(
				{
					params: payload,
					responseType: "blob",
				},
				"store_selection_template"
			)
		);
	};

	const csvDownloadHandler = () => {
		// download csv template for store selection
		dispatch(requestStart());
		downloadCSV(
			[
				{
					["Store ID"]: "123",
				},
			],
			"store_selection_template",
			","
		);
		dispatch(requestComplete("Template downloaded successfully"));
	};

	const onDownloadButtonClick = () => {
		const params = {
			fileName: "stores.xlsx",
		};
		tableRef.current.api.exportDataAsExcel(params);
	};

	const onFilterTextBoxChanged = useCallback((text) => {
		setGlobalSearchText(text);
		tableRef.current.api.setGridOption("quickFilterText", text);
	}, []);

	const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

	return (
		<div className="content_container marginTop-20">
			<span className="text-16-800">Store Selection</span>
			<div className="storeConfigSelectionContainer marginTop-24">
				<ButtonGroup
					selectedOption={selectedFilterType}
					onChange={onFilterTypeTabChange}
					options={filterTypeButton}
				/>
				<p className="text-12-500 marginTop-12">
					Please select one of the methods for uploading data to get
					results
				</p>
				<div className="marginTop-24">
					{selectedFilterType === "selectFilters" && (
						<div className="flexColumn flex24">
							<span className="text-14-800">Select Filter</span>
							<ComponentFilters
								filterConfig={createStoreFilterConfig}
								callAPIonLoad={false}
								screen="CREATE_STORE_CONFIG_COMPONENT"
								onPrimaryButtonClick={onComponentFiltersApply}
								onSecondaryButtonClick={onClearFilter}
								primaryButtonText="Submit"
								secondaryButtonText="Clear Filters"
							/>
						</div>
					)}
					{selectedFilterType === "uploadExcel" && (
						<FileUpload
							fileList={uploadedExcel}
							numberOfFiles={1}
							primaryButtonLabel="Submit"
							secondaryButtonLabel="Cancel"
							onSecondaryButtonClick={() => setUploadedExcel([])}
							onFileListChange={setUploadedExcel}
							onPrimaryButtonClick={onUploadExcelNextClick}
							validFileTypes={[
								{
									fileType: "xlsx",
									templateDownloader: excelDownloadHandler,
									typeOverride: false,
								},
								{
									fileType: "csv",
									templateDownloader: csvDownloadHandler,
									typeOverride: false,
								},
							]}
						/>
					)}
					{selectedFilterType === "copyPaste" && (
						<div>
							<TextArea
								onChange={onCopyPasteChange}
								placeholder="Paste IDs here..."
								value={copyPasteData}
								width={"80vw"}
							/>
							<div className="buttons_container">
								<Button
									onClick={() => setCopyPasteData("")}
									size="large"
									variant="url"
								>
									Reset
								</Button>
								<Button
									onClick={onCopyPasteSubmitClick}
									size="large"
									variant="primary"
									disabled={!copyPasteData.length}
								>
									Submit
								</Button>
							</div>
						</div>
					)}
				</div>
				<div className="marginTop-24">
					{storeTableData?.length ? (
						<div>
							<Table
								tableHeader={"Stores"}
								ref={tableRef}
								suppressMenuHide
								rowData={storeTableData}
								columnDefs={createStoreTableConfig(hierarchyGlobalKeys)}
								rowSelection="multiple"
								onSelectionChanged={onRowSelection}
								onRowDataUpdated={rowDataChanged}
								topRightOptions={
									<div className="centerFlexWithGap12">
										<div className="positionRelative">
											{showGlobalSearch ? (
												<div className="tableGlobalSearchContainer">
													<Input
														onChange={(e) =>
															onFilterTextBoxChanged(
																e.target.value
															)
														}
														placeholder="Search"
														rightIcon={
															<img
																src={SearchIcon}
															/>
														}
														type="text"
														value={globalSearchText}
													/>
												</div>
											) : null}
											<Button
												iconPlacement="left"
												icon={<img src={SearchIcon} />}
												onClick={() =>
													setShowGlobalSearch(
														(prev) => !prev
													)
												}
												size="large"
												variant="tertiary"
											/>
										</div>
										<Button
											iconPlacement="left"
											icon={<img src={DownloadIcon} />}
											onClick={onDownloadButtonClick}
											size="large"
											variant="tertiary"
										/>
									</div>
								}
								topLeftOptions={
									<Badge
										color="default"
										label={`Selected Stores: ${selectedCreateStoreConfigTableData?.length} / ${storeTableData?.length}`}
										size="default"
										variant="subtle"
									/>
								}
							/>
						</div>
					) : (
						<EmptyData text="Please Select Filters to view Stores" />
					)}
				</div>
			</div>
			<Prompt
				handleClose={() => {
					setTableDataOverridePrompt(false);
				}}
				onPrimaryButtonClick={onRetainClick}
				onSecondaryButtonClick={onReplaceAndOverwriteClick}
				primaryButtonLabel="Retain"
				secondaryButtonLabel="Replace & overwrite"
				title="Confirm Selection"
				variant="warning"
				isOpen={showDataOverridePrompt}
			>
				Do you want to Retain and Continue or Replace table with current
				selection
			</Prompt>

			<Modal
				onClose={() => { setShowInvalidDataModal(false) }}
				onPrimaryButtonClick={onActiveInactiveStoreProceed}
				onSecondaryButtonClick={onActiveStoreProceed}
				primaryButtonLabel="Proceed with all Active and Inactive Stores"
				secondaryButtonLabel="Proceed with only Active Stores"
				size="medium"
				title="Stores Detail"
				open={showInvalidDataModal}
			>
				<div className="invalidStoresGrpModalContainer">
					<span className="secondaryText-14-500">
						Based on the data feed Pricesmart received, the uploaded
						list contains some inactive/invalid Stores
					</span>
					<Table
						tableHeader={"Stores"}
						suppressMenuHide
						rowData={invalidTableData}
						columnDefs={invalidDataColumnConfig}
					/>
				</div>
			</Modal>
		</div>
	);
}

export default CreateStoreConfigSelection;
