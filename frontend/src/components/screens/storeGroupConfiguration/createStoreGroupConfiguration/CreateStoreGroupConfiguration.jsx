import React, { useState, useEffect, useRef, useCallback } from "react";
import { Button, Input, Modal } from "impact-ui";
import { Table } from "impact-ui-v3";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";

import ScreenBreadcrumb from "../../../common/breadCrumb/ScreenBreadcrumb";
import CreateStoreConfigSelection from "./CreateStoreConfigSelection";

import { breadcrumbRoutes } from "../../../../constants/RouteConstants";
import {
    capitalizeFirstLetter,
    replaceSpecialCharacter,
    replaceSpecialCharToCharCode,
} from "../../../../utils/helpers/utility_helpers";
import {
    createStoreGroupConfig,
    resetCreateStoreConfigScreenData,
    resetEditStoreGroupFlag,
    getStoreGroupData,
    getEffectedGroupsData,
} from "../../../../store/features/storeConfigurationReducer/storeConfigurationReducer";
import { resetAllFiltersData } from "../../../../store/features/filters/filters";
import { effectedPlansColumnConfig } from "./CreateStoreGroupConstants";
import "./CreateStoreConfiguration.scss";
import { global_labels } from "../../../../constants/Constants";

function CreateStoreGroupConfiguration(props) {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const tableRef = useRef(null);
    const [storeGroupName, setStoreGroupName] = useState("");
    const [description, setDescription] = useState("");
    const [effectedPlansModal, setEffectedPlansModal] = useState(false);
    const [effectedPlansData, setEffectedPlansData] = useState([]);
    const [selectedEffectedPlans, setSelectedEffectedPlans] = useState([]);

    const {
        selectedCreateStoreConfigTableData = [],
        selectedStoreGroupConfigData = [],
    } = useSelector(
        (store) => store?.pricesmartPromoReducer.storeConfiguration
    );

    useEffect(() => {
        // reset filters and screen data on component unmount
        dispatch(resetEditStoreGroupFlag());
        return () => {
            dispatch(resetCreateStoreConfigScreenData());
            dispatch(
                resetAllFiltersData({
                    from: "CREATE_STORE_CONFIG_COMPONENT",
                })
            );
        };
    }, []);

    useEffect(async () => {
        // if user is editing existing store group, fetch store group data
        if (selectedStoreGroupConfigData.length === 1) {
            const response = await dispatch(
                getStoreGroupData({
                    store_group_id:
                        selectedStoreGroupConfigData[0].store_group_id,
                })
            );
            // set store group name and description if data is fetched
            if (response) {
                setStoreGroupName(response?.groupName || "");
                setDescription(response?.description || "");
            }
        }
    }, [selectedStoreGroupConfigData]);

    const onCreateNewGroupClick = async () => {
        // if user is creating new store group, call new group API
        if (!selectedStoreGroupConfigData?.length) {
            callNewGroupAPI();
            return;
        }

        // else fetch effected plans on editing the store group data
        let effectedGroupPayload = {
            store_group_id: selectedStoreGroupConfigData[0]?.store_group_id,
        };
        const response = await dispatch(
            getEffectedGroupsData(effectedGroupPayload)
        );
        // if effects plans exists, show modal to show effected plans
        if (response?.length) {
            setEffectedPlansData(_.cloneDeep(response));
            setEffectedPlansModal(true);
            return;
        }
        // else save edited store group data
        callNewGroupAPI();
    };

    const callNewGroupAPI = async () => {
        // create payload for new store group API
        const payload = {
            application: "promo",
            store_group_name: replaceSpecialCharToCharCode(storeGroupName),
            store_group_description: replaceSpecialCharToCharCode(description),
        };
        // get selected store ids
        let method = "POST";
        payload["store_id"] = selectedCreateStoreConfigTableData?.map(
            (data) => data.store_id
        );
        // if user is editing existing store group, add store group id and guid in payload
        if (selectedStoreGroupConfigData?.length) {
            payload["store_group_id"] =
                selectedStoreGroupConfigData[0].store_group_id;
            payload["guid"] = sessionStorage.getItem("UNIQ_SSE_KEY");
            payload["selected_plans"] = _.cloneDeep(selectedEffectedPlans);
            method = "PUT";
        }
        // call new store group API
        await dispatch(createStoreGroupConfig(payload, method));
        navigate("/pricesmart-promo/store-group-config");
    };

    const onBackButtonClick = () => {
        // navigate to store group configuration screen
        navigate("/pricesmart-promo/store-group-config");
    };

    const onRowSelection = useCallback(() => {
        // get selected effected plans
        const selectedRows = tableRef.current.api.getSelectedRows();
        setSelectedEffectedPlans(_.cloneDeep(selectedRows));
    });

    const onEffectedPlansContinueClick = () => {
        // if user decides to continue with effected plans, save store group data
        setEffectedPlansModal(false);
        callNewGroupAPI();
    };

    const onGridReady = useCallback(() => {
        // select all effected plans on grid ready for modal table
        tableRef.current.api.selectAll();
    });

    const onEffectedPlansCancelClick = () => {
        // if user decides to cancel effected plans, close modal and reset effected plans data
        setEffectedPlansModal(false);
        setEffectedPlansData([]);
        setSelectedEffectedPlans([]);
    };

	return (
		<div>
			<ScreenBreadcrumb
				breadcrumbList={breadcrumbRoutes()?.["createStoreConfiguration"]}
			></ScreenBreadcrumb>
			<div className="screen_data_container paddingTop-12">
				<div className="storeConfigContainer">
					<div className="content_container_border">
						<label className="text-16-800">Details</label>
						<div className="paddingTop-16 flex24">
							<Input
								id="store_detail_name"
								inputProps={{}}
								label="Store Group Name"
								name=""
								onChange={(e) =>
									setStoreGroupName(e.target.value)
								}
								placeholder="Please enter name"
								type="text"
								isRequired={true}
								value={replaceSpecialCharacter(storeGroupName)}
							/>
							<Input
								id="store_detail_description"
								inputProps={{}}
								label="Description"
								name=""
								onChange={(e) => setDescription(e.target.value)}
								placeholder="Please enter description"
								type="text"
								isRequired={true}
								value={replaceSpecialCharacter(description)}
							/>
						</div>
					</div>
					<CreateStoreConfigSelection />
				</div>
			</div>
			<Modal
				onClose={() => {onEffectedPlansCancelClick()}}
				onPrimaryButtonClick={onEffectedPlansContinueClick}
				onSecondaryButtonClick={onEffectedPlansCancelClick}
				primaryButtonLabel="Continue"
				secondaryButtonLabel="Cancel"
				size="medium"
				title="Are you sure?"
				open={effectedPlansModal}
			>
				<div className="invalidStoresGrpModalContainer">
					<span className="secondaryText-14-500">
						The following Strategies/{capitalizeFirstLetter(global_labels?.promo_alias_plural)} will be affected by this
						change. Please unselect the Strategies/{capitalizeFirstLetter(global_labels?.promo_alias_plural)} you want
						to exclude from this change.
					</span>
					<Table
						tableHeader={"Effected Plans"}
						ref={tableRef}
						rowSelection="multiple"
						onSelectionChanged={onRowSelection}
						suppressMenuHide
						rowData={effectedPlansData}
						onGridReady={onGridReady}
						columnDefs={effectedPlansColumnConfig}
					/>
				</div>
			</Modal>
			<div className="footer_section">
				<Button
					onClick={onBackButtonClick}
					size="large"
					variant="secondary"
				>
					Back
				</Button>
				<Button
					onClick={onCreateNewGroupClick}
					size="large"
					variant="primary"
					disabled={
						!storeGroupName ||
						!description ||
						!selectedCreateStoreConfigTableData.length
					}
				>
					Submit Store Group
				</Button>
			</div>
		</div>
	);
}

export default CreateStoreGroupConfiguration;
