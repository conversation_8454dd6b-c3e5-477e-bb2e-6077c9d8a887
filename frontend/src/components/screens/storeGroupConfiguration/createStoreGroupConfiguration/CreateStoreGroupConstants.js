import { wholeStoreFilterConfig } from "../../../../constants/FilterConfigConstants";
import { global_labels, STORE_SELECTION_VALID_INVALID_TABLE } from "../../../../constants/Constants";
import { capitalizeFirstLetter, replaceSpecialCharacter, dateFormatter } from "../../../../utils/helpers/utility_helpers";

export const filterTypeButton = [
    {
        label: "Choose Filters",
        value: "selectFilters",
    },
    {
        label: "Upload XL",
        value: "uploadExcel",
    },
    {
        label: "Copy Paste Data",
        value: "copyPaste",
    },
]

export const createStoreFilterConfig = [
    ...wholeStoreFilterConfig.map(item => ({
        ...item,
        selectOnLoad: false,
        selection: null,
    })),
]

export const createStoreTableConfig = (hierarchyGlobalKeys) => ([
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "store_name",
        headerName: "Store Name",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "store_id",
        headerName: "Store ID",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 148,
        valueFormatter: (params) => parseInt(params.value),
        type: "number",
    },
    {
        field: "s1_name",
        headerName: hierarchyGlobalKeys?.s1_ids,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 148,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s0_name",
        headerName: hierarchyGlobalKeys?.s0_ids,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 148,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s2_name",
        headerName: hierarchyGlobalKeys?.s2_ids,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s4_name",
        headerName: hierarchyGlobalKeys?.s4_ids,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s3_name",
        headerName: hierarchyGlobalKeys?.s3_ids,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "s5_name",
        headerName: hierarchyGlobalKeys?.s5_ids,
        isSearchable: true,
        filter: "agTextColumnFilter",
        // width: 172,
        flex: 1,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
])

export const invalidDataColumnConfig = [
    ...STORE_SELECTION_VALID_INVALID_TABLE,
    {
        field: "status",
        headerName: "Status",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
        flex: 1
    },
]

export const effectedPlansColumnConfig = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "plan_name",
        headerName: "Plan",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "start_date",
        headerName: "Start Date",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "end_date",
        headerName: "End Date",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "plan_status",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Status`,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "user_name",
        headerName: "Created By",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
        width: 150,
        flex: 1,
    },
]