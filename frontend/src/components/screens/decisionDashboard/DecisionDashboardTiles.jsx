import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Carousel from "../../ui/carousel/Carousel";
import _ from "lodash";

import { tilesDataFormatter } from "../../../utils/helpers/utility_helpers";

import InfoCard from "../../ui/infoCard/InfoCard";

import { decisionDashboardTilesConfig } from "./DecisionDashboardConstants";

function DecisionDashboardTiles() {
	const [tilesData, setTilesData] = useState([]);
	const { decisionDashboardTilesData = [] } = useSelector(
		(store) => store?.pricesmartPromoReducer.decisionDashboard
	);

	useEffect(() => {
		if (decisionDashboardTilesData.length) {
			//get metrics data from the api response
			const tiles = decisionDashboardTilesData[0]?.metrics;
			const currency_detail = decisionDashboardTilesData[0]?.currency_detail;
			const formatters_params = {
				...currency_detail
			}
			const tilesData = tilesDataFormatter(
				tiles,
				decisionDashboardTilesConfig,
				formatters_params
			);

			setTilesData(_.cloneDeep(tilesData));
		}
	}, [decisionDashboardTilesData]);

	return (
		<div className="carousel_container marginTop-20">
			<Carousel>
				{tilesData.length
					? _.map(tilesData, (data) => {
							return <InfoCard {...data} />;
					  })
					: null}
			</Carousel>
		</div>
	);
}

export default DecisionDashboardTiles;
