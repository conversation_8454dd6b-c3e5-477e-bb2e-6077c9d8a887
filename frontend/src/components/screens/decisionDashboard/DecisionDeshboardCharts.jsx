import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { ButtonGroup, Select, Tabs } from "impact-ui";
import _ from "lodash";
import moment from "moment";

import Charts from "../../ui/charts/Charts";

import { callDecisionDashboardChartsAPI } from "../../../store/features/decisionDashboardReducer/decisionDashboardReducer";

import {
	chartViewByOptions,
	chartMetricsTabOptions,
	decisionDashboardChartTypes,
} from "./DecisionDashboardConstants";
import { capitalizeFirstLetter, labelCurrencyHandler } from "../../../utils/helpers/utility_helpers";
import { global_labels } from "../../../constants/Constants";

function DecisionDeshboardCharts(props) {
	const dispatch = useDispatch();
	const [activeChart, setActiveChart] = useState("column");
	const [currentChartMetrics, setCurrentChartMetrics] = useState("margin");
	const [yAxisLabel, setYAxisLabel] = useState("GM $");
	const [formatter, setFormatter] = useState("toCurrencyByCurrencyIdWithDecimal");
	const [isOpen, setIsOpen] = useState(false);
	const [chartViewBy, setChartViewBy] = useState({
		label: "Weekly",
		value: "week",
	});
	const [chartData, setChartData] = useState({});

	const { decisionDashboardChartsData } = useSelector(
		(store) => store?.pricesmartPromoReducer.decisionDashboard
	);

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	const { currency_detail } = useSelector(
		(store) => store?.pricesmartPromoReducer.global
	);

	useEffect(() => {
		// if chart data changes, or metric tab changes, set formatted chart data, ya axis label and formatter type
		if (decisionDashboardChartsData.length) {
			setChartData(getSeriesData());
			const yAxisLabel = _.find(chartMetricsTabOptions, {
				value: currentChartMetrics,
			});
			setYAxisLabel(yAxisLabel?.label);
			setFormatter(yAxisLabel?.formatter);
		}
	}, [decisionDashboardChartsData, currentChartMetrics]);

	useEffect(() => {
		// if reset chart options flag is true, reset chart options to default options. This is done when user changes decision dashboard filters
		if (props.resetChartOptionsFlag) {
			setActiveChart("column");
			setCurrentChartMetrics("margin");
			setYAxisLabel("GM $");
			setFormatter("toCurrencyByCurrencyIdWithDecimal");
			setChartViewBy({
				label: "Weekly",
				value: "week",
			});
			props.resetChartsFlagHandler(false);
		}
	}, [props.resetChartOptionsFlag]);

	const getSeriesData = () => {
		const finalizedData = [],
			actualData = [];
		const categories = [];
		// Loop through decisionDashboardChartsData and push data to finalizedData, actualData and categories
		// categories is for x axis labels
		_.forEach(decisionDashboardChartsData, (data) => {
			finalizedData.push(data[`finalized_${currentChartMetrics}`]);
			actualData.push(data[`actualized_${currentChartMetrics}`]);
			categories.push(data[chartViewBy.value]);
		});
		//set seriesData with finalizedData and actualData, and provide the legend name
		const seriesData = [
			{
				name: "Finalized",
				data: finalizedData,
			},
			{
				name: "Actual",
				data: actualData,
			},
		];

		return {
			seriesData,
			categories,
			extra: {
				...currency_detail,
			}
		};
	};

	const handleChartsDayView = (option) => {
		setChartViewBy(option);
		props?.setChartViewBy(option.value);
	};

	return (
		<div className="content_container">
			<div className="text-16-800 marginBottom-24">
				{capitalizeFirstLetter(global_labels?.promo_expanded)} results (Actual vs forecasted)
			</div>
			<div className="flexWithGap12">
				<div className="flex1">
					<Tabs
						value={currentChartMetrics}
						onChange={(_e, value) => setCurrentChartMetrics(value)}
						tabNames={chartMetricsTabOptions.map((option) => {
							return {
								...option,
								label: labelCurrencyHandler(option.label, currency_detail?.currency_symbol || "$"),
							}
						})}
						tabPanels={[]}
					/>
				</div>

				<div className="horizontal-line" />
				<ButtonGroup
					onChange={(_e, val) => setActiveChart(val)}
					options={decisionDashboardChartTypes}
					selectedOption={activeChart}
				/>
				<div className="horizontal-line" />
				<Select
					currentOptions={chartViewByOptions}
					initialOptions={chartViewByOptions}
					setCurrentOptions={() => {}}
					label="View By"
					labelOrientation="left"
					setSelectedOptions={handleChartsDayView}
					isWithSearch={false}
					isMulti={false}
					selectedOptions={chartViewBy}
					isOpen={isOpen}
					setIsOpen={setIsOpen}
					isCloseWhenClickOutside={true}
				/>
			</div>
			<Charts
				chartType={activeChart}
				data={_.cloneDeep(chartData)}
				yAxisLabel={yAxisLabel}
				formatter={formatter}
			/>
		</div>
	);
}

export default DecisionDeshboardCharts;
