import React, { useState, useEffect } from "react";
import moment from "moment";
import { useSelector, useDispatch } from "react-redux";
import _ from "lodash";
import { <PERSON>ton, Checkbox, RadioButtonGroup } from "impact-ui";

import FilterWrapper from "../../common/filters/FilterWrapper";
import ScreenBreadcrumb from "../../common/breadCrumb/ScreenBreadcrumb";
import DecisionDeshboardCharts from "./DecisionDeshboardCharts";
import DecisionDashboardTiles from "./DecisionDashboardTiles";
import DecisionDashboardTable from "./DecisionDashboardTable";
import EmptyData from "../../ui/emptyData/EmptyData";

import {
	decisionDashboardFilterConfig as filterConfig,
	decisionDashboardRequiredFiltersOnLoad as requiredFiltersOnLoad,
	wholeProductFilterConfig,
	wholeStoreFilterConfig,
} from "../../../constants/FilterConfigConstants";
import { breadcrumbRoutes } from "../../../constants/RouteConstants";
import {
	callDecisionDashboardChartsAPI,
	callDecisionDashboardTilesAPI,
	callDecisionDashboardTableAPI,
} from "../../../store/features/decisionDashboardReducer/decisionDashboardReducer";

import "./DecisionDashboard.scss";
import { DEFAULT_DATE_FORMAT, global_labels } from "../../../constants/Constants";
import { resetFiltersForNextIds, setSelectedGenricFilters } from "../../../store/features/filters/filters";
import { capitalizeFirstLetter } from "../../../utils/helpers/utility_helpers";

function DecisionDashboard(props) {
	const dispatch = useDispatch();

	const [showFiltersSection, setShowFiltersSection] = useState(true);
	const [resetChartOptions, setResetChartOptions] = useState(false);
	const [chartViewBy, setChartViewBy] = useState("week");
	const [timeline, setTimeline] = useState(-200);
	const [marketingViewBy, setMarketingViewBy] = useState(["promo_id"]);
	const [decisionDashboardFilterConfig, setDecisionDashboardFilterConfig] = useState(filterConfig);

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	const {
		global_configs
	} = useSelector((store) => store?.pricesmartPromoReducer?.global);

	const {
		decisionDashboardChartsData = [],
		decisionDashboardTableData = [],
	} = useSelector((store) => store?.pricesmartPromoReducer.decisionDashboard);

	useEffect(() => {
		// set default values for the filters
		// show_partially_overlapping_events
		dispatch(setSelectedGenricFilters({
			data: true,
			from: "DECISION_DASHBOARD",
			filterId: "show_partially_overlapping_events",
		}));
		// metrics_display_mode
		dispatch(setSelectedGenricFilters({
			data: "entire_offer_duration",
			from: "DECISION_DASHBOARD",
			filterId: "metrics_display_mode",
		}));
	}, [])

	useEffect(() => {
		const config = [];
		filterConfig.forEach((filter) => {
			let newFilter = _.cloneDeep(filter);
			if (!global_configs?.event?.use_event) {
				newFilter = {
					...filter,
					groupConfig: filter.groupConfig?.filter((group) => group.filterId !== "event"),
				};
			}

			// to handle custom filter
			newFilter.groupConfig.forEach((group) => {
				if (group.filterId === "promo" || group.filterId === "event") {
					group["extraParams"] = {
						...group?.extraParams,
						show_partially_overlapping_events: filtersData?.DECISION_DASHBOARD?.show_partially_overlapping_events || false,
					}
				}
				if (group.filterType === "custom") {
					if (group.filterId === "show_partially_overlapping_events") {
						group["component"] = (
							<Checkbox
								checked={
									filtersData?.DECISION_DASHBOARD?.show_partially_overlapping_events || false
								}
								label={group?.label}
								onChange={(val) => {
									dispatch(setSelectedGenricFilters({
										data: val.target.checked,
										from: "DECISION_DASHBOARD",
										filterId: group?.filterId,
									}));
									dispatch(resetFiltersForNextIds({
										activeScreen: "DECISION_DASHBOARD",
										payload: ["event", "promo"],
									}))
								}}
								required={group?.isMandatory || false}
								variant="default"
							/>
						)
					} else if (group.filterId === "metrics_display_mode") {
						group["component"] = (
							<div className="metrics_display_mode_container">
								<p className="text-14-600">Show metrics of:&nbsp;</p>
								<RadioButtonGroup
									options={group?.options}
									selectedOption={filtersData?.DECISION_DASHBOARD?.metrics_display_mode}
									isDisabled={!filtersData?.DECISION_DASHBOARD?.show_partially_overlapping_events}
									onChange={(_e, val) => {
										dispatch(setSelectedGenricFilters({
											data: val,
											from: "DECISION_DASHBOARD",
											filterId: group?.filterId,
										}));
									}}
									orientation="row"
									className="metrics_radio_button_group"
								/>
							</div>
						)
					}
				}
			});

			config.push(newFilter);
		});
		setDecisionDashboardFilterConfig(config);
	}, [global_configs?.event?.use_event, filtersData?.DECISION_DASHBOARD]);

	useEffect(() => {
		if (chartViewBy && filtersData.DECISION_DASHBOARD) {
			let payload = createPayload();
			payload = {
				...payload,
			}

			dispatch(callDecisionDashboardChartsAPI(_.cloneDeep(payload)));
			dispatch(callDecisionDashboardTableAPI(_.cloneDeep(createPayload())));
		}
	}, [chartViewBy])

	useEffect(() => {
		if (filtersData.DECISION_DASHBOARD) {
			const payload = createPayload();
			// for table API, add action in payload
			payload.action = "get";
			dispatch(callDecisionDashboardTableAPI(_.cloneDeep(payload)));
		}
	}, [timeline, marketingViewBy])

	const createPayload = () => {
		const decisionDashboardFilters = _.cloneDeep(
			filtersData.DECISION_DASHBOARD
		);
		// set all selected filters in the payload

		const product_hierarchy_keys = wholeProductFilterConfig.map(item => item.filterId);
		const store_hierarchy_keys = wholeStoreFilterConfig.map(item => item.filterId);

		const payload = {
			aggregation: {
				marketing: marketingViewBy,
				timeline: timeline,
			},
			product_hierarchies: {},
			store_hierarchies: {},
		};
		_.forEach(Object.keys(decisionDashboardFilters), (key) => {
			if (key === "dateRange") {
				payload.start_date = moment(
					decisionDashboardFilters.dateRange?.start_date
				).format(DEFAULT_DATE_FORMAT);
				payload.end_date = moment(
					decisionDashboardFilters.dateRange?.end_date
				).format(DEFAULT_DATE_FORMAT);
			} else if (key === "event") {
				payload["event_ids"] = _.cloneDeep(
					decisionDashboardFilters[key]?.selectedOptionsArray
				);
			} else if (key === "promo") {
				payload["promo_ids"] = _.cloneDeep(
					decisionDashboardFilters[key]?.selectedOptionsArray
				);
			} else if (product_hierarchy_keys.includes(key)) {
				payload["product_hierarchies"][key] = _.cloneDeep(
					decisionDashboardFilters[key]?.selectedOptionsArray
				);
			} else if (store_hierarchy_keys.includes(key)) {
				payload["store_hierarchies"][key] = _.cloneDeep(
					decisionDashboardFilters[key]?.selectedOptionsArray
				);
			} else {
				payload[key] = decisionDashboardFilters[key].hasOwnProperty("selectedOptionsArray")
					? _.cloneDeep(decisionDashboardFilters[key]?.selectedOptionsArray)
					: _.cloneDeep(decisionDashboardFilters[key]);
			}
		});
		payload.view_by_options = chartViewBy;
		// for tiles API, add screen_type in payload
		payload.screen_type = "decision_dashboard";
		return payload;
	}

	const onFilterApply = () => {
		const payload = createPayload();
		// for table API, add action in payload
		payload.action = "get";
		dispatch(callDecisionDashboardTableAPI(_.cloneDeep(payload)));
		let payloadForTilesAndTable = _.cloneDeep(payload);
		// remove product_hierarchies and store_hierarchies from payload and add it to the payload as the payload structure is different 
		// for tiles API 
		payloadForTilesAndTable = {
			...payloadForTilesAndTable,
		}

		dispatch(callDecisionDashboardChartsAPI(_.cloneDeep(payloadForTilesAndTable)));
		dispatch(callDecisionDashboardTilesAPI(_.cloneDeep(payloadForTilesAndTable)));
		// reset the chart options to default
		setResetChartOptions(true);
	};

	return (
		<div className="screen_container">
			<ScreenBreadcrumb
				breadcrumbList={breadcrumbRoutes()?.["decisionDashboard"]}
			>
				<Button
					iconPlacement="left"
					onClick={() => {
						setShowFiltersSection((prev) => !prev);
					}}
					size="large"
					variant="secondary"
				>
					{showFiltersSection ? "Hide" : "Show"} Filters
				</Button>
			</ScreenBreadcrumb>
			<FilterWrapper
				defaultOpen="product_hierarchy" //provide which filter group should be open by default
				screen="DECISION_DASHBOARD" //provide the screen/component name, should be unique
				callAPIonLoad={true} //should be true if you want to call API on load
				filterConfig={decisionDashboardFilterConfig} //provide the filter config.
				requiredFiltersOnLoad={requiredFiltersOnLoad} //provide the list of filters for which API should be called on load
				onFilterApply={onFilterApply} //provide the function to be called on apply button click
				showFiltersSection={showFiltersSection} //provide the state to show/hide filter tags section
			/>
			{decisionDashboardChartsData.length ||
				decisionDashboardTableData.length ? (
				<div className="screen_data_container flex1">
					<div className="decisionDashboardDataContainer">
						<DecisionDeshboardCharts
							resetChartOptionsFlag={resetChartOptions}
							resetChartsFlagHandler={setResetChartOptions}
							setChartViewBy={setChartViewBy}
						/>
						<DecisionDashboardTiles />
						<DecisionDashboardTable
							resetScreenData={onFilterApply}
							setMarketingViewBy={setMarketingViewBy}
							marketingViewBy={marketingViewBy}
							timeline={timeline}
							setTimeline={setTimeline}
							createPayload={createPayload}
						/>
					</div>
				</div>
			) : (
				<EmptyData text={`Please Select Filters to view ${capitalizeFirstLetter(global_labels?.promo_plural)}`} />
			)}
		</div>
	);
}

export default DecisionDashboard;

//for refernce, I've kept it

/* <ComponentFilters
					filterConfig={componentFilterConfig} //provide the filter config.
					callAPIonLoad={true} //should be true if you want to call API on load
					screen="DECISION_DASHBOARD_COMPONENT" //provide the screen/component name, should be unique
					onPrimaryButtonClick={onComponentFiltersApply} //provide the function to be called on apply button click
					onSecondaryButtonClick={onCancel} //provide the function to be called on clear button click
					primaryButtonText="Submit" //provide the text for primary button
					secondaryButtonText="Clear Filters" //provide the text for secondary button
				/> */
