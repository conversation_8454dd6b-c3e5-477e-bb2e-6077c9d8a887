import moment from "moment";

import { capitalizeFirstLetter, replace<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../utils/helpers/utility_helpers";
import {
	yyyyMmDdFormatter,
	toUnit,
	toCurrencyByCurrencyId,
	toPercentage,
} from "../../../utils/helpers/formatter";

import ColumnChartIcon from "../../../assets/imageAssets/columnChartIcon.svg?.url";
import LineChartIcon from "../../../assets/imageAssets/lineChartIcon.svg?.url";
import FinalizedSalesUnitsIcon from "../../../assets/imageAssets/finSalesUnits.png";
import IncSalesUnitsIcon from "../../../assets/imageAssets/incSalesUnits.png";
import BaselineSalesUnitsIcon from "../../../assets/imageAssets/baselineSalesU.png";
import ActualSalesUIcon from "../../../assets/imageAssets/actualSalesU.png";
import FinalRevenueIcon from "../../../assets/imageAssets/finalRevenue.png";
import BaseRevenueIcon from "../../../assets/imageAssets/baseRevenue.png";
import ActualRevenueIcon from "../../../assets/imageAssets/actualRevenue.png";
import FinalizedGMIcon from "../../../assets/imageAssets/finGMDollar.png";
import ActualGMIcon from "../../../assets/imageAssets/actualGMDollar.png";
import BaselineGMIcon from "../../../assets/imageAssets/baselineGMDollar.png";
import IncrementalGMIcon from "../../../assets/imageAssets/incGMDollar.png";
import FinalizedGMPercentIcon from "../../../assets/imageAssets/finGMPercent.png";
import ActualGMPercentIcon from "../../../assets/imageAssets/actualGMPercent.png";
import FinalizedCMDollarIcon from "../../../assets/imageAssets/finCMDollar.png";
import ActualCMDollarIcon from "../../../assets/imageAssets/actualCMDollar.png";
import FinalizedCMPercentIcon from "../../../assets/imageAssets/finCMPercent.png";
import ActualCMPercentIcon from "../../../assets/imageAssets/actualCMPercent.png";

import {
	cellsWithBadge,
	TableWithInputBoxOption,
	OfferNameCellRenderer,
	OfferStatusCellRenderer,
} from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { containerStore } from "../../../store";
import { editedDecisionDashboardDataSave } from "../../../store/features/decisionDashboardReducer/decisionDashboardReducer";
import { global_labels, HYPERLINK_PRODUCT_TABLE_CONFIG } from "../../../constants/Constants";

export const chartViewByOptions = [
	{
		label: "Weekly",
		value: "week",
	},
	{
		label: "Monthly",
		value: "month",
	},
	{
		label: "Quarterly",
		value: "quarter",
	},
];

export const chartMetricsTabOptions = [
	{
		label: "GM $",
		value: "margin",
		formatter: "toCurrencyByCurrencyIdWithDecimal",
	},
	{
		label: "GM %",
		value: "margin_percent",
		formatter: "toPercentage",
	},
	{
		label: "Revenue",
		value: "revenue",
		formatter: "toCurrencyByCurrencyIdWithDecimal",
	},
	{
		label: "Sales units",
		value: "sales_units",
		formatter: "toUnit",
	},
	{
		label: "CM $",
		value: "contribution_margin",
		formatter: "toCurrencyByCurrencyIdWithDecimal",
	},
	{
		label: "CM %",
		value: "contribution_margin_percent",
		formatter: "toPercentage",
	},
];

export const decisionDashboardChartTypes = [
	{
		icon: <img src={ColumnChartIcon} alt="Column Chart" />,
		value: "column",
	},
	{
		icon: <img src={LineChartIcon} alt="Line Chart" />,
		value: "line",
	},
];

export const decisionDashboardMetricsConfig = [
	{
		label: "ST %",
		value: "st_percent",
		metricsOptions: [
			"actualized_st_percent",
			"finalized_st_percent",
		],
	},
	{
		label: "Margin $",
		value: "margin",
		metricsOptions: [
			"actualized_margin",
			"finalized_margin",
			"original_margin",
			"baseline_margin",
			"actualized_incremental_margin",
			"finalized_incremental_margin",
		],
	},
	{
		label: "Margin %",
		value: "margin_percent",
		metricsOptions: [
			"actualized_margin_percent",
			"finalized_margin_percent",
			"original_margin_percent",
		],
	},
	{
		label: "Revenue",
		value: "revenue",
		metricsOptions: [
			"actualized_revenue",
			"finalized_revenue",
			"original_revenue",
			"baseline_revenue",
			"actualized_incremental_revenue",
			"finalized_incremental_revenue",
		],
	},
	{
		label: "Sales Units",
		value: "sales_units",
		metricsOptions: [
			"actualized_sales_units",
			"finalized_sales_units",
			"original_sales_units",
			"baseline_sales_units",
			"actualized_incremental_sales_units",
			"finalized_incremental_sales_units",
		],
	},
	{
		label: "CM $",
		value: "contribution_margin",
		metricsOptions: [
			"actualized_contribution_margin",
			"finalized_contribution_margin",
			"original_contribution_margin",
		],
	},
	{
		label: "CM %",
		value: "contribution_margin_percent",
		metricsOptions: [
			"actualized_contribution_margin_percent",
			"finalized_contribution_margin_percent",
			"original_contribution_margin_percent",
		],
	},
];

export const decisionDashboardTilesConfig = [
	{
		label: "Finalized Sales Units",
		key: "sales_units_finalized",
		formatter: "toUnit",
		icon: FinalizedSalesUnitsIcon,
	},
	{
		label: "Baseline Sales Units",
		key: "sales_units_baseline",
		formatter: "toUnit",
		icon: BaselineSalesUnitsIcon,
	},
	{
		label: "Incremental Sales Units",
		key: "sales_units_incremental",
		formatter: "toUnit",
		icon: IncSalesUnitsIcon,
	},
	{
		label: "Actualized Sales Units",
		key: "sales_units_actualized",
		formatter: "toUnit",
		icon: ActualSalesUIcon,
	},
	{
		label: "Finalized Revenue",
		key: "revenue_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalRevenueIcon,
	},
	{
		label: "Baseline Revenue",
		key: "revenue_baseline",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: BaseRevenueIcon,
	},
	{
		label: "Incremental Revenue",
		key: "revenue_incremental",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalRevenueIcon,
	},
	{
		label: "Actualized Revenue",
		key: "revenue_actualized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: ActualRevenueIcon,
	},
	{
		label: "Finalized GM",
		key: "gross_margin_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalizedGMIcon,
	},
	{
		label: "Baseline GM",
		key: "gross_margin_baseline",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: BaselineGMIcon,
	},
	{
		label: "Incremental GM",
		key: "gross_margin_incremental",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: IncrementalGMIcon,
	},
	{
		label: "Actualized GM",
		key: "gross_margin_actualized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: ActualGMIcon,
	},
	{
		label: "Finalized GM %",
		key: "gross_margin_percent_finalized",
		formatter: "toUnit",
		icon: FinalizedGMPercentIcon,
	},
	{
		label: "Actualized GM %",
		key: "gross_margin_percent_actualized",
		formatter: "toUnit",
		icon: ActualGMPercentIcon,
	},
	{
		label: "Finalized CM $",
		key: "contribution_gross_margin_finalized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: FinalizedCMDollarIcon,
	},
	{
		label: "Actualized CM $",
		key: "contribution_gross_margin_actualized",
		formatter: "toCurrencyByCurrencyIdWithFormatThousands",
		icon: ActualCMDollarIcon,
	},
	{
		label: "Finalized CM %",
		key: "contribution_gross_margin_percent_finalized",
		formatter: "toUnit",
		icon: FinalizedCMPercentIcon,
	},
	{
		label: "Actualized CM %",
		key: "contribution_gross_margin_percent_actualized",
		formatter: "toUnit",
		icon: ActualCMPercentIcon,
	},
];

export const decisionDashboardTableConfig = [
	{
		field: "",
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true,
		suppressMenu: true,
		filter: false,
		sortable: false,
		pinned: "left",
		maxWidth: 60,
		colId: "checkbox",
		hide: false
	},
	{
		field: "event_name",
		headerName: global_labels.event_primary + " Name",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => {
			if (params.value) {
				return replaceSpecialCharacter(params.value)
			} else {
				return "-"
			}
		},
		hide: false,
		rowGroup: true,
	},
	{
		field: "promo_name",
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Name`,
		cellRenderer: OfferNameCellRenderer,
		width: 350,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRendererParams: {
			enableInput: {
				screen: "decisionDashboard", // reducer name
				enableKey: "enableDDInlineEdit", //input box enable key in reducer state
				storeDataKey: "decisionDashboardSelectedOffers", // selected table data key in reducer state
				uniqueKey: "promo_id", // key to match for selective enable and disable input box
			},
			defaultFlag: false,
			onDataUpdate: (data) =>
				containerStore.dispatch(editedDecisionDashboardDataSave(data)),
			// add onClick handler if needed to perform any action on click of the cell, once passed the label will appear as a link
		},
		hide: false,
		rowGroup: true
	},
	{
		field: "start_date",
		headerName: "Start Date",
		width: 140,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
	},
	{
		field: "end_date",
		headerName: "End Date",
		width: 130,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => yyyyMmDdFormatter(params),
	},
	{
		field: "created_by",
		headerName: "Created By",
		width: 160,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "offer_comment",
		headerName: "Comment",
		cellRenderer: TableWithInputBoxOption,
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRendererParams: {
			enableInput: {
				screen: "decisionDashboard",
				enableKey: "enableDDInlineEdit",
				storeDataKey: "decisionDashboardSelectedOffers",
				uniqueKey: "promo_id",
			},
			defaultFlag: false,
			onDataUpdate: (data) =>
				containerStore.dispatch(editedDecisionDashboardDataSave(data)),
		},
	},
	{
		field: "status",
		width: 180,
		headerName: "Status",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
		cellRenderer: OfferStatusCellRenderer
	},
	{
		field: "actual_performance",
		width: 160,
		headerName: "Actual Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "finalized_performance",
		width: 160,
		headerName: "Finalized Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "ia_recc_discount",
		headerName: "IA Recommended Discount",
		width: 260,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => {
			const { discount } = params.value || {};
			if (!discount) return "-";
			return replaceSpecialCharacter(discount);
		},
	},
	{
		field: "finalized_discount",
		headerName: "Finalized Discount",
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => {
			const { discount } = params.value || {};
			if (!discount) return "-";
			return replaceSpecialCharacter(discount);
		},
	},
	{
		field: "products_count",
		headerName: "Products",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		type: "number",
	},
	{
		field: "stores_count",
		headerName: "Stores",
		width: 150,
		isSearchable: true,
		filter: "agTextColumnFilter",
		type: "number",
	},
	{
		field: "avg_basket_size",
		width: 180,
		headerName: "Avg. basket Size",
		isSearchable: true,
		filter: "agTextColumnFilter",
		type: "number",
	},
	{
		field: "units_per_txn",
		headerName: "Units Per Txn",
		width: 160,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => toUnit(params),
		type: "number",
	},
	{
		field: "no_of_txn",
		width: 140,
		headerName: "# of Txn",
		isSearchable: true,
		filter: "agTextColumnFilter",
		type: "number",
	},
	{
		field: "revenue_per_style",
		headerName: "Revenue per style",
		width: 190,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => {
			const { currency_id, currency_name, currency_symbol } = params.data;
			return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
		},
		type: "number",
	},
	{
		field: "finalized_total_inventory",
		headerName: "Inventory",
		width: 190,
		isSearchable: true,
		filter: "agTextColumnFilter",
		valueFormatter: (params) => {
			return toUnit({ value: params.value });
		},
		type: "number",
	},
	{
		headerName: "ST %",
		children: [
			{
				field: "actualized_st_percent",
				colId: "actualized_st_percent",
				headerName: "Actualized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_st_percent",
				colId: "finalized_st_percent",
				headerName: "Finalized",
				isSearchable: true,
				hide: false,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_stack_st_percent",
				headerName: "Finalized",
				colId: "finalized_stack_st_percent",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				type: "number",
			},
		],
	},
	{
		headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} $`,
		colId: "promo_spend",
		children: [
			{
				field: "actualized_promo_spend",
				headerName: "Actualized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_stack_promo_spend",
				headerName: "Finalized",
				colId: "finalized_stack_promo_spend",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_promo_spend",
				headerName: "Finalized",
				colId: "finalized_promo_spend",
				isSearchable: true,
				filter: "agTextColumnFilter",
				hide: false,
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				width: 150,
				type: "number",
			},
			{
				field: "original_promo_spend",
				headerName: "Original",
				hide: true,
				colId: "original_promo_spend",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				width: 150,
				type: "number",
			},
		],
	},
	{
		headerName: "Sales Units",
		children: [
			{
				field: "actualized_sales_units",
				headerName: "Actualized Units",
				colId: "actualized_sales_units",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
			{
				field: "finalized_sales_units",
				colId: "finalized_sales_units",
				headerName: "IA projected (Finalized)",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 230,
				hide: false,
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
			{
				field: "finalized_stack_sales_units",
				headerName: "IA projected (Finalized)",
				colId: "finalized_stack_sales_units",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
			{
				field: "original_sales_units",
				headerName: "Original Units",
				hide: true,
				colId: "original_sales_units",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
			{
				field: "finalized_baseline_sales_units",
				colId: "baseline_sales_units",
				headerName: "Baseline Contribution",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 220,
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
			{
				field: "actualized_incremental_sales_units",
				colId: "actualized_incremental_sales_units",
				headerName: "Actualized Incremental",
				width: 240,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
			{
				field: "finalized_incremental_sales_units",
				colId: "finalized_incremental_sales_units",
				headerName: "Finalized Incremental",
				isSearchable: true,
				filter: "agTextColumnFilter",
				hide: false,
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
			{
				field: "finalized_stack_incremental_sales_units",
				colId: "finalized_stack_incremental_sales_units",
				headerName: "Finalized Incremental",
				isSearchable: true,
				filter: "agTextColumnFilter",
				hide: true,
				valueFormatter: (params) => {
					return toUnit({ value: params.value });
				},
				type: "number",
			},
		],
	},
	{
		headerName: "Revenue",
		children: [
			{
				field: "actualized_revenue",
				colId: "actualized_revenue",
				headerName: "Actualized Revenue",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_revenue",
				colId: "finalized_revenue",
				headerName: "IA projected (Finalized)",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 230,
				hide: false,
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_stack_revenue",
				headerName: "IA projected (Finalized)",
				colId: "finalized_stack_revenue",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "original_revenue",
				headerName: "Original Revenue",
				hide: true,
				colId: "original_revenue",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_baseline_revenue",
				colId: "baseline_revenue",
				headerName: "Baseline Contribution",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 220,
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actualized_incremental_revenue",
				colId: "actualized_incremental_revenue",
				headerName: "Actualized Incremental",
				width: 240,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_incremental_revenue",
				colId: "finalized_incremental_revenue",
				headerName: "Finalized Incremental",
				isSearchable: true,
				hide: false,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_stack_incremental_revenue",
				colId: "finalized_stack_incremental_revenue",
				headerName: "Finalized Incremental",
				isSearchable: true,
				hide: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
		],
	},
	{
		headerName: "Margin $",
		children: [
			{
				field: "actualized_margin",
				colId: "actualized_margin",
				headerName: "Actualized Margin",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_margin",
				colId: "finalized_margin",
				headerName: "IA projected (Finalized)",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 230,
				hide: false,
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_stack_margin",
				headerName: "IA projected (Finalized)",
				colId: "finalized_stack_margin",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "original_margin",
				headerName: "Original Margin",
				hide: true,
				colId: "original_margin",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_baseline_margin",
				colId: "baseline_margin",
				headerName: "Baseline Contribution",
				isSearchable: true,
				filter: "agTextColumnFilter",
				width: 220,
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actualized_incremental_margin",
				colId: "actualized_incremental_margin",
				headerName: "Actualized Incremental",
				width: 240,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_incremental_margin",
				colId: "finalized_incremental_margin",
				headerName: "Finalized Incremental",
				isSearchable: true,
				hide: false,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_stack_incremental_margin",
				colId: "finalized_stack_incremental_margin",
				headerName: "Finalized Incremental",
				isSearchable: true,
				filter: "agTextColumnFilter",
				hide: true,
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
		],
	},
	{
		headerName: "Margin %",
		children: [
			{
				field: "actualized_margin_percent",
				colId: "actualized_margin_percent",
				headerName: "Actualized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_margin_percent",
				colId: "finalized_margin_percent",
				headerName: "Finalized",
				isSearchable: true,
				hide: false,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_stack_margin_percent",
				headerName: "Finalized",
				colId: "finalized_stack_margin_percent",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				type: "number",
			},
			{
				field: "original_margin_percent",
				headerName: "Original",
				colId: "original_margin_percent",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
		],
	},
	{
		headerName: "Contributional Margin $",
		children: [
			{
				field: "actualized_contribution_margin",
				colId: "actualized_contribution_margin",
				headerName: "Actualized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_contribution_margin",
				colId: "finalized_contribution_margin",
				headerName: "Finalized",
				isSearchable: true,
				hide: false,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_stack_contribution_margin",
				headerName: "Finalized",
				colId: "finalized_stack_contribution_margin",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "original_contribution_margin",
				headerName: "Original",
				colId: "original_contribution_margin",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				width: 150,
				type: "number",
			},
		],
	},
	{
		headerName: "Contributional Margin %",
		children: [
			{
				field: "actualized_contribution_margin_percent",
				colId: "actualized_contribution_margin_percent",
				headerName: "Actualized",
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_contribution_margin_percent",
				colId: "finalized_contribution_margin_percent",
				headerName: "Finalized",
				isSearchable: true,
				hide: false,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
			{
				field: "finalized_stack_contribution_margin_percent",
				headerName: "Finalized",
				colId: "finalized_stack_contribution_margin_percent",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				type: "number",
			},
			{
				field: "original_contribution_margin_percent",
				headerName: "Original",
				colId: "original_contribution_margin_percent",
				hide: true,
				isSearchable: true,
				filter: "agTextColumnFilter",
				valueFormatter: (params) => {
					return toPercentage({ value: params.value });
				},
				width: 150,
				type: "number",
			},
		],
	},
];

export const originalForcastTableColumns = [
	"original_contribution_margin_percent",
	"original_contribution_margin",
	"original_margin_percent",
	"original_margin",
	"original_revenue",
	"original_sales_units",
	"original_promo_spend",
];

export const viewColsOptions = [
	{ value: "stacked", label: "Stacked" },
	{ value: "original", label: "Original #" },
];

export const productTableConfig = (hierarchyGlobalKeys) => ([
    {
        field: "client_product_id",
        headerName: "Product ID",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "product_name",
        headerName: "Description",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 300,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    // {
    //     field: "msrp",
    //     headerName: "MSRP",
    //     isSearchable: true,
    //     filter: "agTextColumnFilter",
    //     width: 150,
    //     valueFormatter: toCurrencyByCurrencyId,
    //     type: "number",
    // },
    {
        field: "current_price",
        headerName: "Current Price",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => {
			const { currency_id, currency_name, currency_symbol } = params.data;
			return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
		},
        type: "number",
    },
	{
		field: "cost",
		headerName: "Cost",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 140,
		valueFormatter: (params) => {
			const { currency_id, currency_name, currency_symbol } = params.data;
			return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
		},
		type: "number",
	},
    {
        field: "total_inventory",
        headerName: "Inventory",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
    },
    // {
    //     field: "launch_price",
    //     headerName: "Launch Price",
    //     isSearchable: true,
    //     filter: "agTextColumnFilter",
    //     flex: 1,
    //     valueFormatter: toCurrencyByCurrencyId,
    //     type: "number",
    // },
	...HYPERLINK_PRODUCT_TABLE_CONFIG(hierarchyGlobalKeys),
	{
		headerName: "Finalized",
		colId: "finalized",
		children: [
			{
				field: "finalized_revenue",
				headerName: "Revenue",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_margin",
				headerName: "Margin",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "finalized_units",
				headerName: "Units",
				type: "number",
			},
			{
				field: "finalized_gm_percent",
				headerName: "GM %",
				valueFormatter: toPercentage,
				type: "number",
			},
		],
	},
	{
		headerName: "Actual",
		colId: "actual",
		children: [
			{
				field: "actual_revenue",
				headerName: "Revenue",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actual_margin",
				headerName: "Margin",
				valueFormatter: (params) => {
					const { currency_id, currency_name, currency_symbol } = params.data;
					return toCurrencyByCurrencyId({ value: params.value, currency_id, currency_name, currency_symbol });
				},
				type: "number",
			},
			{
				field: "actual_units",
				headerName: "Units",
				type: "number",
			},
			{
				field: "actual_gm_percent",
				headerName: "GM %",
				valueFormatter: toPercentage,
				type: "number",
			},
		]
	},
])

export const storeTableConfig = (hierarchyGlobalKeys) => ([
	{
		field: "store_id",
		headerName: "Store ID",
		valueFormatter: (params) => parseInt(params.value),
	},
	{
		field: "store_name",
		headerName: "Store Name",
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s0_name",
		headerName: hierarchyGlobalKeys?.s0_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s1_name",
		headerName: hierarchyGlobalKeys?.s1_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s2_name",
		headerName: hierarchyGlobalKeys?.s2_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s3_name",
		headerName: hierarchyGlobalKeys?.s3_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	{
		field: "s4_name",
		headerName: hierarchyGlobalKeys?.s4_ids,
		valueFormatter: (params) => replaceSpecialCharacter(params.value),
	},
	// {
	// 	field: "s5_name",
	// 	headerName: "City",
	// 	valueFormatter: (params) => replaceSpecialCharacter(params.value),
	// },
])