import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";
import React, { useRef, useState, useCallback, useEffect } from "react";
import {
    Input,
    Button,
    Modal,
    Select,
    Prompt,
    Badge,
    Panel,
} from "impact-ui";
import {Table} from "impact-ui-v3";
import _ from "lodash";
import moment from "moment";

import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import ViewIcon from "../../../assets/imageAssets/viewIcon.svg?.url";
import levelFilterIconInPanel from "../../../assets/imageAssets/levelFilterIconInPanel.svg?.url";

import {
    decisionDashboardTableConfig,
    decisionDashboardMetricsConfig,
    originalForcastTableColumns,
    viewColsOptions,
    storeTableConfig,
    productTableConfig,
} from "./DecisionDashboardConstants";

import {
    decisionDashboardDownload,
    setSelectedDecisionDashboardOffers,
    setEnableInlineEdit,
    inlineEditAPICall,
    withdrawOffer,
    callDecisionDashboardTilesAPI,
    deletePromo,
    executePromo,
    getProductsDetailsOfPromo,
    getStoresDetailsOfPromo,
    getProductsDetailsOfEvent,
    getStoresDetailsOfEvent,
} from "../../../store/features/decisionDashboardReducer/decisionDashboardReducer";
import {
    toastError,
    validateOperation,
} from "../../../store/features/global/global";
import {
    setActivePromoId,
    setMaxStepCount,
} from "../../../store/features/promoReducer/promoReducer";
import { global_labels } from "../../../constants/Constants";
import "./DecisionDashboard.scss";
import { CellOnClickButton } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { fabricatePayloadHierarchy, capitalizeFirstLetter, labelCurrencyHandler, labelCurrencyHandlerForTableDefs } from "../../../utils/helpers/utility_helpers";
import { offerLevel, productSelection, storeSelection, timeSelection } from "../../../constants/FilterConfigConstants";

const DecisionDashboardTable = (props) => {
    const decisionDashboardTableRef = useRef(null);
    const productDetailTableRef = useRef(null);
    const storeDetailTableRef = useRef(null);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const { global_configs, currency_detail } = useSelector(
        (store) => store?.pricesmartPromoReducer?.global
    );

    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [showOriginalData, setShowOriginalData] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [isSelectAll, setIsSelectAll] = useState(true);
    const [showWithdrawPrompt, setShowWithdrawPrompt] = useState(false);
    const [showWithdrawButton, setShowWithdrawButton] = useState(false);
    const [showExecutePrompt, setShowExecutePrompt] = useState(false);
    const [showExecuteButton, setShowExecuteButton] = useState(false);
    const [showDeletePrompt, setShowDeletePrompt] = useState(false);
    const [withDrawFromEditAction, setWithDrawFromEditAction] = useState(false);
    const [isViewColsOpen, setIsViewColsOpen] = useState(false);
    const [selectedViewCols, setSelectedViewCols] = useState([]);
    const [tableColDefs, setTableColDefs] = useState(
        decisionDashboardTableConfig
    );
    const [isSelectAllForViewCols, setIsSelectAllForViewCols] = useState(false);

    const [selectedMetrics, setSelectedMetrics] = useState(
        decisionDashboardMetricsConfig.map((option) => ({
            label: labelCurrencyHandler(option.label, currency_detail?.currency_symbol || "$"),
            ...option,
        }))
    );
    const [isPanelOpen, setIsPanelOpen] = useState(false);
    const [selectedProductHierarchy, setSelectedProductHierarchy] = useState(
        []
    );
    const [selectedStoreHierarchy, setSelectedStoreHierarchy] = useState([]);
    const [selectedTimeHierarchy, setSelectedTimeHierarchy] = useState([]);
    const [selectedOfferLevel, setSelectedOfferLevel] = useState([]);

    const [productDetailModalOpen, setProductDetailModalOpen] = useState(false);
    const [storeDetailModalOpen, setStoreDetailModalOpen] = useState(false);
    const [productDetailTableData, setProductDetailTableData] = useState([]);
    const [storeDetailTableData, setStoreDetailTableData] = useState([]);
    const [showGlobalSearchForProductDetail, setShowGlobalSearchForProductDetail] = useState(false);
    const [globalSearchTextForProductDetail, setGlobalSearchTextForProductDetail] = useState("");
    const [showGlobalSearchForStoreDetail, setShowGlobalSearchForStoreDetail] = useState(false);
    const [globalSearchTextForStoreDetail, setGlobalSearchTextForStoreDetail] = useState("");
    const [filteredOptions, setFilteredOptions] = useState({});

    const {
        decisionDashboardTableData = [],
        decisionDashboardSelectedOffers = [],
        enableDDInlineEdit = false,
        editedDDSelectedRowData = [],
    } = useSelector((store) => store?.pricesmartPromoReducer.decisionDashboard);

    const { hierachy_keys } = useSelector(
        (store) => store?.pricesmartPromoReducer.global
    );

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    // Add this ref at the top of your component
    const marketingViewByRef = useRef(props.marketingViewBy);

    // Update ref whenever props change
    useEffect(() => {
        marketingViewByRef.current = props.marketingViewBy;
    }, [props.marketingViewBy]);

    // Configuration for hierarchy filters to set badges in the panel
    const hierarchyConfig = [
        {
            id: "productHierarchy",
            title: "Product Hierarchy",
            labels: productSelection,
            selected: selectedProductHierarchy,
            setter: setSelectedProductHierarchy,
        },
        {
            id: "storeHierarchy",
            title: "Store Hierarchy",
            labels: storeSelection,
            selected: selectedStoreHierarchy,
            setter: setSelectedStoreHierarchy,
        },
        {
            id: "timeHierarchy",
            title: "Time Hierarchy",
            labels: timeSelection,
            selected: selectedTimeHierarchy,
            setter: setSelectedTimeHierarchy,
        },
        {
            id: "offerLevel",
            title: `${capitalizeFirstLetter(global_labels?.promo_alias)} Level`,
            labels: offerLevel,
            selected: selectedOfferLevel,
            setter: setSelectedOfferLevel,
        },
    ];

    useEffect(() => {
        mustBeModifiesCols(decisionDashboardTableConfig);
    }, [global_configs?.event?.use_event]);

    useEffect(() => {
        //if inline edit is ongoing, hide the checkbox column
        decisionDashboardTableRef?.current?.api?.applyColumnState({
            state: [
                {
                    colId: "checkbox",
                    hide: enableDDInlineEdit,
                },
            ],
            applyOrder: true,
        });
    }, [enableDDInlineEdit]);

    useEffect(() => {
        // const savedState = decisionDashboardTableRef.current.api.getColumnState();
    }, [decisionDashboardTableData]);

    useEffect(() => {
        const { marketingViewBy, timeline } = props;
        const api = decisionDashboardTableRef.current?.api;
        if (!api) return;

        // Determine checkbox visibility
        const showCheckbox =
            timeline != 1 && marketingViewBy.includes("promo_id");

        // Update column visibility in a single batch
        api.setColumnVisible("checkbox", showCheckbox);
        api.setColumnVisible(
            "promo_name",
            marketingViewBy.includes("promo_id")
        );
        api.setColumnVisible("status", !marketingViewBy.includes("event_id"));
    }, [props.marketingViewBy, props.timeline]);

    const mustBeModifiesCols = (coldef = []) => {
        let col_def = _.cloneDeep(coldef);
        if (!global_configs?.event?.use_event) {
            col_def = col_def.filter((col) => col.field === "event_name");
        }

        col_def.forEach((col) => {
            if (
                col.field === "products_count" ||
                col.field === "stores_count"
            ) {
                col["cellRenderer"] = (props) => (
                    <CellOnClickButton {...props} clickHandler={clickHandler} />
                );
            }
        });

        setTableColDefs(col_def);
    };

    const clickHandler = async (data, colDef) => {
        if (colDef.field === "products_count") {
            let productsDetails = [];
            if (marketingViewByRef.current?.includes("promo_id")) {
                productsDetails = await dispatch(
                    getProductsDetailsOfPromo(data.promo_id)
                );
            } else if (marketingViewByRef.current?.includes("event_id")) {
                productsDetails = await dispatch(
                    getProductsDetailsOfEvent(data.event_id)
                );
            }
            setProductDetailTableData(productsDetails);
            setProductDetailModalOpen(true);
            setStoreDetailModalOpen(false);
        } else if (colDef.field === "stores_count") {
            let storesDetails = [];
            if (marketingViewByRef.current?.includes("promo_id")) {
                storesDetails = await dispatch(
                    getStoresDetailsOfPromo(data.promo_id)
                );
            } else if (marketingViewByRef.current?.includes("event_id")) {
                storesDetails = await dispatch(
                    getStoresDetailsOfEvent(data.event_id)
                );
            }
            setStoreDetailTableData(storesDetails);
            setStoreDetailModalOpen(true);
            setProductDetailModalOpen(false);
        }
    };

    const showOriginalDataHandler = () => {
        //get table column state
        const savedState = decisionDashboardTableRef.current.api.getColumnState();

        //iterate through table config and hide/show the columns which are in originalForcastTableColumns
        _.forEach(originalForcastTableColumns, (column) => {
            const index = _.findIndex(
                savedState,
                (state) => state?.colId === column
            );
            savedState[index].hide = !savedState[index].hide;
        });

        //save the updated column state to ag grid ref
        decisionDashboardTableRef.current.api.applyColumnState({
            state: _.cloneDeep(savedState),
            applyOrder: true,
        });

        setShowOriginalData((prev) => !prev);
    };

    const onFilterTextBoxChanged = useCallback((text) => {
        setGlobalSearchText(text);
        decisionDashboardTableRef.current.api.setGridOption(
            "quickFilterText",
            text
        );
    }, []);

    const onFilterTextBoxChangedForModal = useCallback((text, forTableRef) => {
        let ref = null;
        if (forTableRef === "productDetailTableRef") {
            setGlobalSearchTextForProductDetail(text);
            ref = productDetailTableRef;
        } else if (forTableRef === "storeDetailTableRef") {
            setGlobalSearchTextForStoreDetail(text);
            ref = storeDetailTableRef;
        }
        ref.current.api.setGridOption(
            "quickFilterText",
            text
        );
    }, []);

    const handleMetricsChange = (selectedOptions) => {
        setSelectedMetrics(selectedOptions);
        setIsSelectAll(
            selectedOptions.length === decisionDashboardMetricsConfig.length
        );
        const selectedOptionsValues = selectedOptions.map((option) => option.value);
        //find unselected metrics
        const hideOptions = decisionDashboardMetricsConfig.filter(
            (option) => !selectedOptionsValues.includes(option.value)
        );
        const selectedOptionsWithCurrency = selectedOptions.map((option) => ({
            label: labelCurrencyHandler(option.label, currency_detail?.currency_symbol || "$"),
            ...option,
        }));

        //get metricsOptions from selected options
        const showMetrics = selectedOptionsWithCurrency.map(
            (option) => option.metricsOptions
        );

        //get metricsOptions from unselected options
        const hideMetrics = hideOptions.map((option) => option.metricsOptions);

        //flatten the metricsOptions, both selected and unselected
        let flattenMetrics = _.flatten(showMetrics);
        let flattenHideMetrics = _.flatten(hideMetrics);

        //if showOriginalData is false, remove original metrics from flattenMetrics and flattenHideMetrics
        if (!showOriginalData) {
            flattenMetrics = flattenMetrics.filter(
                (item) => !item.includes("original")
            );
            flattenHideMetrics = flattenHideMetrics.filter(
                (item) => !item.includes("original")
            );
        }

        const savedState = decisionDashboardTableRef.current.api.getColumnState();

        //iterate through table config and hide/show the columns which are in originalForcastTableColumns
        _.forEach(savedState, (config) => {
            if (flattenMetrics.includes(config?.colId)) {
                config.hide = false;
            }
            if (flattenHideMetrics.includes(config?.colId)) {
                config.hide = true;
            }
        });
        decisionDashboardTableRef.current.api.applyColumnState({
            state: _.cloneDeep(savedState),
            applyOrder: true,
        });
    };

    const onSelectAllHandler = (e) => {
        //if select all is checked, set all options as selected, else clear all selected options.
        if (e.target.checked) {
            handleMetricsChange(decisionDashboardMetricsConfig);
        } else {
            handleMetricsChange([]);
        }
    };

    const onDownloadOffersTableClick = () => {
        let payload = {
            action: "get",
            report_file_name: "decision_dashboard_report_extensive_data",
            report_name: "decision_dashboard_report_extensive_data",
            report_type: "excel",
            target_currency_id: currency_detail?.currency_id,
        };

        const decisionDashboardFilters = _.cloneDeep(
            filtersData.DECISION_DASHBOARD
        );
        // set all selected filters in the payload
        _.forEach(Object.keys(decisionDashboardFilters), (key) => {
            if (key === "dateRange") {
                payload.start_date = moment(
                    decisionDashboardFilters.dateRange?.start_date
                ).format("MM/DD/YYYY");
                payload.end_date = moment(
                    decisionDashboardFilters.dateRange?.end_date
                ).format("MM/DD/YYYY");
            } else {
                payload[key] = decisionDashboardFilters[key];
            }
        });

        payload = {
            ...payload,
            ...fabricatePayloadHierarchy(decisionDashboardFilters),
        }

        let marketing = selectedOfferLevel;
        if (selectedOfferLevel.includes(-200)) {
            marketing = [];
            offerLevel.forEach((ele) => {
                if (ele.key !== -200) {
                    marketing.push(ele.key);
                }
            });
        }

        payload["aggregation"] = {
            product_hierarchy_levels: selectedProductHierarchy,
            store_hierarchy_levels: selectedStoreHierarchy,
            marketing: marketing,
            timeline: selectedTimeHierarchy?.[0], // just this time time hierarchy is single value, if they come back for multiple values, we need to change this
        };
        dispatch(decisionDashboardDownload(payload));
        setIsPanelOpen(false);
    };

    const onRowSelection = useCallback(() => {
        //get selected product groups and set in product
        if (enableDDInlineEdit) return;
        const selectedRows = decisionDashboardTableRef.current.api.getSelectedRows();
        let showWithdrawButton = false,
            showExecuteButton = false;
        //if selected rows are present, check if any of the selected rows have status id 8 or 4
        if (selectedRows?.length) {
            showWithdrawButton = selectedRows.some(
                (row) => row.status_id === 8
            );
            showExecuteButton = selectedRows.some((row) => row.status_id === 4);
        }
        setShowWithdrawButton(showWithdrawButton);
        setShowExecuteButton(showExecuteButton);
        dispatch(setSelectedDecisionDashboardOffers(_.cloneDeep(selectedRows)));
    });

    const withdrawPromo = async (promoIds = []) => {
        //build payload for withdraw
        const withdrawPayload = {
            action: "withdraw",
            promo_ids: promoIds,
            guid: sessionStorage.getItem("UNIQ_SSE_KEY"),
        };
        const withdrawRes = await dispatch(withdrawOffer(withdrawPayload));
        //if withdraw is successful, refresh screen data
        if (withdrawRes) {
            if (withDrawFromEditAction) {
                dispatch(setActivePromoId(promoIds[0]));
                dispatch(
                    setMaxStepCount(
                        decisionDashboardSelectedOffers?.[0].step_count
                    )
                );
                navigate(
                    `/pricesmart-promo/workbench/create-offer?promo=${promoIds[0]}`
                );
                setWithDrawFromEditAction(false);
                return;
            }
            props.resetScreenData();
        }
    };

    const onWithdrawButtonClick = () => {
        //set prompt to false
        setShowWithdrawPrompt(false);
        const promoIds = [];
        let withdrawPromoFlag = false;
        //get all promo ids of selected offers where status id is 8
        //if start date is today or future date, set withdrawPromoFlag to true
        _.forEach(decisionDashboardSelectedOffers, (row) => {
            if (row.status_id === 8) {
                if (moment().isSameOrAfter(moment(row.start_date)))
                    withdrawPromoFlag = true;
                promoIds.push(row.promo_id);
            }
        });
        //if withdrawPromoFlag is true, show error message and return
        if (withdrawPromoFlag) {
            dispatch(
                toastError(`Ongoing/Completed ${global_labels?.promo_alias_plural} cannot be withdrawn!`)
            );
            return;
        }
        //else call withdrawPromo function
        withdrawPromo(promoIds);
    };

    const onExecuteButtonClick = async () => {
        //set prompt to false
        setShowExecutePrompt(false);
        const promoIds = [];
        //get all promo ids of selected offers where status id is 4
        _.forEach(decisionDashboardSelectedOffers, (row) => {
            if (row.status_id === 4) {
                promoIds.push(row.promo_id);
            }
        });
        //build payload for execute
        const executePayload = {
            promo_ids: promoIds,
        };

        const executeRes = await dispatch(executePromo(executePayload));
        //if execute is successful, refresh screen data
        if (executeRes) {
            props.resetScreenData();
        }
    };

    const handleDelete = async () => {
        let isSelectionValid = true;
        setShowDeletePrompt(false);
        const selectedPromoIds = [];
        //check if selected offers have start date as today or past date
        _.forEach(decisionDashboardSelectedOffers, (ele) => {
            if (moment().isSameOrAfter(moment(ele.start_date))) {
                isSelectionValid = false;
            }
            selectedPromoIds.push(ele.promo_id);
        });
        // if start date is today or past date, show error message and return
        if (!isSelectionValid) {
            dispatch(
                toastError(`Ongoing/Completed ${global_labels?.promo_alias_plural} can not be deleted!`)
            );
            return;
        }
        //build payload for delete, and cell the api, once delete is done, refresh screen data
        const deletePromoPayload = {
            promo_ids: selectedPromoIds,
        };
        const isDeleted = dispatch(deletePromo(deletePromoPayload));

        if (isDeleted) {
            props.resetScreenData();
        }
    };

    const onInlineEditButtonClick = async () => {
        let isSelectionValid = true;
        let selectedPromos = [];

        //check if selected offers have start date as today or past date
        _.forEach(decisionDashboardSelectedOffers, (ele) => {
            selectedPromos.push(ele.promo_id);
            if (moment().isSameOrAfter(moment(ele.start_date))) {
                isSelectionValid = false;
            }
        });

        // if start date is today or past date, show error message and return

        if (!isSelectionValid) {
            dispatch(toastError(`Ongoing/Completed ${global_labels?.promo_alias_plural} can not be edited!`));
            return;
        }
        //check if selected offers are allowed to be edited by calling the api
        const isOpAllowed = await dispatch(
            validateOperation({
                promo_id: [...selectedPromos],
            })
        );
        //if selected offers are not allowed to be edited, show error message and return
        if (!isOpAllowed.is_valid) {
            dispatch(toastError(isOpAllowed.message));
            return;
        }
        //set enableDDInlineEdit to true
        dispatch(setEnableInlineEdit(true));
    };

    const onInlineEditUpdateClick = async () => {
        //check if any changes are made, if not show error message and return
        if (!editedDDSelectedRowData.length) {
            dispatch(toastError("No changes are made."));
            return;
        }
        let errMsg = "";

        //check if promo name is empty, if empty show error message and return
        _.forEach(editedDDSelectedRowData, (promo) => {
            if (!promo.promo_name || promo.promo_name === "") {
                errMsg = `${capitalizeFirstLetter(global_labels?.promo_primary)} name cannot be empty.`;
            }
        });
        if (errMsg) {
            dispatch(toastError(errMsg));
            return;
        }
        //build payload for inline edit
        const payloadData = _.map(editedDDSelectedRowData, (data) => {
            return {
                promo_id: data.promo_id,
                comments: data.offer_comment,
                promo_name: data.promo_name,
            };
        });
        //call the api for inline edit, once done, refresh screen data
        const inlineEditResponse = await dispatch(
            inlineEditAPICall({ promos: _.cloneDeep(payloadData) })
        );

        if (inlineEditResponse) {
            props.resetScreenData();
        }
        dispatch(setEnableInlineEdit(false));
    };

    const onInlineEditCancelClick = () => {
        dispatch(setEnableInlineEdit(false));
        decisionDashboardTableRef.current.api.deselectAll();
    };

    const handleEditOfferClick = async () => {
        if (decisionDashboardSelectedOffers?.[0]?.status_id === 8) {
            setShowWithdrawPrompt(true);
            setWithDrawFromEditAction(true);
            return;
        }

        const res = await dispatch(
            validateOperation({
                promo_id: [decisionDashboardSelectedOffers?.[0]?.promo_id],
            })
        );

        if (!res.is_valid) {
            dispatch(toastError(res.message));
            return;
        }

        dispatch(
            setActivePromoId(decisionDashboardSelectedOffers?.[0].promo_id)
        );
        dispatch(
            setMaxStepCount(decisionDashboardSelectedOffers?.[0].step_count)
        );

        if (res) {
            navigate(
                `/pricesmart-promo/workbench/create-offer?promo=${decisionDashboardSelectedOffers?.[0]?.promo_id}`
            );
        }
    };

    const handleViewOfferClick = async () => {
        dispatch(
            setActivePromoId(decisionDashboardSelectedOffers?.[0].promo_id)
        );
        dispatch(
            setMaxStepCount(decisionDashboardSelectedOffers?.[0].step_count)
        );

        navigate(
            `/pricesmart-promo/workbench/create-offer?promo=${decisionDashboardSelectedOffers?.[0]?.promo_id}&view=true`
        );
    };

    const handleViewColsChange = (selectedOptions) => {
        const selectedValues = selectedOptions.map((ele) => ele.value);
        setIsSelectAllForViewCols(
            selectedValues.length === viewColsOptions.length
        );
        const colsToBeAddedStacked = [
            "finalized_stack_promo_spend",
            "finalized_stack_sales_units",
            "finalized_stack_revenue",
            "finalized_stack_margin",
            "finalized_stack_margin_percent",
            "finalized_stack_contribution_margin",
            "finalized_stack_contribution_margin_percent",
            "finalized_stack_incremental_sales_units",
            "finalized_stack_incremental_revenue",
            "finalized_stack_incremental_margin",
            "finalized_stack_st_percent",
        ];
        const colsToBeAddedOriginal = [
            "original_promo_spend",
            "original_sales_units",
            "original_revenue",
            "original_margin",
            "original_margin_percent",
            "original_contribution_margin",
            "original_contribution_margin_percent",
        ];
        const colsDefs = _.cloneDeep(decisionDashboardTableConfig);
        if (
            selectedValues.includes("stacked") ||
            selectedValues.includes("original")
        ) {
            const colsToBeHiddenStacked = [
                "finalized_promo_spend",
                "finalized_sales_units",
                "finalized_revenue",
                "finalized_margin",
                "finalized_margin_percent",
                "finalized_contribution_margin",
                "finalized_contribution_margin_percent",
                "finalized_incremental_sales_units",
                "finalized_incremental_revenue",
                "finalized_incremental_margin",
                "finalized_st_percent",
            ];

            colsDefs.forEach((col) => {
                // as the field only comes in children (in this particular case), we need to check if the column has children, else add the check in the parent if needed
                if (col.children && col.children.length > 0) {
                    col.children.forEach((child) => {
                        if (
                            (colsToBeAddedOriginal.includes(child.colId) &&
                                selectedValues.includes("original")) ||
                            (colsToBeAddedStacked.includes(child.colId) &&
                                selectedValues.includes("stacked"))
                        ) {
                            child.hide = false;
                        }
                        if (
                            colsToBeHiddenStacked.includes(child.colId) &&
                            selectedValues.includes("stacked")
                        ) {
                            child.hide = true;
                        }
                    });
                }
            });
            mustBeModifiesCols(colsDefs);
        } else {
            mustBeModifiesCols(decisionDashboardTableConfig);
        }
        setSelectedViewCols(selectedOptions);
    };

    const onSelectAllHandlerForViewCols = (e) => {
        if (e.target.checked) {
            handleViewColsChange(viewColsOptions);
        } else {
            handleViewColsChange([]);
        }
    };

    // Function to handle chip selection in panel
    // This function updates the selected state for hierarchy chips in the panel
    const handleChipSelect = (id, key, selected, setter) => {
        setter((prev) => {
            if (id === "timeHierarchy" && key !== -200) {
                return [key];
            } else if (key === -200) {
                return prev.includes(-200) ? [] : [-200];
            } else {
                return prev.includes(key)
                    ? prev.filter((v) => v !== key)
                    : [...prev.filter((v) => v !== -200), key];
            }
        });
    };

    const handleMarketingViewBy = (value) => {
        const { marketingViewBy, setMarketingViewBy } = props;
        // If value exists, remove it; if not, add it (toggle behavior)
        const updatedViewBy = marketingViewBy.includes(value)
            ? marketingViewBy.filter((ele) => ele !== value)
            : [...marketingViewBy, value];
        if (updatedViewBy.length == 0) {
            dispatch(
                toastError("Atlest one view by option should be selected")
            );
            return;
        }
        setMarketingViewBy(updatedViewBy);
    };

    const handleTimelineChange = () => {
        const { timeline, setTimeline } = props;
        // Direct toggle between "weekly" and null
        setTimeline(timeline === 1 ? -200 : 1);
    };

    const handleUpdateKPI = () => {
        const payload = {
            ...props.createPayload(),
            promo_ids: decisionDashboardSelectedOffers.map(
                (offer) => offer.promo_id
            ),
        };
        dispatch(callDecisionDashboardTilesAPI(payload));
        decisionDashboardTableRef.current.api.deselectAll();
    };

    const onDownloadProductButtonClick = () => {
        // download products data as excel
        const params = {
            fileName: "products.xlsx",
        };
        productDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const onDownloadStoreButtonClick = () => {
        // download stores data as excel
        const params = {
            fileName: "stores.xlsx",
        };
        storeDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const handleFilterOptions = (filteredOptions,key) => {
        setFilteredOptions(prevState => ({
            ...prevState,
            [key]: filteredOptions
        }));
    }

	const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

    return (
        <div className="marginTop-20">
            <Table
                ref={decisionDashboardTableRef}
                tableHeader={`${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`}
                suppressMenuHide
                rowData={decisionDashboardTableData}
                columnDefs={labelCurrencyHandlerForTableDefs(tableColDefs, currency_detail?.currency_symbol || "$")}
                rowSelection="multiple"
                onSelectionChanged={onRowSelection}
                suppressRowClickSelection={true}
                groupDefaultExpanded={-1}
                // groupUseEntireRow={false}
                // groupSuppressAutoColumn={true}
                hideRowCollapseIcon={true}
                groupDisplayType={"custom"}
                groupHideOpenParents={true}
                topRightOptions={
                    enableDDInlineEdit ? (
                        <div className="centerFlexWithGap12">
                            <Button
                                onClick={onInlineEditUpdateClick}
                                size="large"
                                variant="primary"
                            >
                                Update
                            </Button>
                            <Button
                                onClick={onInlineEditCancelClick}
                                size="large"
                                variant="secondary"
                            >
                                Cancel
                            </Button>
                        </div>
                    ) : (
                        <div className="centerFlexWithGap12">
                            {!decisionDashboardSelectedOffers.length ? (
                                <div className="centerFlexWithGap12">
                                    <Select
                                        currentOptions={
                                            filteredOptions?.["metrics"] || decisionDashboardMetricsConfig
                                        }
                                        initialOptions={
                                            decisionDashboardMetricsConfig
                                        }
                                        label="Metrics"
                                        labelOrientation="left"
                                        setSelectedOptions={handleMetricsChange}
                                        setCurrentOptions={(data) => handleFilterOptions(data,"metrics")}
                                        placeholder="Select.."
                                        isRequired={false}
                                        isWithSearch={false}
                                        isMulti={true}
                                        isSelectAll={isSelectAll}
                                        onSelectAll={onSelectAllHandler}
                                        setIsSelectAll={setIsSelectAll}
                                        selectedOptions={selectedMetrics}
                                        isOpen={isOpen}
                                        setIsOpen={setIsOpen}
                                        isCloseWhenClickOutside={true}
                                        toggleSelectAll
                                    />
                                    <Select
                                        currentOptions={
                                            filteredOptions?.["show"] || viewColsOptions
                                        }
                                        initialOptions={viewColsOptions}
                                        label="Show"
                                        labelOrientation="left"
                                        setSelectedOptions={
                                            handleViewColsChange
                                        }
                                        setCurrentOptions={(data) => handleFilterOptions(data,"show")}
                                        placeholder="Select.."
                                        isRequired={false}
                                        isWithSearch={false}
                                        isMulti={true}
                                        isSelectAll={isSelectAllForViewCols}
                                        onSelectAll={
                                            onSelectAllHandlerForViewCols
                                        }
                                        setIsSelectAll={
                                            setIsSelectAllForViewCols
                                        }
                                        selectedOptions={selectedViewCols}
                                        isOpen={isViewColsOpen}
                                        setIsOpen={setIsViewColsOpen}
                                        isCloseWhenClickOutside={true}
                                        toggleSelectAll
                                    />
                                    <div className="horizontal-line" />
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={DownloadIcon}
                                                alt="download"
                                            />
                                        }
                                        onClick={() => setIsPanelOpen(true)}
                                        size="large"
                                        variant="tertiary"
                                    />
                                </div>
                            ) : null}

                            {decisionDashboardSelectedOffers.length ? (
                                <div className="centerFlexWithGap12">
                                    <Button
                                        onClick={handleUpdateKPI}
                                        size="large"
                                        variant="secondary"
                                    >
                                        Update KPI's
                                    </Button>
                                    <Button
                                        onClick={onInlineEditButtonClick}
                                        size="large"
                                        variant="secondary"
                                    >
                                        Inline Edit
                                    </Button>
                                    {showWithdrawButton ? (
                                        <Button
                                            onClick={() =>
                                                setShowWithdrawPrompt(true)
                                            }
                                            size="large"
                                            variant="tertiary"
                                        >
                                            Withdraw
                                        </Button>
                                    ) : null}
                                    {showExecuteButton ? (
                                        <Button
                                            onClick={() =>
                                                setShowExecutePrompt(true)
                                            }
                                            size="large"
                                            variant="primary"
                                        >
                                            Execute
                                        </Button>
                                    ) : null}
                                    <div className="horizontal-line" />
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={DeleteIcon}
                                                alt="delete"
                                            />
                                        }
                                        onClick={() =>
                                            setShowDeletePrompt(true)
                                        }
                                        size="large"
                                        variant="secondary"
                                        type="destructive"
                                        className="delete-button"
                                    />
                                </div>
                            ) : null}
                            {decisionDashboardSelectedOffers.length === 1 ? (
                                <div className="centerFlexWithGap12">
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={ViewIcon} alt="view" />}
                                        onClick={() => {
                                            handleViewOfferClick();
                                        }}
                                        size="large"
                                        variant="tertiary"
                                    />
                                    <Button
                                        iconPlacement="left"
                                        icon={<img src={EditIcon} alt="edit" />}
                                        onClick={() => {
                                            handleEditOfferClick();
                                        }}
                                        size="large"
                                        variant="tertiary"
                                    />
                                    <div className="horizontal-line" />
                                </div>
                            ) : null}
                            <div className="positionRelative">
                                {showGlobalSearch ? (
                                    <div className="tableGlobalSearchContainer">
                                        <Input
                                            onChange={(e) =>
                                                onFilterTextBoxChanged(
                                                    e.target.value
                                                )
                                            }
                                            placeholder="Search"
                                            rightIcon={
                                                <img
                                                    src={SearchIcon}
                                                    alt="search"
                                                />
                                            }
                                            type="text"
                                            value={globalSearchText}
                                        />
                                    </div>
                                ) : null}
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={SearchIcon} alt="search" />}
                                    onClick={() =>
                                        setShowGlobalSearch((prev) => !prev)
                                    }
                                    size="large"
                                    variant="tertiary"
                                />
                            </div>
                        </div>
                    )
                }
                topLeftOptions={
                    global_configs?.event?.use_event ? (
                        <div className="centerFlexWithGap12">
                            <hr className="vertical-divider " />
                            <div className="centerFlexWithGap12">
                                <Badge
                                    color={
                                        props.marketingViewBy.includes(
                                            "event_id"
                                        )
                                            ? "info"
                                            : "default"
                                    }
                                    label={global_labels.event_primary}
                                    onClick={() => {
                                        handleMarketingViewBy("event_id");
                                    }}
                                    variant="stroke"
                                />
                                <Badge
                                    color={
                                        props.marketingViewBy.includes(
                                            "promo_id"
                                        )
                                            ? "info"
                                            : "default"
                                    }
                                    label={capitalizeFirstLetter(global_labels?.promo_primary)}
                                    onClick={() => {
                                        handleMarketingViewBy("promo_id");
                                    }}
                                    variant="stroke"
                                />
                                <Badge
                                    color={
                                        props.timeline == 1 ? "info" : "default"
                                    }
                                    label={"Week"}
                                    onClick={() => {
                                        handleTimelineChange();
                                    }}
                                    variant="stroke"
                                />
                            </div>
                        </div>
                    ) : null
                }
            />
            <Prompt
                handleClose={() => {
                    setShowWithdrawPrompt(false);
                    setWithDrawFromEditAction(false);
                }}
                onPrimaryButtonClick={onWithdrawButtonClick}
                onSecondaryButtonClick={() => {
                    setShowWithdrawPrompt(false);
                    setWithDrawFromEditAction(false);
                }}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title={`Confirm withdrawal and edit of selected ${global_labels?.promo_alias}`}
                variant="warning"
                isOpen={showWithdrawPrompt}
            >
                {withDrawFromEditAction
                    ? `Are you sure you want to withdraw this ${global_labels?.promo_alias} and continue editing?`
                    : `Are you sure you want to withdraw these ${global_labels?.promo_alias_plural}?`}
            </Prompt>
            <Prompt
                handleClose={() => setShowExecutePrompt(false)}
                onPrimaryButtonClick={onExecuteButtonClick}
                onSecondaryButtonClick={() => setShowExecutePrompt(false)}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title={`Confirm Execution of ${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`}
                variant="warning"
                isOpen={showExecutePrompt}
            >
                Are you sure you want to execute these {global_labels?.promo_alias_plural}?
            </Prompt>
            <Prompt
                handleClose={() => setShowDeletePrompt(false)}
                onPrimaryButtonClick={handleDelete}
                onSecondaryButtonClick={() => setShowDeletePrompt(false)}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title={`Confirm Deletion of ${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`}
                variant="warning"
                isOpen={showDeletePrompt}
            >
                Are you sure you want to delete these {global_labels?.promo_alias_plural}?
            </Prompt>
            {isPanelOpen && (
                <Panel
                    anchor="right"
                    open={isPanelOpen}
                    onClose={() => setIsPanelOpen(false)}
                    onPrimaryButtonClick={onDownloadOffersTableClick}
                    onSecondaryButtonClick={() => setIsPanelOpen(false)}
                    primaryButtonLabel="Download"
                    secondaryButtonLabel="Cancel"
                    size="large"
                    title={
                        <div className="panel-title-with-icon">
                            <img
                                src={levelFilterIconInPanel}
                                alt="Level Filters"
                                className="panel-icon"
                            />
                            <span>Level Filters</span>
                        </div>
                    }
                >
                    <div className="aggregation-panel-content">
                        {hierarchyConfig.map(
                            ({ id, title, labels, selected, setter }) => (
                                <div key={id} className="hierarchy-chip-group">
                                    <h4>{title}</h4>
                                    <div className="chip-wrapper">
                                        {labels.map(({ label, key }) => (
                                            <Badge
                                                key={label}
                                                label={label}
                                                variant="stroke"
                                                color={
                                                    selected.includes(key)
                                                        ? "info"
                                                        : "default"
                                                }
                                                onClick={() =>
                                                    handleChipSelect(
                                                        id,
                                                        key,
                                                        selected,
                                                        setter
                                                    )
                                                }
                                            />
                                        ))}
                                    </div>
                                </div>
                            )
                        )}
                    </div>
                </Panel>
            )}
            <Modal
                open={productDetailModalOpen}
                onClose={() => {
                    setProductDetailModalOpen(false);
                    setGlobalSearchTextForProductDetail("");
                    setShowGlobalSearchForProductDetail(false);
                }}
                onPrimaryButtonClick={() => {
                    setProductDetailModalOpen(false);
                    setGlobalSearchTextForProductDetail("");
                    setShowGlobalSearchForProductDetail(false);
                }}
                primaryButtonLabel="Ok"
                size="medium"
                title="Product Details"
                className="product-store-detail-modal"
            >
                <Table
                    ref={productDetailTableRef}
                    suppressMenuHide
                    rowData={productDetailTableData}
                    columnDefs={productTableConfig(hierarchyGlobalKeys)}
                    suppressRowClickSelection={true}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <div className="centerFlexWithGap12">
                            <div className="positionRelative">
                                {showGlobalSearchForProductDetail ? (
                                    <div className="tableGlobalSearchContainer">
                                        <Input
                                            onChange={(e) =>
                                                onFilterTextBoxChangedForModal(
                                                    e.target.value,
                                                    "productDetailTableRef"
                                                )
                                            }
                                            placeholder="Search"
                                            rightIcon={
                                                <img
                                                    src={SearchIcon}
                                                    alt="search"
                                                />
                                            }
                                            type="text"
                                            value={globalSearchTextForProductDetail}
                                        />
                                    </div>
                                ) : null}
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={SearchIcon} alt="search" />}
                                    onClick={() =>
                                        setShowGlobalSearchForProductDetail((prev) => !prev)
                                    }
                                    size="large"
                                    variant="tertiary"
                                />
                            </div>
                            <Button
                                iconPlacement="left"
                                icon={<img src={DownloadIcon} alt="download product" />}
                                onClick={onDownloadProductButtonClick}
                                size="large"
                                variant="tertiary"
                            />
                        </div>
                    }
                />
            </Modal>
            <Modal
                open={storeDetailModalOpen}
                onClose={() => {
                    setStoreDetailModalOpen(false);
                    setGlobalSearchTextForStoreDetail("");
                    setShowGlobalSearchForStoreDetail(false);
                }}
                onPrimaryButtonClick={() => {
                    setStoreDetailModalOpen(false);
                    setGlobalSearchTextForStoreDetail("");
                    setShowGlobalSearchForStoreDetail(false);
                }}
                primaryButtonLabel="Ok"
                size="medium"
                title="Store Details"
                className="product-store-detail-modal"
            >
                <Table
                    ref={storeDetailTableRef}
                    suppressMenuHide
                    rowData={storeDetailTableData}
                    columnDefs={storeTableConfig(hierarchyGlobalKeys)}
                    suppressRowClickSelection={true}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <div className="centerFlexWithGap12">
                            <div className="positionRelative">
                                {showGlobalSearchForStoreDetail ? (
                                    <div className="tableGlobalSearchContainer">
                                        <Input
                                            onChange={(e) =>
                                                onFilterTextBoxChangedForModal(
                                                    e.target.value,
                                                    "storeDetailTableRef"
                                                )
                                            }
                                            placeholder="Search"
                                            rightIcon={
                                                <img src={SearchIcon} alt="search" />
                                            }
                                            type="text"
                                            value={globalSearchTextForStoreDetail}
                                        />
                                    </div>
                                ) : null}
                                <Button
                                    iconPlacement="left"
                                    icon={<img src={SearchIcon} alt="search" />}
                                    onClick={() =>
                                        setShowGlobalSearchForStoreDetail((prev) => !prev)
                                    }
                                    size="large"
                                    variant="tertiary"
                                />
                            </div>
                            <Button
                                iconPlacement="left"
                                icon={<img src={DownloadIcon} alt="download store" />}
                                onClick={onDownloadStoreButtonClick}
                                size="large"
                                variant="tertiary"
                            />
                        </div>
                    }
                />
            </Modal>
        </div>
    );
};

export default DecisionDashboardTable;
