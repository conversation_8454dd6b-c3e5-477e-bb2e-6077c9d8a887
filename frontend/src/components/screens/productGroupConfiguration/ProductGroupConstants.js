import { dateFormatter, toCurrencyByCurrencyId } from "../../../utils/helpers/formatter";
import { capitalizeFirstLetter, replace<PERSON><PERSON><PERSON><PERSON>haracter } from "../../../utils/helpers/utility_helpers";
import { CellsWithLinkandIcon } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import { productGroupLinkClick } from "../../../store/features/productConfigurationReducer/productConfigurationReducer";

import { containerStore } from "../../../store";
import { global_labels, HYPERLINK_PRODUCT_TABLE_CONFIG } from "../../../constants/Constants";


export const productTableConfiguation = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 60,
    },
    {
        field: "product_group_name",
        headerName: "Group Name",
        pinned: "left",
        isSearchable: true,
        filter: "agTextColumnFilter",
        cellRenderer: CellsWithLinkandIcon,
        cellRendererParams: {
            onClick: (data) => containerStore.dispatch(productGroupLinkClick(data)),
            icons: {
                processing: {
                    key: "group_under_process",
                    trueValue: 1,
                }
            }
        },
        width: 250,
    },
    {
        field: "product_group_description",
        headerName: "Description",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "products_count",
        headerName: "Products",
        isSearchable: true,
        filter: "agTextColumnFilter",
        type: "number",
    },
    {
        field: "product_group_type",
        headerName: "Product Group Type",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l0_name",
        headerName: "Division",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l1_name",
        headerName: "Group",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l2_name",
        headerName: "Department",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_by_user",
        headerName: "Created By",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "created_at",
        headerName: "Created On",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 172,
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "modified_by_user",
        headerName: "Modified By",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 172,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "modified_at",
        headerName: "Modified On",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 172,
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "promos_count",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 150,
        type: "number",
    },
]
export const promoBasicTableConfiguation = [
    {
        headerName: `${capitalizeFirstLetter(global_labels?.promo_primary)} Name`,
        field: "promo_name",
    },
    {
        headerName: "Start Date",
        field: "start_date",
    },
    {
        headerName: "End Date",
        field: "end_date",
    },
    {
        headerName: "Status",
        field: "promo_status",
    },
    {
        headerName: "Created By",
        field: "created_by",
    },
]

export const productTableConfig = (hierarchyGlobalKeys) => ([
    {
        field: "client_product_id",
        headerName: "Product ID",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "product_name",
        headerName: "Description",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 300,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "current_price",
        headerName: "Current Price",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
        type: "number",
    },
	{
		field: "cost",
		headerName: "Cost",
		isSearchable: true,
		filter: "agTextColumnFilter",
		width: 140,
		valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
		type: "number",
	},
    {
        field: "total_inventory",
        headerName: "Inventory",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
    },
    ...HYPERLINK_PRODUCT_TABLE_CONFIG(hierarchyGlobalKeys),
])