import React from "react";
import { useDispatch } from "react-redux";
import ComponentFilters from "../../../ui/componentFilters/ComponentFilters";
import { createWholeCategoryFilterConfig } from "./CreateProductGroupConstants";
import { resetAllFiltersData } from "../../../../store/features/filters/filters";

function CreateWholeCategoryProducts() {
	const dispatch = useDispatch();
	const onClearFilter = () => {
		// clear filters data for whole category product configuration
		dispatch(
			resetAllFiltersData({
				from: "CREATE_WHOLE_CATEGORY_PRODUCT_CONFIG_COMPONENT",
			})
		);
	};
	return (
		<ComponentFilters
			filterConfig={createWholeCategoryFilterConfig}
			callAPIonLoad={false}
			screen="CREATE_WHOLE_CATEGORY_PRODUCT_CONFIG_COMPONENT"
			onSecondaryButtonClick={onClearFilter}
			secondaryButtonText="Clear Filters"
		/>
	);
}

export default CreateWholeCategoryProducts;
