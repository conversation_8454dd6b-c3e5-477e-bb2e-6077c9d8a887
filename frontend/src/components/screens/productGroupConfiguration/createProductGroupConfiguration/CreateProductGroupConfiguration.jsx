import React, { useState, useEffect, useRef, useCallback } from "react";
import { Button, Input, Modal, RadioButtonGroup } from "impact-ui";
import { Table } from "impact-ui-v3";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";
import _ from "lodash";

import ScreenBreadcrumb from "../../../common/breadCrumb/ScreenBreadcrumb";
import CreateSpecificProductsConfig from "./CreateSpecificProductsConfig";
import CreateWholeCategoryProducts from "./CreateWholeCategoryProducts";

import { breadcrumbRoutes } from "../../../../constants/RouteConstants";
import {
    capitalizeFirstLetter,
    replaceSpecialCharToCharCode,
    replaceSpecialCharacter,
} from "../../../../utils/helpers/utility_helpers";
import {
    createProductGroupConfig,
    resetCreateProductConfigScreenData,
    resetEditProductGroupFlag,
    getProductGroupData,
    getEffectedGroupsData,
} from "../../../../store/features/productConfigurationReducer/productConfigurationReducer";
import {
    resetAllFiltersData,
    getFilterOptions,
} from "../../../../store/features/filters/filters";
import {
    effectedPlansColumnConfig,
    createWholeCategoryFilterConfig,
} from "./CreateProductGroupConstants";
import "./CreateProductConfiguration.scss";
import { global_labels } from "../../../../constants/Constants";

function CreateProductGroupConfiguration(props) {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const tableRef = useRef(null);
    const [productGroupName, setProductGroupName] = useState("");
    const [description, setDescription] = useState("");
    const [effectedPlansModal, setEffectedPlansModal] = useState(false);
    const [effectedPlansData, setEffectedPlansData] = useState([]);
    const [selectedEffectedPlans, setSelectedEffectedPlans] = useState([]);
    const [wholeCatOrSpecificProd, setWholeCatOrSpecificProd] = useState(
        "wholeCategory"
    );

    const {
        selectedCreateProductConfigTableData = [],
        selectedProductGroupConfigData = [],
    } = useSelector(
        (product) => product?.pricesmartPromoReducer.productConfiguration
    );

    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    useEffect(() => {
        dispatch(resetEditProductGroupFlag());
        // reset filters and screen data on component unmount
        return () => {
            dispatch(resetCreateProductConfigScreenData());
            dispatch(
                resetAllFiltersData({
                    from: "CREATE_PRODUCT_CONFIG_COMPONENT",
                })
            );
            dispatch(
                resetAllFiltersData({
                    from: "CREATE_WHOLE_CATEGORY_PRODUCT_CONFIG_COMPONENT",
                })
            );
        };
    }, []);

    useEffect(async () => {
        // if user is editing existing product group, fetch product group data
        if (selectedProductGroupConfigData.length === 1) {
            const response = await dispatch(
                getProductGroupData({
                    product_group_id:
                        selectedProductGroupConfigData[0].product_group_id,
                })
            );
            // set product group name and description if data is fetched
            if (response) {
                const wholeCategory = response?.isWholeCategory
                    ? "wholeCategory"
                    : "specificProducts";
                setWholeCatOrSpecificProd(wholeCategory);
                setProductGroupName(response?.groupName || "");
                setDescription(response?.description || "");
                // if whole category is selected, fetch filters data
                if (response?.isWholeCategory)
                    callWholeCategoryFilters(response?.hierarchy);
            }
        }
    }, [selectedProductGroupConfigData]);

    const callWholeCategoryFilters = (selectedData) => {
        //for edit product group, when whole category is selected, fetch filters data
        for (let i = 0; i < createWholeCategoryFilterConfig.length; i++) {
            const filterConfig = createWholeCategoryFilterConfig[i];
            const key = filterConfig.filterId;
            const currentFilters = {};
            //find previous filters data, and send in payload
            for (let j = i - 1; j >= 0; j--) {
                const prevFilters = createWholeCategoryFilterConfig[j];
                if (
                    prevFilters?.filterId &&
                    selectedData[prevFilters.filterId]
                ) {
                    currentFilters[prevFilters.filterId] = _.map(
                        selectedData?.[prevFilters.filterId] || [],
                        (data) => data.value
                    );
                }
            }
            //get selected options array to set the selected filters on tool
            const selectedOptionsArray = _.map(
                selectedData?.[key] || [],
                (data) => data.value
            );
            const payload = {
                hierarchy_filters: _.cloneDeep(currentFilters),
                query_column: filterConfig.filterId,
            };
            dispatch(
                getFilterOptions({
                    requestObject: _.cloneDeep(payload),
                    filterEndpoint: filterConfig?.apiEndpoint,
                    from: "CREATE_WHOLE_CATEGORY_PRODUCT_CONFIG_COMPONENT",
                    selectedItems: selectedOptionsArray,
                    selectOnLoad: true,
                    filterName: filterConfig.filterId,
                })
            );
        }
    };

    const onCreateNewGroupClick = async () => {
        // if user is creating new product group, call new group API
        if (!selectedProductGroupConfigData?.length) {
            callNewGroupAPI();
            return;
        }

        // else fetch effected plans on editing the product group data
        let effectedGroupPayload = {
            id: selectedProductGroupConfigData[0]?.product_group_id,
        };
        const response = await dispatch(
            getEffectedGroupsData(effectedGroupPayload)
        );
        // if effects plans exists, show modal to show effected plans
        if (response?.length) {
            setEffectedPlansData(_.cloneDeep(response));
            setEffectedPlansModal(true);
            return;
        }
        // else save edited product group data
        callNewGroupAPI();
    };

    const callNewGroupAPI = async () => {
        // create payload for new product group API
        const payload = {
            application: "promo",
            is_hierarchy_level: 0,
            product_hierarchy: {},
            product_group_name: replaceSpecialCharToCharCode(productGroupName),
            product_group_description: replaceSpecialCharToCharCode(
                description
            ),
        };

        let method = "POST";

        if (wholeCatOrSpecificProd === "wholeCategory") {
            const wholeCategoryFiltersData =
                filtersData?.CREATE_WHOLE_CATEGORY_PRODUCT_CONFIG_COMPONENT;
            // get selected filters for whole category
            _.forEach(Object.keys(wholeCategoryFiltersData), (key) => {
                payload["product_hierarchy"][key] =
                    wholeCategoryFiltersData[key]?.selectedOptionsArray;
            });
            payload["is_hierarchy_level"] = 1;
        } else {
            // get selected product ids for specific products
            payload["product_ids"] = selectedCreateProductConfigTableData?.map(
                (data) => data.product_id
            );
        }

        // if user is editing existing product group, add product group id and guid in payload
        if (selectedProductGroupConfigData?.length) {
            payload["product_group_id"] =
                selectedProductGroupConfigData[0].product_group_id;
            payload["guid"] = sessionStorage.getItem("UNIQ_SSE_KEY");
            payload["selected_plans"] = _.cloneDeep(selectedEffectedPlans);
            method = "PUT";
        }

        // call new product group API
        await dispatch(createProductGroupConfig(payload, method));
        navigate("/pricesmart-promo/product-group-config");
    };

    const onBackButtonClick = () => {
        // navigate to product group configuration screen
        navigate("/pricesmart-promo/product-group-config");
    };

    const onRowSelection = useCallback(() => {
        // get selected effected plans
        const selectedRows = tableRef.current.api.getSelectedRows();
        setSelectedEffectedPlans(_.cloneDeep(selectedRows));
    });

    const onEffectedPlansContinueClick = () => {
        // if user decides to continue with effected plans, save product group data
        setEffectedPlansModal(false);
        callNewGroupAPI();
    };

    const onGridReady = useCallback(() => {
        // select all effected plans on grid ready for modal table
        tableRef.current.api.selectAll();
    });

    const onEffectedPlansCancelClick = () => {
        // if user decides to cancel effected plans, close modal and reset effected plans data
        setEffectedPlansModal(false);
        setEffectedPlansData([]);
        setSelectedEffectedPlans([]);
    };

    const setWholeCatOrSpecificProdHandler = (e) => {
        // set whole category or specific products
        setWholeCatOrSpecificProd(e.target.value);
    };

    const getSubmitButtonDisabledState = () => {
        // check if submit button should be disabled
        let isDisabled = !productGroupName || !description;
        if (wholeCatOrSpecificProd === "wholeCategory") {
            // if whole category is selected, check if all filters are empty
            const wholeCategoryFiltersData =
                filtersData?.CREATE_WHOLE_CATEGORY_PRODUCT_CONFIG_COMPONENT;
            let isFiltersEmpty = true;
            if (!_.isEmpty(wholeCategoryFiltersData)) {
                _.forEach(Object.keys(wholeCategoryFiltersData), (key) => {
                    if (
                        wholeCategoryFiltersData[key]?.selectedOptionsArray
                            ?.length
                    ) {
                        isFiltersEmpty = false;
                    }
                });
            }
            isDisabled = isDisabled || isFiltersEmpty;
        } else {
            // if specific products is selected, check if selected products are empty
            isDisabled =
                isDisabled || !selectedCreateProductConfigTableData.length;
        }
        return isDisabled;
    };

	return (
		<div>
			<ScreenBreadcrumb
				breadcrumbList={breadcrumbRoutes()?.["createProductConfiguration"]}
			></ScreenBreadcrumb>
			<div className="screen_data_container paddingTop-12">
				<div className="productConfigContainer">
					<div className="content_container_border">
						<label className="text-16-800">Details</label>
						<div className="paddingTop-16 flex24">
							<Input
								id="product_detail_name"
								inputProps={{}}
								label="Product Group Name"
								name=""
								onChange={(e) =>
									setProductGroupName(e.target.value)
								}
								placeholder="Please enter name"
								type="text"
								isRequired={true}
								value={replaceSpecialCharacter(productGroupName)}
							/>
							<Input
								id="product_detail_description"
								inputProps={{}}
								label="Description"
								name=""
								onChange={(e) => setDescription(e.target.value)}
								placeholder="Please enter description"
								type="text"
								isRequired={true}
								value={replaceSpecialCharacter(description)}
							/>
						</div>
					</div>
					<div className="content_container marginTop-20">
						<span className="text-16-800">Product Selection</span>
						<div className="marginTop-24 marginBottom-24">
							<RadioButtonGroup
								isDisabled={
									selectedProductGroupConfigData.length
								}
								name="ia-test-radio-group"
								onChange={setWholeCatOrSpecificProdHandler}
								options={[
									{
										label: "Whole Category",
										value: "wholeCategory",
									},
									{
										label: "Specific Products",
										value: "specificProducts",
									},
								]}
								orientation="row"
								selectedOption={wholeCatOrSpecificProd}
							/>
						</div>
						{wholeCatOrSpecificProd === "wholeCategory" ? (
							<CreateWholeCategoryProducts />
						) : (
							<CreateSpecificProductsConfig />
						)}
					</div>
				</div>
			</div>
			<Modal
				onClose={() => onEffectedPlansCancelClick()}
				onPrimaryButtonClick={onEffectedPlansContinueClick}
				onSecondaryButtonClick={onEffectedPlansCancelClick}
				primaryButtonLabel="Continue"
				secondaryButtonLabel="Cancel"
				size="medium"
				title="Are you sure?"
				open={effectedPlansModal}
			>
				<div className="invalidProductsGrpModalContainer">
					<span className="secondaryText-14-500">
						The following Strategies/{capitalizeFirstLetter(global_labels?.promo_alias_plural)} will be affected by this
						change. Please unselect the Strategies/{capitalizeFirstLetter(global_labels?.promo_alias_plural)} you want
						to exclude from this change.
					</span>
					<Table
						tableHeader={"Effected Plans"}
						ref={tableRef}
						rowSelection="multiple"
						onSelectionChanged={onRowSelection}
						suppressMenuHide
						rowData={effectedPlansData}
						onGridReady={onGridReady}
						columnDefs={effectedPlansColumnConfig}
					/>
				</div>
			</Modal>
			<div className="footer_section">
				<Button
					onClick={onBackButtonClick}
					size="large"
					variant="secondary"
				>
					Back
				</Button>
				<Button
					onClick={onCreateNewGroupClick}
					size="large"
					variant="primary"
					disabled={getSubmitButtonDisabledState()}
				>
					Submit Product Group
				</Button>
			</div>
		</div>
	);
}

export default CreateProductGroupConfiguration;
