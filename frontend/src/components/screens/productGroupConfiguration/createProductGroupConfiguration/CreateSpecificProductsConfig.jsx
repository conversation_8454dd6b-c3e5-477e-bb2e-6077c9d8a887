import React, { useState, useEffect, useRef, useCallback } from "react";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import {
	ButtonGroup,
	FileUpload,
	TextArea,
	Prompt,
	Modal,
	Button,
	Input,
	Badge,
} from "impact-ui";
import { Table } from "impact-ui-v3";
import ComponentFilters from "../../../ui/componentFilters/ComponentFilters";
import EmptyData from "../../../ui/emptyData/EmptyData";
import {
	resetAllFiltersData,
	overwriteFilters,
} from "../../../../store/features/filters/filters";
import {
	filterTypeButton,
	createSpecificProductFilterConfig,
	createProductTableConfig,
	invalidDataColumnConfig,
} from "./CreateProductGroupConstants";
import {
	callCreateProductConfigFilterTableAPI,
	setSelectedCreateProductConfigTableData,
	getCreateProductGrpDataFromExcel,
	getCreateProductGrpDataFromCopyPaste,
} from "../../../../store/features/productConfigurationReducer/productConfigurationReducer";

import { fabricatePayloadHierarchy, mergeFiltersData } from "../../../../utils/helpers/utility_helpers";
import DownloadIcon from "../../../../assets/imageAssets/downloadIcon.svg?.url";
import SearchIcon from "../../../../assets/imageAssets/searchIcon.svg?.url";
import "./CreateProductConfiguration.scss";

import {
	excelTemplateDownload,
	toastError,
} from "../../../../store/features/global/global";

function CreateSpecificProductsConfig(props) {
	const dispatch = useDispatch();
	const tableRef = useRef();

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);
	const {
		createProductConfigTableData = [],
		selectedCreateProductConfigTableData = [],
		tableDataFromUploadorCopyPaste = {},
	} = useSelector(
		(store) => store?.pricesmartPromoReducer.productConfiguration
	);

	const [showGlobalSearch, setShowGlobalSearch] = useState(false);
	const [globalSearchText, setGlobalSearchText] = useState("");
	const [selectedFilterType, setSelectedFilterType] = useState(
		"selectFilters"
	);
	const [productTableData, setProductTableData] = useState([]);
	const [uploadedExcel, setUploadedExcel] = useState([]);
	const [lastFiltersData, setLastFiltersData] = useState({});
	const [showDataOverridePrompt, setTableDataOverridePrompt] = useState(
		false
	);
	const [showInvalidDataModal, setShowInvalidDataModal] = useState(false);
	const [invalidTableData, setInvalidTableData] = useState([]);
	const [copyPasteData, setCopyPasteData] = useState("");
	const [tempTableData, setTempTableData] = useState([]);
	const [flagToStopRepeatedSetData, setFlagToStopRepeatedSetData] = useState(false);


	useEffect(() => {
		// if product data is fetched from API by selecting the filters, set it in table
		// if (
		// 	createProductConfigTableData &&
		// 	selectedFilterType === "selectFilters"
		// ) {
		// 	// if there is no previous data in table, set the fetched data
		// 	if (!productTableData?.length) {
		// 		setProductTableData(createProductConfigTableData);
		// 	} else {
		// 		// if there is previous data in table, show prompt to retain or replace
		// 		setTableDataOverridePrompt(true);
		// 	}
		// }
		if (!_.isEmpty(createProductConfigTableData)) {
			// if there is no previous data in table, set the fetched data
			handleSetStoreTableData(createProductConfigTableData);
		}
	}, [createProductConfigTableData]);

	useEffect(() => {
		// if product data is fetched from API by uploading excel or copy paste, set it in table
		if (
			(selectedFilterType === "uploadExcel" ||
				selectedFilterType === "copyPaste") &&
			!_.isEmpty(tableDataFromUploadorCopyPaste)
		) {
			// if there are invalid or inactive products, show modal with invalid and inactive products
			if (
				tableDataFromUploadorCopyPaste?.invalid?.length ||
				tableDataFromUploadorCopyPaste?.inactive?.length
			) {
				const newTableData = [
					...tableDataFromUploadorCopyPaste?.invalid,
					...tableDataFromUploadorCopyPaste?.inactive,
				];
				setInvalidTableData(_.cloneDeep(newTableData));
				setShowInvalidDataModal(true);
			} else {
				// if there are only valid products, set it in table
				handleSetStoreTableData(tableDataFromUploadorCopyPaste.valid);
			}
		}
	}, [tableDataFromUploadorCopyPaste]);

	const handleSetStoreTableData = (data) => {
		if (flagToStopRepeatedSetData) {
			return;
		}
		if (productTableData?.length) {
			setTableDataOverridePrompt(true);
			setTempTableData(data);
		} else if (
			(selectedFilterType === "upload" || selectedFilterType === "copy_paste")
		) {
			setProductTableData(data);
		} else
			setProductTableData(data);
		setFlagToStopRepeatedSetData(true);
	};

	const onComponentFiltersApply = () => {
		let callAPI = true;
		// check if all mandatory filters are selected
		const filtersDataSelected = _.cloneDeep(
			filtersData["CREATE_PRODUCT_CONFIG_COMPONENT"]
		);
		_.forEach(createSpecificProductFilterConfig, (config) => {
			const key = config.filterId;
			if (
				config.isMandatory &&
				!filtersDataSelected?.[key]?.selectedOptionsArray?.length
			) {
				callAPI = false;
			}
		});
		if (!callAPI) {
			dispatch(toastError("Please select all mandatory filters"));
			return;
		}
		// set last filters data for replace and retain prompt
		if (_.isEmpty(lastFiltersData))
			setLastFiltersData(_.cloneDeep(filtersDataSelected));
		// if all mandatory filters are selected, call API to get product data
		const payload = fabricatePayloadHierarchy(filtersDataSelected);
		setFlagToStopRepeatedSetData(false);
		dispatch(callCreateProductConfigFilterTableAPI(payload));
	};

	const onClearFilter = () => {
		// clear filters data for product configuration
		dispatch(
			resetAllFiltersData({
				from: "CREATE_PRODUCT_CONFIG_COMPONENT",
			})
		);
	};

	const onRowSelection = useCallback(() => {
		// set selected products data
		const selectedRows = tableRef.current.api.getSelectedRows();
		dispatch(
			setSelectedCreateProductConfigTableData(_.cloneDeep(selectedRows))
		);
	});

	const rowDataChanged = () => {
		// whenever row data updates in AG Grid, select all rows
		if (tableRef?.current?.api) tableRef.current.api.selectAll();
	};

	const onReplaceAndOverwriteClick = () => {
		// replace table data with current selection and set last filters data same as current selection
		setProductTableData(tempTableData);
		setTableDataOverridePrompt(false);
		// adding this as the dispatch will trigger the useEffect then the handleSetStoreTableData will be called
		// and it will again set the same data in table, so we need to stop that
		setFlagToStopRepeatedSetData(true);
		setTempTableData([]);
		setLastFiltersData(filtersData["CREATE_PRODUCT_CONFIG_COMPONENT"]);
	};

	const onRetainClick = () => {
		//concat current data with previous data, and set it in table
		let currentRows = _.cloneDeep(productTableData);
		const tempRows = _.cloneDeep(tempTableData || []).map(item => ({
			...item,
			// convert product_id to number as on upload it is string, and _.uniqBy is not working with string
			product_id: Number(item.product_id)
		}));
		currentRows = [
			...currentRows,
			...tempRows,
		];
		currentRows = _.uniqBy(currentRows, "product_id");
		setProductTableData(currentRows);
		setTableDataOverridePrompt(false);
		setTempTableData([]);
		// concat currennt data with previous data and set both last filters and current filters as same
		setFlagToStopRepeatedSetData(true);
		const updatedFiltersData = mergeFiltersData(
			_.cloneDeep(lastFiltersData),
			_.cloneDeep(filtersData["CREATE_PRODUCT_CONFIG_COMPONENT"])
		);
		setLastFiltersData(_.cloneDeep(updatedFiltersData));
		dispatch(
			overwriteFilters({
				filtersData: _.cloneDeep(updatedFiltersData),
				activeScreen: "CREATE_PRODUCT_CONFIG_COMPONENT",
			})
		);
	};

	const onFilterTypeTabChange = (e) => {
		// set selected filter type and reset all filters data if filter type is not select filters
		setSelectedFilterType(e.target.value);
		if (e.target.value !== "selectFilters") {
			dispatch(
				resetAllFiltersData({
					from: "CREATE_PRODUCT_CONFIG_COMPONENT",
				})
			);
		}
		setUploadedExcel([]);
		setCopyPasteData("");
	};

	const onUploadExcelNextClick = async () => {
		// on next click of excel upload, call API to get product data
		const formData = new FormData();
		formData.append("file", uploadedExcel[0]?.file);
		setFlagToStopRepeatedSetData(false);
		const responseFlag = await dispatch(
			getCreateProductGrpDataFromExcel(formData)
		);
		// if product data is not fetched, reset uploaded excel data
		if (!responseFlag) {
			setUploadedExcel([]);
		}
	};

	const onActiveInactiveProductProceed = () => {
		// on proceed click of invalid products modal, set valid and inactive products in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		const newTableData = [
			...tableDataFromUploadorCopyPaste?.valid || [],
			...tableDataFromUploadorCopyPaste?.inactive || [],
		];
		handleSetStoreTableData(newTableData);
	};

	const onActiveProductProceed = () => {
		// on proceed click of invalid products modal, set only valid products in table
		setInvalidTableData([]);
		setShowInvalidDataModal(false);
		handleSetStoreTableData(tableDataFromUploadorCopyPaste.valid || []);
	};

	const onCopyPasteChange = (e) => {
		// set copy paste data in state
		const data = e.target.value;
		let array = _.filter(data.split("\n"), (item) => item !== "");
		let val = array.join(",");
		setCopyPasteData(val);
	};

	const onCopyPasteSubmitClick = () => {
		// on submit click of copy paste, call API to get product data
		let productIds = copyPasteData.split(",");
		productIds = _.map(productIds, (value) => value.trim());
		productIds = _.filter(productIds, (item) => item !== "").map(item => [...item.split(" ")]);
		const payload = { products_data: productIds };
		setFlagToStopRepeatedSetData(false);
		setCopyPasteData("");
		dispatch(getCreateProductGrpDataFromCopyPaste(payload));
	};

	const excelDownloadHandler = () => {
		// download excel template for product selection
		const payload = {
			type: "promo_product_upload",
		};
		dispatch(
			excelTemplateDownload(
				{
					params: payload,
					responseType: "blob",
				},
				"product_selection_template"
			)
		);
	};

	const onDownloadButtonClick = () => {
		// download products data as excel
		const params = {
			fileName: "products.xlsx",
		};
		tableRef.current.api.exportDataAsExcel(params);
	};

	const onFilterTextBoxChanged = useCallback((text) => {
		setGlobalSearchText(text);
		tableRef.current.api.setGridOption("quickFilterText", text);
	}, []);

	return (
		<div>
			<div className="productConfigSelectionContainer marginTop-24">
				<ButtonGroup
					selectedOption={selectedFilterType}
					onChange={onFilterTypeTabChange}
					options={filterTypeButton}
				/>
				<p className="text-12-500 marginTop-12">
					Please select one of the methods for uploading data to get
					results
				</p>
				<div className="marginTop-24">
					{selectedFilterType === "selectFilters" && (
						<div className="flexColumn flex24">
							<span className="text-14-800">Select Filter</span>
							<ComponentFilters
								filterConfig={createSpecificProductFilterConfig}
								callAPIonLoad={false}
								screen="CREATE_PRODUCT_CONFIG_COMPONENT"
								onPrimaryButtonClick={onComponentFiltersApply}
								onSecondaryButtonClick={onClearFilter}
								primaryButtonText="Submit"
								secondaryButtonText="Clear Filters"
							/>
						</div>
					)}
					{selectedFilterType === "uploadExcel" && (
						<FileUpload
							fileList={uploadedExcel}
							numberOfFiles={1}
							primaryButtonLabel="Submit"
							secondaryButtonLabel="Cancel"
							onSecondaryButtonClick={() => setUploadedExcel([])}
							onFileListChange={setUploadedExcel}
							onPrimaryButtonClick={onUploadExcelNextClick}
							validFileTypes={[
								{
									fileType: "xlsx",
									templateDownloader: excelDownloadHandler,
									typeOverride: false,
								},
							]}
						/>
					)}
					{selectedFilterType === "copyPaste" && (
						<div>
							<TextArea
								onChange={onCopyPasteChange}
								placeholder="Paste IDs here..."
								value={copyPasteData}
								width={"80vw"}
							/>
							<div className="buttons_container">
								<Button
									onClick={() => setCopyPasteData("")}
									size="large"
									variant="url"
								>
									Reset
								</Button>
								<Button
									onClick={onCopyPasteSubmitClick}
									size="large"
									variant="primary"
									disabled={!copyPasteData?.length}
								>
									Submit
								</Button>
							</div>
						</div>
					)}
				</div>
				<div className="marginTop-24">
					{productTableData?.length ? (
						<div>
							<Table
								tableHeader={"Products"}
								ref={tableRef}
								suppressMenuHide
								rowData={productTableData}
								columnDefs={createProductTableConfig}
								rowSelection="multiple"
								onSelectionChanged={onRowSelection}
								onRowDataUpdated={rowDataChanged}
								topRightOptions={
									<div className="centerFlexWithGap12">
										<div className="positionRelative">
											{showGlobalSearch ? (
												<div className="tableGlobalSearchContainer">
													<Input
														onChange={(e) =>
															onFilterTextBoxChanged(
																e.target.value
															)
														}
														placeholder="Search"
														rightIcon={
															<img
																src={SearchIcon}
															/>
														}
														type="text"
														value={globalSearchText}
													/>
												</div>
											) : null}
											<Button
												iconPlacement="left"
												icon={<img src={SearchIcon} />}
												onClick={() =>
													setShowGlobalSearch(
														(prev) => !prev
													)
												}
												size="large"
												variant="tertiary"
											/>
										</div>
										<Button
											iconPlacement="left"
											icon={<img src={DownloadIcon} />}
											onClick={onDownloadButtonClick}
											size="large"
											variant="tertiary"
										/>
									</div>
								}
								topLeftOptions={
									<Badge
										color="default"
										label={`Selected Products: ${selectedCreateProductConfigTableData?.length} / ${productTableData?.length}`}
										size="default"
										variant="subtle"
									/>
								}
							/>
						</div>
					) : (
						<EmptyData text="Please Select Filters to view Products" />
					)}
				</div>
			</div>
			<Prompt
				handleClose={() => {
					setProductTableData([]);
					setTableDataOverridePrompt(false);
				}}
				onPrimaryButtonClick={onRetainClick}
				onSecondaryButtonClick={onReplaceAndOverwriteClick}
				primaryButtonLabel="Retain"
				secondaryButtonLabel="Replace & overwrite"
				title="Confirm Selection"
				variant="warning"
				isOpen={showDataOverridePrompt}
			>
				Do you want to Retain and Continue or Replace table with current
				selection
			</Prompt>

			<Modal
				onClose={() => { setShowInvalidDataModal(false) }}
				onPrimaryButtonClick={onActiveInactiveProductProceed}
				onSecondaryButtonClick={onActiveProductProceed}
				primaryButtonLabel="Proceed with all Active and Inactive Products"
				secondaryButtonLabel="Proceed with only Active Products"
				size="medium"
				title="Products Detail"
				open={showInvalidDataModal}
			>
				<div className="invalidProductsGrpModalContainer">
					<span className="secondaryText-14-500">
						Based on the data feed Pricesmart received, the uploaded
						list contains some inactive/invalid SKUs
					</span>
					<Table
						tableHeader={"Products"}
						suppressMenuHide
						rowData={invalidTableData}
						columnDefs={invalidDataColumnConfig}
					/>
				</div>
			</Modal>
		</div>
	);
}

export default CreateSpecificProductsConfig;
