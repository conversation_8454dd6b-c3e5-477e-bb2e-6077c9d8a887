import { capitalizeFirstLetter, replaceSpec<PERSON><PERSON><PERSON><PERSON>, dateFormatter } from "../../../../utils/helpers/utility_helpers";
import { toCurrencyByCurrencyId } from "../../../../utils/helpers/formatter";
import { global_labels, PRODUCT_SELECTION_VALID_INVALID_TABLE } from "../../../../constants/Constants";
import { wholeProductFilterConfig } from "../../../../constants/FilterConfigConstants";

export const filterTypeButton = [
    {
        label: "Choose Filters",
        value: "selectFilters",
    },
    {
        label: "Upload XL",
        value: "uploadExcel",
    },
    {
        label: "Copy Paste Data",
        value: "copyPaste",
    },
]

export const createSpecificProductFilterConfig = [
    ...wholeProductFilterConfig.map(item => ({
        ...item,
        selectOnLoad: false,
        selection: null,
    })),

]

export const createWholeCategoryFilterConfig = [
    ...wholeProductFilterConfig.map(item => ({
        ...item,
        isMandatory: false,
        selectOnLoad: false,
        selection: null,
    })),

]

export const createProductTableConfig = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "client_product_id",
        headerName: "Product ID",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "product_name",
        headerName: "Description",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 300,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "msrp",
        headerName: "MSRP",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 150,
        valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
        type: "number",
    },
    {
        field: "current_price",
        headerName: "Current Price",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
        type: "number",
    },
    {
        field: "oh_inventory",
        headerName: "Inventory",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
        type: "number",
    },
    {
        field: "cost",
        headerName: "Cost",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 140,
        valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
        type: "number",
    },
    {
        field: "launch_price",
        headerName: "Launch Price",
        isSearchable: true,
        filter: "agTextColumnFilter",
        flex: 1,
        valueFormatter: (params) => toCurrencyByCurrencyId({ value: params.value, ...params.data }),
        type: "number",
    },
]

export const invalidDataColumnConfig = [
    ...PRODUCT_SELECTION_VALID_INVALID_TABLE,
    {
        field: "status",
        headerName: "Status",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
        flex: 1
    },
]

export const effectedPlansColumnConfig = [
    {
        field: "",
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        suppressMenu: true,
        filter: false,
        sortable: false,
        pinned: "left",
        maxWidth: 50,
    },
    {
        field: "plan",
        headerName: "Plan",
        pinned: "left",
        width: 150,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "plan_name",
        headerName: "Plan Name",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "start_date",
        headerName: "Start Date",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "end_date",
        headerName: "End Date",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => dateFormatter(params),
    },
    {
        field: "plan_status",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Status`,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "user_name",
        headerName: "Created By",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
        width: 150,
        flex: 1,
    },
]