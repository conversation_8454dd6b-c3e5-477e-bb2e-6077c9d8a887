import React, { useState, useCallback, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom-v5-compat";
import _ from "lodash";
import { Button, ProgressBar, Input, Modal, Prompt } from "impact-ui";
import { Table } from "impact-ui-v3";
import ScreenBreadcrumb from "../../common/breadCrumb/ScreenBreadcrumb";

import FilterWrapper from "../../common/filters/FilterWrapper";
import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import DeleteIcon from "../../../assets/imageAssets/deleteIcon.svg?.url";
import EditIcon from "../../../assets/imageAssets/editIcon.svg?.url";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";

import { breadcrumbRoutes } from "../../../constants/RouteConstants";
import {
    productGroupConfigurationFilterConfig as filterConfig,
    productGroupConfigurationRequiredFiltersOnLoad as requiredFiltersOnLoad,
} from "../../../constants/FilterConfigConstants";

import {
    productTableConfiguation,
    promoBasicTableConfiguation,
    productTableConfig,
} from "./ProductGroupConstants";
import {
    callProductConfigTableAPI,
    callProductConfigUngroupedDetailsAPI,
    setSelectedProductGroupConfigData,
    getProductGroupProcessStatus,
    productGroupDownload,
    deleteProductGroup,
    getProductDetailsForProductGroup,
} from "../../../store/features/productConfigurationReducer/productConfigurationReducer";

import EmptyData from "../../ui/emptyData/EmptyData";
import { CellOnClickButton } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";
import {
    requestComplete,
    requestStart,
    toastError,
} from "../../../store/features/global/global";
import { API } from "../../../utils/axios";
import {
    capitalizeFirstLetter,
    fabricatePayloadHierarchy,
    replaceSpecialCharacter,
    replaceSpecialCharToCharCode,
} from "../../../utils/helpers/utility_helpers";
import { global_labels } from "../../../constants/Constants";

function ProductGroupConfiguration() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const tableRef = useRef(null);
    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const {
        productConfigurationTableData = [],
        ungroupedProductDetails,
        selectedProductGroupConfigData = [],
        editProductGroupFlag = false,
    } = useSelector(
        (store) => store?.pricesmartPromoReducer.productConfiguration
    );

    const { hierachy_keys } = useSelector(
        (store) => store?.pricesmartPromoReducer.global
    );

    const [showFiltersSection, setShowFiltersSection] = useState(true);
    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [showDeletePrompt, setShowDeletePrompt] = useState(false);

    const [modifiedColumn, setModifiedColumn] = useState(null);
    const [showModal, setShowModal] = useState(false);
    const [dialogTableData, setDialogTableData] = useState([]);
    const [productDetailModalOpen, setProductDetailModalOpen] = useState(false);
    const [productDetailTableData, setProductDetailTableData] = useState([]);
    const productDetailTableRef = useRef(null);
    useEffect(async () => {
        if (editProductGroupFlag) {
            await onEditProductGroupClick();
        }
    }, [editProductGroupFlag]);

    useEffect(() => {
        if (productTableConfiguation.length) {
            const coldef = _.cloneDeep(productTableConfiguation);
            const col = coldef.find((ele) => ele.field == "promos_count");
            const col_products = coldef.find(
                (ele) => ele.field == "products_count"
            );
            if (col) {
                col["cellRenderer"] = (props) => (
                    <CellOnClickButton {...props} clickHandler={clickHandler} />
                );
            }
            if (col_products) {
                col_products["cellRenderer"] = (props) => (
                    <CellOnClickButton
                        {...props}
                        clickHandler={getProduDetails}
                    />
                );
            }
            setModifiedColumn(coldef);
        }
    }, [productTableConfiguation]);

    const getProduDetails = async (data) => {
        const productDetails = await dispatch(
            getProductDetailsForProductGroup({
                product_group_id: data.product_group_id,
            })
        );
        if (productDetails) {
            setProductDetailTableData(productDetails);
            setProductDetailModalOpen(true);
        }
    };

    const onFilterApply = () => {
        //extract filters data for product configuration from product
        const productConfigFilters = _.cloneDeep(
            filtersData.PRODUCT_CONFIGURATION
        );
        let payload = fabricatePayloadHierarchy(productConfigFilters);

        dispatch(callProductConfigTableAPI(payload));
        //add only_meta_data flag for ungrouped product details payload to get only meta data
        payload.only_meta_data = true;
        dispatch(callProductConfigUngroupedDetailsAPI(payload));
    };

    const onCreateNewGroupClick = () => {
        //navigate to create product group screen
        navigate("/pricesmart-promo/product-group-config/create-product-group");
    };

    const onEditProductGroupClick = async () => {
        //fetch selected product group id and call API to get process status
        const selectedProductGroup = selectedProductGroupConfigData[0];
        const payload = {
            product_group_id: selectedProductGroup.product_group_id,
        };
        const response = await dispatch(getProductGroupProcessStatus(payload));
        // if process not ongoing then navigate to edit product group screen
        if (response)
            navigate(
                "/pricesmart-promo/product-group-config/create-product-group"
            );
    };

    const onRowSelection = useCallback(() => {
        //get selected product groups and set in product
        const selectedRows = tableRef.current.api.getSelectedRows();
        dispatch(setSelectedProductGroupConfigData(_.cloneDeep(selectedRows)));
    });

    const onDownloadProductGroupClick = () => {
        //download product group details
        //extract filters data for product configuration from product
        const productConfigFilters = _.cloneDeep(
            filtersData.PRODUCT_CONFIGURATION
        );

        //create payload for downlad API
        let payload = {
            ...fabricatePayloadHierarchy(productConfigFilters),
        };

        dispatch(productGroupDownload(payload));
    };

    const onFilterTextBoxChanged = useCallback((text) => {
        const encodedText = replaceSpecialCharToCharCode(text);
        setGlobalSearchText(encodedText);
        tableRef.current.api.setGridOption("quickFilterText", encodedText);
    }, []);

    const handleDelete = async () => {
        setShowDeletePrompt(false);

        //build payload for delete, and cell the api, once delete is done, refresh screen data
        const payload = {
            product_group_id: selectedProductGroupConfigData?.map(
                (ele) => ele.product_group_id
            ),
        };
        const isDeleted = await dispatch(deleteProductGroup(payload));

        if (isDeleted) {
            onFilterApply();
            dispatch(setSelectedProductGroupConfigData([]));
        }
    };

    const clickHandler = async (data) => {
        if (data?.["product_group_id"]) {
            dispatch(requestStart());
            await API.get(`product-groups/${data?.["product_group_id"]}/promos`)
                .then((res) => {
                    dispatch(requestComplete());
                    setShowModal(true);
                    const { data } = res;
                    setDialogTableData(data.data);
                })
                .catch((err) => {
                    dispatch(requestComplete());
                    dispatch(toastError("Somthing went wrong"));
                });
        }
    };

    React.useEffect(() => {
        dispatch(setSelectedProductGroupConfigData([]));
    }, []);

    const onDownloadProductButtonClick = () => {
        // download products data as excel
        const params = {
            fileName: "products.xlsx",
        };
        productDetailTableRef.current.api.exportDataAsExcel(params);
    };

    const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

    return (
        <div className="screen_container">
            <ScreenBreadcrumb
                breadcrumbList={breadcrumbRoutes()?.["productConfiguration"]}
            >
                <Button
                    iconPlacement="left"
                    onClick={() => {
                        setShowFiltersSection((prev) => !prev);
                    }}
                    size="large"
                    variant="secondary"
                >
                    {showFiltersSection ? "Hide" : "Show"} Filters
                </Button>
            </ScreenBreadcrumb>
            <FilterWrapper
                defaultOpen="product_hierarchy"
                screen="PRODUCT_CONFIGURATION"
                callAPIonLoad={true}
                filterConfig={filterConfig}
                requiredFiltersOnLoad={requiredFiltersOnLoad}
                onFilterApply={onFilterApply}
                showFiltersSection={showFiltersSection}
            />
            <div className="screen_data_container flex1 paddingTop-20">
                {productConfigurationTableData?.length ? (
                    <div>
                        <Table
                            ref={tableRef}
                            tableHeader={"Product Groups"}
                            suppressMenuHide
                            rowData={productConfigurationTableData || []}
                            columnDefs={modifiedColumn || []}
                            rowSelection="multiple"
                            onSelectionChanged={onRowSelection}
                            suppressRowClickSelection={true}
                            topRightOptions={
                                <div className="centerFlexWithGap12">
                                    {!selectedProductGroupConfigData?.length && (
                                        <>
                                            <ProgressBar
                                                customLabel={`${ungroupedProductDetails?.ungrouped_count}(${ungroupedProductDetails?.ungrouped_percentage}%) products are not in any group`}
                                                value={
                                                    ungroupedProductDetails?.ungrouped_percentage ||
                                                    0
                                                }
                                                showTime={false}
                                            />
                                            <div className="horizontal-line" />

                                            <Button
                                                iconPlacement="left"
                                                icon={
                                                    <img src={DownloadIcon} />
                                                }
                                                onClick={
                                                    onDownloadProductGroupClick
                                                }
                                                size="large"
                                                variant="tertiary"
                                            />
                                        </>
                                    )}
                                    {selectedProductGroupConfigData.length ===
                                        1 ? (
                                        <Button
                                            iconPlacement="left"
                                            icon={<img src={EditIcon} />}
                                            onClick={onEditProductGroupClick}
                                            size="large"
                                            variant="tertiary"
                                        />
                                    ) : null}
                                    {selectedProductGroupConfigData.length ? (
                                        <Button
                                            iconPlacement="left"
                                            icon={<img src={DeleteIcon} />}
                                            onClick={() =>
                                                setShowDeletePrompt(true)
                                            }
                                            size="large"
                                            variant="secondary"
                                            type="destructive"
                                            className="delete-button"
                                        />
                                    ) : null}
                                    {!selectedProductGroupConfigData.length ? (
                                        <div className="centerFlexWithGap12">
                                            <div className="positionRelative">
                                                {showGlobalSearch ? (
                                                    <div className="tableGlobalSearchContainer">
                                                        <Input
                                                            onChange={(e) =>
                                                                onFilterTextBoxChanged(
                                                                    e.target
                                                                        .value
                                                                )
                                                            }
                                                            placeholder="Search"
                                                            rightIcon={
                                                                <img
                                                                    src={
                                                                        SearchIcon
                                                                    }
                                                                />
                                                            }
                                                            type="text"
                                                            value={replaceSpecialCharacter(
                                                                globalSearchText
                                                            )}
                                                        />
                                                    </div>
                                                ) : null}
                                                <Button
                                                    iconPlacement="left"
                                                    icon={
                                                        <img src={SearchIcon} />
                                                    }
                                                    onClick={() =>
                                                        setShowGlobalSearch(
                                                            (prev) => !prev
                                                        )
                                                    }
                                                    size="large"
                                                    variant="tertiary"
                                                />
                                            </div>
                                            <div className="horizontal-line" />
                                            <Button
                                                onClick={onCreateNewGroupClick}
                                                size="large"
                                                variant="primary"
                                            >
                                                Create New Group
                                            </Button>
                                        </div>
                                    ) : null}
                                </div>
                            }
                        />
                    </div>
                ) : (
                    <div>
                        <div className="display-flex-end marginBottom-20">
                            <Button
                                onClick={onCreateNewGroupClick}
                                size="large"
                                variant="primary"
                            >
                                Create New Group
                            </Button>
                        </div>
                        <EmptyData text="Please Select Filters to view Product Groups" />
                    </div>
                )}
            </div>
            <Modal
                open={showModal}
                onClose={() => setShowModal(false)}
                title={`${capitalizeFirstLetter(global_labels?.promo_alias_plural)} that are part of this group`}
            >
                <Table
                    rowData={dialogTableData || []}
                    columnDefs={promoBasicTableConfiguation || []}
                />
            </Modal>
            <Modal
                open={productDetailModalOpen}
                onClose={() => {
                    setProductDetailModalOpen(false);
                }}
                onPrimaryButtonClick={() => {
                    setProductDetailModalOpen(false);
                }}
                primaryButtonLabel="Ok"
                size="medium"
                title="Product Details"
                className="product-store-detail-modal"
            >
                <Table
                    ref={productDetailTableRef}
                    suppressMenuHide
                    rowData={productDetailTableData}
                    columnDefs={productTableConfig(hierarchyGlobalKeys)}
                    suppressRowClickSelection={true}
                    height={"400px"}
                    className="product-store-detail-table"
                    topRightOptions={
                        <Button
                            iconPlacement="left"
                            icon={<img src={DownloadIcon} />}
                            onClick={onDownloadProductButtonClick}
                            size="large"
                            variant="tertiary"
                        />
                    }
                />
            </Modal>
            <Prompt
                handleClose={() => setShowDeletePrompt(false)}
                onPrimaryButtonClick={handleDelete}
                onSecondaryButtonClick={() => setShowDeletePrompt(false)}
                primaryButtonLabel="Proceed"
                secondaryButtonLabel="Cancel"
                title="Confirm deletion of product groups"
                variant="warning"
                isOpen={showDeletePrompt}
            >
                Are you sure you want to delete these product groups?
            </Prompt>
        </div>
    );
}

export default ProductGroupConfiguration;
