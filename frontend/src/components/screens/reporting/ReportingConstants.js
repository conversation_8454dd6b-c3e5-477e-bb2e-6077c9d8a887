import moment from "moment";
import { capitalizeFirstLetter, replace<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../utils/helpers/utility_helpers";
import {
    yyyyMmDdFormatter,
    toUnit,
    toPercentage,
    toCurrencyByCurrencyId,
} from "../../../utils/helpers/formatter";
import { global_labels } from "../../../constants/Constants";
import { cellsWithBadge } from "../../../utils/helpers/tableHelpers/tableCellRendererHelpers";

export const offerLevel = [
    {
        label: "Overall",
        key: -200,
    },
    {
        label: "Offer",
        key: 0,
    },
];

export const defaultHiddenColumns = {
    Division: "division",
    Group: "group",
    Department: "department",
    Class: "class",
    Day: "date",
    Week: "week_start_date",
    Country: "country",
    Channel: "channel",
    "Offer Name": "offer_name",
    FOB: "fob",
};

export const defaultProductHierarchy = ["Division", "Group", "Department"];
export const defaultStoreHierarchy = ["Channel"];
export const defaultTimeHierarchy = ["Day"];
export const defaultOfferLevel = ["Offer"];
export const defaultAggregationLevel = "o0--p0--p1--p2--s1--t0";

export const reportingMetricsTableConfig = [
    {
        field: "Report Level",
        headerName: "Report Level",
        colId: "report_level",
        isSearchable: true,
        filter: "agTextColumnFilter",
    },
    {
        field: "Offer Name",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Name`,
        colId: "offer_name",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "Event Name", // this a field name not a label to make it dynamic
        headerName: `${capitalizeFirstLetter(global_labels?.event_primary)} Name`,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => {
            if (params.value) {
                return replaceSpecialCharacter(params.value)
            } else {
                return "-"
            }
        },
    },
    {
        field: "actual_performance",
		width: 160,
		headerName: "Actual Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "finalized_performance",
		width: 160,
		headerName: "Finalized Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
    {
        field: "Division",
        headerName: "Division",
        colId: "division",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 160,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "DMM",
        headerName: "Group",
        colId: "group",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "Dept",
        headerName: "Department",
        colId: "department",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 180,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "Class",
        headerName: "Class",
        colId: "class",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "FOB",
        headerName: "FOB",
        colId: "fob",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "Country",
        headerName: "Country",
        colId: "country",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 152,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "Channel",
        headerName: "Channel",
        colId: "channel",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 152,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "Date",
        headerName: "Date",
        colId: "date",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 140,
        valueFormatter: (params) => yyyyMmDdFormatter(params),
    },
    {
        field: "Week Start Date",
        headerName: "Week",
        colId: "week_start_date",
        hide: true,
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 152,
        valueFormatter: (params) => yyyyMmDdFormatter(params),
    },
    {
        field: "Actual ST%",
        headerName: "Actual ST%",
        colId: "actual_st_percent",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => toPercentage(params),
        type: "number",
    },
    {
        field: "Actual BOP Inventory",
        headerName: "Actual BOP Inventory",
        colId: "actual_bop_inventory",
        isSearchable: true,
        filter: "agTextColumnFilter",
        width: 150,
        valueFormatter: (params) => toUnit(params),
        type: "number",
    },
    {
        field: "Actual Discount",
        headerName: "Actual Discount",
        colId: "actual_discount",
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => toPercentage(params),
        type: "number",
        // flex: 1,
    },
    {
        headerName: "ASP",
        colId: "asp",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual ASP",
                headerName: "Actual",
                colId: "actual_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted ASP",
                headerName: "Forecasted",
                colId: "forecasted_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var FC ASP",
                headerName: "Var FC",
                colId: "var_fc_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year ASP",
                headerName: "Last Year",
                colId: "last_year_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LY ASP",
                headerName: "Var LY",
                colId: "var_ly_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Plan ASP",
                headerName: "Plan",
                colId: "plan_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var Plan ASP",
                headerName: "Var Plan",
                colId: "var_plan_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week ASP",
                headerName: "Last Week",
                colId: "last_week_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LW ASP",
                headerName: "Var LW",
                colId: "var_lw_asp",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
    {
        headerName: "AUM",
        colId: "aum",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual AUM",
                headerName: "Actual",
                colId: "actual_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted AUM",
                headerName: "Forecasted",
                colId: "forecasted_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var FC AUM",
                headerName: "Var FC",
                colId: "var_fc_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year AUM",
                headerName: "Last Year",
                colId: "last_year_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LY AUM",
                headerName: "Var LY",
                colId: "var_ly_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Plan AUM",
                headerName: "Plan",
                colId: "plan_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var Plan AUM",
                headerName: "Var Plan",
                colId: "var_plan_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week AUM",
                headerName: "Last Week",
                colId: "last_week_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LW AUM",
                headerName: "Var LW",
                colId: "var_lw_aum",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
    {
        headerName: "Sales $",
        colId: "sales",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual Sales $",
                headerName: "Actual",
                colId: "actual_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Actual Incremental Sales $",
                headerName: "Actual Incremental",
                colId: "actual_incremental_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Actual Var Plan Sales $",
                headerName: "Actual Var Plan",
                colId: "actual_var_plan_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted Sales $",
                headerName: "Forecasted",
                colId: "forecasted_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Finalized Incremental Sales $",
                headerName: "Finalized Incremental",
                colId: "finalized_incremental_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var FC Sales $",
                headerName: "Var FC",
                colId: "var_fc_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year Sales $",
                headerName: "Last Year",
                colId: "last_year_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LY Sales $",
                headerName: "Var LY",
                colId: "var_ly_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "LY Var Plan Sales $",
                headerName: "Var LY Plan",
                colId: "ly_var_plan_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week Sales $",
                headerName: "Last Week",
                colId: "last_week_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LW Sales $",
                headerName: "Var LW",
                colId: "var_lw_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "LW Var Plan Sales $",
                headerName: "Var LW Plan",
                colId: "lw_var_plan_sales",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
    {
        headerName: "Sales Units",
        colId: "sales_units",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual Sales U",
                headerName: "Actual",
                colId: "actual_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toUnit(params),
                type: "number",
                hide: false,
            },
            {
                field: "Actual Incremental Sales U",
                headerName: "Actual",
                colId: "actual_incremental_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toUnit(params),
                type: "number",
                hide: false,
            },
            {
                field: "Actual Var Plan Sales U",
                headerName: "Var Plan",
                colId: "actual_var_plan_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted Sales U",
                headerName: "Forecasted",
                colId: "forecasted_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => toUnit(params),
                type: "number",
                hide: false,
            },
            {
                field: "Finalized Incremental Sales U",
                headerName: "Forecasted",
                colId: "finalized_incremental_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => toUnit(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var FC Sales U",
                headerName: "Var FC",
                colId: "var_fc_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year Sales U",
                headerName: "Last Year",
                colId: "last_year_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toUnit(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var LY Sales U",
                headerName: "Var LY",
                colId: "var_ly_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "LY Var Plan Sales U",
                headerName: "Var LY Plan",
                colId: "ly_var_plan_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week Sales U",
                headerName: "Last Week",
                colId: "last_week_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toUnit(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var LW Sales U",
                headerName: "Var LW",
                colId: "var_lw_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "LW Var Plan Sales U",
                headerName: "Var LW Plan",
                colId: "lw_var_plan_sales_units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
    {
        headerName: "GM $",
        colId: "gm",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual GM $",
                headerName: "Actual",
                colId: "actual_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Actual Incremental GM $",
                headerName: "Actual Incremental",
                colId: "actual_incremental_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Actual Var Plan GM $",
                headerName: "Var Plan",
                colId: "actual_var_plan_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted GM $",
                headerName: "Forecasted",
                colId: "forecasted_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Finalized Incremental GM $",
                headerName: "Finalized Incremental",
                colId: "finalized_incremental_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var FC GM $",
                headerName: "Var FC",
                colId: "var_fc_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year GM $",
                headerName: "Last Year",
                colId: "last_year_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LY GM $",
                headerName: "Var LY",
                colId: "var_ly_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "LY Var Plan GM $",
                headerName: "Var LY Plan",
                colId: "ly_var_plan_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week GM $",
                headerName: "Last Week",
                colId: "last_week_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LW GM $",
                headerName: "Var LW",
                colId: "var_lw_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "LW Var Plan GM $",
                headerName: "Var LW Plan",
                colId: "lw_var_plan_gm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
    {
        headerName: "GM %",
        colId: "gm_percent",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual GM %",
                headerName: "Actual",
                colId: "actual_gm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted GM %",
                headerName: "Forecasted",
                colId: "forecasted_gm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var FC GM %",
                headerName: "Var FC",
                colId: "var_fc_gm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                //valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year GM %",
                headerName: "Last Year",
                colId: "last_year_gm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var LY GM %",
                headerName: "Var LY",
                colId: "var_ly_gm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                // valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week GM %",
                headerName: "Last Week",
                colId: "last_week_gm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var LW GM %",
                headerName: "Var LW",
                colId: "var_lw_gm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                // valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
    {
        headerName: "CM $",
        colId: "cm",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual CM $",
                headerName: "Actual",
                colId: "actual_cm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted CM $",
                headerName: "Forecasted",
                colId: "forecasted_cm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var FC CM $",
                headerName: "Var FC",
                colId: "var_fc_cm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year CM $",
                headerName: "Last Year",
                colId: "last_year_cm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LY CM $",
                headerName: "Var LY",
                colId: "var_ly_cm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week CM $",
                headerName: "Last Week",
                colId: "last_week_cm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
                hide: false,
            },
            {
                field: "Var LW CM $",
                headerName: "Var LW",
                colId: "var_lw_cm",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
    {
        headerName: "CM %",
        colId: "cm_percent",
        flex: 1,
        hide: false,
        children: [
            {
                field: "Actual CM %",
                headerName: "Actual",
                colId: "actual_cm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Forecasted CM %",
                headerName: "Forecasted",
                colId: "forecasted_cm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var FC CM %",
                headerName: "Var FC",
                colId: "var_fc_cm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                // valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Year CM %",
                headerName: "Last Year",
                colId: "last_year_cm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var LY CM %",
                headerName: "Var LY",
                colId: "var_ly_cm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                // valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Last Week CM %",
                headerName: "Last Week",
                colId: "last_week_cm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
            {
                field: "Var LW CM %",
                headerName: "Var LW",
                colId: "var_lw_cm_percent",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                // valueFormatter: (params) => toPercentage(params),
                type: "number",
                hide: false,
            },
        ],
    },
];

export const offerPerformanceMetricsTableConfig = [
    {
        field: "promo_name",
        headerName: `${capitalizeFirstLetter(global_labels?.promo_alias)} Name`,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "event_name",
        headerName: global_labels.event_primary + " Name", 
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => {
            if (params.value) {
                return replaceSpecialCharacter(params.value)
            } else {
                return "-"
            }
        },
    },
    {
        field: "start_date",
        headerName: "Start Date",
        width: 150,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => yyyyMmDdFormatter(params),
    },
    {
        field: "end_date",
        headerName: "End Date",
        width: 150,
        isSearchable: true,
        filter: "agTextColumnFilter",
        valueFormatter: (params) => yyyyMmDdFormatter(params),
    },
    {
        field: "actual_performance",
		width: 160,
		headerName: "Actual Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
	{
		field: "finalized_performance",
		width: 160,
		headerName: "Finalized Performance",
		isSearchable: true,
		filter: "agTextColumnFilter",
		cellRenderer: cellsWithBadge,
		cellRendererParams: {
			color: {
				Dilutive: "error",
				Average: "warning",
				Good: "success",
			},
		},
	},
    {
        headerName: "Incremental",
        children: [
            {
                field: "actualized_incremental_margin",
                headerName: "Margin",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 148,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
            },
            {
                field: "actualized_incremental_revenue",
                headerName: "Revenue",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
            },
            {
                field: "actualized_incremental_sales_units",
                headerName: "Sales Units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => toUnit(params),
                type: "number",
            },
        ],
    },
    {
        headerName: "Actual",
        children: [
            {
                field: "actualized_promo_spend",
                headerName: `${capitalizeFirstLetter(global_labels?.promo_primary)} Spend`,
                isSearchable: true,
                filter: "agTextColumnFilter",
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
            },
            {
                field: "actualized_margin",
                headerName: "Margin",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 148,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
            },
            {
                field: "actualized_revenue",
                headerName: "Revenue",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 160,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
            },
            {
                field: "actualized_sales_units",
                headerName: "Sales Units",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 172,
                valueFormatter: (params) => toUnit(params),
                type: "number",
            },
            {
                field: "actualized_margin_percent",
                headerName: "GM %",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 148,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
            },
            {
                field: "actualized_contribution_margin",
                headerName: "CM $",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 148,
                valueFormatter: (params) => {
                    const { currency_name, currency_id, currency_symbol } = params.data;
                    return toCurrencyByCurrencyId({ value: params.value,  currency_name, currency_id, currency_symbol});
                },
                type: "number",
            },
            {
                field: "actualized_contribution_margin_percent",
                headerName: "CM %",
                isSearchable: true,
                filter: "agTextColumnFilter",
                width: 148,
                valueFormatter: (params) => toPercentage(params),
                type: "number",
            },
        ],
    },
];


export const metricsConfig = [
    {
        label: "Sales $",
        value: "sales"
    },
    {
        label: "Sales Units",
        value: "sales_units"
    },
    {
        label: "GM $",
        value: "gm"
    },
    {
        label: "GM %",
        value: "gm_percent"
    },
    {
        label: "CM $",
        value: "cm"
    },
    {
        label: "CM %",
        value: "cm_percent"
    },
];

export const default_selected_metrics = [
    {
        label: "Sales $",
        value: "sales"
    },
    {
        label: "Sales Units",
        value: "sales_units"
    },
    {
        label: "GM $",
        value: "gm"
    },
    {
        label: "GM %",
        value: "gm_percent"
    },
    {
        label: "CM $",
        value: "cm"
    },
    {
        label: "CM %",
        value: "cm_percent"
    },
]


export const metricsCols = [
    {
        label: "Actual",
        value: "actual"
    },
    {
        label: "Forecasted",
        value: "forecasted"
    },
    {
        label: "Var FC",
        value: "var_fc"
    },
    {
        label: "Last Year",
        value: "last_year"
    },
    {
        label: "Var LY",
        value: "var_ly"
    },
    {
        label: "Last Week",
        value: "last_week"
    },
    {
        label: "Var LW",
        value: "var_lw"
    }
]

export const default_selected_metrics_cols = [
    {
        label: "Actual",
        value: "actual"
    },
    {
        label: "Forecasted",
        value: "forecasted"
    },
    {
        label: "Var FC",
        value: "var_fc"
    },
    {
        label: "Last Year",
        value: "last_year"
    },
    {
        label: "Var LY",
        value: "var_ly"
    },
    {
        label: "Last Week",
        value: "last_week"
    },
    {
        label: "Var LW",
        value: "var_lw"
    }
]
