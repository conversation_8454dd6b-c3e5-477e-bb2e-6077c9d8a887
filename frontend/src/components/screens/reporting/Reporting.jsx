import React, { useState, useRef, useEffect, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import moment from "moment";
import _ from "lodash";

// Import assets and components
import DownloadIcon from "../../../assets/imageAssets/downloadIcon.svg?.url";
import levelFilterIcon from "../../../assets/imageAssets/levelFilterIcon.svg?.url";
import levelFilterIconInPanel from "../../../assets/imageAssets/levelFilterIconInPanel.svg?.url";
import SearchIcon from "../../../assets/imageAssets/searchIcon.svg?.url";
import FilterIcon from "../../../assets/imageAssets/filterIcon.svg?.url";

import {
    Button,
    Panel,
    Badge,
    Input,
    RadioButtonGroup,
    Select
} from "impact-ui";
import { Table } from "impact-ui-v3";
import FilterWrapper from "../../common/filters/FilterWrapper";
import ScreenBreadcrumb from "../../common/breadCrumb/ScreenBreadcrumb";
import Charts from "../../ui/charts/Charts";

// Import Redux actions and constants
import {
    fetchReportingMetrics,
    fetchWaterfallMetrics,
    fetchOfferPerformanceMetrics,
    downloadOfferReports,
} from "../../../store/features/reportingReducer/reportingReducer";
import { global_labels } from "../../../constants/Constants";
import {
    reportingFilterConfig as filterConfig,
    reportingRequiredFiltersOnLoad as requiredFiltersOnLoad,
    productSelection,
    storeSelection,
    timeSelection,
} from "../../../constants/FilterConfigConstants";

import {
    offerLevel,
    defaultHiddenColumns,
    defaultProductHierarchy,
    defaultStoreHierarchy,
    defaultTimeHierarchy,
    defaultOfferLevel,
    defaultAggregationLevel,
    reportingMetricsTableConfig,
    offerPerformanceMetricsTableConfig,
    metricsConfig,
    metricsCols,
    default_selected_metrics,
    default_selected_metrics_cols,
} from "./ReportingConstants";
import { breadcrumbRoutes } from "../../../constants/RouteConstants";
import "./Reporting.scss";
import { capitalizeFirstLetter,fabricatePayloadHierarchy, labelCurrencyHandlerForTableDefs } from "../../../utils/helpers/utility_helpers";
import { toCurrencyByCurrencyIdWithDecimalFormatted } from "../../../utils/helpers/formatter";

function Reporting() {
    const dispatch = useDispatch();
    const firstLoadRef = useRef(false); // To ensure first load logic runs only once
    const isGridApiInitializedRef = useRef(false); // Track Grid API initialization

    const [showFiltersSection, setShowFiltersSection] = useState(true); // Toggle filter section visibility
    const { filtersData } = useSelector(
        (store) => store?.pricesmartPromoReducer.filters
    );

    const {
        reportingMetrics = {},
        waterfallMetrics = {},
        offerPerformanceMetrics = {},
    } = useSelector((store) => store?.pricesmartPromoReducer?.reporting);

    const {
        global_configs,
        currency_detail
    } = useSelector((store) => store?.pricesmartPromoReducer?.global);

    // State variables for selected hierarchy levels and aggregation key
    const [selectedProductHierarchy, setSelectedProductHierarchy] = useState(
        defaultProductHierarchy
    );
    const [selectedStoreHierarchy, setSelectedStoreHierarchy] = useState(
        defaultStoreHierarchy
    );
    const [selectedTimeHierarchy, setSelectedTimeHierarchy] = useState(
        defaultTimeHierarchy
    );
    const [selectedOfferLevel, setSelectedOfferLevel] = useState(
        defaultOfferLevel
    );
    const [aggregationKey, setAggregationKey] = useState(
        defaultAggregationLevel
    );

    const [showGlobalSearch, setShowGlobalSearch] = useState(false);
    const [globalSearchText, setGlobalSearchText] = useState("");
    const [currentSearchTable, setCurrentSearchTable] = useState(null);

    const reportingMetricsTableRef = useRef(null); // Reference to reporting metrics table
    const offerPerformanceMetricsTableRef = useRef(null); // Reference to offer performance metrics table
    const [isPanelOpen, setIsPanelOpen] = useState(false); // Track panel open/close state
    const [offersTable, setOffersTable] = useState("top"); // Track selected offers table (top or bottom)
    const [offerPerformanceMetricsTableColDefs, setOfferPerformanceMetricsTableColDefs] = useState(offerPerformanceMetricsTableConfig);
    const [reportingMetricsTableColDefs, setReportingMetricsTableColDefs] = useState(reportingMetricsTableConfig);
    const [reportingFilterConfig, setReportingFilterConfig] = useState(filterConfig);

    const [showMetricsFilter, setShowMetricsFilter] = useState(false);
    const [selectedMetrics, setSelectedMetrics] = useState(default_selected_metrics);
    const [selectedMetricsCols, setSelectedMetricsCols] = useState(default_selected_metrics_cols);
    const [metricDropdownOpen, setMetricDropdownOpen] = useState(false);
    const [metricColsDropdownOpen, setMetricColsDropdownOpen] = useState(false);
    const [metricsSelectAll, setMetricsSelectAll] = useState(true);
    const [metricsColsSelectAll, setMetricsColsSelectAll] = useState(true);

    useEffect(() => {
        if (!global_configs?.event?.use_event) {
            setOfferPerformanceMetricsTableColDefs(offerPerformanceMetricsTableConfig.filter((col) => col.field !== "event_name"));
            setReportingMetricsTableColDefs(reportingMetricsTableConfig.filter((col) => col.field !== `${capitalizeFirstLetter(global_labels?.event_primary)} Name`));
            const config = [];
            // remove event filter from the filter config, if the event flag is false
            filterConfig.forEach((filter) => {
                const newFilter = {
                    ...filter,
                    groupConfig: filter.groupConfig?.filter((group) => group.filterId !== "event"),
                };
                config.push(newFilter);
            });
            setReportingFilterConfig(config);
        }
    }, [global_configs?.event?.use_event]);

    // Configuration for hierarchy filters to set badges in the panel
    const hierarchyConfig = [
        {
            id: "productHierarchy",
            title: "Product Hierarchy",
            labels: productSelection,
            selected: selectedProductHierarchy,
            setter: setSelectedProductHierarchy,
        },
        {
            id: "storeHierarchy",
            title: "Store Hierarchy",
            labels: storeSelection,
            selected: selectedStoreHierarchy,
            setter: setSelectedStoreHierarchy,
        },
        {
            id: "timeHierarchy",
            title: "Time Hierarchy",
            labels: timeSelection,
            selected: selectedTimeHierarchy,
            setter: setSelectedTimeHierarchy,
        },
        {
            id: "offerLevel",
            title: `${capitalizeFirstLetter(global_labels?.promo_alias)} Level`,
            labels: offerLevel,
            selected: selectedOfferLevel,
            setter: setSelectedOfferLevel,
        },
    ];

    const handleSearchButtonClick = (tableRefKey) => {
        // Set which table we're searching
        setCurrentSearchTable(tableRefKey);

        // Toggle visibility of the search input
        setShowGlobalSearch((prev) => !prev);
    };

    // Function to handle grid readiness - sets up column visibility on initial load
    const onGridReady = () => {
        if (!firstLoadRef.current) {
            updateColumnVisibility(true); // Run default visibility logic
            firstLoadRef.current = true; // Mark as completed
        }
    };

    //funtion to search the table
    const onFilterTextBoxChanged = useCallback(
        (text) => {
            setGlobalSearchText(text);

            // Select the correct table reference based on the current search table
            const tableRef =
                currentSearchTable === "reportingMetrics"
                    ? reportingMetricsTableRef
                    : offerPerformanceMetricsTableRef;

            // Check if the reference is available and apply the filter
            if (tableRef?.current?.api) {
                tableRef.current.api.setGridOption("quickFilterText", text);
            }
        },
        [currentSearchTable]
    );

    // Function to initialize filter data for API payload
    // This function extracts filter data from the Redux store and formats it into the appropriate payload structure for API requests
    const initializeFiltersDataAndPayload = () => {
        const reportingFilters = _.cloneDeep(filtersData.REPORTING);
        let payload = {};

        // Process each filter and construct payload
        _.forEach(Object.keys(reportingFilters), (key) => {
            if (key === "dateRange") {
                payload.start_date = moment(
                    reportingFilters.dateRange?.start_date
                ).format("YYYY-MM-DD");
                payload.end_date = moment(
                    reportingFilters.dateRange?.end_date
                ).format("YYYY-MM-DD");
            } else if (key === "event") {
                payload["event_ids"] = _.cloneDeep(
                    reportingFilters[key]?.selectedOptionsArray
                );
            }
        });

        payload = {
            ...payload,
            ...fabricatePayloadHierarchy(reportingFilters),
        }
        payload.promo_ids =
            reportingFilters?.completedOffers?.selectedOptionsArray || [];
        payload.target_currency_id = currency_detail?.currency_id;
        return payload;
    };

    // Function to generate aggregation key and levels
    // This function creates a unique aggregation key based on the selected hierarchy levels and returns both the key and aggregation levels
    const generateAggregationKey = () => {
        const buildLevelKeys = (selection, hierarchy, keyType, prefix = "") =>
            selection.map(
                (label) => prefix + hierarchy.find((p) => p.label === label)?.[keyType]
            );

        return {
            reduxKey: [
                ...buildLevelKeys(
                    selectedProductHierarchy,
                    productSelection,
                    "key",
                    "p"
                ),
                ...buildLevelKeys(
                    selectedStoreHierarchy,
                    storeSelection,
                    "key",
                    "s"
                ),
                ...buildLevelKeys(
                    selectedTimeHierarchy,
                    timeSelection,
                    "key",
                    "t"
                ),
                ...buildLevelKeys(
                    selectedOfferLevel,
                    offerLevel,
                    "key",
                    "o"
                ),
            ]
                .sort()
                .join("--"),

            aggregationLevel: {
                product_hierarchy_levels: buildLevelKeys(
                    selectedProductHierarchy,
                    productSelection,
                    "key"
                ),
                store_hierarchy_levels: buildLevelKeys(
                    selectedStoreHierarchy,
                    storeSelection,
                    "key"
                ),
                time_levels: buildLevelKeys(
                    selectedTimeHierarchy,
                    timeSelection,
                    "key"
                )[0],
                offer_level: buildLevelKeys(
                    selectedOfferLevel,
                    offerLevel,
                    "key"
                )[0],
            },
        };
    };

    // Main function to apply filters
    // This function triggers the appropriate Redux actions to fetch reporting, waterfall, and offer performance metrics based on the applied filters
    const onFilterApply = async (
        shouldGetWaterfall = true,
        shouldGetOfferPerformance = true,
        aggregationKeyUpdated = aggregationKey
    ) => {
        const payload = initializeFiltersDataAndPayload(); // Initialize payload from filters
        const { aggregationLevel } = generateAggregationKey(); // Generate aggregation key and levels



        const reportingMetricsPayload = { ...payload, ...aggregationLevel };

        // Dispatch Redux actions to fetch reporting metrics
        dispatch(
            fetchReportingMetrics({
                payload: reportingMetricsPayload,
                aggregation: aggregationKeyUpdated,
            })
        );
        if (shouldGetWaterfall) {
            dispatch(
                fetchWaterfallMetrics({ payload, elementLabels: global_labels })
            );
        }
        if (shouldGetOfferPerformance) {
            dispatch(fetchOfferPerformanceMetrics(payload));
        }

        updateColumnVisibility(); // Update column visibility after filter application
    };

    // Function to handle chip selection in panel
    // This function updates the selected state for hierarchy chips in the panel
    const handleChipSelect = (id, label, selected, setter) => {
        setter((prev) => {
            if (id === "timeHierarchy" && label !== "Overall") {
                return [label];
            } else if (label === "Overall") {
                return prev.includes("Overall") ? [] : ["Overall"];
            } else {
                return prev.includes(label)
                    ? prev.filter((v) => v !== label)
                    : [...prev.filter((v) => v !== "Overall"), label];
            }
        });
    };

    // Function to update column visibility based on selected filters
    // This function controls which columns are visible in the reporting metrics table based on selected hierarchy levels
    const updateColumnVisibility = (useDefault = false) => {
        if (!reportingMetricsTableRef.current?.api) {
            // Retry only if the Grid API hasn't been initialized
            if (!isGridApiInitializedRef.current) {
                setTimeout(() => updateColumnVisibility(useDefault), 100);
            }
            return;
        }

        // Mark the Grid API as initialized
        isGridApiInitializedRef.current = true;

        // Determine which columns to unhide based on selected filters
        const productHierarchy = useDefault
            ? defaultProductHierarchy
            : selectedProductHierarchy;
        const storeHierarchy = useDefault
            ? defaultStoreHierarchy
            : selectedStoreHierarchy;
        const timeHierarchy = useDefault
            ? defaultTimeHierarchy
            : selectedTimeHierarchy;
        const offerLevel = useDefault ? defaultOfferLevel : selectedOfferLevel;

        let unhideLabels = [];

        if (!productHierarchy.includes("Overall")) {
            unhideLabels = [...unhideLabels, ...productHierarchy, "FOB"];
        }
        if (!storeHierarchy.includes("Overall")) {
            unhideLabels = [...unhideLabels, ...storeHierarchy];
        }
        if (!timeHierarchy.includes("Overall")) {
            unhideLabels = [...unhideLabels, ...timeHierarchy];
        }
        if (!offerLevel.includes("Overall")) {
            unhideLabels = [...unhideLabels, ...offerLevel, `${capitalizeFirstLetter(global_labels?.promo_alias)} Name`];
        }

        // Determine columns to be shown based on selected filters
        const fieldsToUnhide = unhideLabels
            .map((label) => defaultHiddenColumns[label])
            .filter(Boolean);

        const columnsToManage = Object.values(defaultHiddenColumns);
        const currentColumnState = reportingMetricsTableRef.current.api.getColumnState();

        const newColumnState = currentColumnState.map((colState) => {
            if (columnsToManage.includes(colState.colId)) {
                return {
                    ...colState,
                    hide: !fieldsToUnhide.includes(colState.colId),
                };
            }
            return colState; // Leave other columns unchanged
        });

        // Apply the new column state
        reportingMetricsTableRef.current.api.applyColumnState({
            state: _.cloneDeep(newColumnState),
            applyOrder: true,
        });
    };

    // Function to handle Apply button click in the panel
    // Triggers the filter application and updates the aggregation key after data is loaded
    const handleApply = async () => {
        const { reduxKey } = generateAggregationKey();

        setIsPanelOpen(false); // Close the panel

        // Trigger the API call and wait for it to complete, if data not present in redux for the aggregation key  
        if (_.isEmpty(reportingMetrics[reduxKey])) {
            await onFilterApply(true, true, reduxKey);
        }
        // Update the aggregation key only after the data has loaded
        setAggregationKey(reduxKey);

        updateColumnVisibility();
    };

    // Function to handle Reset button click in the panel, resets the selected hierarchy filters to their default values
    const handleReset = () => {
        // Reset selected filters to defaults
        setSelectedProductHierarchy([...defaultProductHierarchy]);
        setSelectedStoreHierarchy([...defaultStoreHierarchy]);
        setSelectedTimeHierarchy([...defaultTimeHierarchy]);
        setSelectedOfferLevel([...defaultOfferLevel]);
        setAggregationKey(defaultAggregationLevel);
        setIsPanelOpen(false);
        updateColumnVisibility(true); // Reset column visibility to default
    };

    // This function prepares the payload and endpoint based on the selected report type and triggers the download action
    const handleDownload = (reportType) => {
        const payload = initializeFiltersDataAndPayload(); // Initialize payload from filters
        const { aggregationLevel } = generateAggregationKey(); // Generate aggregation levels

        let finalPayload = {
            ...payload,
            report_type: "excel",
            for_download: true,
        };
        let endpoint = "";

        // Determine report type and set endpoint accordingly
        if (reportType === "reportingMetrics") {
            finalPayload = {
                ...finalPayload,
                ...aggregationLevel,
                report_name: `post_offer_analysis_${moment(new Date()).format(
                    "MM/DD/YYYY"
                )}`,
            };
            endpoint = "download-post-offer-analysis-report";
        } else if (reportType === "top") {
            finalPayload = {
                ...finalPayload,
                report_name: `top_10_performing_offers_${moment(
                    new Date()
                ).format("MM/DD/YYYY")}`,
            };
            endpoint = "download-top-and-bottom-performing-offers";
        } else if (reportType === "bottom") {
            finalPayload = {
                ...finalPayload,
                is_top_10: false,
                report_name: `bottom_10_performing_offers_${moment(
                    new Date()
                ).format("MM/DD/YYYY")}`,
            };
            endpoint = "download-top-and-bottom-performing-offers";
        }

        // Dispatch Redux action to download the report
        dispatch(downloadOfferReports({ payload: finalPayload, endpoint }));
    };

    const handleMetricsFilterApply = () => {
        const selectedMetrics_values = selectedMetrics.map((metric) => metric.value);
        const selectedMetricsCols_values = selectedMetricsCols.map((col) => col.value);
        const cols_icluded_for_filtering = metricsConfig.map((metric) => metric.value);

        const updatedMetricsTableColDefs = reportingMetricsTableColDefs.map((colDef) => {
            if (cols_icluded_for_filtering.includes(colDef.colId)) {
                if (!selectedMetrics_values.includes(colDef.colId)) {
                    colDef.hide = true;
                    if (colDef.children) {
                        colDef.children.forEach((child) => {
                            child.hide = true;
                        });
                    }
                } else {
                    colDef.hide = false;
                    if (colDef.children) {
                        colDef.children.forEach((child) => {
                            if (!selectedMetricsCols_values.find((col) => child.colId.includes(col))) {
                                child.hide = true;
                            } else {
                                child.hide = false;
                            }
                        });
                    }
                }
            }
            return colDef;
        });
        setReportingMetricsTableColDefs(updatedMetricsTableColDefs);
        setShowMetricsFilter(false);
    };

    return (
        <div className="screen_container">
            <ScreenBreadcrumb breadcrumbList={breadcrumbRoutes()?.["reporting"]}>
                <Button
                    iconPlacement="left"
                    onClick={() => setShowFiltersSection((prev) => !prev)}
                    size="large"
                    variant="secondary"
                >
                    {showFiltersSection ? "Hide" : "Show"} Filters
                </Button>
            </ScreenBreadcrumb>
            <FilterWrapper
                defaultOpen="product_hierarchy"
                screen="REPORTING"
                callAPIonLoad={true}
                filterConfig={reportingFilterConfig}
                requiredFiltersOnLoad={requiredFiltersOnLoad}
                onFilterApply={onFilterApply}
                showFiltersSection={showFiltersSection}
            />
            <div className="screen_data_container flex1">
                <div className="marginTop-20">
                    {!_.isEmpty(reportingMetrics) && (
                        <Table
                            ref={reportingMetricsTableRef}
                            tableHeader={`Post ${capitalizeFirstLetter(global_labels?.promo_alias)} Analysis`}
                            suppressMenuHide
                            rowData={
                                reportingMetrics?.[aggregationKey]?.length
                                    ? reportingMetrics[aggregationKey]
                                    : []
                            }
                            columnDefs={labelCurrencyHandlerForTableDefs(reportingMetricsTableColDefs, currency_detail?.currency_symbol || "$")}
                            suppressRowClickSelection={true}
                            onGridReady={onGridReady}
                            topRightOptions={
                                <div className="centerFlexWithGap12">
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={levelFilterIcon}
                                                alt="Filter"
                                            />
                                        }
                                        onClick={() => setIsPanelOpen(true)}
                                        size="large"
                                        variant="tertiary"
                                    />
                                    {showMetricsFilter && (
                                        <div className="positionRelative">
                                            <div className="metrics-filter-container">
                                                <div className="flexWithGap12 marginBottom-16">
                                                    <Select
                                                        currentOptions={metricsConfig}
                                                        initialOptions={metricsConfig}
                                                        label="Metrics"
                                                        labelOrientation="top"
                                                        setSelectedOptions={(selectedOptions) => {
                                                            setSelectedMetrics(selectedOptions);
                                                            setMetricsSelectAll(selectedOptions.length === metricsConfig.length);
                                                        }}
                                                        setCurrentOptions={() => { }}
                                                        placeholder="Select.."
                                                        isRequired={false}
                                                        isWithSearch={false}
                                                        isMulti={true}
                                                        isSelectAll={metricsSelectAll}
                                                        onSelectAll={() => {
                                                            setSelectedMetrics(!metricsSelectAll ? metricsConfig : []);
                                                            setMetricsSelectAll(!metricsSelectAll);
                                                        }}
                                                        setIsSelectAll={setMetricsSelectAll}
                                                        selectedOptions={selectedMetrics}
                                                        isOpen={metricDropdownOpen}
                                                        setIsOpen={setMetricDropdownOpen}
                                                        isCloseWhenClickOutside={true}
                                                        toggleSelectAll
                                                    />
                                                    <Select
                                                        currentOptions={metricsCols}
                                                        initialOptions={metricsCols}
                                                        label="Show"
                                                        labelOrientation="top"
                                                        setSelectedOptions={(selectedOptions) => {
                                                            setSelectedMetricsCols(selectedOptions);
                                                            setMetricsColsSelectAll(selectedOptions.length === metricsCols.length);
                                                        }}
                                                        setCurrentOptions={() => { }}
                                                        placeholder="Select.."
                                                        isRequired={false}
                                                        isWithSearch={false}
                                                        isMulti={true}
                                                        isSelectAll={metricsColsSelectAll}
                                                        onSelectAll={() => {
                                                            setSelectedMetricsCols(!metricsColsSelectAll ? metricsCols : []);
                                                            setMetricsColsSelectAll(!metricsColsSelectAll);
                                                        }}
                                                        setIsSelectAll={setMetricsColsSelectAll}
                                                        selectedOptions={selectedMetricsCols}
                                                        isOpen={metricColsDropdownOpen}
                                                        setIsOpen={setMetricColsDropdownOpen}
                                                        isCloseWhenClickOutside={true}
                                                        toggleSelectAll
                                                    />
                                                </div>
                                                <div className="filter-btn-container">
                                                    <Button
                                                        onClick={() => setShowMetricsFilter(false)}
                                                        size="large"
                                                        variant="secondary"
                                                    >
                                                        Cancel
                                                    </Button>
                                                    <Button
                                                        onClick={() => handleMetricsFilterApply()}
                                                        size="large"
                                                        variant="primary"
                                                    >
                                                        Apply
                                                    </Button>

                                                </div>
                                            </div>
                                        </div>
                                    )}
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={FilterIcon}
                                                alt="Filter"
                                            />
                                        }
                                        onClick={() => setShowMetricsFilter((prev) => !prev)}
                                        size="large"
                                        variant="tertiary"
                                    />
                                    <div className="horizontal-line" />
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={DownloadIcon}
                                                alt="Download"
                                            />
                                        }
                                        onClick={() =>
                                            handleDownload("reportingMetrics")
                                        }
                                        size="large"
                                        variant="tertiary"
                                    />
                                    <div className="positionRelative">
                                        {showGlobalSearch &&
                                            currentSearchTable ===
                                            "reportingMetrics" ? (
                                            <div className="tableGlobalSearchContainer">
                                                <Input
                                                    onChange={(e) =>
                                                        onFilterTextBoxChanged(
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Search"
                                                    rightIcon={
                                                        <img src={SearchIcon} />
                                                    }
                                                    type="text"
                                                    value={globalSearchText}
                                                />
                                            </div>
                                        ) : null}
                                        <Button
                                            iconPlacement="left"
                                            icon={<img src={SearchIcon} />}
                                            onClick={() =>
                                                handleSearchButtonClick(
                                                    "reportingMetrics"
                                                )
                                            }
                                            size="large"
                                            variant="tertiary"
                                        />
                                    </div>
                                </div>
                            }
                        />
                    )}
                </div>

                <div className="marginTop-20 centerFlexWithGap12">
                    {!_.isEmpty(waterfallMetrics.margin) && (
                        <div className="reporting-waterfall-chart">
                            <Charts
                                data={waterfallMetrics?.margin || []}
                                chartType="waterfall"
                                yAxisLabel="Margin"
                                formatter="kFormatter"
                            />
                        </div>
                    )}
                    {!_.isEmpty(waterfallMetrics.revenue) && (
                        <div className="reporting-waterfall-chart">
                            <Charts
                                data={waterfallMetrics?.revenue || []}
                                chartType="waterfall"
                                yAxisLabel="Revenue"
                                formatter="kFormatter"
                            />
                        </div>
                    )}
                </div>

                <div className="marginTop-20">
                    {!_.isEmpty(offerPerformanceMetrics) && (
                        <Table
                            ref={offerPerformanceMetricsTableRef}
                            tableHeader={`Top and Bottom Performing ${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`}
                            suppressMenuHide
                            rowData={offerPerformanceMetrics[offersTable] || []}
                            columnDefs={labelCurrencyHandlerForTableDefs(offerPerformanceMetricsTableColDefs, currency_detail?.currency_symbol || "$")}
                            rowSelection="multiple"
                            suppressRowClickSelection={true}
                            topRightOptions={
                                <div className="centerFlexWithGap12">
                                    <RadioButtonGroup
                                        onChange={(event) => {
                                            setOffersTable(event.target.value);
                                        }}
                                        options={[
                                            {
                                                label: `Bottom 10 ${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`,
                                                value: "bottom",
                                            },
                                            {
                                                label: `Top 10 ${capitalizeFirstLetter(global_labels?.promo_alias_plural)}`,
                                                value: "top",
                                            },
                                        ]}
                                        orientation="row"
                                        selectedOption={offersTable}
                                    />
                                    <div className="horizontal-line" />
                                    <Button
                                        iconPlacement="left"
                                        icon={
                                            <img
                                                src={DownloadIcon}
                                                alt="Download"
                                            />
                                        }
                                        onClick={() =>
                                            handleDownload(offersTable)
                                        }
                                        size="large"
                                        variant="tertiary"
                                    />
                                    <div className="positionRelative">
                                        {showGlobalSearch &&
                                            currentSearchTable ===
                                            "offerPerformanceMetrics" ? (
                                            <div className="tableGlobalSearchContainer">
                                                <Input
                                                    onChange={(e) =>
                                                        onFilterTextBoxChanged(
                                                            e.target.value
                                                        )
                                                    }
                                                    placeholder="Search"
                                                    rightIcon={
                                                        <img src={SearchIcon} />
                                                    }
                                                    type="text"
                                                    value={globalSearchText}
                                                />
                                            </div>
                                        ) : null}
                                        <Button
                                            iconPlacement="left"
                                            icon={<img src={SearchIcon} />}
                                            onClick={() =>
                                                handleSearchButtonClick(
                                                    "offerPerformanceMetrics"
                                                )
                                            }
                                            size="large"
                                            variant="tertiary"
                                        />
                                    </div>
                                </div>
                            }
                        />
                    )}
                </div>
            </div>
            {isPanelOpen && (
                <Panel
                    anchor="right"
                    open={isPanelOpen}
                    onClose={() => setIsPanelOpen(false)}
                    onPrimaryButtonClick={() => {
                        if (selectedProductHierarchy.includes("SKU")) {
                            handleDownload("reportingMetrics")
                        } else {
                            handleApply()
                        }
                    }}
                    onSecondaryButtonClick={handleReset}
                    primaryButtonLabel={selectedProductHierarchy.includes("SKU") ? "Download" : "Apply"}
                    secondaryButtonLabel="Clear Filters"
                    size="large"
                    title={
                        <div className="panel-title-with-icon">
                            <img
                                src={levelFilterIconInPanel}
                                alt="Level Filters"
                                className="panel-icon"
                            />
                            <span>Level Filters</span>
                        </div>
                    }
                >
                    <div className="aggregation-panel-content">
                        {hierarchyConfig.map(
                            ({ id, title, labels, selected, setter }) => (
                                <div
                                    key={id}
                                    className="hierarchy-chip-group"
                                >
                                    <h4>{title}</h4>
                                    <div className="chip-wrapper">
                                        {labels.map(({ label }) => (
                                            <Badge
                                                key={label}
                                                label={label}
                                                variant="stroke"
                                                color={
                                                    selected.includes(label)
                                                        ? "info"
                                                        : "default"
                                                }
                                                onClick={() =>
                                                    handleChipSelect(
                                                        id,
                                                        label,
                                                        selected,
                                                        setter
                                                    )
                                                }
                                            />
                                        ))}
                                    </div>
                                </div>
                            )
                        )}
                    </div>
                </Panel>
            )}
        </div>
    );
}

export default Reporting;
