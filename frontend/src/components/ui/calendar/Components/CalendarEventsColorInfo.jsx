import React from "react";
import { eventStatusColors } from "../Constants";

export default function CalendarEventsColorInfo() {
	return (
		<div className="event-legend">
			{Object.keys(eventStatusColors).map((status) => (
				<div className="event-legend-content">
					<div
						className="event-legend-content-circle"
						style={{
							backgroundColor: eventStatusColors[status].color,
							border: `1px solid ${eventStatusColors[status].border}`,
						}}
					/>
					<div className="event-legend-content-text">
						{eventStatusColors[status].label}
					</div>
				</div>
			))}
		</div>
	);
}
