.calendar-wrapper {
    // font-family: Manrope;
	background: #ffffff 0% 0% no-repeat padding-box;
	box-shadow: 0px 0px 6px #00000029;
	border-radius: 5px;
}

.no-content {
	display: flex;
	height: 100px;
	width: 100%;
}

.header-row {
	height: 40px;
	display: flex;
	background-color: #F8F9FB;
	justify-content: center;
	align-items: center;
	border-bottom: 1px solid rgba(195, 200, 212, 1);
	overflow-y: hidden;
}

.header-row.scroll {
	overflow-y: scroll;
}

.header-row .header-cell {
	text-align: center;
	letter-spacing: 0px;
	color: #1d1d1d;
	font-family: Poppins;
	display: flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
    height: 45px
}

.navigation-arrow-left,
.navigation-arrow-right {
	display: inline-flex;
	border: 1px solid #e2e2e2;
	border-radius: 8px;
	cursor: pointer;
	background: #fff;
	padding: 4px;
}
.navigation-arrow-left {
	margin-left: 10px;
}

.navigation-arrow-right {
	margin-right: 10px;
}

.header-title-container {
	flex: 1;
    // background-color: #F8F9FB;
}

.header-title-container .header-title {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
}

.header-title.bold {
    // font-family: Manrope;
    font-size: 14px;
    font-weight: 800;
    line-height: 14px;
    text-align: left;
}

.header-title.day {
    // font-family: Manrope;
    font-size: 14px;
    line-height: 14px;
    font-weight: 500;
    text-align: left;
}

.header-title-container .header-title svg {
	height: 16px;
	width: 16px;
	fill: #0055af;
	cursor: pointer;
	position: relative;
}

.header-row.month .header-cell {
	font-weight: 600;
	font-size: 14px;
	line-height: 40px;
	border-left: 1px solid rgba(195, 200, 212, 1);
}

.header-row.week .header-cell {
	font-size: 12px;
	line-height: 40px;
}

.header-row.week .header-cell.day {
	border-left: 1px solid rgba(217, 221, 231, 1);
}

.header-row.week .header-cell.month {
	border-left: 1px solid rgba(195, 200, 212, 1);
}

.header-row.day {
	height: 45px;
    background-color: #fff;
}

.header-row.day .header-cell {
	font-size: 10px;
	line-height: 16px;
}

.header-row.day .header-cell.day {
	border-left: 1px solid rgba(217, 221, 231, 1);
}

.header-row.day .header-cell.week {
	border-left: 1px solid rgba(217, 221, 231, 1);
}

.header-row.day .header-cell.month {
	border-left: 1px solid rgba(195, 200, 212, 1);
}

.header-row.month .header-cell:nth-of-type(1),
.header-row.week .header-cell:nth-of-type(1),
.header-row.day .header-cell:nth-of-type(1) {
	border-left: transparent;
}

.header-row.week .header-cell.day.current {
    background-color: rgba(235, 247, 241, 1);

}

.header-row.day .header-cell.day.current {
    background: rgba(236, 238, 253, 1);
}

.flex-c-a {
	justify-content: center;
	align-items: center;
}

.event-list-container {
	min-height: 240px;
	overflow-x: hidden;
	// overflow-y: scroll;
    // max-height: 320px;
}

.no-scroll {
	overflow: hidden;
}

.cal-row {
	display: flex;
	position: relative;
	min-height: 40px;
	border-bottom: 1px dashed #e2e2e2;
}

.cal-cell {
	height: 40px;
	box-sizing: border-box;
}

.cal-cell.day {
	border-left: 1px dashed #eaebedb8;
}

.cal-cell.week {
	border-left: 1px solid #c8ced7;
}

.cal-cell.month {
	border-left: 2px solid #7584987a;
	.header-title.day {
		font-size: 10px;
	}
}

.cal-cell:nth-of-type(1) {
	border-left: transparent;
}

.event-container {
	border: 1px solid #24a148;
	border-radius: 4px;
	display: flex;
	position: absolute;
	white-space: nowrap;
	overflow: hidden;
	cursor: pointer;
	height: 30px;
	// z-index: 99;
	align-items: center;
	padding: 0px 2px;
	box-sizing: border-box;
	margin-top: 5px;
}

.event-container:hover + .event-detail-container {
	display: block;
}

.event-detail-container:hover {
	display: block;
}

.event-container > p.event-title {
    // font-family: Manrope;
    font-size: 14px;
    font-weight: 500;
    line-height: 19.12px;
    text-align: left;
	display: flex;
	gap: 3px;
	align-items: center;
}

.event-container > p.event-title .event-title-text {
	text-overflow: ellipsis;
	flex: 1;
	white-space: nowrap;
	overflow: hidden;
}

.event-container > p.event-title svg {
	width: 16px;
	height: 16px;
	fill: #ff832b;
	background: #fff;
	padding: 1px;
	border-radius: 50%;
}

.event-container {
    background: #E9ECF1;
    border: #d4d4d4;
    color: #31416E;
}

.event-container.Good,
.event-container.completed {
	background: #C4E8D5;
	border: 1px solid #C4E8D5;
	color: #31416E;
}

.event-container.Average {
	background: #F6EBBF;
	border: 1px solid #F6EBBF;
	color: #31416E;
}

.event-container.Dilutive {
	background: #F6CCCC;
	border: 1px solid #F6CCCC;
	color: #31416E;
}

.event-container.upcoming {
	background: #ECEEFD;
	border: 1px solid #4259EE;
	color: #31416E;
}

.event-detail-container {
	display: none;
	position: absolute;
	z-index: 999;
	background: #ffffff 0% 0% no-repeat padding-box;
	box-shadow: 0px 0px 6px #00000029;
	border: 1px solid #eaebed;
	border-radius: 4px;
	padding: 12px 8px;
}

.event-container.selected {
	background: #00accb;
	border: 1px solid #00accb;
	color: #ffffff;
}

.event-container p {
	margin: 0;
	overflow: hidden;
	text-overflow: ellipsis;
}

.loading-text {
	position: absolute;
	z-index: 1001;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	overflow: hidden;
	color: #1976d2;
}

.calendar-events-wrapper {
	position: relative;
    height: calc(100vh - 480px);
    min-height: 325px;
    overflow: auto;
}

.event-legend {
	display: flex;
	gap: 20px;
	padding: 12px 0 0 0;
	align-items: center;
	justify-content: end;
}

.event-legend-content {
	display: flex;
	align-items: center;
    gap: 5px;
}

.event-legend-content-circle {
	height: 8px;
	width: 8px;
	display: inline-block;
}

.event-legend-content-text {
    // font-family: Manrope;
    font-size: 12px;
    font-weight: 500;
    line-height: 15px;
    text-align: left;
}

.event-detail-container {
    width: 500px;
    height: 174px;
}

.event-detail-container .status-indicator {
	display: inline-block;
	border-radius: 50%;
	width: 14px;
	height: 14px;
}

.event-detail-container.Good {
    border: 1px solid #C4E8D5;
}

.event-detail-container.Average {
    border: 1px solid #F6EBBF;
}

.event-detail-container.Dilutive {
    border: 1px solid #F6CCCC;
}

.event-detail-container.upcoming {
    border: 1px solid #4259EE;
}

.event-detail-container.Good .status-indicator,
.event-detail-container.completed .status-indicator {
	background-color: #24a148;
}

.event-detail-container.Average .status-indicator,
.event-detail-container.ongoing .status-indicator {
	background-color: #ff832b;
}

.event-detail-container.Dilutive .status-indicator {
	background-color: #da1e28;
}

.event-detail-container.upcoming .status-indicator {
	background-color: #4259EE;
}

.disabled-lock {
	color: #75849080;
}

p.new-tag {
	padding: 2px 10px;
	background: #0055af;
	border-radius: 4px;
	color: #fff;
	margin: 0;
}

.event-detail-container {
    .event-detail-title-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    p.event-detail-title {
        // font-family: Manrope;
        font-size: 12px;
        font-weight: 700;
        line-height: 13.8px;
        letter-spacing: -0.01em;
        text-align: left;
    }

    .event-detail-cta {
        display: flex;
        align-items: center;
        gap: 3px;
    }

    .event-details {
        display: flex;
        justify-content: space-between;
    }

    .event-detail-column {
        display: flex;
        flex-direction: column;
        gap: 32px;
    }

    .event-detail-value-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .event-detail-value-title {
        // font-family: Manrope;
        font-size: 12px;
        font-weight: 400;
        line-height: 13.8px;
        letter-spacing: -0.01em;
        text-align: left;
    }

    .event-detail-value {
        // font-family: Manrope;
        font-size: 12px;
        font-weight: 700;
        line-height: 13.8px;
        letter-spacing: -0.01em;
        text-align: left;
    }
}