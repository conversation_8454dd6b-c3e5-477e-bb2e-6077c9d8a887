import React, { Component } from "react";
import _ from "lodash";
import CalendarHeader from "./Components/CalendarHeader";
import CalendarEventsList from "./Components/CalendarEventsList";
import CalendarEventsColorInfo from "./Components/CalendarEventsColorInfo";
import {
	getCurrent,
	getWeekCount,
	filterEventsforDisplay,
	getQtrMoWkDateMapping,
	getWeekAndQuarterFromMonth,
	getWeekAndMonthFromQuarter,
	getMonthAndQuarterFromWeek,
	geStartAndEndDate,
	getCurrentQuarterMappingForViewType,
} from "./Helpers";
import "./Calendar.scss";

class Calendar extends Component {
	constructor(props) {
		super(props);
		if (!_.isEmpty(props.fiscalCalendar)) {
			const {
				currYr,
				currWk,
				currMo,
				currQtr,
				minDate,
				maxDate,
			} = getCurrent(
				this.props.fiscalCalendar,
				this.props.calMinDate,
				this.props.calMaxDate
			);

			const {
				noOfWks,
				qtrMoWkDateMapping,
				currentDateSelectionMapping,
				totalDaysInView,
			} = this.generateMapping(currYr, currQtr, currMo);

			this.state = {
				calTypToggleVal: props.calendarFrequency || "month",
				currMo,
				noOfWks,
				qtrMoWkDateMapping,
				currentDateSelectionMapping,
				totalDaysInView,
				calViewState: !_.isEmpty(
					props.calCurrentState?.[props.calDateRange]
				)
					? props.calCurrentState?.[props.calDateRange]
					: {
							wk: currWk,
							mo: currMo,
							qtr: currQtr,
							yr: currYr,
					  },
				minDate,
				maxDate,
			};
		} else {
			this.state = {
				calTypToggleVal: props.calendarFrequency || "month",
				currMo: 0,
				noOfWks: 52,
				calViewState: {},
				qtrMoWkDateMapping: {},
				currentDateSelectionMapping: {},
				totalDaysInView: 0,
			};
		}
		if (_.isEmpty(this.props.calCurrentState?.[this.props.calDateRange]) && this.props.setCalCurrentState) {
			this.props.setCalCurrentState({
				...this.props.calCurrentState,
				[this.props.calDateRange]: {
					...this.state.calViewState,
				},
			});
		}
	}

	componentDidMount() {
		// Detect if mouse is being used or trackpad
		// If Mouse, the browser renders a different scrollbar which takes up extra space in the scrollable div
		// If Trackpad, the browser renders a sleeker scrollbar that overlays on the scrollable div content
		const elements = document.querySelectorAll(".header-row");
		if (this.props.isTrackingDeviceExternal) {
			// Add overflow-y: hidden from header-row
			elements.forEach((element) => {
				element.classList.add("scroll");
			});
		} else {
			// Add overflow-y: scroll to the header-row class (month/week/day displays)
			elements.forEach((element) => {
				element.classList.remove("scroll");
			});
		}
	}

	componentDidUpdate(prevProps, prevState) {
		if (
			prevProps.isTrackingDeviceExternal !==
				this.props.isTrackingDeviceExternal ||
			prevProps.eventList !== this.props.eventList
		) {
			const elements = document.querySelectorAll(".header-row");
			if (this.props.isTrackingDeviceExternal) {
				// Add overflow-y: hidden from header-row
				elements.forEach((element) => {
					element.classList.add("scroll");
				});
			} else {
				// Add overflow-y: scroll to the header-row class (month/week/day displays)
				elements.forEach((element) => {
					element.classList.remove("scroll");
				});
			}
		}

		if (prevProps.calendarFrequency !== this.props.calendarFrequency) {
			if (!_.isEmpty(this.state.qtrMoWkDateMapping)) {
				const {
					currentDateSelectionMapping,
					totalDaysInView,
				} = getCurrentQuarterMappingForViewType({
					headerViewType: this.props.calendarFrequency,
					headerMapping: this.state.qtrMoWkDateMapping,
					currentQuarter: this.state.calViewState.qtr,
					currentMonth: this.state.calViewState.mo,
					fiscalCalendar: this.props.fiscalCalendar,
				});
				this.setState({
					calTypToggleVal: this.props.calendarFrequency,
					currentDateSelectionMapping,
					totalDaysInView,
				});
			} else {
				this.setState({
					calTypToggleVal: this.props.calendarFrequency,
				});
			}
		}

		if (
			!prevProps?.calMinDate?.isSame(this.props.calMinDate) ||
			!prevProps?.calMaxDate?.isSame(this.props.calMaxDate) ||
			!_.isEqual(prevProps.fiscalCalendar, this.props.fiscalCalendar)
		) {
			if (!_.isEmpty(this.props.fiscalCalendar)) {
				const {
					currYr,
					currWk,
					currMo,
					currQtr,
					minDate,
					maxDate,
				} = getCurrent(
					this.props.fiscalCalendar,
					this.props.calMinDate,
					this.props.calMaxDate
				);

				let curr_yr = currYr;
				let curr_qtr = currQtr;
				let curr_mo = currMo;

				if (!_.isEmpty(this.props.calCurrentState?.[this.props.calDateRange])) {
					curr_yr = this.props.calCurrentState?.[this.props.calDateRange]?.yr;
					curr_qtr = this.props.calCurrentState?.[this.props.calDateRange]?.qtr;
					curr_mo = this.props.calCurrentState?.[this.props.calDateRange]?.mo;
				}

				const {
					noOfWks,
					qtrMoWkDateMapping,
					currentDateSelectionMapping,
					totalDaysInView,
				} = this.generateMapping(curr_yr, curr_qtr, curr_mo);

				this.setState({
					currMo,
					noOfWks,
					qtrMoWkDateMapping,
					currentDateSelectionMapping,
					totalDaysInView,
					calViewState: !_.isEmpty(
						this.props.calCurrentState?.[this.props.calDateRange]
					)
						? this.props.calCurrentState?.[this.props.calDateRange]
						: {
								wk: currWk,
								mo: currMo,
								qtr: currQtr,
								yr: currYr,
						  },
					minDate,
					maxDate,
				});
			}
		}

		if (
			!_.isEmpty(this.props.calCurrentState?.[this.props.calDateRange]) &&
			!_.isEqual(
				this.props.calCurrentState?.[this.props.calDateRange],
				this.state.calViewState
			)
		) {
			const {
				currYr,
				currQtr,
				currMo,
			} = getCurrent(
				this.props.fiscalCalendar,
				this.props.calMinDate,
				this.props.calMaxDate
			);

			let curr_yr = currYr;
			let curr_qtr = currQtr;
			let curr_mo = currMo;

			if (!_.isEmpty(this.props.calCurrentState?.[this.props.calDateRange])) {
				curr_yr = this.props.calCurrentState?.[this.props.calDateRange]?.yr;
				curr_qtr = this.props.calCurrentState?.[this.props.calDateRange]?.qtr;
				curr_mo = this.props.calCurrentState?.[this.props.calDateRange]?.mo;
			}

			const {
				qtrMoWkDateMapping,
			} = this.generateMapping(curr_yr, curr_qtr, curr_mo);

			const {
				currentDateSelectionMapping,
				totalDaysInView,
			} = getCurrentQuarterMappingForViewType({
				headerViewType: this.props.calendarFrequency,
				headerMapping: qtrMoWkDateMapping,
				currentQuarter: this.props.calCurrentState?.[
					this.props.calDateRange
				]?.qtr,
				currentMonth: this.props.calCurrentState?.[
					this.props.calDateRange
				]?.mo,
				fiscalCalendar: this.props.fiscalCalendar,
			});

			this.setState({
				calViewState: this.props.calCurrentState?.[
					this.props.calDateRange
				],
				currentDateSelectionMapping,
				totalDaysInView,
			});
		}

		if (_.isEmpty(this.props.calCurrentState?.[this.props.calDateRange]) && !_.isEmpty(this.state.calViewState)) {
			this.props.setCalCurrentState({
				...this.props.calCurrentState,
				[this.props.calDateRange]: {
					...this.state.calViewState,
				},
			});
		}
	}

	generateMapping = (yr, qtr, mo) => {
		const noOfWks = getWeekCount(yr, this.props.fiscalCalendar);
		const {
			qtrMoWkDateMapping,
			currentDateSelectionMapping,
			totalDaysInView,
		} = getQtrMoWkDateMapping(
			this.props.fiscalCalendar,
			yr,
			qtr,
			mo,
			this.props.calendarFrequency,
			true
		);
		return {
			noOfWks,
			qtrMoWkDateMapping,
			currentDateSelectionMapping,
			totalDaysInView,
		};
	};

	onOutofBoundary = (type) => {
		if (this.props.onBoundaryChange) {
			this.props.onBoundaryChange(type);
		} else {
			this.props.toastError("Out of selected date range. Please change the dates and filter again to see results.");
		}
		return false;
	}

	onPrevClick = () => {
		const {
			calViewState,
			calTypToggleVal,
			qtrMoWkDateMapping,
		} = this.state;
		switch (calTypToggleVal) {
			case "week": {
				if (calViewState.wk === 1) {
					this.onPrevYrChng();
					return;
				}
				const prevWk = calViewState.wk - 1;
				const {
					quarterFromWeek,
					monthFromWeek,
				} = getMonthAndQuarterFromWeek(qtrMoWkDateMapping, prevWk);
				// Check if this is valid for selection
				if (
					this.state.calViewState?.yr > this.state.minDate?.currYr ||
					(this.state.calViewState?.yr ==
						this.state.minDate?.currYr &&
						prevWk >= this.state.minDate?.currWk)
				) {
					const {
						qtrMoWkDateMapping,
					} = this.generateMapping(this.state.calViewState.yr, this.state.calViewState.qtr, this.state.calViewState.mo);
					
					let qtrMoWkDate_Mapping = _.cloneDeep(qtrMoWkDateMapping);
					let month_from_week = monthFromWeek;
					let quarter_from_week = quarterFromWeek;
					let week_from_week = prevWk;
					let yr_from_week = this.state.calViewState.yr;
					if (
						(this.state.minDate?.currYr < this.state.calViewState.yr)
						&& prevWk === 0
					) {
						const {
							qtrMoWkDateMapping,
						} = this.generateMapping(this.state.calViewState.yr - 1, 4, 12);

						qtrMoWkDate_Mapping = qtrMoWkDateMapping;
						yr_from_week = qtrMoWkDateMapping.year;
						quarter_from_week = 4;
						month_from_week = 12;
						week_from_week = 52;
					}
					const {
						currentDateSelectionMapping,
						totalDaysInView,
					} = getCurrentQuarterMappingForViewType({
						headerViewType: this.props.calendarFrequency,
						headerMapping: qtrMoWkDate_Mapping,
						currentQuarter: quarter_from_week,
						currentMonth: month_from_week,
						fiscalCalendar: this.props.fiscalCalendar,
					});
					this.setState({
						calViewState: {
							...calViewState,
							wk: week_from_week,
							mo: month_from_week,
							qtr: quarter_from_week,
							yr: yr_from_week,
						},
						currentDateSelectionMapping,
						totalDaysInView,
					});
					this.props.setCalCurrentState({
						...this.props.calCurrentState,
						[this.props.calDateRange]: {
							...calViewState,
							wk: week_from_week,
							mo: month_from_week,
							qtr: quarter_from_week,
							yr: yr_from_week,
						},
					});
				} else {
					this.onOutofBoundary("prevWk");
					return false;
				}

				break;
			}
			case "month": {
				if (calViewState.mo === 1) {
					this.onPrevYrChng();
					return;
				}
				const prevMo = calViewState.mo - 1;

				let {
					quarterFromMonth,
					weekFromMonth,
				} = getWeekAndQuarterFromMonth(qtrMoWkDateMapping, prevMo);

				// Check if this is valid for selection
				if (
					calViewState?.yr > this.state.minDate?.currYr ||
					(calViewState?.yr == this.state.minDate?.currYr &&
						prevMo >= this.state.minDate?.currMo)
				) {
					const {
						qtrMoWkDateMapping,
					} = this.generateMapping(
						this.state.calViewState.yr,
						Math.ceil((this.state.calViewState.mo - 1) / 3),
						this.state.calViewState.mo - 1
					);

					let qtrMoWkDate_Mapping = _.cloneDeep(qtrMoWkDateMapping);
					let month_from_month = prevMo;
					let quarter_from_month = quarterFromMonth;
					let week_from_month = weekFromMonth;
					let yr_from_month = calViewState.yr;
					if (prevMo === 0) {
						const {
							qtrMoWkDateMapping,
						} = this.generateMapping(this.state.calViewState.yr - 1, 4, 12);

						qtrMoWkDate_Mapping = qtrMoWkDateMapping;
						quarter_from_month = 4;
						month_from_month = 12;
						week_from_month = 52;
						yr_from_month = qtrMoWkDateMapping.year;
					}
					const {
						currentDateSelectionMapping,
						totalDaysInView,
					} = getCurrentQuarterMappingForViewType({
						headerViewType: this.props.calendarFrequency,
						headerMapping: qtrMoWkDate_Mapping,
						currentQuarter: quarter_from_month,
						currentMonth: month_from_month,
						fiscalCalendar: this.props.fiscalCalendar,
					});
					this.setState({
						calViewState: {
							...calViewState,
							wk: week_from_month,
							mo: month_from_month,
							qtr: quarter_from_month,
							yr: yr_from_month,
						},
						currentDateSelectionMapping,
						totalDaysInView,
					});
					this.props.setCalCurrentState({
						...this.props.calCurrentState,
						[this.props.calDateRange]: {
							...calViewState,
							wk: week_from_month,
							mo: month_from_month,
							qtr: quarter_from_month,
							yr: yr_from_month,
						},
					});
				} else {
					this.onOutofBoundary("prevMo");
					return false;
				}
				break;
			}
			case "quarter": {
				if (calViewState.qtr === 1) {
					this.onPrevYrChng();
					return;
				}
				const prevQtr = calViewState.qtr - 1;
				let {
					monthForQuarter,
					weekForQuarter,
				} = getWeekAndMonthFromQuarter(qtrMoWkDateMapping, prevQtr);

				// Check if this is valid for selection
				if (
					this.state.calViewState?.yr > this.state.minDate?.currYr ||
					(this.state.calViewState?.yr ==
						this.state.minDate?.currYr &&
						prevQtr >= this.state.minDate?.currQtr)
				) {
					const {
						qtrMoWkDateMapping,
					} = this.generateMapping(this.state.calViewState.yr, this.state.calViewState.qtr - 1, this.state.calViewState.mo);

					let qtrMoWkDate_Mapping = _.cloneDeep(qtrMoWkDateMapping);
					let month_for_quarter = monthForQuarter;
					let quarter_for_quarter = prevQtr;
					let week_for_quarter = weekForQuarter;
					let yr_for_quarter = calViewState.yr;
					if (prevQtr === 0) {
						const {
							qtrMoWkDateMapping,
						} = this.generateMapping(this.state.calViewState.yr - 1, 4, 12);

						qtrMoWkDate_Mapping = qtrMoWkDateMapping;
						quarter_for_quarter = 4;
						month_for_quarter = 12;
						week_for_quarter = 52;
						yr_for_quarter = qtrMoWkDateMapping.year;
					}
					const {
						currentDateSelectionMapping,
						totalDaysInView,
					} = getCurrentQuarterMappingForViewType({
						headerViewType: this.props.calendarFrequency,
						headerMapping: qtrMoWkDate_Mapping,
						currentQuarter: quarter_for_quarter,
						currentMonth: month_for_quarter,
						fiscalCalendar: this.props.fiscalCalendar,
					});
					this.setState({
						calViewState: {
							...calViewState,
							wk: week_for_quarter,
							mo: month_for_quarter,
							qtr: quarter_for_quarter,
							yr: yr_for_quarter,
						},
						currentDateSelectionMapping,
						totalDaysInView,
					});

					this.props.setCalCurrentState({
						...this.props.calCurrentState,
						[this.props.calDateRange]: {
							...calViewState,
							wk: week_for_quarter,
							mo: month_for_quarter,
							qtr: quarter_for_quarter,
							yr: yr_for_quarter,
						},
					});
				} else {
					this.onOutofBoundary("prevQtr");
					return false;
				}
				break;
			}
			case "year":
				this.onPrevYrChng();
				break;

			default:
				break;
		}
	};

	onNxtClick = () => {
		const {
			calViewState,
			calTypToggleVal,
			noOfWks,
			qtrMoWkDateMapping,
		} = this.state;
		switch (calTypToggleVal) {
			case "week": {
				if (calViewState.wk === noOfWks) {
					this.onNxtYrChng();
					return;
				}
				const nxtWk = calViewState.wk + 1;

				const {
					quarterFromWeek,
					monthFromWeek,
				} = getMonthAndQuarterFromWeek(qtrMoWkDateMapping, nxtWk);

				if (
					this.state.calViewState?.yr < this.state.maxDate?.currYr ||
					(this.state.calViewState?.yr ==
						this.state.maxDate?.currYr &&
						nxtWk <= this.state.maxDate?.currWk)
				) {
					const {
						qtrMoWkDateMapping,
					} = this.generateMapping(this.state.calViewState.yr, this.state.calViewState.qtr, this.state.calViewState.mo);

					let qtrMoWkDate_Mapping = _.cloneDeep(qtrMoWkDateMapping);
					let month_from_week = monthFromWeek;
					let quarter_from_week = quarterFromWeek;
					let week_from_week = nxtWk;
					let yr_from_week = calViewState.yr;
					if (nxtWk === 53) {
						const {
							qtrMoWkDateMapping,
						} = this.generateMapping(this.state.calViewState.yr + 1, 1, 1);
						qtrMoWkDate_Mapping = qtrMoWkDateMapping;
						yr_from_week = qtrMoWkDateMapping.year;
						month_from_week = 1;
						quarter_from_week = 1;
						week_from_week = 1;
					}
					const {
						currentDateSelectionMapping,
						totalDaysInView,
					} = getCurrentQuarterMappingForViewType({
						headerViewType: this.props.calendarFrequency,
						headerMapping: qtrMoWkDate_Mapping,
						currentQuarter: quarter_from_week,
						currentMonth: month_from_week,
						fiscalCalendar: this.props.fiscalCalendar,
					});

					this.setState({
						calViewState: {
							...calViewState,
							wk: week_from_week,
							mo: month_from_week,
							qtr: quarter_from_week,
							yr: yr_from_week,
						},
						currentDateSelectionMapping,
						totalDaysInView,
					});

					this.props.setCalCurrentState({
						...this.props.calCurrentState,
						[this.props.calDateRange]: {
							...calViewState,
							wk: week_from_week,
							mo: month_from_week,
							qtr: quarter_from_week,
							yr: yr_from_week,
						},
					});
				} else {
					this.onOutofBoundary("nextWk");
					return false;
				}

				break;
			}
			case "month": {
				if (calViewState.mo === 12) {
					this.onNxtYrChng();
					return;
				}
				const nxtMo = calViewState.mo + 1;
				const {
					quarterFromMonth,
					weekFromMonth,
				} = getWeekAndQuarterFromMonth(qtrMoWkDateMapping, nxtMo);
				if (
					calViewState?.yr < this.state.maxDate?.currYr ||
					(calViewState?.yr == this.state.maxDate?.currYr &&
						nxtMo <= this.state.maxDate?.currMo)
				) {
					const {
						qtrMoWkDateMapping,
					} = this.generateMapping(
						this.state.calViewState.yr,
						Math.ceil((this.state.calViewState.mo + 1) / 3),
						this.state.calViewState.mo + 1
					);

					let qtrMoWkDate_Mapping = _.cloneDeep(qtrMoWkDateMapping);
					let month_from_month = nxtMo;
					let quarter_from_month = quarterFromMonth;
					let week_from_month = weekFromMonth;
					let yr_from_month = this.state.calViewState.yr;
					if (
						(this.state.maxDate?.currYr > this.state.calViewState.yr)
						&& nxtMo === 13
					) {
						const {
							qtrMoWkDateMapping,
						} = this.generateMapping(this.state.calViewState.yr + 1, 1, 1);

						qtrMoWkDate_Mapping = qtrMoWkDateMapping;
						yr_from_month = qtrMoWkDateMapping.year;
						month_from_month = 1;
						quarter_from_month = 1;
						week_from_month = 1;
					}
					const {
						currentDateSelectionMapping,
						totalDaysInView,
					} = getCurrentQuarterMappingForViewType({
						headerViewType: this.props.calendarFrequency,
						headerMapping: qtrMoWkDate_Mapping,
						currentQuarter: quarter_from_month,
						currentMonth: month_from_month,
						fiscalCalendar: this.props.fiscalCalendar,
					});
					this.setState({
						calViewState: {
							...calViewState,
							wk: week_from_month,
							mo: month_from_month,
							qtr: quarter_from_month,
							yr: yr_from_month,
						},
						currentDateSelectionMapping: currentDateSelectionMapping,
						totalDaysInView: totalDaysInView,
					});

					this.props.setCalCurrentState({
						...this.props.calCurrentState,
						[this.props.calDateRange]: {
							...calViewState,
							wk: week_from_month,
							mo: month_from_month,
							qtr: quarter_from_month,
							yr: yr_from_month,
						},
					});
				} else {
					this.onOutofBoundary("nextMo");
					return false;
				}
				break;
			}
			case "quarter": {
				if (calViewState.qtr === 4) {
					this.onNxtYrChng();
					return;
				}
				const nxtQtr = calViewState.qtr + 1;

				let {
					monthForQuarter,
					weekForQuarter,
				} = getWeekAndMonthFromQuarter(qtrMoWkDateMapping, nxtQtr);

				if (
					this.state.calViewState?.yr < this.state.maxDate?.currYr ||
					(this.state.calViewState?.yr ==
						this.state.maxDate?.currYr &&
						nxtQtr <= this.state.maxDate?.currQtr)
				) {
					const {
						qtrMoWkDateMapping,
					} = this.generateMapping(this.state.calViewState.yr, this.state.calViewState.qtr + 1, this.state.calViewState.mo);

					let qtrMoWkDate_Mapping = _.cloneDeep(qtrMoWkDateMapping);
					let month_for_quarter = monthForQuarter;
					let quarter_for_quarter = nxtQtr;
					let week_for_quarter = weekForQuarter;
					let yr_for_quarter = this.state.calViewState.yr;
					if (
						(this.state.maxDate?.currYr > this.state.calViewState.yr)
						&& nxtQtr === 5
					) {
						const {
							qtrMoWkDateMapping,
						} = this.generateMapping(this.state.calViewState.yr + 1, 1, 1);

						qtrMoWkDate_Mapping = qtrMoWkDateMapping;
						month_for_quarter = 1;
						quarter_for_quarter = 1;
						week_for_quarter = 1;
						yr_for_quarter = qtrMoWkDateMapping.year;
					}
					const {
						currentDateSelectionMapping,
						totalDaysInView,
					} = getCurrentQuarterMappingForViewType({
						headerViewType: this.props.calendarFrequency,
						headerMapping: qtrMoWkDate_Mapping,
						currentQuarter: quarter_for_quarter,
						currentMonth: month_for_quarter,
						fiscalCalendar: this.props.fiscalCalendar,
					});
					this.setState({
						calViewState: {
							...calViewState,
							wk: week_for_quarter,
							mo: month_for_quarter,
							qtr: quarter_for_quarter,
							yr: yr_for_quarter,
						},
						currentDateSelectionMapping,
						totalDaysInView,
					});

					this.props.setCalCurrentState({
						...this.props.calCurrentState,
						[this.props.calDateRange]: {
							...calViewState,
							wk: week_for_quarter,
							mo: month_for_quarter,
							qtr: quarter_for_quarter,
							yr: yr_for_quarter,
						},
					});
				} else {
					this.onOutofBoundary("nextQtr");
					return false;
				}
				break;
			}
			case "year":
				this.onNxtYrChng();
				break;

			default:
				break;
		}
	};

	onYrChng = (yr, calViewState) => {
		this.setState({
			calViewState,
			...this.generateMapping(yr, calViewState.qtr, calViewState.mo),
		});
		this.props.setCalCurrentState({
			...this.props.calCurrentState,
			[this.props.calDateRange]: {
				...calViewState,
				...this.generateMapping(yr, calViewState.qtr, calViewState.mo),
			},
		});
	};

	onNxtYrChng = () => {
		const nxtYr = this.state.calViewState.yr + 1;
		const max_year_allowed_from_fiscal_calendar = this.props?.fiscalCalendar?.[this.props?.fiscalCalendar?.length - 1]?.year;
		if (nxtYr > max_year_allowed_from_fiscal_calendar) {
			this.props.toastError("Fiscal year data not present");
			return;
		}
		if (nxtYr <= this.state.maxDate?.currYr) {
			const calViewState = {
				mo: 1,
				wk: 1,
				qtr: 1,
				yr: nxtYr,
			};
			this.onYrChng(nxtYr, calViewState);
		} else {
			this.onOutofBoundary("nextYr");
			return false;
		}
	};

	onPrevYrChng = () => {
		const prevYr = this.state.calViewState.yr - 1;
		const min_year_allowed_from_fiscal_calendar = this.props?.fiscalCalendar?.[0]?.year;
		if (prevYr < min_year_allowed_from_fiscal_calendar) {
			this.props.toastError("Fiscal year data not present");
			return;
		}
		if (prevYr >= this.state.minDate?.currYr) {
			const calViewState = {
				mo: 12,
				wk: getWeekCount(prevYr, this.props.fiscalCalendar),
				qtr: 4,
				yr: prevYr,
			};
			this.onYrChng(prevYr, calViewState);
		} else {
			this.onOutofBoundary("prevYr");
			return false;
		}
	};

	getCalDates = () => {
		const {
			calTypToggleVal,
			calViewState,
		} = this.state;
		const { wk, mo, qtr, yr } = calViewState;
		const {
			qtrMoWkDateMapping,
			currentDateSelectionMapping,
		} = this.generateMapping(yr, qtr, mo);
		const { minDate, maxDate } = geStartAndEndDate(
			qtrMoWkDateMapping,
			currentDateSelectionMapping,
			qtr,
			mo,
			wk,
			calTypToggleVal
		);
		return { minDate, maxDate };
	};

	render() {
		const {
			calViewState,
			qtrMoWkDateMapping,
			currentDateSelectionMapping,
			totalDaysInView,
		} = this.state;
		const { onCalEventClick, eventList } = this.props;
		if (_.isEmpty(qtrMoWkDateMapping)) {
			return "";
		}
		const { minDate: calStDt, maxDate: calEnDt } = this.getCalDates();
		const filteredEvents = filterEventsforDisplay({
			eventList,
			calStDt,
			calEnDt,
		});

		return (
			<div>
				<div className="calendar-wrapper" id="calendar-wrapper">
					<CalendarHeader
						year={calViewState.yr}
						onPrevClick={this.onPrevClick}
						onNxtClick={this.onNxtClick}
						currentDateSelectionMapping={
							currentDateSelectionMapping
						}
						totalDaysInView={totalDaysInView}
					/>
					<div
						className="calendar-events-wrapper"
						id="calendar-events-wrapper"
					>
						<CalendarEventsList
							filteredEvents={filteredEvents}
							calStDt={calStDt}
							calEnDt={calEnDt}
							onCalEventClick={onCalEventClick}
							getDetailContent={this.props.getDetailContent}
							getEventTileExtraContent={
								this.props.getEventTileExtraContent
							}
							currentDateSelectionMapping={
								currentDateSelectionMapping
							}
							totalDaysInView={totalDaysInView}
						/>
					</div>
				</div>
				<CalendarEventsColorInfo />
			</div>
		);
	}
}

export default Calendar;
