import React, { useEffect } from "react";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "impact-ui";

import {
	getFilterOptions,
	setDateRangeToStore,
} from "../../../store/features/filters/filters";

import ComponentFiltersSelect from "./ComponentFiltersSelect";
import "./ComponentFilters.scss";

function ComponentFilters(props) {
	const { filterConfig, callAPIonLoad = false, screen } = props;

	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	const dispatch = useDispatch();

	useEffect(() => {
		if (callAPIonLoad) {
			//iterates over complete config, and apply all auto filters.
			_.forEach(filterConfig, (group) => {
				if (group?.selectOnLoad) {
					//checks if filter is a dropdown, and if options are already available in store
					if (
						group?.filterType === "dropdown" &&
						_.isEmpty(filtersData?.[screen]?.[group?.filterId]?.options)
					) {
						//if options are not present, creates the payload based on apiEndpoint type, and calls the API
						let payload = {
							hierarchy_filters: {},
							query_column: group.filterId,
						};

						const group_ids = props.filterConfig.map(item => item.filterId);
						for (let i = 0; i < group_ids.length; i++) {
							const key = group_ids[i];
							const data = filtersData?.[props?.screen]?.[key];
							if (i >= group_ids.indexOf(props.filterId))
								break;
							if (
								key !== "dateRange"
								&& group_ids.includes(key)
								&& !_.isEmpty(data?.selectedOptionsArray)
							) {
								payload["hierarchy_filters"][key] = _.cloneDeep(
									data?.selectedOptionsArray
								);
							}
						}
						if (!_.isEmpty(group?.extraParams)) {
							payload = {
								...payload,
								...group.extraParams,
							};
						}
						dispatch(
							getFilterOptions({
								requestObject: _.cloneDeep(payload),
								filterEndpoint: group?.apiEndpoint,
								from: screen || "",
								selectedItems: group?.selection || null,
								selectOnLoad: group?.selectOnLoad || false,
								filterName: group.filterId,
							})
						);
					}
					//if filters is a dropdown, sets the start and end date in redux store.
					if (group?.filterType === "dateRange") {
						const dateRange = {
							start_date: group?.start_date || null,
							end_date: group?.end_date || null,
						};
						dispatch(
							setDateRangeToStore({
								dateRange: _.cloneDeep(dateRange),
								from: screen || "",
								key: group.filterId,
							})
						);
					}
				}
			});
		}
	}, [
		filterConfig
	]);

	return (
		<div>
			<div className="componentFiltersContainer">
				{_.map(filterConfig, (config) => {
					if (config.filterType === "dropdown") {
						return (
							<ComponentFiltersSelect {...config} {...props} />
						);
					}
				})}
			</div>
			<div className="componentFiltersBtnContainer">
				{props?.secondaryButtonText ? (
					<Button
						onClick={props.onSecondaryButtonClick}
						size="large"
						variant="url"
					>
						{props.secondaryButtonText}
					</Button>
				) : null}
				{props?.primaryButtonText ? (
					<Button
						onClick={props.onPrimaryButtonClick}
						size="large"
						variant="primary"
					>
						{props.primaryButtonText}
					</Button>
				) : null}
			</div>
		</div>
	);
}

export default ComponentFilters;
