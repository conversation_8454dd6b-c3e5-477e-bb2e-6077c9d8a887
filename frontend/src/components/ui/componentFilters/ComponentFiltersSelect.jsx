import React, { useState, useEffect } from "react";
import _ from "lodash";
import { Select } from "impact-ui";
import { useSelector, useDispatch } from "react-redux";
import { global_labels } from "../../../constants/Constants";

import {
	setSelectedFilters,
	resetFiltersDataForId,
	getFilterOptions,
} from "../../../store/features/filters/filters";

function ComponentFiltersSelect(props) {
	const dispatch = useDispatch();
	const [currentOptions, setCurrentOptions] = useState([]);
	const [isOpen, setIsOpen] = useState(false);
	const [isSelectAll, setIsSelectAll] = useState(false);
	const { filtersData } = useSelector(
		(store) => store?.pricesmartPromoReducer.filters
	);

	const { hierachy_keys } = useSelector(
		(store) => store?.pricesmartPromoReducer.global
	);

	const hierarchyGlobalKeys = React.useMemo(() => ({
		...global_labels,
		...hierachy_keys,
	}), [hierachy_keys]);

	useEffect(() => {
		//once the options are present, sets in respective useState
		if (filtersData?.[props?.screen]?.[props.filterId]?.options) {
			// Re-order the options so that selected filters appear on top
			let reOrderedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.options || []
			);
			let selectedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.selectedOptionsArray
			);

			if (
				!_.isEmpty(selectedOptions) &&
				selectedOptions.length !== reOrderedOptions.length
			) {
				reOrderedOptions.sort((a, b) => {
					if (
						selectedOptions.includes(a.value) &&
						!selectedOptions.includes(b.value)
					) {
						return -1; // Keep `a` before `b`
					} else if (
						!selectedOptions.includes(a.value) &&
						selectedOptions.includes(b.value)
					) {
						return 1; // Keep `b` before `a`
					} else {
						// Secondary condition: sort by `label`
						return a.label - b.label;
					}
				});
			}
			setCurrentOptions(reOrderedOptions);
		}
	}, [filtersData?.[props?.screen]?.[props.filterId]?.options]);

	useEffect(() => {
		//to set the select all checkbox checked or unchecked based on selected options.
		if (
			filtersData?.[props?.screen]?.[props.filterId]?.selectedOptions
				.length ===
			filtersData?.[props?.screen]?.[props.filterId]?.options?.length
		) {
			setIsSelectAll(true);
		} else {
			setIsSelectAll(false);
		}
	}, [filtersData?.[props?.screen]?.[props.filterId]?.selectedOptions]);

	const onSelectionChange = (data) => {
		if (props.isDisabled) return;
		dispatch(
			setSelectedFilters({
				data,
				from: props?.screen,
				filterId: props.filterId,
			})
		);
		// find the index of current filter.
		const filterIndex = _.findIndex(
			props.filterConfig,
			(data) => data.filterId === props.filterId
		);
		if (filterIndex < 0) return;
		let deleteDataIds = [];
		//to get all filter(s) id in a group, which are after current filter.
		for (let i = filterIndex + 1; i < props.filterConfig.length; i++) {
			deleteDataIds.push(props.filterConfig[i].filterId);
		}
		// send all Ids which lies after current filter. Store has functionality written to clear out
		// all filters data whose id will be present in deleteDataIds
		dispatch(
			resetFiltersDataForId({
				data: _.cloneDeep(deleteDataIds),
				from: props?.screen,
			})
		);
	};
	const onClearAll = () => {
		onSelectionChange([]);
	};

	const onDropdownOpen = () => {
		//when you open any dropdown, if options are present in store, do nothing.
		if (filtersData[props?.screen]?.[props.filterId]?.options?.length) {
			// Re-order the options so that selected filters appear on top
			let reOrderedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.options
			);
			let selectedOptions = _.cloneDeep(
				filtersData[props?.screen][props.filterId]?.selectedOptionsArray
			);

			if (
				!_.isEmpty(selectedOptions) &&
				selectedOptions.length !== reOrderedOptions.length
			) {
				reOrderedOptions.sort((a, b) => {
					if (
						selectedOptions.includes(a.value) &&
						!selectedOptions.includes(b.value)
					) {
						return -1; // Keep `a` before `b`
					} else if (
						!selectedOptions.includes(a.value) &&
						selectedOptions.includes(b.value)
					) {
						return 1; // Keep `b` before `a`
					} else {
						// Secondary condition: sort by `label`
						return a.label - b.label;
					}
				});
			}
			setCurrentOptions(reOrderedOptions);
			return;
		}
		// find the index of current filter.
		const filterIndex = _.findIndex(
			props.filterConfig,
			(data) => data.filterId === props.filterId
		);
		if (filterIndex > -1) {
			//if current filter is the first filter in the group, no need for any validations. Call the API.
			if (filterIndex === 0) {
				callFiltersOptionAPI();
			} else {
				//if current filter is not the first filter in the group,
				//check if current filter is mandatory filter
				const filterConfig = props.filterConfig[filterIndex];
				//if current filter is mandatory, check if previous filter has selected items, if yes, call the API.
				if (filterConfig?.isMandatory) {
					const prevFilterId =
						props.filterConfig[filterIndex - 1].filterId;
					if (
						filtersData[props?.screen][prevFilterId]
							?.selectedOptions?.length
					) {
						callFiltersOptionAPI();
					}
				} else {
					//if current filter is not mandatory, iterate through current group.
					let callAPIFlag = true;
					_.forEach(props?.filterConfig, (data) => {
						//if there is mandatory filter which does not have selected options, set callAPIFlag as false
						if (
							data?.isMandatory &&
							data?.filterId !== "dateRange"
						) {
							if (
								!filtersData[props?.screen][data?.filterId]
									?.selectedOptions?.length
							) {
								callAPIFlag = false;
							}
						}
						if (
							data?.filterId == "dateRange" &&
							(!filtersData[props?.screen][data?.filterId]
								.start_date ||
								!filtersData[props?.screen][data?.filterId]
									.end_date)
						) {
							callAPIFlag = false;
						}
					});
					//if callAPIFlag is true, call the API.
					if (callAPIFlag) callFiltersOptionAPI();
				}
			}
		}
	};

	const callFiltersOptionAPI = () => {
		let payload = {
			allow_only_active_products: true,
			application: "promo",
			filters: {
				product_hierarchy: {},
				store_hierarchy: {},
			},
			hierarchy_type: props.filterId,
			// screen_name: props?.screen || "",
		};
		//in current group, find the index of current filter.
		const filterIndex = _.findIndex(
			props.filterConfig,
			(data) => data.filterId === props.filterId
		);

		//fetch current filter config, and check apiEndpoint.
		const group = props.filterConfig[filterIndex];
		const group_ids = props.filterConfig.map(item => item.filterId) || [];
		payload = {
			hierarchy_filters: {},
			query_column: props.filterId,
		};
		for (let i = 0; i < group_ids.length; i++) {
			const key = group_ids[i];
			const data = filtersData?.[props?.screen]?.[key];
			if (i >= group_ids.indexOf(props.filterId))
				break;
			if (
				key !== "dateRange"
				&& group_ids.includes(key)
				&& !_.isEmpty(data?.selectedOptionsArray)
			) {
				payload["hierarchy_filters"][key] = _.cloneDeep(
					data?.selectedOptionsArray
				);
			}
		}

		if (!_.isEmpty(group?.extraParams)) {
			payload = {
				...payload,
				...group.extraParams,
			};
		}

		// call the API and set the data.
		const selectedItems = filtersData?.[props?.screen]?.[props.filterId]?.selectedOptionsArray || [];
		dispatch(
			getFilterOptions({
				requestObject: _.cloneDeep(payload),
				filterEndpoint: group?.apiEndpoint,
				from: props?.screen || "",
				selectedItems: selectedItems,
				selectOnLoad: !!selectedItems?.length,
				filterName: group?.filterId,
			})
		);
	};

	const onSelectAllHandler = (e) => {
		if (props.isDisabled) return;
		//if select all is checked, set all options as selected, else clear all selected options.
		if (e.target.checked) {
			onSelectionChange(
				filtersData?.[props?.screen]?.[props.filterId]?.options
			);
		} else {
			onSelectionChange([]);
		}
	};

	return (
		<Select
			currentOptions={currentOptions}
			initialOptions={_.cloneDeep(
				filtersData[props?.screen]?.[props.filterId]?.options || []
			)}
			label={hierarchyGlobalKeys?.[props?.filterId] || ""}
			labelOrientation="top"
			name={hierarchyGlobalKeys?.[props?.filterId] || ""}
			setSelectedOptions={onSelectionChange}
			onClearAll={onClearAll}
			onDropdownOpen={onDropdownOpen}
			onSelectAll={onSelectAllHandler}
			placeholder="Select.."
			isWithSearch={true}
			isRequired={props?.isMandatory || false}
			isSelectAll={isSelectAll}
			toggleSelectAll={props?.isMulti || false}
			isClearable={true}
			isMulti={props?.isMulti || false}
			selectedOptions={
				filtersData?.[props?.screen]?.[props.filterId]
					?.selectedOptions || []
			}
			setCurrentOptions={setCurrentOptions}
			setIsSelectAll={setIsSelectAll}
			isOpen={isOpen}
			setIsOpen={setIsOpen}
			isCloseWhenClickOutside={true}
		/>
	);
}

export default ComponentFiltersSelect;
