import * as formatters from "../../../../utils/helpers/formatter";

const seriesColors = [
	{
		color: "rgba(86, 50, 213, 0.60)",
		legendColor: "rgba(104, 122, 241, 0.70)",
	},
	{
		color: "rgba(39, 185, 210, 0.60)",
		legendColor: "rgba(59, 178, 115, 0.70);",
	},
];

export const lineCharts = (props) => {
	const { data, yAxisLabel } = props;
	const getchartSeriesData = () => {
		let seriesData = [];
		if (data?.seriesData?.length) {
			_.forEach(data.seriesData, (data, index) => {
				seriesData.push({
					...data,
					...seriesColors[index],
				});
			});
		}
		return _.cloneDeep(seriesData);
	};
	return {
		chart: {
			type: "line",
			height: 350,
			spacing: [12, 12, 8, 12],
			plotBackgroundColor: "#ffffff",
			styleMode: true,
		},
		navigation: {
			buttonOptions: {
				enabled: false,
			},
		},
		title: {
			text: "",
		},
		yAxis: {
			title: {
				text: yAxisLabel || "",
				margin: 60,
				style: {
					color: "#60697D",
					fontSize: "14px",
					fontWeight: "500",
				},
			},
			labels: {
				formatter: function () {
					return formatters.kFormatter(this.value);
				},
				style: {
					color: "#60697D",
					fontSize: "14px",
					fontWeight: "500",
				},
			},
			tickAmount: 8,
			tickeInterval: 20,
			gridLineColor: "#EEF0F5",
			lineColor: "#D9DDE7",
			lineWidth: 1,
		},
		xAxis: {
			labels: {
				align: "center",
				x: 8,
				style: {
					color: "#60697D",
					fontSize: "14px",
					fontWeight: "500",
				},
			},
			lineColor: "#D9DDE7",
			lineWidth: 1,
			categories: data?.categories || [],
		},
		legend: {
			enabled: true,
			align: "right",
			symbolPadding: 0,
			symbolWidth: 0,
			symbolHeight: 0,
			squareSymbol: false,
			useHTML: true,
			margin: 12,
			labelFormatter: function () {
				return `<span style="display: flex; align-items: center; gap: 8px;"><span style="width: 12px; height: 12px; background-color: ${
					typeof this.color === "object"
						? this.options?.legendColor
						: this.color
				}; border-radius: 2px;"></span> <span style="font-color: #31416E; font-size: 14px;">${
					this.name
				}</span></span>`;
			},
		},
		tooltip: {
			enabled: true,
			useHTML: true,
			backgroundColor: "#000000",
			style: {
				color: "#FFFFFF",
			},
			formatter: function () {
				return `<div style="display: flex; flex-direction: column; gap: 8px;"><div>${
					this.x
				}</div><div>${
					props?.formatter
						? formatters[props.formatter]({
							value: this.y,
							...props?.data?.extra,
						})
						: this.y
				}</div></div>`;
			},
		},
		plotOptions: {
			series: {
				label: {
					enabled: false,
					style: {
						color: "#60697D",
						fontSize: "14px",
						fontWeight: "500",
					},
				},
				states: {
					hover: {
						enabled: true,
					},
				},
				lineWidth: 3,
				marker: {
					radius: 5,
					symbol: "circle",
				},
			},
		},
		series: getchartSeriesData(),
		credits: {
			enabled: false,
		},
	};
};
