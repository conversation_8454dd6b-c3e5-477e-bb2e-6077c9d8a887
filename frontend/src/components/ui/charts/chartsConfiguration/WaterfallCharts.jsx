import * as formatters from "../../../../utils/helpers/formatter";

export const waterfallCharts = (props) => {
  const { data,  yAxisLabel="", formatter: formatterName } = props;

  // Define gradient colors for positive, negative, and total values
  const upColorGradient = {
    linearGradient: { x1: 0, y1: 1, x2: 0 },
    stops: [
      [0, "rgba(204, 252, 226, 0.50)"], // Light Green
      [1, "#3BB273"], // Green
    ],
  };

  const downColorGradient = {
    linearGradient: { x1: 0, y1: 1, x2: 0 },
    stops: [
      [0, "#C13453"], // Red (Updated Red Color)
      [1, "rgba(255, 204, 204, 0.50)"], // Light Red (End with light red)
    ],
  };

  const totalColorGradient = {
    linearGradient: { x1: 0, y1: 1, x2: 0 },
    stops: [
      [0, "rgba(179, 189, 248, 0.2)"], // Light Blue
      [1, "#687AF1"], // Blue
    ],
  };

  // Extract repeated text styles into a constant
  const textStyle = {
    color: "#60697D",
    fontSize: "12px",
    fontWeight: "400",
  };

  // Process data to set colors appropriately without mutating original data
  const processedData = data.map((point) => ({
    ...point,
    ...(point.isSum && { color: totalColorGradient }),
    ...(point.y > 0 && !point.isSum && { color: upColorGradient }),
    ...(point.y < 0 && !point.isSum && { color: downColorGradient }),
  }));

  // Ensure formatter functions are accessible within formatter callbacks
  const formatValue = (value) => {
    return formatterName && formatters[formatterName]
      ? formatters[formatterName]({ value })
      : formatters.kFormatter(value); // Fallback to kFormatter
  };

  const formatLabel = (value) => formatters.kFormatter(value);

  // Calculate dynamic ticks based on max value
  const maxValue = Math.max(...data.map((point) => Math.abs(point.y)));
  const dynamicTicks = Math.ceil(maxValue / 100) + 2;

  return {
    chart: {
      type: "waterfall",
      height: 350,
      spacing: [12, 12, 8, 12],
      plotBackgroundColor: "#ffffff",
      styleMode: true,
    },
    navigation: {
      buttonOptions: {
        enabled: false,
      },
    },
    title: {
      text:  yAxisLabel || "",
      align: "left",
      margin: 20,
      style: {
        color: "#000000", 
        fontSize: "14px",
        fontWeight: "600",
      },
    },
    yAxis: {
      title: {
        text: null, // Remove Y-axis label
      },
      labels: {
        formatter: function () {
          return formatLabel(this.value);
        },
        style: textStyle,
      },
      tickAmount: dynamicTicks,
      tickInterval: Math.ceil(maxValue / dynamicTicks),
      gridLineColor: "#EEF0F5",
      lineColor: "#D9DDE7",
      lineWidth: 1,
    },
    xAxis: {
      type: "category",
      labels: {
        align: "center",
        x: 8,
        style: textStyle,
      },
      lineColor: "#D9DDE7",
      lineWidth: 1,
    },
    legend: {
      enabled: false,
    },
    tooltip: {
      enabled: true,
      headerFormat: "",
      useHTML: true,
      backgroundColor: "#000000",
      style: {
        color: "#FFFFFF",
      },
      formatter: function () {
        return `
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <div>${formatLabel(this.y)}</div>
          </div>
        `;
      },
    },
    plotOptions: {
      series: {
        borderRadius: {
          radius: 4,
        },
        dataLabels: {
          enabled: false, // Disable value display on bars
        },
        states: {
          hover: {
            enabled: true,
          },
        },
        lineWidth: 1,
        pointWidth: 35,
        groupPadding: 0.1, // Add spacing between bars
        pointPadding: 0.1,
      },
    },
    series: [
      {
        data: processedData,
        pointPadding: 0,
        borderWidth: 0,
        pointWidth: 35, // Adjusted width
      },
    ],
    credits: {
      enabled: false,
    },
  };
};
