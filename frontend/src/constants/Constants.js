import moment from "moment";
import { replaceSpecial<PERSON><PERSON>cter } from "../utils/helpers/utility_helpers";

export const APP_NAME = "Pricesmart Markdown";

export const DEFAULT_CURRENCY = {
	label: "USD",
	value: 1,
	symbol: "$",
};

export const setGlobalLabelsFromGlobalConfig = (global_configs) => {
	const label_keys = [
		"primary",
		"expanded",
		"standard",
		"plural",
		"standard_plural",
		"alias",
		"alias_plural",
	];

	Object.keys(global_configs).map((key) => {
		global_labels[key] = global_configs[key]?.primary;
		label_keys.forEach((label_key) => {
			if (global_configs[key]?.[label_key]) {
				global_labels[`${key}_${label_key}`] = global_configs[key]?.[label_key];
			}
		});
	});
};

export const SUB_MODULES = {
	decisionDashboard: ["Decision Dashboard Strategy"],
	workbench: ["Workbench Strategy"],
	approval: ["Approval Strategy"],
	reporting: ["Reports"],
	storeGroupConfiguration: ["Store Group"],
	productGroupConfiguration: ["Product Group"],
	pcdConfiguration: ["PCD Configuration"],
	strategyConfiguration: ["Strategy Configuration"],
};

export const calendarConfig = {
	fstDayOfWk: 1, // Monday
	fstMoOfYr: 0, // Jan
};

export const DEFAULT_DATE_FORMAT = "YYYY-MM-DD";
export const DEFAULT_START_DATE_MOMENT = moment().add(0, "days");
export const DEFAULT_END_DATE_MOMENT = moment().add(120, "days");
export const DEFAULT_START_DATE = moment()
	.add(0, "days")
	.format(DEFAULT_DATE_FORMAT);
export const DEFAULT_END_DATE = moment()
	.add(120, "days")
	.format(DEFAULT_DATE_FORMAT);
export const DEFAULT_START_DATE_MOMENT_REPORTING = moment().subtract(
	31,
	"days"
);
export const DEFAULT_END_DATE_MOMENT_REPORTING = moment().subtract(1, "days");
export const DEFAULT_START_DATE_MOMENT_DECISION_DASHBOARD = moment().subtract(
	14,
	"days"
);

export const NUMBER_OF_EXTRA_YEARS_IN_DATE_PICKER = 1;

export const singleSelectStyles = {
	control: (provided, state) => ({
		...provided,
		height: "32px",
		minHeight: "32px",
		fontSize: "0.8rem",
		border: "1px solid #cccccc",
		top: "2px",
		cursor: "pointer",
		boxShadow: "none",
		"&:hover": {
			border: "1px solid #cccccc",
		},
	}),
	valueContainer: (provided, state) => ({
		...provided,
		height: "32px",
		padding: "0 6px",
	}),
	input: (provided, state) => ({
		...provided,
		margin: "0px",
		color: "transparent",
	}),
	indicatorsContainer: (provided, state) => ({
		...provided,
		height: "32px",
		// minHeight: "32px",
		padding: "6px",
	}),
	indicatorSeparator: (provided, state) => ({
		...provided,
		display: "none",
	}),
	indicatorContainer: (provided, state) => ({
		...provided,
		padding: "0px 8px",
	}),
	dropdownIndicator: (provided, state) => ({
		...provided,
		padding: "2px 4px 2px",
		fontSize: "0.8rem",
	}),
	menuList: (provided, state) => ({
		...provided,
		fontSize: "0.8rem",
		padding: 0,
	}),
};

export const STRATEGY_STATUS = [
	{
		label: "Draft/Copied",
		value: 0,
	},
	{
		label: "Pending Review",
		value: 1,
	},
	{
		label: "Finalized",
		value: 2,
	},
	{
		label: "Archived",
		value: 6,
	},
];

export const MODEL_API = {
	WORKBENCH_AGGREGATE_METRICS: {
		MODEL_ID: 1,
		COLUMNS: [
			// "target_gross_margin",
			"finalized_gross_margin",
			"finalized_baseline_gross_margin",
			"finalized_incremental_gross_margin",
			"projected_gross_margin",
			// "target_revenue",
			"finalized_revenue",
			"finalized_baseline_revenue",
			"finalized_incremental_revenue",
			"projected_revenue",
			// "target_sales_units",
			"finalized_sales_units",
			"finalized_baseline_sales_units",
			"finalized_incremental_sales_units",
			"projected_sales_units",
		],
	},
	METRICS_EVENT_TIME: {
		MODEL_ID: 4,
	},
	METRICS_PROMO_TIME: {
		MODEL_ID: 3,
	},
	METRICS_BULK_PROMO_EDIT: {
		MODEL_ID: 5,
	},
	METRICS_EVENT_PRODUCTS: {
		MODEL_ID: 8,
	},
	METRICS_PROMO_PRODUCTS: {
		MODEL_ID: 7,
	},
	METRICS_EVENTS_PROMO_LIST: {
		MODEL_ID: 9,
	},
	METRICS_EVENTS_FILTERS_LIST: {
		MODEL_ID: 10,
	},
	METRICS_REPORTING: {
		MODEL_ID: 12,
	},
	METRICS_REPORTING_TOP_BOTTOM_OFFERS: {
		MODEL_ID: 11,
	},
	METRICS_REPORTING_WATERFALL: {
		MODEL_ID: 13,
	},
	METRICS_REPORTING_EVENT_FILTER: {
		MODEL_ID: 14,
	},
	METRICS_TOP_BOTTOM_OFFERS_DOWNLOAD: {
		MODEL_ID: 15,
	},
	GLOBAL_CONFIG_OPTIONS: {
		MODEL_ID: 16,
		parameters: {
			// event_ad_type: true,
			// offer_types: true,
			// event_channel: true,
		},
	},
	SAP_DOWNLOAD: {
		DOWNLOAD_1: 17,
		DOWNLOAD_2: 20,
		DOWNLOAD_3: 22,
		DOWNLOAD_4: 25,
	},
};

export const SUPPORT = "/support";

export const CONFLICT_TYPES = {
	0: "IN FINALIZED",
	1: "IN REQUEST",
};

export const overriddenValueColumns = [
	"markdown_finalized",
	"sales_units_ia_finalized",
	"sales_units_baseline",
	"sales_units_ia_incremental",
	"sales_dollars_ia_finalized",
	"sales_dollars_baseline",
	"sales_dollars_ia_incremental",
	"gross_margin_ia_finalized",
	"gross_margin_baseline",
	"gross_margin_ia_incremental",
	"margin_ia_recommended",
	"margin_scenario_1",
	"margin_scenario_2",
	"sales_ia_recommended",
	"sales_scenario_1",
	"sales_scenario_2",
	"sales_units_ia_recommended",
	"sales_units_scenario_1",
	"sales_units_scenario_2",
	"incremental_margin_ia_recommended",
	"incremental_margin_scenario_1",
	"incremental_margin_scenario_2",
	"incremental_sales_ia_recommended",
	"incremental_sales_scenario_1",
	"incremental_sales_scenario_2",
	"incremental_sales_units_ia_recommended",
	"incremental_sales_units_scenario_1",
	"incremental_sales_units_scenario_2",
	"markdown_budget",
	"units_incremental_ia",
	"revenue_incremental_finalized",
	"revenue_incremental_ia",
	"gross_margin_incremental_finalized",
	"gross_margin_incremental_ia",
	"units_finalized",
	"sales_units_baseline",
	"units_incremental_finalized",
	"revenue_finalized",
	"revenue_baseline",
	"revenue_incremental_finalized",
	"gross_margin_finalized",
	"gross_margin_baseline",
	"gross_margin_incremental_finalized",
	"units_baseline",
];

export const rule_flexibility_type_id = {
	Flexible: 0,
	Inflexible: 1,
};

export const Simulation_mapping = {
	ia_recc: {
		label: "IA Recommended",
		value: "ia_recc",
		finalize: true,
		is_edit: false,
		dataKeys: [
			{
				Units: "ia_recc_sales_units",
				"Revenue $": "ia_recc_revenue",
				"Margin $": "ia_recc_gm_dollar",
			},
			{
				"Margin %": "ia_recc_gm_percent",
				"AUR $": "ia_recc_aur",
				"AUM $": "ia_recc_aum",
			},
			{
				"ST %": "ia_recc_st_percent",
				"Markdown $": "ia_recc_markdown_dollar",
			},
		],
	},
	draft_scenario: {
		label: "Scenario In Draft",
		value: "draft_override",
		finalize: true,
		dataKeys: [
			{
				Units: "draft_override_sales_units",
				"Revenue $": "draft_override_revenue",
				"Margin $": "draft_override_gm_dollar",
			},
			{
				"Margin %": "draft_override_gm_percent",
				"AUR $": "draft_override_aur",
				"AUM $": "draft_override_aum",
			},
			{
				"ST %": "draft_override_st_percent",
				"Markdown $": "draft_override_markdown_dollar",
			},
		],
	},
	bl_override: {
		label: "Finalized",
		value: "bl_override",
		finalize: false,
		dataKeys: [
			{
				Units: "bl_override_sales_units",
				"Revenue $": "bl_override_revenue",
				"Margin $": "bl_override_gm_dollar",
			},
			{
				"Margin %": "bl_override_gm_percent",
				"AUR $": "bl_override_aur",
				"AUM $": "bl_override_aum",
			},
			{
				"ST %": "bl_override_st_percent",
				"Markdown $": "bl_override_markdown_dollar",
			},
		],
	},
	last_week: {
		label: "Last Week",
		finalize: false,
		dataKeys: [
			{
				Units: "lw_sales_units",
				"Revenue $": "lw_revenue",
				"Margin $": "lw_gm_dollar",
			},
			{
				"Margin %": "lw_gm_percent",
				"AUR $": "lw_aur",
				"AUM $": "lw_aum",
			},
			{
				"ST %": "lw_st_percent",
				"Markdown $": "lw_markdown_dollar",
			},
		],
	},
	historical: {
		label: "Historical",
		finalize: false,
		dataKeys: [
			{
				Units: "till_date_sales_units",
				"Revenue $": "till_date_revenue",
				"Margin $": "till_date_gm_dollar",
			},
			{
				"Margin %": "till_date_gm_percent",
				"AUR $": "till_date_aur",
				"AUM $": "till_date_aum",
			},
			{
				"ST %": "till_date_st_percent",
				"Markdown $": "till_date_markdown_dollar",
			},
		],
	},
};

export const columnsForIaTable = {
	bl_override: {
		label: "Finalized",
		id: "bl_override",
	},
	ia_reco: {
		label: "IA Recom",
		id: "ia_reco",
	},
};

export const lockColumnsKey = ["product_level_value"];

export const cellsWithLockAndInput = ["bl_override", "ia_reco"];

export const optimizationType = {
	ia_optimization: 0,
	partial_optimization: 1,
	no_optimization: 2,
};

export const disbleColumnMenu = ["price_point"];

export const APPROVAL_OVERALL_METRICS = {
	units_sold: {
		label: "Units Sold",
		itemIcon: "inventory",
	},
	revenue: {
		label: "Revenue",
		itemIcon: "signal_cellular_alt",
	},
	margin: {
		label: "Margin",
		itemIcon: "paid",
	},
	sell_through: {
		label: "ST %",
		itemIcon: "percent",
	},
};

//// Impact V3 Constants

export const global_labels = {
	l0_ids: "Division",
	l1_ids: "Group",
	l2_ids: "Department",
	l3_ids: "Class",
	l4_ids: "Sub Class",
	l5_ids: "Parent",
	l6_ids: "Style",
	color_id: "Color ID",
	style_color: "SKU",
	brand: "Brand",
	lifecycle_indicator_ids: "Lifecycle Indicator",
	s0_ids: "Country",
	s1_ids: "Channel",
	s2_ids: "Group",
	s3_ids: "District",
	s4_ids: "State",
	s5_ids: "City",
	store_ids: "Store ID",
	dateRange: "Date Range",
	completedOffers: "Completed Offers",
	store_launch_date: "Store Launch Date",
	ecomm_launch_date: "Ecomm Launch Date",
	color: "Color",
	tier: "Store Tier",
	date_range: "Date Range",
	country: "Country",
	store_type: "Store Type",
	international_store: "International Store",
	store_status: "Active Stores",
	store_tier: "Store Tier",
	store_id: "Store ID",
	store_code: "Store ID",
	store_name: "Store Name",
	region: "Region",
	state: "State",
	product_code: "Product ID",
	product_name: "Product Name",
	department: "Department",
	product_class: "Class",
	product_subclass: "Sub-Class",
	item_group: "Item Group",
	bin: "Tier",
	events: "Events",
	promos: "Promotions",
	overall: "Overall",
	finalized_markdown: "Markdown $",
	projected_markdown: "Projected Markdown $",
	actuals_markdown: "Actualized Markdown $",
	gross_margin_target: "Target GM",
	target_gross_margin: "Target GM",
	target_gm_dollar: "Target GM",
	ia_projected_gm_dollar: "IA Projected GM",
	sales_units: "Sales Unit",
	revenue: "Revenue",
	bl_over_ride_gm_dollar: "Finalized GM",
	markdown_dollar: "Markdown $",
	finalized_margin: "Finalized GM",
	finalized_gross_margin: "Finalized GM",
	finalized_baseline_margin: "Baseline GM",
	finalized_baseline_gross_margin: "Baseline GM",
	finalized_incremental_margin: "Incremental GM",
	finalized_incremental_gross_margin: "Incremental GM",
	projected_margin: "IA Projected GM",
	projected_gross_margin: "IA Projected GM",
	revenue_target: "Target Revenue",
	target_revenue: "Target Revenue",
	finalized_revenue: "Finalized Revenue",
	finalized_baseline_revenue: "Baseline Revenue",
	finalized_incremental_revenue: "Incremental Revenue",
	projected_revenue: "IA Projected Revenue",
	sales_units_target: "Target Sales Units",
	target_sales_units: "Target Sales Units",
	finalized_sales_units: "Finalized Sales Units",
	finalized_baseline_sales_units: "Baseline Sales Units",
	finalized_incremental_sales_units: "Incremental Sales Units",
	actuals_sales_units: "Actualized Sales Units",
	actuals_revenue: "Actualized Revenue",
	actuals_gross_margin: "Actualized GM",
	projected_sales_units: "IA Projected Sales Units",
	scenario_1: "Scenario 1",
	scenario_2: "Scenario 2",
	affinity_margin: "Margin Affinity",
	affinity_sales: "Sales Affinity",
	aum: "AUM $",
	aur: "AUR $",
	baseline_margin: "Baseline Margin",
	baseline_sales: "Baseline Revenue",
	baseline_sales_units: "Baseline Sales Units",
	cannibalization_margin: "Cannibalization",
	cannibalization_sales: "Cannibalization",
	incremental_margin: "Incremental Margin",
	incremental_sales: "Incremental Revenue",
	incremental_sales_units: "Incremental Sales Units",
	gross_margin: "GM $",
	margin: "GM $",
	baseline: "Baseline",
	sales: "Sales",
	incremental: "Incremental",
	pull_forward_margin: "Pull Forward",
	pull_forward_sales: "Pull Forward",
	revenue: "Revenue",
	sales_units: "Sales Units",
	gm_percent: "GM %",
	affinity: "Affinity",
	cannibalization: "Cannibalization",
	pull_forward: "Pull Forward",
	total: "Total",
	bxgy: "BxGy",
	percent_off: "% Off",
	extra_amount_off: "Amount Off",
	fixed_price: "PP",
	projected: "IA Projected",
	target: "Target",
	finalized: "Finalized",
	actual: "Actual",
	lift: "Lift",
	event: "Campaign",
	promo: "Promotions",
	promo_short: "Promo",
	net_incremental: "Lift",
	promo_spend: "Markdown Spend ($)",
	baseline_margin: "Baseline Margin",
	margin_positive: "Margin Positive Promotions",
	margin_negative: "Margin Negative Promotions",
	baseline_revenue: "Baseline Revenue",
	revenue_positive: "Revenue Positive Promotions",
	revenue_negative: "Revenue Negative Promotions",
	channelOptions: "Channel Type",
	adTypeOptions: "Ad Type",
	strategy_id: "Strategy",
	calendar_config_ids: "Calendar Configuration",
	rule_type: "Rule Type",
	rule_flexibility_type_id: "Flexibility",
	rule_id: "Rule Name",
	//chart labels 2nd step create strategy
	store_oh: "Store OH",
	dc_oh: "DC OH",
	dc_on_order: "DC on Order",
	store_in_transit: "Store in Transit",
	strategy_status: "Strategy Status",
	pcd_types: "PCD Type",
	start_days: "Repeat/Start day",
	pcd_start_date: "PCD",
	mkd_type: "Markdown Type",
	units: "Units",
};

export const SPECIAL_CHARACTER_MAPPING = {
	__ia_char_01: "'",
	__ia_char_02: '"',
	__ia_char_03: "/",
	__ia_char_04: "\\",
	__ia_char_05: "`",
	__ia_char_06: "~",
	__ia_char_07: "!",
	__ia_char_08: "@",
	__ia_char_09: "#",
	__ia_char_10: "$",
	__ia_char_11: "%",
	__ia_char_12: "^",
	__ia_char_13: "&",
	__ia_char_14: "*",
	__ia_char_15: "(",
	__ia_char_16: ")",
	__ia_char_19: "=",
	__ia_char_20: "+",
	__ia_char_21: "{",
	__ia_char_22: "}",
	__ia_char_23: "[",
	__ia_char_24: "]",
	__ia_char_25: "|",
	__ia_char_26: ":",
	__ia_char_27: ";",
	__ia_char_28: "<",
	__ia_char_29: ">",
	__ia_char_30: ",",
	__ia_char_31: ".",
	__ia_char_32: "?",
};

// The backend expects fields to be in the exact order defined in the following configuration
export const PRODUCT_SELECTION_VALID_INVALID_TABLE = [
	{
        field: "client_product_id",
        headerName: "Product ID",
        flex: 1
    }
]

// The backend expects fields to be in the exact order defined in the following configuration
export const STORE_SELECTION_VALID_INVALID_TABLE = [
	{
        field: "store_id",
        headerName: 'Store ID',
        flex: 1
    }
]

export const HYPERLINK_PRODUCT_TABLE_CONFIG = (hierarchyGlobalKeys) => ([
	{
        field: "l0_name",
        headerName: hierarchyGlobalKeys?.l0_ids,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l2_name",
        headerName: hierarchyGlobalKeys?.l2_ids,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "l3_name",
        headerName: hierarchyGlobalKeys?.l3_ids,
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    },
    {
        field: "brand",
        headerName: "Brand",
        valueFormatter: (params) => replaceSpecialCharacter(params.value),
    }
])