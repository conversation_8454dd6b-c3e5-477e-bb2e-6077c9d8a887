import DecisionDashboardIcon from "../assets/imageAssets/decisionDashboardIcon.svg?.url";
import WorkbenchIcon from "../assets/imageAssets/workbenchIcon.svg?.url";
import MarketingCalendarIcon from "../assets/imageAssets/marketingCalendarIcon.svg?.url";
import ConfigurationIcon from "../assets/imageAssets/configurationIcon.svg?.url";
import ReportingIcon from "../assets/imageAssets/reportingIcon.svg?.url";
import { global_labels } from "./Constants";
import { capitalizeFirstLetter } from "../utils/helpers/utility_helpers";
//if you are adding/modifying routes and if applicable, make sure to update the protectedRoutes, breadcrumbRoutes and sideBarRoutes

export const getProtectedRoutes = () => {
	return [
		{
			id: "decision-dashboard",
			path: "/pricesmart-promo/decision-dashboard",
			component: "screens/decisionDashboard/DecisionDashboard",
		},
		{
			id: "workbench",
			path: "/pricesmart-promo/workbench",
			component: "screens/workbench/Workbench",
		},
		{
			id: "marketing-calendar",
			path: "/pricesmart-promo/marketing-calendar",
			component: "screens/marketingCalendar/MarketingCalendar",
		},
		{
			id: "product-group-config",
			path: "/pricesmart-promo/product-group-config",
			component:
				"screens/productGroupConfiguration/ProductGroupConfiguration",
		},
		{
			id: "create-product-group",
			path: "/pricesmart-promo/product-group-config/create-product-group",
			component:
				"screens/productGroupConfiguration/createProductGroupConfiguration/CreateProductGroupConfiguration",
		},
		{
			id: "store-group-config",
			path: "/pricesmart-promo/store-group-config",
			component: "screens/storeGroupConfiguration/StoreGroupConfiguration",
		},
		{
			id: "create-store-group",
			path: "/pricesmart-promo/store-group-config/create-store-group",
			component:
				"screens/storeGroupConfiguration/createStoreGroupConfiguration/CreateStoreGroupConfiguration",
		},
		{
			id: "reporting",
			path: "/pricesmart-promo/reporting",
			component: "screens/reporting/Reporting",
		},
		{
			id: "create-offer",
			path: `/pricesmart-promo/workbench/create-offer`,
			component: "screens/offer/Offer",
		},
		// {
		// 	id: "create-event",
		// 	path: `/pricesmart-promo/marketing-calendar/create-${global_labels?.event?.toLocaleLowerCase()}`,
		// 	component: "screens/marketingCalendar/createEvent/CreateEvent",
		// },
		{
			id: "create-event-configurable",
			path: `/pricesmart-promo/marketing-calendar/create-event`,
			component: "screens/marketingCalendar/createEvent/CreatEventConfigurable",
		},
		{
			id: "not-found",
			path: "/pricesmart-promo/not-found",
			// not given component cause we will directly fetch component and pass, as the NotFound component
			// is found in containers/common/NotFound not from components/..
		},
	];
};

export const sideBarRoutes = [
	{
		link: "/pricesmart-promo/decision-dashboard",
		label: "Decision Dashboard",
		value: "decision-dashboard",
		children: [],
		icon: <img src={DecisionDashboardIcon} alt="decision dashboard" />,
	},
	{
		link: "/pricesmart-promo/marketing-calendar",
		label: "Marketing Calendar",
		value: "marketing-calendar",
		children: [],
		icon: <img src={MarketingCalendarIcon} alt="marketing calendar" />,
	},
	{
		link: "/pricesmart-promo/workbench",
		label: `${capitalizeFirstLetter(global_labels?.promo_primary)} Workbench`,
		value: "workbench",
		children: [],
		icon: <img src={WorkbenchIcon} alt="workbench" />,
	},
	{
		link: "/pricesmart-promo/configurations",
		label: "Configuration",
		value: "configuration",
		children: [
			{
				children: [],
				label: "Store Configuration",
				link: "/pricesmart-promo/store-group-config",
				value: "store-group-config",
			},
			{
				children: [],
				label: "Product Configuration",
				link: "/pricesmart-promo/product-group-config",
				value: "product-group-config",
			},
		],
		icon: <img src={ConfigurationIcon} alt="configuration"/>,
	},
	{
		link: "/pricesmart-promo/reporting",
		label: "Reporting",
		value: "reporting",
		children: [],
		icon: <img src={ReportingIcon} alt="reporting"/>
	},

]

export const breadcrumbRoutes = () => {
	return {
		decisionDashboard: [
			{
				label: "Decision Dashboard",
				path: "/pricesmart-promo/decision-dashboard",
			},
		],
		marketingCalendar: [
			{
				label: "Marketing Calendar",
				path: "/pricesmart-promo/marketing-calendar",
			},
		],
		workbench: [
			{
				label: `${capitalizeFirstLetter(global_labels?.promo_primary)} Workbench`,
				path: "/pricesmart-promo/workbench",
			},
		],
		storeConfiguration: [
			{
				label: "Store Configuration",
				path: "/pricesmart-promo/store-group-config",
			},
		],
		createStoreConfiguration: [
			{
				label: "Store Configuration",
				path: "/pricesmart-promo/store-group-config",
			},
			{
				label: "Create Store Group Config",
				path: "/pricesmart-promo/store-group-config/create-store-group",
			},
		],
		productConfiguration: [
			{
				label: "Product Configuration",
				path: "/pricesmart-promo/product-group-config",
			},
		],
		createProductConfiguration: [
			{
				label: "Product Configuration",
				path: "/pricesmart-promo/product-group-config",
			},
			{
				label: "Create Product Group Config",
				path: "/pricesmart-promo/product-group-config/create-product-group",
			}
		],
		reporting: [
			{
				label: "Reporting",
				path: "/pricesmart-promo/reporting",
			},
		],
		offer: [
			{
				label: `${capitalizeFirstLetter(global_labels?.promo_primary)} Workbench`,
				path: "/pricesmart-promo/workbench",
			},
			{
				label: `Create ${capitalizeFirstLetter(global_labels?.promo_alias)}`,
				path: "/pricesmart-promo/workbench/create-offer",
			},
		],
		createEvent: [
			{
				label: "Marketing Calendar",
				path: "/pricesmart-promo/marketing-calendar",
			},
			{
				label: `Create ${capitalizeFirstLetter(global_labels?.event_primary)}`,
				path: "/pricesmart-promo/marketing-calendar/create-event",
			},
		],
	}
}
