import moment from "moment";
import { global_labels } from "./Constants";
import { capitalizeFirstLetter } from "../utils/helpers/utility_helpers";

// -----------------------------Common Product Filter Config ------------------------------------------------------------
export const wholeProductFilterConfig = [
    {
        filterId: "l0_ids",
        filterType: "dropdown",
        apiEndpoint: "product-filters",
        section: "product_hierarchy",
        isMandatory: true,
        isMulti: true,
        selectOnLoad: true,
        selection: "All",
    },
    {
        filterId: "l1_ids",
        filterType: "dropdown",
        apiEndpoint: "product-filters",
        section: "product_hierarchy",
        isMandatory: true,
        isMulti: true,
        selectOnLoad: true,
        selection: "All",
    },
    {
        filterId: "l2_ids",
        filterType: "dropdown",
        apiEndpoint: "product-filters",
        section: "product_hierarchy",
        isMandatory: true,
        isMulti: true,
        selectOnLoad: true,
        selection: "All",
    },
    {
        filterId: "l3_ids",
        filterType: "dropdown",
        apiEndpoint: "product-filters",
        section: "product_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "l4_ids",
        filterType: "dropdown",
        apiEndpoint: "product-filters",
        section: "product_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "l5_ids",
        filterType: "dropdown",
        apiEndpoint: "product-filters",
        section: "product_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "brand",
        filterType: "dropdown",
        apiEndpoint: "product-filters",
        section: "product_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "lifecycle_indicator_ids",
        filterType: "dropdown",
        apiEndpoint: "lifecycle-indicators",
        section: "product_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
];

export const commonProductFilterConfig = [
   ...wholeProductFilterConfig.filter(item => !["l5_ids", "lifecycle_indicator_ids"].includes(item.filterId)),
]

export const wholeStoreFilterConfig = [
    {
        filterId: "s0_ids",
        filterType: "dropdown",
        apiEndpoint: "store-filters",
        section: "store_hierarchy",
        isMandatory: true,
        isMulti: true,
        selectOnLoad: true,
        selection: "All",
    },
    {
        filterId: "s1_ids",
        filterType: "dropdown",
        apiEndpoint: "store-filters",
        section: "store_hierarchy",
        isMandatory: true,
        isMulti: true,
        selectOnLoad: true,
        selection: "All",
    },
    {
        filterId: "s2_ids",
        filterType: "dropdown",
        apiEndpoint: "store-filters",
        section: "store_hierarchy",
        isMulti: true,
        isMandatory: false,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "s3_ids",
        filterType: "dropdown",
        apiEndpoint: "store-filters",
        section: "store_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "s4_ids",
        filterType: "dropdown",
        apiEndpoint: "store-filters",
        section: "store_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "s5_ids",
        filterType: "dropdown",
        apiEndpoint: "store-filters",
        section: "store_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
    },
    {
        filterId: "store_ids",
        filterType: "dropdown",
        apiEndpoint: "store-filters",
        section: "store_hierarchy",
        isMandatory: false,
        isMulti: true,
        selectOnLoad: false,
        selection: null,
      },
]

export const commonStoreFilterConfig = [
    ...wholeStoreFilterConfig.filter(item => !["s3_ids", "s4_ids", "s5_ids", "store_ids"].includes(item.filterId)),
]

// -----------------------------Decision Dashboard ------------------------------------------------------------
export const decisionDashboardFilterConfig = [
    {
        id: 1,
        // numberOfFilter: 0,
        required: true,
        title: "Product",
        value: "product_hierarchy",
        groupConfig: [
            ...commonProductFilterConfig,
        ],
    },
    {
        id: 2,
        // numberOfFilter: 0,
        required: true,
        title: "Store",
        value: "store_hierarchy",
        groupConfig: [
            ...commonStoreFilterConfig,
        ],
    },
    {
        id: 3,
        // numberOfFilter: 0,
        required: true,
        title: "Time Period",
        value: "date_selection",
        groupConfig: [
            {
                filterId: "dateRange",
                filterType: "dateRange",
                apiEndpoint: null,
                section: "date_selection",
                isMandatory: true,
                selectOnLoad: true,
                start_date: moment().subtract(8, "weeks"),
                end_date: moment().add(4, "weeks"),
                row: 1,
            },
            {
                filterId: "event",
                filterType: "dropdown",
                apiEndpoint: "marketing-calendar/events",
                section: "date_selection",
                row: 1,
                isMandatory: false,
                isMulti: true,
                selection: "All",
                extraParams: {
                    is_finalized: true,
                }
            },
            {
                filterId: "promo",
                filterType: "dropdown",
                apiEndpoint: "promos",
                section: "date_selection",
                row: 1,
                isMandatory: false,
                isMulti: true,
                selection: "All",
                extraParams: {
                    is_finalized: true,
                }
            },
            {
                filterId: "show_partially_overlapping_events",
                label: `Show Partially Overlapping ${global_labels?.event_plural}`,
                filterType: "custom",
                section: "date_selection",
                row: 0,
                resetData: false,
            },
            {
                filterId: "metrics_display_mode",
                filterType: "custom",
                resetData: false,
                section: "date_selection",
                row: 2,
                options: [
                    {
                        label: `Enitre ${global_labels?.promo_alias} Duration`,
                        value: "entire_offer_duration",
                    },
                    {
                        label: "Selected Date Range",
                        value: "selected_date_range",
                    }
                ]
            }
        ],
    },
];

export const decisionDashboardRequiredFiltersOnLoad = [
    "l0_ids",
    "l1_ids",
    "l2_ids",
    "s0_ids",
    "s1_ids",
    "dateRange",
];

// -----------------------------Marketing Calendar ------------------------------------------------------------
export const marketingCalendarFilterConfig = [
    {
        id: 1,
        // numberOfFilter: 0,
        required: true,
        title: "Product",
        value: "product_hierarchy",
        groupConfig: [
            ...commonProductFilterConfig,
        ],
    },
    {
        id: 2,
        // numberOfFilter: 0,
        required: true,
        title: "Store",
        value: "store_hierarchy",
        groupConfig: [
            ...commonStoreFilterConfig,
        ],
    },
    {
        id: 3,
        // numberOfFilter: 0,
        required: true,
        title: "Time Period",
        value: "date_selection",
        groupConfig: [
            {
                filterId: "dateRange",
                filterType: "dateRange",
                apiEndpoint: null,
                section: "date_selection",
                isMandatory: true,
                selectOnLoad: true,
                start_date: moment().startOf("day"),
                end_date: moment().add(6, "weeks").endOf("day"),
                row: 1,
            },
            {
                filterId: "event",
                filterType: "dropdown",
                apiEndpoint: "marketing-calendar/events",
                section: "date_selection",
                isMandatory: false,
                isMulti: true,
                selection: "All",
                row: 1,
            },
            {
                filterId: "promo",
                filterType: "dropdown",
                apiEndpoint: "promos",
                section: "date_selection",
                isMandatory: false,
                isMulti: true,
                selection: "All",
                row: 1,
            },
            {
                filterId: "show_partially_overlapping_events",
                label: `Show Partially Overlapping ${global_labels?.event_plural}`,
                filterType: "custom",
                apiEndpoint: null,
                section: "date_selection",
                resetData: false,
                isMandatory: false,
                row: 0,
            },
            {
                filterId: "metrics_display_mode",
                filterType: "custom",
                apiEndpoint: null,
                section: "date_selection",
                resetData: false,
                isMandatory: false,
                row: 2,
                options: [
                    {
                        label: `Entire ${global_labels?.promo_alias} Duration`,
                        value: "entire_offer_duration",
                    },
                    {
                        label: "Selected Date Range",
                        value: "selected_date_range",
                    },
                ],
            },
        ],
    },
];

export const marketingCalendarRequiredFiltersOnLoad = [
    "l0_ids",
    "l1_ids",
    "l2_ids",
    "s0_ids",
    "s1_ids",
    "dateRange",
];

// -----------------------------Workbench ------------------------------------------------------------
export const workbenchFilterConfig = [
    {
        id: 1,
        // numberOfFilter: 0,
        required: true,
        title: "Product",
        value: "product_hierarchy",
        groupConfig: [
            ...commonProductFilterConfig,
        ],
    },
    {
        id: 2,
        // numberOfFilter: 0,
        required: true,
        title: "Store",
        value: "store_hierarchy",
        groupConfig: [
            ...commonStoreFilterConfig,
        ],
    },
    {
        id: 3,
        // numberOfFilter: 0,
        required: true,
        title: "Time Period",
        value: "date_selection",
        groupConfig: [
            {
                filterId: "dateRange",
                filterType: "dateRange",
                apiEndpoint: null,
                section: "date_selection",
                isMandatory: true,
                selectOnLoad: true,
                // minDate: moment().subtract(4, "weeks"),
                // maxDate: moment().add(9, "weeks"),
                start_date: moment(),
                end_date: moment().add(4, "weeks"),
                row: 1,
            },
            {
                filterId: "event",
                filterType: "dropdown",
                apiEndpoint: "marketing-calendar/events",
                section: "date_selection",
                isMandatory: false,
                isMulti: true,
                selection: "All",
                row: 1,
            },
            {
                filterId: "promo",
                filterType: "dropdown",
                apiEndpoint: "promos",
                section: "date_selection",
                isMandatory: false,
                isMulti: true,
                selection: "All",
                row: 1,
            },
            {
                filterId: "show_partially_overlapping_events",
                label: `Show Partially Overlapping ${global_labels?.event_plural}`,
                filterType: "custom",
                apiEndpoint: null,
                section: "date_selection",
                resetData: false,
                row: 0,
            },
            {
                filterId: "metrics_display_mode",
                filterType: "custom",
                section: "date_selection",
                resetData: false,
                row: 2,
                options: [
                    {
                        label: `Entire ${global_labels?.promo_alias} Duration`,
                        value: "entire_offer_duration",
                    },
                    {
                        label: "Selected Date Range",
                        value: "selected_date_range",
                    },
                ],
            }
        ],
    },
];

export const workbenchRequiredFiltersOnLoad = [
    "l0_ids",
    "l1_ids",
    "l2_ids",
    "s0_ids",
    "s1_ids",
    "dateRange",
];

// -----------------------------Store Group Configuration ------------------------------------------------------------
export const storeGroupConfigurationFilterConfig = [
    {
        id: 1,
        // numberOfFilter: 0,
        required: true,
        title: "Store Selection",
        value: "store_hierarchy",
        groupConfig: [
            ...commonStoreFilterConfig,
        ]
    }
];

export const storeGroupConfigurationRequiredFiltersOnLoad = [
    "s0_ids",
    "s1_ids",
];

// -----------------------------Product Group Configuration ------------------------------------------------------------
export const productGroupConfigurationFilterConfig = [
    {
        id: 1,
        required: true,
        title: "Product",
        value: "product_hierarchy",
        groupConfig: [
            ...commonProductFilterConfig,
        ]
    }
];

export const productGroupConfigurationRequiredFiltersOnLoad = [
    "l0_ids",
    "l1_ids",
    "l2_ids",
];

// -----------------------------Reporting ------------------------------------------------------------
export const reportingFilterConfig = [
    {
        id: 1,
        required: true,
        title: "Product",
        value: "product_hierarchy",
        groupConfig: [
            ...commonProductFilterConfig,
        ],
    },
    {
        id: 2,
        required: true,
        title: "Store",
        value: "store_hierarchy",
        groupConfig: [
            ...commonStoreFilterConfig,
        ],
    },
    {
        id: 3,
        required: true,
        title: `Select ${global_labels?.promo_alias_plural} and Date Range`,
        value: "date_selection",
        groupConfig: [
            {
                filterId: "dateRange",
                filterType: "dateRange",
                apiEndpoint: null,
                section: "date_selection",
                isMandatory: true,
                selectOnLoad: true,
                // minDate: moment().subtract(4, "weeks"),
                // maxDate: moment().add(9, "weeks"),
                start_date: moment().subtract(4, "weeks"),
                end_date: moment(),
            },
            {
                filterId: "event",
                filterType: "dropdown",
                apiEndpoint: "marketing-calendar/events",
                section: "date_selection",
                isMandatory: false,
                isMulti: true,
                selection: "All",
            },
            {
                filterId: "completedOffers",
                filterType: "dropdown",
                apiEndpoint: "get-promos-by-filters",
                section: "date_selection",
                isMulti: true,
                isMandatory: false,
                selection: null,
            },
        ],
    },
];

export const reportingRequiredFiltersOnLoad = [
    "l0_ids",
    "l1_ids",
    "l2_ids",
    "s0_ids",
    "s1_ids",
    "dateRange",
];

// -----------------------------Other ------------------------------------------------------------
// for the download filter and reporting filtering
export const productSelection = [
    {
        label: "Overall",
        key: -200,
    },
    {
        label: "Division",
        key: 0,
    },
    {
        label: "Group",
        key: 1,
    },
    {
        label: "Department",
        key: 2,
    },
    {
        label: "Class",
        key: 3,
    },
    {
        label: "Sub Class",
        key: 4,
    },
    {
        label: "SKU",
        key: 5,
    },
    {
        label: "Brand",
        key: -1,
    },
];

export const storeSelection = [
    {
        label: "Overall",
        key: -200,
    },
    {
        label: "Country",
        key: 0,
    },
    {
        label: "Channel",
        key: 1,
    },
];

export const timeSelection = [
    {
        label: "Overall",
        key: -200,
    },
    {
        label: "Day",
        key: 0,
    },
    {
        label: "Week",
        key: 1,
    },
];
// check the offer level in the reporting constants when changing the offer level here, as it might be needed for the offer level filter in the reporting
export const offerLevel = [
    {
        label: "Overall",
        key: -200,
    },
    {
        label: `${capitalizeFirstLetter(global_labels?.promo_alias)}`,
        key: "promo_id",
    },
	{
		label: `${capitalizeFirstLetter(global_labels?.event_primary)}`,
		key: "event_id",
	},
];