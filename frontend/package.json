{"name": "pricesmart", "version": "1.0.1", "private": true, "dependencies": {"@date-io/date-fns": "^2.11.0", "@emotion/react": "^11.7.0", "@emotion/styled": "^11.6.0", "@mui/base": "^5.0.0-beta.18", "@mui/icons-material": "^5.2.1", "@mui/lab": "^5.0.0-alpha.59", "@mui/material": "^5.2.3", "@mui/styles": "^5.8.0", "@mui/x-date-pickers": "^5.0.0-alpha.4", "@pmmmwh/react-refresh-webpack-plugin": "0.5.11", "@reduxjs/toolkit": "^1.6.2", "@svgr/webpack": "^6.5.1", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@typescript-eslint/eslint-plugin": "^4.5.0", "@typescript-eslint/parser": "^4.5.0", "@yaireo/tagify": "4.9.4", "ag-grid-community": "^32.1.0", "ag-grid-enterprise": "^32.1.0", "ag-grid-react": "^32.1.0", "axios": "^0.24.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.0", "babel-loader": "9.1.0", "babel-plugin-named-asset-import": "^0.3.7", "babel-preset-react-app": "^10.0.0", "bfj": "^7.0.2", "camelcase": "^6.1.0", "case-sensitive-paths-webpack-plugin": "2.3.0", "crypto-js": "^4.2.0", "css-loader": "^6.7.3", "date-fns": "^2.27.0", "dompurify": "^3.2.4", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "env-cmd": "^10.1.0", "eslint": "^8.31.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "eslint-webpack-plugin": "^2.5.2", "file-loader": "6.2.0", "file-saver": "^2.0.5", "firebase": "^8.2.9", "fs-extra": "^9.0.1", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "history": "^5.3.0", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "3.0.0", "impact-ui": "^3.5.6-alpha.17", "impact-ui-v3": "npm:impact-ui@3.5.5-alpha.17", "jest": "26.6.0", "jest-circus": "26.6.0", "jest-resolve": "26.6.0", "jest-watch-typeahead": "0.6.1", "jwt-decode": "^4.0.0", "mini-css-extract-plugin": "^2.7.2", "moment": "^2.29.1", "moment-timezone": "^0.5.47", "notistack": "^3.0.1", "numeral": "^2.0.6", "optimize-css-assets-webpack-plugin": "5.0.4", "pnp-webpack-plugin": "1.6.4", "postcss-flexbugs-fixes": "4.2.1", "postcss-loader": "^7.0.2", "postcss-normalize": "8.0.1", "postcss-preset-env": "^7.8.3", "postcss-safe-parser": "5.0.2", "posthog-js": "^1.43.1", "proj4": "^2.8.0", "prompts": "2.4.0", "react": "^17.0.2", "react-app-polyfill": "^2.0.0", "react-dates": "^21.8.0", "react-dev-utils": "^11.0.3", "react-dom": "^17.0.2", "react-dropzone": "^14.2.3", "react-firebaseui": "^6.0.0", "react-lazyload": "^3.2.0", "react-loading-overlay": "^1.0.1", "react-multiselect-checkboxes": "^0.1.1", "react-numeric-input": "^2.2.3", "react-papaparse": "^4.4.0", "react-redux": "^7.2.6", "react-refresh": "^0.14.0", "react-router": "6.3.0", "react-router-dom": "^5.2.0", "react-router-dom-v5-compat": "^6.22.3", "react-select": "^2.4.4", "react-slick": "^0.29.0", "react-uuid": "^2.0.0", "resolve": "1.18.1", "resolve-url-loader": "^3.1.2", "rfc4648": "^1.5.4", "sass": "~1.32.12", "sass-loader": "^13.2.0", "semver": "7.3.2", "sheetjs-style": "^0.15.8", "slick-carousel": "^1.8.1", "style-loader": "^3.3.1", "styled-components": "^5.3.3", "terser-webpack-plugin": "4.2.3", "ts-pnp": "1.2.0", "url-loader": "4.1.1", "web-vitals": "^1.1.2", "webpack": "5.72.1", "webpack-dev-server": "4.9.3", "webpack-manifest-plugin": "2.2.0", "workbox-webpack-plugin": "5.1.4"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "7.20.7", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.12.5", "@fortawesome/fontawesome-free": "^5.13.0", "@svgr/webpack": "^6.5.1", "babel-loader": "^9.1.0", "babel-plugin-module-resolver": "^3.2.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^4.2.2", "dotenv-webpack": "^8.1.0", "eslint": "^8.31.0", "eslint-config-prettier": "^8.6.0", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-prettier": "^4.2.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.7.2", "postcss-loader": "^7.0.2", "postcss-preset-env": "^7.8.3", "prettier": "2.0.5", "process": "^0.11.10", "react-refresh": "^0.14.0", "redux-devtools-extension": "^2.13.8", "sass": "~1.32.12", "sass-loader": "^13.2.0", "style-loader": "^3.3.1", "webpack": "5.72.1", "webpack-cli": "4.10.0", "webpack-dev-server": "4.9.3", "webpack-merge": "^5.8.0"}, "scripts": {"start": "git submodule init && git submodule update --remote && cross-env NODE_ENV=development ENVIRONMENT=devs webpack serve --config config/webpack.dev.js", "build": "git submodule init && git submodule update --remote && cross-env NODE_ENV=production ENVIRONMENT=production webpack --config config/webpack.prod.js", "build:saks-demo": "git submodule init && git submodule update --remote &&cross-env NODE_ENV=production ENVIRONMENT=demo CLIENT=saks webpack --config config/webpack.prod.js", "build:pricesmart-dev": "git submodule init && git submodule update --remote &&cross-env NODE_ENV=production ENVIRONMENT=devs CLIENT=generic webpack --config config/webpack.prod.js", "build:pricesmart-test": "git submodule init && git submodule update --remote && cross-env NODE_ENV=production ENVIRONMENT=test CLIENT=generic webpack --config config/webpack.prod.js", "build:saks-uat": "cross-env NODE_ENV=production ENVIRONMENT=uat webpack --config config/webpack.prod.js", "build:saks-prod": "cross-env NODE_ENV=production ENVIRONMENT=production webpack --config config/webpack.prod.js", "test": "node scripts/test.js", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}